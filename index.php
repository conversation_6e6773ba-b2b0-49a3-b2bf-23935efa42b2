<?php
// Modern Taxi Freddy Website - Based on provided layout example
?>
<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>i <PERSON> - <PERSON>veren & Waasland</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        /* Header Navigation */
        .header-nav {
            background: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            color: #333;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-menu a:hover {
            color: #009FE8;
        }

        .cta-button {
            background: #FFD700;
            color: #333;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            background: #FFC107;
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect fill="%23333" width="1200" height="600"/><rect fill="%23555" x="100" y="200" width="300" height="200" rx="10"/><rect fill="%23555" x="500" y="200" width="300" height="200" rx="10"/><rect fill="%23555" x="900" y="200" width="200" height="200" rx="10"/></svg>');
            background-size: cover;
            background-position: center;
            color: white;
            text-align: center;
            padding: 120px 20px;
            margin-bottom: 80px;
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Services Grid */
        .services {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 80px;
        }

        .services-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .services-content h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.2;
        }

        .services-content p {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .service-features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .feature {
            text-align: center;
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: #FFD700;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
        }

        .feature h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .feature p {
            font-size: 0.95rem;
            color: #666;
            line-height: 1.5;
        }

        /* CTA Section */
        .cta-section {
            background: #FFD700;
            margin: 80px 20px;
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            max-width: 1160px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-section h2 {
            font-size: 2.2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
        }

        .cta-section p {
            font-size: 1.1rem;
            color: #555;
            margin-bottom: 30px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-section .cta-button {
            background: white;
            color: #333;
            padding: 15px 35px;
            font-size: 1.1rem;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .cta-section .cta-button:hover {
            background: #f8f9fa;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        /* Footer */
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 40px 20px;
            margin-top: 80px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 20px;
            }

            .nav-menu {
                gap: 20px;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1.1rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .service-features {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .services-content h2 {
                font-size: 2rem;
            }

            .cta-section {
                margin: 40px 20px;
                padding: 40px 20px;
            }

            .cta-section h2 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <nav class="header-nav">
        <div class="nav-container">
            <div class="logo">TAXI FREDDY</div>
            <ul class="nav-menu">
                <li><a href="#home">Home</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
            <a href="tel:0475266158" class="cta-button">Bellen nu</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <h1>Uw Betrouwbare Taxiservice in Beveren en het Waasland</h1>
            <p>Professioneel taxivervoer, 24 uur per dag. Bel ons voor betrouwbare vervoersdiensten!</p>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services">
        <div class="services-grid">
            <div class="services-content">
                <h2>Professioneel Taxivervoer voor elke gelegenheid</h2>
                <p>Taxi Freddy biedt een breed scala aan vervoersdiensten aan, afgestemd op uw specifieke behoeften.</p>
                <a href="tel:0475266158" class="cta-button">Reserveer nu</a>
            </div>

            <div class="service-features">
                <div class="feature">
                    <div class="feature-icon">⏰</div>
                    <h3>24/7 Beschikbaarheid</h3>
                    <p>Dag en nacht zijn wij van dienst voor al uw vervoersbehoeften.</p>
                </div>

                <div class="feature">
                    <div class="feature-icon">🚗</div>
                    <h3>Ervaren Chauffeurs</h3>
                    <p>Professionele, vriendelijke chauffeurs met uitstekende lokale kennis.</p>
                </div>

                <div class="feature">
                    <div class="feature-icon">📅</div>
                    <h3>Stipte Service</h3>
                    <p>Altijd op tijd voor uw geplande ritten.</p>
                </div>

                <div class="feature">
                    <div class="feature-icon">💰</div>
                    <h3>Vaste Prijzen</h3>
                    <p>Transparante tarieven zonder verborgen kosten.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <h2>Klaar om te Vertrekken? Boek uw Taxi nu!</h2>
        <p>Taxi Freddy staat voor u klaar. Onze professionele chauffeurs staan klaar om u veilig naar uw bestemming te brengen.</p>
        <a href="tel:0475266158" class="cta-button">Reserveer nu</a>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <p>Copyright © 2024</p>
    </footer>
</body>
</html>
