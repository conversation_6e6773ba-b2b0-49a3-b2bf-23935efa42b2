#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { InitializeResult } from './lib/schema/2024-11-05/schema.js';
import { wpRequest } from './lib/wordpress-api.js';
import { log } from './lib/utils.js';
import {
  CallToolRequestSchema,
  ListResourcesRequestSchema,
  ListToolsRequestSchema,
  ServerCapabilitiesSchema,
  ListPromptsRequestSchema,
  GetPromptRequestSchema,
  ListResourceTemplatesRequestSchema,
  ReadResourceRequestSchema,
  SubscribeRequestSchema,
  UnsubscribeRequestSchema,
  SetLevelRequestSchema,
  CompleteRequestSchema,
  ListRootsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';

// Define request types
type ListToolsRequest = z.infer<typeof ListToolsRequestSchema>;
type CallToolRequest = z.infer<typeof CallToolRequestSchema>;
type ListResourcesRequest = z.infer<typeof ListResourcesRequestSchema>;
type ListResourceTemplatesRequest = z.infer<typeof ListResourceTemplatesRequestSchema>;
type ReadResourceRequest = z.infer<typeof ReadResourceRequestSchema>;
type SubscribeRequest = z.infer<typeof SubscribeRequestSchema>;
type UnsubscribeRequest = z.infer<typeof UnsubscribeRequestSchema>;
type ListPromptsRequest = z.infer<typeof ListPromptsRequestSchema>;
type GetPromptRequest = z.infer<typeof GetPromptRequestSchema>;
type SetLevelRequest = z.infer<typeof SetLevelRequestSchema>;
type CompleteRequest = z.infer<typeof CompleteRequestSchema>;
type ListRootsRequest = z.infer<typeof ListRootsRequestSchema>;

// Check Node.js version
const requiredNodeVersion = 22;
const currentNodeVersion = parseInt(process.version.slice(1).split('.')[0]);
if (currentNodeVersion < requiredNodeVersion) {
  log(`Error: This application requires Node.js version ${requiredNodeVersion} or higher.`);
  log(`Current version: ${process.version}`);
  process.exit(1);
}

// Check if fetch is available
if (typeof globalThis.fetch !== 'function') {
  log(
    'Error: This application requires the fetch API, which is not available in your Node.js environment.'
  );
  log('Please ensure you are using Node.js 22 or later, or install node-fetch.');
  process.exit(1);
}

async function WordPressProxy() {
  const init = (await wpRequest({ method: 'initialize' })) as InitializeResult;

  const server = new Server(
    {
      name: init.serverInfo.name,
      version: init.serverInfo.version,
    },
    {
      capabilities: init.capabilities as any, // Type assertion to fix linter error
    }
  );

  const withLogging = (schema: string, handler: Function) => async (request: any) => {
    log(`Received ${schema} request:`, JSON.stringify(request));
    const response = await handler(request);
    log(`${schema} response:`, JSON.stringify(response));
    return response;
  };

  // List Tools Handler
  server.setRequestHandler(
    ListToolsRequestSchema,
    withLogging('ListTools', async (request: ListToolsRequest) => {
      log('Processing ListToolsRequest');
      log(JSON.stringify(request));
      const response = await wpRequest({
        method: 'tools/list',
        cursor: request.params?.cursor,
      });
      return response;
    })
  );

  // Call Tool Handler
  server.setRequestHandler(
    CallToolRequestSchema,
    withLogging('CallTool', async (request: CallToolRequest) => {
      log('Processing CallToolRequest');
      log(JSON.stringify(request));
      const response = await wpRequest({
        method: 'tools/call',
        name: request.params.name,
        arguments: request.params.arguments,
      });
      return response;
    })
  );

  // List Resources Handler
  server.setRequestHandler(
    ListResourcesRequestSchema,
    withLogging('ListResources', async (request: ListResourcesRequest) => {
      log('Processing ListResourcesRequest');
      log(JSON.stringify(request));
      const response = await wpRequest({
        method: 'resources/list',
        cursor: request.params?.cursor,
      });
      return response;
    })
  );

  // List Resource Templates Handler
  server.setRequestHandler(
    ListResourceTemplatesRequestSchema,
    withLogging('ListResourceTemplates', async (request: ListResourceTemplatesRequest) => {
      log('Processing ListResourceTemplatesRequest');
      log(JSON.stringify(request));
      const response = await wpRequest({
        method: 'resources/templates/list',
        cursor: request.params?.cursor,
      });
      return response;
    })
  );

  // Read Resource Handler
  server.setRequestHandler(
    ReadResourceRequestSchema,
    withLogging('ReadResource', async (request: ReadResourceRequest) => {
      log('Processing ReadResourceRequest');
      log(JSON.stringify(request));
      const response = await wpRequest({
        method: 'resources/read',
        uri: request.params.uri,
      });
      return response;
    })
  );

  // Subscribe Handler
  server.setRequestHandler(
    SubscribeRequestSchema,
    withLogging('Subscribe', async (request: SubscribeRequest) => {
      log('Processing SubscribeRequest');
      log(JSON.stringify(request));
      const response = await wpRequest({
        method: 'resources/subscribe',
        uri: request.params.uri,
      });
      return response;
    })
  );

  // Unsubscribe Handler
  server.setRequestHandler(
    UnsubscribeRequestSchema,
    withLogging('Unsubscribe', async (request: UnsubscribeRequest) => {
      log('Processing UnsubscribeRequest');
      log(JSON.stringify(request));
      const response = await wpRequest({
        method: 'resources/unsubscribe',
        uri: request.params.uri,
      });
      return response;
    })
  );

  // List Prompts Handler
  server.setRequestHandler(
    ListPromptsRequestSchema,
    withLogging('ListPrompts', async (request: ListPromptsRequest) => {
      log('Processing ListPromptsRequest');
      log(JSON.stringify(request));
      const response = await wpRequest({
        method: 'prompts/list',
        cursor: request.params?.cursor,
      });
      return response;
    })
  );

  // Get Prompt Handler
  server.setRequestHandler(
    GetPromptRequestSchema,
    withLogging('GetPrompt', async (request: GetPromptRequest) => {
      log('Processing GetPromptRequest');
      log(JSON.stringify(request));
      const response = await wpRequest({
        method: 'prompts/get',
        name: request.params.name,
        arguments: request.params.arguments,
      });
      return response;
    })
  );

  // Set Logging Level Handler
  server.setRequestHandler(
    SetLevelRequestSchema,
    withLogging('SetLevel', async (request: SetLevelRequest) => {
      log('Processing SetLevelRequest');
      log(JSON.stringify(request));
      const response = await wpRequest({
        method: 'logging/setLevel',
        level: request.params.level,
      });
      return response;
    })
  );

  // Complete Handler
  server.setRequestHandler(
    CompleteRequestSchema,
    withLogging('Complete', async (request: CompleteRequest) => {
      log('Processing CompleteRequest');
      log(JSON.stringify(request));
      const response = await wpRequest({
        method: 'completion/complete',
        ref: request.params.ref,
        argument: request.params.argument,
      });
      return response;
    })
  );

  // List Roots Handler
  server.setRequestHandler(
    ListRootsRequestSchema,
    withLogging('ListRoots', async (request: ListRootsRequest) => {
      log('Processing ListRootsRequest');
      log(JSON.stringify(request));
      const response = await wpRequest({
        method: 'roots/list',
      });
      return response;
    })
  );

  const transport = new StdioServerTransport();
  // Connect to the transport
  server
    .connect(transport)
    .then(() => {
      log('MCP server connected to transport successfully');
    })
    .catch(error => {
      log(`Error starting MCP server:`, error);
      process.exit(1);
    });
}

WordPressProxy();
