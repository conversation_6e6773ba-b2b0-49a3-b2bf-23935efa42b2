
# Website Build Specification – WordPress + Rank Math SEO

> **Gebruik**: Dit document dient als handleiding voor een AI‑agent (bijv. Augment in VS Code) die automatisch Markdown‑bestanden genereert én publiceert als WordPress‑pagina’s/‑berichten. Het doel is om een schaalbare, SEO‑geoptimaliseerde site op te leveren volgens de huidige (2025) Google SEO‑richtlijnen en de mogelijkheden van Rank Math Pro.

---

## 1 ▪︎ Basis­configuratie

| Onderdeel | Vereiste instelling | Waarom |
|-----------|--------------------|--------|
| **WordPress ↝ Algemeen** | Taal = Nederlands, Tijdzone = Europe/Brussels, Permalinks = `/%category%/%postname%/` | Consistent voor E‑E‑A‑T & lokale SEO |
| **Thema** | Blocksy (child‑theme) of gelijkwaardig lichtgewicht blokthema | Core Web Vitals, compatibel met Rank Math |
| **Rank Math – Setup Wizard** | **Mode** = Advanced<br>**Sitemap** = Enabled<br>**404 Monitor** = Enabled<br>**Redirections** = Enabled<br>**Schema** = Full schema graph<br>**Analytics** = Connected (GSC + GA4) | Volledige feature‑set benutten |
| **Performance** | Lazy‑loading, WebP, HTTP/2, object cache | Page Experience & snelheid |

---``

---

## 3 ▪︎ Globale front‑matter‑conventies  
*(geldt voor elk Markdown‑bestand)*

```yaml
---
# CORE
post_type: "{{post_type}}"        # page • post • kb
slug: "{{slug}}"
title: "{{rank_math_title}}"      # Max 60 karakters
excerpt: "{{rank_math_description}}"  # 150‑160 karakters
status: "publish"                 # of draft
layout: "default"                 # Voor thema‑template

# RANK MATH
rank_math_focus_keyword: "{{focus_kw}}"
rank_math_robots: "index, follow"
rank_math_schema_type: "{{schema_type}}" # Article • Service • FAQPage • HowTo …
rank_math_canonical_url: "{{canonical}}"
rank_math_breadcrumb_title: "{{breadcrumb}}"

# METADATA
author: "{{author_name}}"
last_modified: "{{lastmod}}" # YYYY‑MM‑DD
hero_image: "{{hero_img_url}}"
---
```

> ⚠︎ **AI‑agent**: VALIDATE dat `focus_kw` exact voorkomt in `title`, `H1`, `intro` en ten minste 1 x in de eerste 160 woorden.

---

## 4 ▪︎ Pagina‑sjablonen   *(kernblokken & instructies)*

### 4.1 Homepage
1. **Hero section** – Titel `{{USP}}`, subtitel, CTA‑knop.
2. **Diensten‑overzicht** – Tegels met pictogram → interne links.
3. **Klant­cases** – 3 cards (foto + quote).
4. **Kennisbank call‑out** – Laatste 3 artikelen.
5. **CTA‑banner** – Aanmelden/Contact.

*Schema*: `WebPage` + `Organization` logo.

---

### 4.2 Dienstpagina
```
# {{h1}}                       <!-- focus_kw in H1 -->
> {{wervende_intro}}

## Voordelen
- {{benefit_1}}
- {{benefit_2}}
- {{benefit_3}}

## Onze werkwijze in 3 stappen
1. {{step_1}}
2. {{step_2}}
3. {{step_3}}

## FAQ
<details><summary>{{faq_q1}}</summary>
{{faq_a1}}
</details>

[**Vraag een offerte aan ›**](/contact/)
```
*Schema*: `Service` + `FAQPage`.

---

### 4.3 SEO‑stadspagina
- Kopieer lay‑out van Dienstpagina.  
- Voeg blok **“Waarom {{stad}}?”** na de voordelen.  
- **Interne links**: verwijzing naar buursteden (max 3).

*Extra front‑matter*: `rank_math_geo_target: "{{stad}}"`  
*Schema*: `Service` + `LocalBusiness` (`areaServed` = {{stad}}).

---

### 4.4 Kennisbankartikel (How‑to)
- Intro (paragraaf) → waarom probleem belangrijk is.
- TOC (↝ anchor links H2‑H3).
- Stappenplan (H2’s genummerd).
- Screenshot‑galerij (alt‑tekst = beschrijving + focus_kw).
- CTA “Heb je hulp nodig?” → Contact.

*Schema*: `HowTo`.

---

### 4.5 Blogpost
- Hook‑paragraaf (< 100 woorden).
- H2‑secties elke 300‑400 woorden.
- Eén Callout‑box met tip/lawquote.
- Uitgelichte afbeelding 1200 × 675 px (WebP).

*Schema*: `Article` (subtype *BlogPosting*).

---

## 5 ▪︎ Interne linking‑regels
1. **Maximaal 1 exact match anchor** per pagina (spam‑preventie).
2. Gebruik synoniemen voor overige anchor‑teksten.
3. Minimaal 3 interne links naar dieper liggend niveau (silo‑bottom‑up).
4. Breadcrumbs worden door Rank Math automatisch ge‑renderd; **AI‑agent** hoeft enkel `rank_math_breadcrumb_title` in YAML te zetten.

---

## 6 ▪︎ Afbeeldingen & media
- Bestandsnaam = `focus_kw-beschrijving.webp`.
- ALT‑tekst = beschrijvend + 1 keer focus_kw.
- Plaatjes > 100 kB? → AI‑agent comprimeert (lossy 80 %).
- Lazy‑load attributen (`loading="lazy" decoding="async"`).

---

## 7 ▪︎ Post‑publish QA‑checklist (AI‑agent uitvoeren)
1. **Run Rank Math Content AI** → score ≥ 80 / 100.
2. Controleer **Rich Results Test** voor schema‑fouten.
3. Valideer **Core Web Vitals** (LCP < 2.5 s, CLS < 0.1, INP < 200 ms).
4. Controleer dat `last_modified` is opgehaald door Google (via GSC → Inspect URL).
5. Maak indien nodig 301‑redirects bij slug‑wijzigingen.

---

## 8 ▪︎ Automatisering & workflow
```mermaid
graph TD
A[Markdown in repo] --> B(AI‑agent bouwt front‑matter)
B --> C(Git push)
C --> D(Github Actions: WP‑CLI deploy)
D --> E(Rank Math Analytics & IndexNow ping)
E --> F(Slack notificatie ✅)
```

**AI‑agent taken:**
- Genereer `.md` → commit naar juiste map.
- Trigger CI/CD.
- Na publicatie  → call Rank Math REST `/analytics/update-stats`.

---

## 9 ▪︎ Naming‑conventies & variabelen
| Placeholder | Beschrijving | Validatie |
|-------------|-------------|-----------|
| `{{focus_kw}}` | Primair zoekwoord (1‑3 woorden) | Lowercase, geen stopwords |
| `{{slug}}` | Hyphens, ≤ 60 tekens | !== bestaande slugs |
| `{{canonical}}` | Standaard eigen URL | Absolute URL |
| `{{lastmod}}` | Datum van laatste substantieve wijziging | <= vandaag |

*NB*: Voeg waar van toepassing secundaire keywords toe in H2‑koppen i.p.v. in slug.

---

## 10 ▪︎ Updates & versiebeheer
- **Versienummering document** in front‑matter van dit bestand (`spec_version: "1.0.0"`).
- **Changelog** bijhouden onderaan dit bestand.

---

### Laatste wijziging document: {{DATE:2025‑06‑11}}

---

```
# Changelog
- 1.0.0 (2025‑06‑11) – Eerste versie.
```
