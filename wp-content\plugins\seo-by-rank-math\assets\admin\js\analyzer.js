!function(){var e={1924:function(e,t,r){"use strict";var n=r(210),i=r(5559),o=i(n("String.prototype.indexOf"));e.exports=function(e,t){var r=n(e,!!t);return"function"==typeof r&&o(e,".prototype.")>-1?i(r):r}},5559:function(e,t,r){"use strict";var n=r(8612),i=r(210),o=r(2490),u=r(4453),a=i("%Function.prototype.apply%"),s=i("%Function.prototype.call%"),l=i("%Reflect.apply%",!0)||n.call(s,a),c=r(4429),d=i("%Math.max%");e.exports=function(e){if("function"!=typeof e)throw new u("a function is required");var t=l(n,s,arguments);return o(t,1+d(0,e.length-(arguments.length-1)),!0)};var p=function(){return l(n,a,arguments)};c?c(e.exports,"apply",{value:p}):e.exports.apply=p},2296:function(e,t,r){"use strict";var n=r(4429),i=r(3464),o=r(4453),u=r(7296);e.exports=function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new o("`obj` must be an object or a function`");if("string"!=typeof t&&"symbol"!=typeof t)throw new o("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new o("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new o("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new o("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new o("`loose`, if provided, must be a boolean");var a=arguments.length>3?arguments[3]:null,s=arguments.length>4?arguments[4]:null,l=arguments.length>5?arguments[5]:null,c=arguments.length>6&&arguments[6],d=!!u&&u(e,t);if(n)n(e,t,{configurable:null===l&&d?d.configurable:!l,enumerable:null===a&&d?d.enumerable:!a,value:r,writable:null===s&&d?d.writable:!s});else{if(!c&&(a||s||l))throw new i("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");e[t]=r}}},4429:function(e,t,r){"use strict";var n=r(210)("%Object.defineProperty%",!0)||!1;if(n)try{n({},"a",{value:1})}catch(e){n=!1}e.exports=n},3981:function(e){"use strict";e.exports=EvalError},1648:function(e){"use strict";e.exports=Error},4726:function(e){"use strict";e.exports=RangeError},6712:function(e){"use strict";e.exports=ReferenceError},3464:function(e){"use strict";e.exports=SyntaxError},4453:function(e){"use strict";e.exports=TypeError},3915:function(e){"use strict";e.exports=URIError},7648:function(e){"use strict";var t=Object.prototype.toString,r=Math.max,n=function(e,t){for(var r=[],n=0;n<e.length;n+=1)r[n]=e[n];for(var i=0;i<t.length;i+=1)r[i+e.length]=t[i];return r};e.exports=function(e){var i=this;if("function"!=typeof i||"[object Function]"!==t.apply(i))throw new TypeError("Function.prototype.bind called on incompatible "+i);for(var o,u=function(e,t){for(var r=[],n=t||0,i=0;n<e.length;n+=1,i+=1)r[i]=e[n];return r}(arguments,1),a=r(0,i.length-u.length),s=[],l=0;l<a;l++)s[l]="$"+l;if(o=Function("binder","return function ("+function(e,t){for(var r="",n=0;n<e.length;n+=1)r+=e[n],n+1<e.length&&(r+=t);return r}(s,",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof o){var t=i.apply(this,n(u,arguments));return Object(t)===t?t:this}return i.apply(e,n(u,arguments))})),i.prototype){var c=function(){};c.prototype=i.prototype,o.prototype=new c,c.prototype=null}return o}},8612:function(e,t,r){"use strict";var n=r(7648);e.exports=Function.prototype.bind||n},210:function(e,t,r){"use strict";var n,i=r(1648),o=r(3981),u=r(4726),a=r(6712),s=r(3464),l=r(4453),c=r(3915),d=Function,p=function(e){try{return d('"use strict"; return ('+e+").constructor;")()}catch(e){}},f=Object.getOwnPropertyDescriptor;if(f)try{f({},"")}catch(e){f=null}var y=function(){throw new l},h=f?function(){try{return y}catch(e){try{return f(arguments,"callee").get}catch(e){return y}}}():y,g=r(1405)(),m=r(8185)(),b=Object.getPrototypeOf||(m?function(e){return e.__proto__}:null),v={},D="undefined"!=typeof Uint8Array&&b?b(Uint8Array):n,w={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?n:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?n:ArrayBuffer,"%ArrayIteratorPrototype%":g&&b?b([][Symbol.iterator]()):n,"%AsyncFromSyncIteratorPrototype%":n,"%AsyncFunction%":v,"%AsyncGenerator%":v,"%AsyncGeneratorFunction%":v,"%AsyncIteratorPrototype%":v,"%Atomics%":"undefined"==typeof Atomics?n:Atomics,"%BigInt%":"undefined"==typeof BigInt?n:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?n:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?n:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?n:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":i,"%eval%":eval,"%EvalError%":o,"%Float32Array%":"undefined"==typeof Float32Array?n:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?n:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?n:FinalizationRegistry,"%Function%":d,"%GeneratorFunction%":v,"%Int8Array%":"undefined"==typeof Int8Array?n:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?n:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?n:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":g&&b?b(b([][Symbol.iterator]())):n,"%JSON%":"object"==typeof JSON?JSON:n,"%Map%":"undefined"==typeof Map?n:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&g&&b?b((new Map)[Symbol.iterator]()):n,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?n:Promise,"%Proxy%":"undefined"==typeof Proxy?n:Proxy,"%RangeError%":u,"%ReferenceError%":a,"%Reflect%":"undefined"==typeof Reflect?n:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?n:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&g&&b?b((new Set)[Symbol.iterator]()):n,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?n:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":g&&b?b(""[Symbol.iterator]()):n,"%Symbol%":g?Symbol:n,"%SyntaxError%":s,"%ThrowTypeError%":h,"%TypedArray%":D,"%TypeError%":l,"%Uint8Array%":"undefined"==typeof Uint8Array?n:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?n:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?n:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?n:Uint32Array,"%URIError%":c,"%WeakMap%":"undefined"==typeof WeakMap?n:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?n:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?n:WeakSet};if(b)try{null.error}catch(e){var k=b(b(e));w["%Error.prototype%"]=k}var F=function e(t){var r;if("%AsyncFunction%"===t)r=p("async function () {}");else if("%GeneratorFunction%"===t)r=p("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=p("async function* () {}");else if("%AsyncGenerator%"===t){var n=e("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if("%AsyncIteratorPrototype%"===t){var i=e("%AsyncGenerator%");i&&b&&(r=b(i.prototype))}return w[t]=r,r},E={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},A=r(8612),C=r(8824),S=A.call(Function.call,Array.prototype.concat),j=A.call(Function.apply,Array.prototype.splice),x=A.call(Function.call,String.prototype.replace),B=A.call(Function.call,String.prototype.slice),O=A.call(Function.call,RegExp.prototype.exec),_=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,P=/\\(\\)?/g,R=function(e,t){var r,n=e;if(C(E,n)&&(n="%"+(r=E[n])[0]+"%"),C(w,n)){var i=w[n];if(i===v&&(i=F(n)),void 0===i&&!t)throw new l("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:n,value:i}}throw new s("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new l("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new l('"allowMissing" argument must be a boolean');if(null===O(/^%?[^%]*%?$/,e))throw new s("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=B(e,0,1),r=B(e,-1);if("%"===t&&"%"!==r)throw new s("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new s("invalid intrinsic syntax, expected opening `%`");var n=[];return x(e,_,(function(e,t,r,i){n[n.length]=r?x(i,P,"$1"):t||e})),n}(e),n=r.length>0?r[0]:"",i=R("%"+n+"%",t),o=i.name,u=i.value,a=!1,c=i.alias;c&&(n=c[0],j(r,S([0,1],c)));for(var d=1,p=!0;d<r.length;d+=1){var y=r[d],h=B(y,0,1),g=B(y,-1);if(('"'===h||"'"===h||"`"===h||'"'===g||"'"===g||"`"===g)&&h!==g)throw new s("property names with quotes must have matching quotes");if("constructor"!==y&&p||(a=!0),C(w,o="%"+(n+="."+y)+"%"))u=w[o];else if(null!=u){if(!(y in u)){if(!t)throw new l("base intrinsic for "+e+" exists, but the property is not available.");return}if(f&&d+1>=r.length){var m=f(u,y);u=(p=!!m)&&"get"in m&&!("originalValue"in m.get)?m.get:u[y]}else p=C(u,y),u=u[y];p&&!a&&(w[o]=u)}}return u}},7296:function(e,t,r){"use strict";var n=r(210)("%Object.getOwnPropertyDescriptor%",!0);if(n)try{n([],"length")}catch(e){n=null}e.exports=n},1044:function(e,t,r){"use strict";var n=r(4429),i=function(){return!!n};i.hasArrayLengthDefineBug=function(){if(!n)return null;try{return 1!==n([],"length",{value:1}).length}catch(e){return!0}},e.exports=i},8185:function(e){"use strict";var t={foo:{}},r=Object;e.exports=function(){return{__proto__:t}.foo===t.foo&&!({__proto__:null}instanceof r)}},1405:function(e,t,r){"use strict";var n="undefined"!=typeof Symbol&&Symbol,i=r(5419);e.exports=function(){return"function"==typeof n&&("function"==typeof Symbol&&("symbol"==typeof n("foo")&&("symbol"==typeof Symbol("bar")&&i())))}},5419:function(e){"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0}},8824:function(e,t,r){"use strict";var n=Function.prototype.call,i=Object.prototype.hasOwnProperty,o=r(8612);e.exports=o.call(n,i)},631:function(e,t,r){var n="function"==typeof Map&&Map.prototype,i=Object.getOwnPropertyDescriptor&&n?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,o=n&&i&&"function"==typeof i.get?i.get:null,u=n&&Map.prototype.forEach,a="function"==typeof Set&&Set.prototype,s=Object.getOwnPropertyDescriptor&&a?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,l=a&&s&&"function"==typeof s.get?s.get:null,c=a&&Set.prototype.forEach,d="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,p="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,f="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,y=Boolean.prototype.valueOf,h=Object.prototype.toString,g=Function.prototype.toString,m=String.prototype.match,b=String.prototype.slice,v=String.prototype.replace,D=String.prototype.toUpperCase,w=String.prototype.toLowerCase,k=RegExp.prototype.test,F=Array.prototype.concat,E=Array.prototype.join,A=Array.prototype.slice,C=Math.floor,S="function"==typeof BigInt?BigInt.prototype.valueOf:null,j=Object.getOwnPropertySymbols,x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,B="function"==typeof Symbol&&"object"==typeof Symbol.iterator,O="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===B||"symbol")?Symbol.toStringTag:null,_=Object.prototype.propertyIsEnumerable,P=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function R(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||k.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var n=e<0?-C(-e):C(e);if(n!==e){var i=String(n),o=b.call(t,i.length+1);return v.call(i,r,"$&_")+"."+v.call(v.call(o,/([0-9]{3})/g,"$&_"),/_$/,"")}}return v.call(t,r,"$&_")}var T=r(4654),z=T.custom,N=$(z)?z:null;function q(e,t,r){var n="double"===(r.quoteStyle||t)?'"':"'";return n+e+n}function I(e){return v.call(String(e),/"/g,"&quot;")}function M(e){return!("[object Array]"!==K(e)||O&&"object"==typeof e&&O in e)}function L(e){return!("[object RegExp]"!==K(e)||O&&"object"==typeof e&&O in e)}function $(e){if(B)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!x)return!1;try{return x.call(e),!0}catch(e){}return!1}e.exports=function e(t,n,i,a){var s=n||{};if(W(s,"quoteStyle")&&"single"!==s.quoteStyle&&"double"!==s.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(W(s,"maxStringLength")&&("number"==typeof s.maxStringLength?s.maxStringLength<0&&s.maxStringLength!==1/0:null!==s.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var h=!W(s,"customInspect")||s.customInspect;if("boolean"!=typeof h&&"symbol"!==h)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(W(s,"indent")&&null!==s.indent&&"\t"!==s.indent&&!(parseInt(s.indent,10)===s.indent&&s.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(W(s,"numericSeparator")&&"boolean"!=typeof s.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var D=s.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return V(t,s);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var k=String(t);return D?R(t,k):k}if("bigint"==typeof t){var C=String(t)+"n";return D?R(t,C):C}var j=void 0===s.depth?5:s.depth;if(void 0===i&&(i=0),i>=j&&j>0&&"object"==typeof t)return M(t)?"[Array]":"[Object]";var z=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;r=E.call(Array(e.indent+1)," ")}return{base:r,prev:E.call(Array(t+1),r)}}(s,i);if(void 0===a)a=[];else if(H(a,t)>=0)return"[Circular]";function U(t,r,n){if(r&&(a=A.call(a)).push(r),n){var o={depth:s.depth};return W(s,"quoteStyle")&&(o.quoteStyle=s.quoteStyle),e(t,o,i+1,a)}return e(t,s,i+1,a)}if("function"==typeof t&&!L(t)){var Y=function(e){if(e.name)return e.name;var t=m.call(g.call(e),/^function\s*([\w$]+)/);if(t)return t[1];return null}(t),ee=X(t,U);return"[Function"+(Y?": "+Y:" (anonymous)")+"]"+(ee.length>0?" { "+E.call(ee,", ")+" }":"")}if($(t)){var te=B?v.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):x.call(t);return"object"!=typeof t||B?te:G(te)}if(function(e){if(!e||"object"!=typeof e)return!1;if("undefined"!=typeof HTMLElement&&e instanceof HTMLElement)return!0;return"string"==typeof e.nodeName&&"function"==typeof e.getAttribute}(t)){for(var re="<"+w.call(String(t.nodeName)),ne=t.attributes||[],ie=0;ie<ne.length;ie++)re+=" "+ne[ie].name+"="+q(I(ne[ie].value),"double",s);return re+=">",t.childNodes&&t.childNodes.length&&(re+="..."),re+="</"+w.call(String(t.nodeName))+">"}if(M(t)){if(0===t.length)return"[]";var oe=X(t,U);return z&&!function(e){for(var t=0;t<e.length;t++)if(H(e[t],"\n")>=0)return!1;return!0}(oe)?"["+Z(oe,z)+"]":"[ "+E.call(oe,", ")+" ]"}if(function(e){return!("[object Error]"!==K(e)||O&&"object"==typeof e&&O in e)}(t)){var ue=X(t,U);return"cause"in Error.prototype||!("cause"in t)||_.call(t,"cause")?0===ue.length?"["+String(t)+"]":"{ ["+String(t)+"] "+E.call(ue,", ")+" }":"{ ["+String(t)+"] "+E.call(F.call("[cause]: "+U(t.cause),ue),", ")+" }"}if("object"==typeof t&&h){if(N&&"function"==typeof t[N]&&T)return T(t,{depth:j-i});if("symbol"!==h&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!o||!e||"object"!=typeof e)return!1;try{o.call(e);try{l.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var ae=[];return u&&u.call(t,(function(e,r){ae.push(U(r,t,!0)+" => "+U(e,t))})),J("Map",o.call(t),ae,z)}if(function(e){if(!l||!e||"object"!=typeof e)return!1;try{l.call(e);try{o.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var se=[];return c&&c.call(t,(function(e){se.push(U(e,t))})),J("Set",l.call(t),se,z)}if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{d.call(e,d);try{p.call(e,p)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return Q("WeakMap");if(function(e){if(!p||!e||"object"!=typeof e)return!1;try{p.call(e,p);try{d.call(e,d)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return Q("WeakSet");if(function(e){if(!f||!e||"object"!=typeof e)return!1;try{return f.call(e),!0}catch(e){}return!1}(t))return Q("WeakRef");if(function(e){return!("[object Number]"!==K(e)||O&&"object"==typeof e&&O in e)}(t))return G(U(Number(t)));if(function(e){if(!e||"object"!=typeof e||!S)return!1;try{return S.call(e),!0}catch(e){}return!1}(t))return G(U(S.call(t)));if(function(e){return!("[object Boolean]"!==K(e)||O&&"object"==typeof e&&O in e)}(t))return G(y.call(t));if(function(e){return!("[object String]"!==K(e)||O&&"object"==typeof e&&O in e)}(t))return G(U(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if(t===r.g)return"{ [object globalThis] }";if(!function(e){return!("[object Date]"!==K(e)||O&&"object"==typeof e&&O in e)}(t)&&!L(t)){var le=X(t,U),ce=P?P(t)===Object.prototype:t instanceof Object||t.constructor===Object,de=t instanceof Object?"":"null prototype",pe=!ce&&O&&Object(t)===t&&O in t?b.call(K(t),8,-1):de?"Object":"",fe=(ce||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(pe||de?"["+E.call(F.call([],pe||[],de||[]),": ")+"] ":"");return 0===le.length?fe+"{}":z?fe+"{"+Z(le,z)+"}":fe+"{ "+E.call(le,", ")+" }"}return String(t)};var U=Object.prototype.hasOwnProperty||function(e){return e in this};function W(e,t){return U.call(e,t)}function K(e){return h.call(e)}function H(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}function V(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,n="... "+r+" more character"+(r>1?"s":"");return V(b.call(e,0,t.maxStringLength),t)+n}return q(v.call(v.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,Y),"single",t)}function Y(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+D.call(t.toString(16))}function G(e){return"Object("+e+")"}function Q(e){return e+" { ? }"}function J(e,t,r,n){return e+" ("+t+") {"+(n?Z(r,n):E.call(r,", "))+"}"}function Z(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+E.call(e,","+r)+"\n"+t.prev}function X(e,t){var r=M(e),n=[];if(r){n.length=e.length;for(var i=0;i<e.length;i++)n[i]=W(e,i)?t(e[i],e):""}var o,u="function"==typeof j?j(e):[];if(B){o={};for(var a=0;a<u.length;a++)o["$"+u[a]]=u[a]}for(var s in e)W(e,s)&&(r&&String(Number(s))===s&&s<e.length||B&&o["$"+s]instanceof Symbol||(k.call(/[^\w$]/,s)?n.push(t(s,e)+": "+t(e[s],e)):n.push(s+": "+t(e[s],e))));if("function"==typeof j)for(var l=0;l<u.length;l++)_.call(e,u[l])&&n.push("["+t(u[l])+"]: "+t(e[u[l]],e));return n}},5798:function(e){"use strict";var t=String.prototype.replace,r=/%20/g,n="RFC1738",i="RFC3986";e.exports={default:i,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:n,RFC3986:i}},129:function(e,t,r){"use strict";var n=r(8261),i=r(5235),o=r(5798);e.exports={formats:o,parse:i,stringify:n}},5235:function(e,t,r){"use strict";var n=r(2769),i=Object.prototype.hasOwnProperty,o=Array.isArray,u={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:n.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1},a=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},s=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},l=function(e,t,r,n){if(e){var o=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,u=/(\[[^[\]]*])/g,a=r.depth>0&&/(\[[^[\]]*])/.exec(o),l=a?o.slice(0,a.index):o,c=[];if(l){if(!r.plainObjects&&i.call(Object.prototype,l)&&!r.allowPrototypes)return;c.push(l)}for(var d=0;r.depth>0&&null!==(a=u.exec(o))&&d<r.depth;){if(d+=1,!r.plainObjects&&i.call(Object.prototype,a[1].slice(1,-1))&&!r.allowPrototypes)return;c.push(a[1])}if(a){if(!0===r.strictDepth)throw new RangeError("Input depth exceeded depth option of "+r.depth+" and strictDepth is true");c.push("["+o.slice(a.index)+"]")}return function(e,t,r,n){for(var i=n?t:s(t,r),o=e.length-1;o>=0;--o){var u,a=e[o];if("[]"===a&&r.parseArrays)u=r.allowEmptyArrays&&(""===i||r.strictNullHandling&&null===i)?[]:[].concat(i);else{u=r.plainObjects?Object.create(null):{};var l="["===a.charAt(0)&&"]"===a.charAt(a.length-1)?a.slice(1,-1):a,c=r.decodeDotInKeys?l.replace(/%2E/g,"."):l,d=parseInt(c,10);r.parseArrays||""!==c?!isNaN(d)&&a!==c&&String(d)===c&&d>=0&&r.parseArrays&&d<=r.arrayLimit?(u=[])[d]=i:"__proto__"!==c&&(u[c]=i):u={0:i}}i=u}return i}(c,t,r,n)}};e.exports=function(e,t){var r=function(e){if(!e)return u;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=void 0===e.charset?u.charset:e.charset,r=void 0===e.duplicates?u.duplicates:e.duplicates;if("combine"!==r&&"first"!==r&&"last"!==r)throw new TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||u.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:u.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:u.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:u.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:u.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:u.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:u.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:u.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:u.decoder,delimiter:"string"==typeof e.delimiter||n.isRegExp(e.delimiter)?e.delimiter:u.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:u.depth,duplicates:r,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:u.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:u.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:u.plainObjects,strictDepth:"boolean"==typeof e.strictDepth?!!e.strictDepth:u.strictDepth,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:u.strictNullHandling}}(t);if(""===e||null==e)return r.plainObjects?Object.create(null):{};for(var c="string"==typeof e?function(e,t){var r={__proto__:null},l=t.ignoreQueryPrefix?e.replace(/^\?/,""):e;l=l.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var c,d=t.parameterLimit===1/0?void 0:t.parameterLimit,p=l.split(t.delimiter,d),f=-1,y=t.charset;if(t.charsetSentinel)for(c=0;c<p.length;++c)0===p[c].indexOf("utf8=")&&("utf8=%E2%9C%93"===p[c]?y="utf-8":"utf8=%26%2310003%3B"===p[c]&&(y="iso-8859-1"),f=c,c=p.length);for(c=0;c<p.length;++c)if(c!==f){var h,g,m=p[c],b=m.indexOf("]="),v=-1===b?m.indexOf("="):b+1;-1===v?(h=t.decoder(m,u.decoder,y,"key"),g=t.strictNullHandling?null:""):(h=t.decoder(m.slice(0,v),u.decoder,y,"key"),g=n.maybeMap(s(m.slice(v+1),t),(function(e){return t.decoder(e,u.decoder,y,"value")}))),g&&t.interpretNumericEntities&&"iso-8859-1"===y&&(g=a(g)),m.indexOf("[]=")>-1&&(g=o(g)?[g]:g);var D=i.call(r,h);D&&"combine"===t.duplicates?r[h]=n.combine(r[h],g):D&&"last"!==t.duplicates||(r[h]=g)}return r}(e,r):e,d=r.plainObjects?Object.create(null):{},p=Object.keys(c),f=0;f<p.length;++f){var y=p[f],h=l(y,c[y],r,"string"==typeof e);d=n.merge(d,h,r)}return!0===r.allowSparse?d:n.compact(d)}},8261:function(e,t,r){"use strict";var n=r(7478),i=r(2769),o=r(5798),u=Object.prototype.hasOwnProperty,a={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},s=Array.isArray,l=Array.prototype.push,c=function(e,t){l.apply(e,s(t)?t:[t])},d=Date.prototype.toISOString,p=o.default,f={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:i.encode,encodeValuesOnly:!1,format:p,formatter:o.formatters[p],indices:!1,serializeDate:function(e){return d.call(e)},skipNulls:!1,strictNullHandling:!1},y={},h=function e(t,r,o,u,a,l,d,p,h,g,m,b,v,D,w,k,F,E){for(var A,C=t,S=E,j=0,x=!1;void 0!==(S=S.get(y))&&!x;){var B=S.get(t);if(j+=1,void 0!==B){if(B===j)throw new RangeError("Cyclic object value");x=!0}void 0===S.get(y)&&(j=0)}if("function"==typeof g?C=g(r,C):C instanceof Date?C=v(C):"comma"===o&&s(C)&&(C=i.maybeMap(C,(function(e){return e instanceof Date?v(e):e}))),null===C){if(l)return h&&!k?h(r,f.encoder,F,"key",D):r;C=""}if("string"==typeof(A=C)||"number"==typeof A||"boolean"==typeof A||"symbol"==typeof A||"bigint"==typeof A||i.isBuffer(C))return h?[w(k?r:h(r,f.encoder,F,"key",D))+"="+w(h(C,f.encoder,F,"value",D))]:[w(r)+"="+w(String(C))];var O,_=[];if(void 0===C)return _;if("comma"===o&&s(C))k&&h&&(C=i.maybeMap(C,h)),O=[{value:C.length>0?C.join(",")||null:void 0}];else if(s(g))O=g;else{var P=Object.keys(C);O=m?P.sort(m):P}var R=p?r.replace(/\./g,"%2E"):r,T=u&&s(C)&&1===C.length?R+"[]":R;if(a&&s(C)&&0===C.length)return T+"[]";for(var z=0;z<O.length;++z){var N=O[z],q="object"==typeof N&&void 0!==N.value?N.value:C[N];if(!d||null!==q){var I=b&&p?N.replace(/\./g,"%2E"):N,M=s(C)?"function"==typeof o?o(T,I):T:T+(b?"."+I:"["+I+"]");E.set(t,j);var L=n();L.set(y,E),c(_,e(q,M,o,u,a,l,d,p,"comma"===o&&k&&s(C)?null:h,g,m,b,v,D,w,k,F,L))}}return _};e.exports=function(e,t){var r,i=e,l=function(e){if(!e)return f;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||f.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=o.default;if(void 0!==e.format){if(!u.call(o.formatters,e.format))throw new TypeError("Unknown format option provided.");r=e.format}var n,i=o.formatters[r],l=f.filter;if(("function"==typeof e.filter||s(e.filter))&&(l=e.filter),n=e.arrayFormat in a?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":f.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var c=void 0===e.allowDots?!0===e.encodeDotInKeys||f.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:f.addQueryPrefix,allowDots:c,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:f.allowEmptyArrays,arrayFormat:n,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:f.charsetSentinel,commaRoundTrip:e.commaRoundTrip,delimiter:void 0===e.delimiter?f.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:f.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:f.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:f.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:f.encodeValuesOnly,filter:l,format:r,formatter:i,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:f.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:f.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:f.strictNullHandling}}(t);"function"==typeof l.filter?i=(0,l.filter)("",i):s(l.filter)&&(r=l.filter);var d=[];if("object"!=typeof i||null===i)return"";var p=a[l.arrayFormat],y="comma"===p&&l.commaRoundTrip;r||(r=Object.keys(i)),l.sort&&r.sort(l.sort);for(var g=n(),m=0;m<r.length;++m){var b=r[m];l.skipNulls&&null===i[b]||c(d,h(i[b],b,p,y,l.allowEmptyArrays,l.strictNullHandling,l.skipNulls,l.encodeDotInKeys,l.encode?l.encoder:null,l.filter,l.sort,l.allowDots,l.serializeDate,l.format,l.formatter,l.encodeValuesOnly,l.charset,g))}var v=d.join(l.delimiter),D=!0===l.addQueryPrefix?"?":"";return l.charsetSentinel&&("iso-8859-1"===l.charset?D+="utf8=%26%2310003%3B&":D+="utf8=%E2%9C%93&"),v.length>0?D+v:""}},2769:function(e,t,r){"use strict";var n=r(5798),i=Object.prototype.hasOwnProperty,o=Array.isArray,u=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),a=function(e,t){for(var r=t&&t.plainObjects?Object.create(null):{},n=0;n<e.length;++n)void 0!==e[n]&&(r[n]=e[n]);return r},s=1024;e.exports={arrayToObject:a,assign:function(e,t){return Object.keys(t).reduce((function(e,r){return e[r]=t[r],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],n=0;n<t.length;++n)for(var i=t[n],u=i.obj[i.prop],a=Object.keys(u),s=0;s<a.length;++s){var l=a[s],c=u[l];"object"==typeof c&&null!==c&&-1===r.indexOf(c)&&(t.push({obj:u,prop:l}),r.push(c))}return function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(o(r)){for(var n=[],i=0;i<r.length;++i)void 0!==r[i]&&n.push(r[i]);t.obj[t.prop]=n}}}(t),e},decode:function(e,t,r){var n=e.replace(/\+/g," ");if("iso-8859-1"===r)return n.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(n)}catch(e){return n}},encode:function(e,t,r,i,o){if(0===e.length)return e;var a=e;if("symbol"==typeof e?a=Symbol.prototype.toString.call(e):"string"!=typeof e&&(a=String(e)),"iso-8859-1"===r)return escape(a).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var l="",c=0;c<a.length;c+=s){for(var d=a.length>=s?a.slice(c,c+s):a,p=[],f=0;f<d.length;++f){var y=d.charCodeAt(f);45===y||46===y||95===y||126===y||y>=48&&y<=57||y>=65&&y<=90||y>=97&&y<=122||o===n.RFC1738&&(40===y||41===y)?p[p.length]=d.charAt(f):y<128?p[p.length]=u[y]:y<2048?p[p.length]=u[192|y>>6]+u[128|63&y]:y<55296||y>=57344?p[p.length]=u[224|y>>12]+u[128|y>>6&63]+u[128|63&y]:(f+=1,y=65536+((1023&y)<<10|1023&d.charCodeAt(f)),p[p.length]=u[240|y>>18]+u[128|y>>12&63]+u[128|y>>6&63]+u[128|63&y])}l+=p.join("")}return l},isBuffer:function(e){return!(!e||"object"!=typeof e)&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(o(e)){for(var r=[],n=0;n<e.length;n+=1)r.push(t(e[n]));return r}return t(e)},merge:function e(t,r,n){if(!r)return t;if("object"!=typeof r){if(o(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(n&&(n.plainObjects||n.allowPrototypes)||!i.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var u=t;return o(t)&&!o(r)&&(u=a(t,n)),o(t)&&o(r)?(r.forEach((function(r,o){if(i.call(t,o)){var u=t[o];u&&"object"==typeof u&&r&&"object"==typeof r?t[o]=e(u,r,n):t.push(r)}else t[o]=r})),t):Object.keys(r).reduce((function(t,o){var u=r[o];return i.call(t,o)?t[o]=e(t[o],u,n):t[o]=u,t}),u)}}},6542:function(e,t,r){e.exports={labels:r(3621),scoringStrategy:r(1006)}},1006:function(e,t,r){var n=r(623);e.exports={apply:function(e,t,r){if(t>0){var i=e[t-1];n[i]&&(r=-r)}return r}}},7076:function(e,t,r){var n={"./en/index":6542};function i(e){var t=o(e);return r(t)}function o(e){if(!r.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}i.keys=function(){return Object.keys(n)},i.resolve=o,e.exports=i,i.id=7076},8286:function(e,t,r){var n=r(1862),i=r(4862),o=function(e){this.options=e};o.prototype.registerLanguage=function(e,t){i.addLanguage(e,t)},o.prototype.analyze=function(e,t,r){void 0===e&&(e=""),"function"==typeof t&&(r=t,t={});var o=(t=t||{}).language||"en",u=i.getLabels(o);"object"==typeof t.extras&&(u=Object.assign(u,t.extras));for(var a=n(e),s=0,l=[],c=[],d=[],p=[],f=a.length;f--;){var y=a[f];if(u.hasOwnProperty(y)){l.push(y);var h=u[y];(h=i.applyScoringStrategy(o,a,f,h))>0&&c.push(y),h<0&&d.push(y),s+=h;var g={};g[y]=h,p.push(g)}}var m={score:s,comparative:a.length>0?s/a.length:0,calculation:p,tokens:a,words:l,positive:c,negative:d};if("function"!=typeof r)return m;process.nextTick((function(){r(null,m)}))},e.exports=o},4862:function(e,t,r){var n=r(9362),i=r(6542);Object.assign(i.labels,n);var o={en:i};e.exports={addLanguage:function(e,t){if(!t.labels)throw new Error("language.labels must be defined!");Object.assign(t.labels,n),o[e]=t},getLanguage:function(e){if(!e)return o.en;if(!o[e])try{var t=r(7076)("./"+e+"/index");this.addLanguage(e,t)}catch(t){throw new Error("No language found: "+e)}return o[e]},getLabels:function(e){return this.getLanguage(e).labels},applyScoringStrategy:function(e,t,r,n){return(this.getLanguage(e).scoringStrategy||u).apply(t,r,n)}};var u={apply:function(e,t,r){return r}}},1862:function(e){e.exports=function(e){return e.toLowerCase().replace(/\n/g," ").replace(/[.,\/#!?$%\^&\*;:{}=_`\"~()]/g," ").replace(/\s\s+/g," ").trim().split(" ")}},2490:function(e,t,r){"use strict";var n=r(210),i=r(2296),o=r(1044)(),u=r(7296),a=r(4453),s=n("%Math.floor%");e.exports=function(e,t){if("function"!=typeof e)throw new a("`fn` is not a function");if("number"!=typeof t||t<0||t>4294967295||s(t)!==t)throw new a("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],n=!0,l=!0;if("length"in e&&u){var c=u(e,"length");c&&!c.configurable&&(n=!1),c&&!c.writable&&(l=!1)}return(n||l||!r)&&(o?i(e,"length",t,!0,!0):i(e,"length",t)),e}},7478:function(e,t,r){"use strict";var n=r(210),i=r(1924),o=r(631),u=r(4453),a=n("%WeakMap%",!0),s=n("%Map%",!0),l=i("WeakMap.prototype.get",!0),c=i("WeakMap.prototype.set",!0),d=i("WeakMap.prototype.has",!0),p=i("Map.prototype.get",!0),f=i("Map.prototype.set",!0),y=i("Map.prototype.has",!0),h=function(e,t){for(var r,n=e;null!==(r=n.next);n=r)if(r.key===t)return n.next=r.next,r.next=e.next,e.next=r,r};e.exports=function(){var e,t,r,n={assert:function(e){if(!n.has(e))throw new u("Side channel does not contain "+o(e))},get:function(n){if(a&&n&&("object"==typeof n||"function"==typeof n)){if(e)return l(e,n)}else if(s){if(t)return p(t,n)}else if(r)return function(e,t){var r=h(e,t);return r&&r.value}(r,n)},has:function(n){if(a&&n&&("object"==typeof n||"function"==typeof n)){if(e)return d(e,n)}else if(s){if(t)return y(t,n)}else if(r)return function(e,t){return!!h(e,t)}(r,n);return!1},set:function(n,i){a&&n&&("object"==typeof n||"function"==typeof n)?(e||(e=new a),c(e,n,i)):s?(t||(t=new s),f(t,n,i)):(r||(r={key:{},next:null}),function(e,t,r){var n=h(e,t);n?n.value=r:e.next={key:t,next:e.next,value:r}}(r,n,i))}};return n}},2511:function(e,t,r){var n;e=r.nmd(e),function(i){t&&t.nodeType,e&&e.nodeType;var o="object"==typeof r.g&&r.g;o.global!==o&&o.window!==o&&o.self;var u,a=2147483647,s=36,l=1,c=26,d=38,p=700,f=72,y=128,h="-",g=/^xn--/,m=/[^\x20-\x7E]/,b=/[\x2E\u3002\uFF0E\uFF61]/g,v={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},D=s-l,w=Math.floor,k=String.fromCharCode;function F(e){throw new RangeError(v[e])}function E(e,t){for(var r=e.length,n=[];r--;)n[r]=t(e[r]);return n}function A(e,t){var r=e.split("@"),n="";return r.length>1&&(n=r[0]+"@",e=r[1]),n+E((e=e.replace(b,".")).split("."),t).join(".")}function C(e){for(var t,r,n=[],i=0,o=e.length;i<o;)(t=e.charCodeAt(i++))>=55296&&t<=56319&&i<o?56320==(64512&(r=e.charCodeAt(i++)))?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),i--):n.push(t);return n}function S(e){return E(e,(function(e){var t="";return e>65535&&(t+=k((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=k(e)})).join("")}function j(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function x(e,t,r){var n=0;for(e=r?w(e/p):e>>1,e+=w(e/t);e>D*c>>1;n+=s)e=w(e/D);return w(n+(D+1)*e/(e+d))}function B(e){var t,r,n,i,o,u,d,p,g,m,b,v=[],D=e.length,k=0,E=y,A=f;for((r=e.lastIndexOf(h))<0&&(r=0),n=0;n<r;++n)e.charCodeAt(n)>=128&&F("not-basic"),v.push(e.charCodeAt(n));for(i=r>0?r+1:0;i<D;){for(o=k,u=1,d=s;i>=D&&F("invalid-input"),((p=(b=e.charCodeAt(i++))-48<10?b-22:b-65<26?b-65:b-97<26?b-97:s)>=s||p>w((a-k)/u))&&F("overflow"),k+=p*u,!(p<(g=d<=A?l:d>=A+c?c:d-A));d+=s)u>w(a/(m=s-g))&&F("overflow"),u*=m;A=x(k-o,t=v.length+1,0==o),w(k/t)>a-E&&F("overflow"),E+=w(k/t),k%=t,v.splice(k++,0,E)}return S(v)}function O(e){var t,r,n,i,o,u,d,p,g,m,b,v,D,E,A,S=[];for(v=(e=C(e)).length,t=y,r=0,o=f,u=0;u<v;++u)(b=e[u])<128&&S.push(k(b));for(n=i=S.length,i&&S.push(h);n<v;){for(d=a,u=0;u<v;++u)(b=e[u])>=t&&b<d&&(d=b);for(d-t>w((a-r)/(D=n+1))&&F("overflow"),r+=(d-t)*D,t=d,u=0;u<v;++u)if((b=e[u])<t&&++r>a&&F("overflow"),b==t){for(p=r,g=s;!(p<(m=g<=o?l:g>=o+c?c:g-o));g+=s)A=p-m,E=s-m,S.push(k(j(m+A%E,0))),p=w(A/E);S.push(k(j(p,0))),o=x(r,D,n==i),r=0,++n}++r,++t}return S.join("")}u={version:"1.4.1",ucs2:{decode:C,encode:S},decode:B,encode:O,toASCII:function(e){return A(e,(function(e){return m.test(e)?"xn--"+O(e):e}))},toUnicode:function(e){return A(e,(function(e){return g.test(e)?B(e.slice(4).toLowerCase()):e}))}},void 0===(n=function(){return u}.call(t,r,t,e))||(e.exports=n)}()},8575:function(e,t,r){"use strict";var n=r(2511);function i(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}var o=/^([a-z0-9.+-]+:)/i,u=/:[0-9]*$/,a=/^(\/\/?(?!\/)[^?\s]*)(\?[^\s]*)?$/,s=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),l=["'"].concat(s),c=["%","/","?",";","#"].concat(l),d=["/","?","#"],p=/^[+a-z0-9A-Z_-]{0,63}$/,f=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,y={javascript:!0,"javascript:":!0},h={javascript:!0,"javascript:":!0},g={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},m=r(129);function b(e,t,r){if(e&&"object"==typeof e&&e instanceof i)return e;var n=new i;return n.parse(e,t,r),n}i.prototype.parse=function(e,t,r){if("string"!=typeof e)throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var i=e.indexOf("?"),u=-1!==i&&i<e.indexOf("#")?"?":"#",s=e.split(u);s[0]=s[0].replace(/\\/g,"/");var b=e=s.join(u);if(b=b.trim(),!r&&1===e.split("#").length){var v=a.exec(b);if(v)return this.path=b,this.href=b,this.pathname=v[1],v[2]?(this.search=v[2],this.query=t?m.parse(this.search.substr(1)):this.search.substr(1)):t&&(this.search="",this.query={}),this}var D=o.exec(b);if(D){var w=(D=D[0]).toLowerCase();this.protocol=w,b=b.substr(D.length)}if(r||D||b.match(/^\/\/[^@/]+@[^@/]+/)){var k="//"===b.substr(0,2);!k||D&&h[D]||(b=b.substr(2),this.slashes=!0)}if(!h[D]&&(k||D&&!g[D])){for(var F,E,A=-1,C=0;C<d.length;C++){-1!==(S=b.indexOf(d[C]))&&(-1===A||S<A)&&(A=S)}-1!==(E=-1===A?b.lastIndexOf("@"):b.lastIndexOf("@",A))&&(F=b.slice(0,E),b=b.slice(E+1),this.auth=decodeURIComponent(F)),A=-1;for(C=0;C<c.length;C++){var S;-1!==(S=b.indexOf(c[C]))&&(-1===A||S<A)&&(A=S)}-1===A&&(A=b.length),this.host=b.slice(0,A),b=b.slice(A),this.parseHost(),this.hostname=this.hostname||"";var j="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!j)for(var x=this.hostname.split(/\./),B=(C=0,x.length);C<B;C++){var O=x[C];if(O&&!O.match(p)){for(var _="",P=0,R=O.length;P<R;P++)O.charCodeAt(P)>127?_+="x":_+=O[P];if(!_.match(p)){var T=x.slice(0,C),z=x.slice(C+1),N=O.match(f);N&&(T.push(N[1]),z.unshift(N[2])),z.length&&(b="/"+z.join(".")+b),this.hostname=T.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),j||(this.hostname=n.toASCII(this.hostname));var q=this.port?":"+this.port:"",I=this.hostname||"";this.host=I+q,this.href+=this.host,j&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==b[0]&&(b="/"+b))}if(!y[w])for(C=0,B=l.length;C<B;C++){var M=l[C];if(-1!==b.indexOf(M)){var L=encodeURIComponent(M);L===M&&(L=escape(M)),b=b.split(M).join(L)}}var $=b.indexOf("#");-1!==$&&(this.hash=b.substr($),b=b.slice(0,$));var U=b.indexOf("?");if(-1!==U?(this.search=b.substr(U),this.query=b.substr(U+1),t&&(this.query=m.parse(this.query)),b=b.slice(0,U)):t&&(this.search="",this.query={}),b&&(this.pathname=b),g[w]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){q=this.pathname||"";var W=this.search||"";this.path=q+W}return this.href=this.format(),this},i.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var t=this.protocol||"",r=this.pathname||"",n=this.hash||"",i=!1,o="";this.host?i=e+this.host:this.hostname&&(i=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(i+=":"+this.port)),this.query&&"object"==typeof this.query&&Object.keys(this.query).length&&(o=m.stringify(this.query,{arrayFormat:"repeat",addQueryPrefix:!1}));var u=this.search||o&&"?"+o||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||g[t])&&!1!==i?(i="//"+(i||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):i||(i=""),n&&"#"!==n.charAt(0)&&(n="#"+n),u&&"?"!==u.charAt(0)&&(u="?"+u),t+i+(r=r.replace(/[?#]/g,(function(e){return encodeURIComponent(e)})))+(u=u.replace("#","%23"))+n},i.prototype.resolve=function(e){return this.resolveObject(b(e,!1,!0)).format()},i.prototype.resolveObject=function(e){if("string"==typeof e){var t=new i;t.parse(e,!1,!0),e=t}for(var r=new i,n=Object.keys(this),o=0;o<n.length;o++){var u=n[o];r[u]=this[u]}if(r.hash=e.hash,""===e.href)return r.href=r.format(),r;if(e.slashes&&!e.protocol){for(var a=Object.keys(e),s=0;s<a.length;s++){var l=a[s];"protocol"!==l&&(r[l]=e[l])}return g[r.protocol]&&r.hostname&&!r.pathname&&(r.pathname="/",r.path=r.pathname),r.href=r.format(),r}if(e.protocol&&e.protocol!==r.protocol){if(!g[e.protocol]){for(var c=Object.keys(e),d=0;d<c.length;d++){var p=c[d];r[p]=e[p]}return r.href=r.format(),r}if(r.protocol=e.protocol,e.host||h[e.protocol])r.pathname=e.pathname;else{for(var f=(e.pathname||"").split("/");f.length&&!(e.host=f.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==f[0]&&f.unshift(""),f.length<2&&f.unshift(""),r.pathname=f.join("/")}if(r.search=e.search,r.query=e.query,r.host=e.host||"",r.auth=e.auth,r.hostname=e.hostname||e.host,r.port=e.port,r.pathname||r.search){var y=r.pathname||"",m=r.search||"";r.path=y+m}return r.slashes=r.slashes||e.slashes,r.href=r.format(),r}var b=r.pathname&&"/"===r.pathname.charAt(0),v=e.host||e.pathname&&"/"===e.pathname.charAt(0),D=v||b||r.host&&e.pathname,w=D,k=r.pathname&&r.pathname.split("/")||[],F=(f=e.pathname&&e.pathname.split("/")||[],r.protocol&&!g[r.protocol]);if(F&&(r.hostname="",r.port=null,r.host&&(""===k[0]?k[0]=r.host:k.unshift(r.host)),r.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===f[0]?f[0]=e.host:f.unshift(e.host)),e.host=null),D=D&&(""===f[0]||""===k[0])),v)r.host=e.host||""===e.host?e.host:r.host,r.hostname=e.hostname||""===e.hostname?e.hostname:r.hostname,r.search=e.search,r.query=e.query,k=f;else if(f.length)k||(k=[]),k.pop(),k=k.concat(f),r.search=e.search,r.query=e.query;else if(null!=e.search){if(F)r.host=k.shift(),r.hostname=r.host,(j=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=j.shift(),r.hostname=j.shift(),r.host=r.hostname);return r.search=e.search,r.query=e.query,null===r.pathname&&null===r.search||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r}if(!k.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var E=k.slice(-1)[0],A=(r.host||e.host||k.length>1)&&("."===E||".."===E)||""===E,C=0,S=k.length;S>=0;S--)"."===(E=k[S])?k.splice(S,1):".."===E?(k.splice(S,1),C++):C&&(k.splice(S,1),C--);if(!D&&!w)for(;C--;C)k.unshift("..");!D||""===k[0]||k[0]&&"/"===k[0].charAt(0)||k.unshift(""),A&&"/"!==k.join("/").substr(-1)&&k.push("");var j,x=""===k[0]||k[0]&&"/"===k[0].charAt(0);F&&(r.hostname=x?"":k.length?k.shift():"",r.host=r.hostname,(j=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=j.shift(),r.hostname=j.shift(),r.host=r.hostname));return(D=D||r.host&&k.length)&&!x&&k.unshift(""),k.length>0?r.pathname=k.join("/"):(r.pathname=null,r.path=null),null===r.pathname&&null===r.search||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=e.auth||r.auth,r.slashes=r.slashes||e.slashes,r.href=r.format(),r},i.prototype.parseHost=function(){var e=this.host,t=u.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)},t.parse=b,t.resolve=function(e,t){return b(e,!1,!0).resolve(t)},t.resolveObject=function(e,t){return e?b(e,!1,!0).resolveObject(t):t},t.format=function(e){return"string"==typeof e&&(e=b(e)),e instanceof i?e.format():i.prototype.format.call(e)},t.Url=i},4654:function(){},9362:function(e){"use strict";e.exports=JSON.parse('{"😂":1,"❤":3,"♥":3,"😍":3,"😭":-1,"😘":3,"😊":3,"👌":2,"💕":3,"👏":2,"😁":2,"☺":3,"♡":3,"👍":2,"😩":-2,"🙏":2,"✌":2,"😏":1,"😉":2,"🙌":2,"🙈":2,"💪":2,"😄":2,"😒":-2,"💃":3,"💖":3,"😃":2,"😔":-1,"🎉":3,"😜":2,"🌸":3,"💜":3,"💙":3,"✨":1,"💗":3,"★":1,"█":-1,"☀":2,"😡":-1,"😎":2,"💋":3,"😋":3,"🙊":2,"😴":-1,"🎶":2,"💞":3,"😌":2,"🔫":-1,"💛":3,"💁":1,"💚":3,"♫":1,"😞":-1,"😆":2,"😝":2,"😪":-1,"😫":-1,"👊":1,"💀":-2,"😀":2,"😚":3,"😻":3,"💘":3,"☕":1,"👋":2,"🎊":3,"🍕":2,"❄":2,"😕":-2,"💔":-1,"😤":-2,"😈":1,"✈":2,"🔝":2,"😰":-1,"⚽":3,"😑":-2,"👑":3,"👉":1,"🍃":1,"🎁":3,"😠":-2,"🐧":2,"☆":2,"🍀":1,"🎈":3,"🎅":1,"😓":-1,"😣":-2,"😐":-2,"✊":2,"😨":-1,"😖":-1,"💤":1,"💓":3,"👎":-1,"💦":2,"✔":1,"😷":-1,"🙋":2,"🎄":2,"💩":-1,"🎵":2,"😛":3,"👯":2,"💎":2,"🌿":1,"🎂":3,"🌟":1,"🔮":1,"👫":1,"🏆":3,"✖":1,"☝":1,"😙":3,"⛄":2,"👅":2,"♪":2,"🍂":2,"💏":1,"🌴":2,"👈":2,"🌹":3,"🙆":2,"👻":1,"💰":1,"🍻":2,"🙅":-2,"🌞":2,"🍁":2,"⭐":2,"▪":1,"🎀":3,"🐷":1,"🙉":1,"🌺":2,"💅":1,"🐶":2,"🌚":2,"👽":1,"🎤":2,"👭":2,"🎧":2,"👆":1,"🍸":2,"🍷":2,"®":1,"🍉":3,"😇":3,"🏃":2,"😿":-2,"│":1,"🍺":2,"▶":1,"😲":-1,"🎸":2,"🍹":3,"💫":2,"📚":1,"😶":-1,"🌷":2,"💝":3,"💨":1,"🏈":2,"💍":2,"☔":1,"👸":3,"🇪":3,"░":-1,"🍩":1,"👾":1,"☁":1,"🌻":2,"↿":3,"🐯":2,"👼":1,"🍔":1,"😸":2,"👶":2,"↾":3,"💐":3,"🌊":2,"🍦":2,"🍓":3,"👇":1,"💆":1,"🍴":2,"😧":-1,"🇸":2,"😮":1,"🚫":-3,"😽":2,"🌈":2,"🙀":1,"⚠":-1,"🎮":2,"╯":-1,"🍆":2,"🍰":2,"✓":1,"👐":-1,"🍟":1,"🍌":2,"💑":3,"👬":-1,"🐣":2,"🎃":3,"▬":2,"￼":-3,"🐾":3,"🎓":2,"🏊":2,"📷":2,"👄":2,"🌼":4,"🚶":-1,"🐱":2,"🐸":-1,"🇺":2,"👿":-3,"🚬":2,"✿":1,"🐒":2,"🌍":3,"┊":5,"🐥":3,"🐼":1,"🎥":1,"💄":2,"⛔":2,"🏀":1,"💉":1,"💟":3,"🚗":1,"📝":1,"♦":2,"💭":1,"🌙":3,"🐟":3,"👣":1,"✂":-3,"🗿":2,"👪":-1,"🍭":1,"🌃":2,"❌":1,"🐰":3,"💊":2,"🚨":3,"😦":-2,"🍪":1,"🍣":-2,"✧":1,"🎆":3,"🎎":4,"🇩":3,"✅":2,"📱":1,"🙍":-2,"🍑":1,"🎼":1,"🔊":2,"🌌":2,"🍎":1,"🐻":2,"╰":-1,"💇":1,"♬":1,"🔴":2,"🍱":-2,"🍊":2,"🍒":1,"🐭":3,"👟":2,"🌎":1,"🍍":2,"🐮":3,"📲":1,"☼":1,"🌅":1,"🇷":3,"👠":1,"🌽":2,"💧":-1,"🍬":1,"😺":2,"🚀":2,"¦":3,"💢":1,"🎬":1,"🍧":1,"🍜":2,"🐏":3,"🏄":2,"➤":1,"⬆":1,"🍋":1,"🆗":2,"⚪":2,"📺":2,"🍅":1,"⛅":2,"🐢":1,"👙":2,"🏡":2,"🌾":2,"◉":1,"✏":1,"🐬":2,"🇹":3,"♣":1,"🐝":1,"🌝":1,"🇮":3,"🔋":-3,"🐍":1,"♔":2,"🔵":1,"😾":-2,"🌕":3,"🐨":2,"🔐":1,"💿":3,"🌳":2,"👰":2,"❀":2,"⚓":3,"🚴":3,"▀":-1,"👗":1,"➕":2,"💬":2,"▒":-1,"🔜":1,"🍨":1,"💲":1,"🍙":1,"🍥":-4,"▸":1,"♛":1,"😼":1,"🐙":2,"👨":2,"🍚":2,"♨":4,"🎹":1,"♕":2,"▃":5,"🇬":1,"🇧":1,"☠":-1,"🐠":2,"🚹":3,"💵":2,"✰":4,"╠":1,"👛":2,"🌱":3,"💻":1,"🌏":1,"▄":-1,"👓":1,"◄":1,"⚾":-1,"🌲":2,"👴":1,"🏠":2,"🍇":1,"🍘":2,"🐇":1,"🔞":-1,"👵":2,"◀":1,"🔙":1,"🌵":1,"🍮":-1,"🎇":3,"🐎":2,"➔":-1,"🐤":2,"╩":1,"🌑":2,"🚲":2,"🐑":-1,"🏁":2,"🎾":3,"╚":1,"🈹":1,"👮":-2,"☹":-3,"🐵":2,"✪":1,"◕":2,"🗼":3,"▐":-1,"♠":1,"┳":-2,"👺":-2,"🐚":1,"👂":-1,"🗽":1,"🍵":2,"🆒":2,"🐺":1,"⇨":2,"🌓":3,"🔒":1,"╬":-1,"👳":3,"🌂":1,"🚌":1,"♩":3,"🍡":-1,"❥":1,"🎡":1,"💌":2,"🐩":2,"🌜":2,"⌚":1,"🚿":3,"🔆":3,"🌛":3,"💂":-1,"🐔":1,"🙎":-1,"🏩":2,"🇫":2,"🔨":-1,"📢":2,"🐦":2,"🐲":-1,"♻":2,"🌘":3,"🌔":3,"👖":2,"😗":3,"🐄":1,"◟":-1,"🍢":-1,"🎨":1,"⬇":2,"🚼":3,"🇴":2,"🌗":3,"🌖":3,"🔅":5,"👜":1,"🐌":3,"💼":3,"🐹":1,"🌠":3,"🐈":1,"🌁":1,"⚫":1,"♧":2,"🏰":1,"🚵":2,"🎢":2,"🎷":3,"🎐":1,"┈":-4,"╗":2,"🌇":3,"⏰":2,"🚂":1,"◠":2,"🎿":2,"🆔":4,"🌒":3,"🐪":3,"╔":1,"╝":2,"👔":2,"🆓":1,"🐋":1,"▽":2,"🐛":1,"👕":2,"💳":2,"🏧":5,"💡":3,"⬅":2,"🐫":2,"🇱":2,"📹":2,"👞":2,"👚":3,"□":-2,"🚣":3,"🏉":3,"🗻":3,"╦":2,"⛺":3,"🐕":1,"🏂":2,"👡":2,"📻":2,"✒":1,"🌰":3,"🏢":1,"🎒":3,"⌒":3,"🏫":-2,"📴":4,"🚢":1,"🚚":-1,"🐉":1,"❒":1,"🔔":5,"◢":4,"🏥":1,"🚖":-1,"▌":-2,"☛":2,"💒":3,"🚤":2,"🐐":2,"■":-2,"🔚":2,"🎻":2,"🔷":1,"🎽":2,"📅":1,"🎺":3,"🍈":-3,"✉":1,"◤":5,"○":3,"🍼":3,"🚛":-2,"📓":1,"☉":1,"💴":-2,"➰":-1,"🔌":-1,"📕":1,"📣":2,"🚓":1,"🐗":3,"⛳":4,"┻":-3,"┛":3,"┃":2,"💺":1,"🏇":-1,"☻":1,"📞":2,"Ⓐ":-1,"🌉":3,"🚩":-2,"✎":3,"📃":2,"🏨":1,"📌":-3,"♎":-1,"💷":2,"🚄":3,"▲":3,"⛵":3,"🔸":1,"🚜":5,"🐆":2,"👒":1,"❕":1,"🔛":2,"♢":2,"🇲":2,"❅":4,"👝":2,"✞":2,"◡":1,"🎋":3,"👥":1,"🐡":1,"◆":4,"🔭":2,"🎪":1,"🐜":3,"♌":4,"☐":-5,"👷":1,"🔈":1,"📄":5,"🚐":4,"🌋":3,"📡":1,"🚳":5,"✘":4,"🅰":1,"🇼":2,"┓":3,"┣":3,"Ⓛ":2,"Ⓔ":2,"👤":4,"🚁":1,"🎠":3,"🐁":-2,"📗":1,"┐":-1,"♂":1,"📯":-1,"🔩":1,"👢":4,"◂":2,"📰":1,"📶":2,"🌄":1,"🗾":2,"🔶":2,"🏤":2,"🎩":2,"Ⓜ":1,"🔧":-4,"🐅":1,"♮":1,"🅾":-1,"📦":1,"🚊":1,"🔲":3,"△":1,"📆":5,"❛":2,"📉":2,"▵":2,"🔎":3,"☜":1,"🇯":2,"🇵":2,"📘":1,"ⓔ":3,"🔑":1,"⭕":2,"🔘":1,"🚭":5,"🚉":3,"🚪":3,"➳":2,"🚃":3,"┯":-3,"🆙":2,"🆖":1,"┗":5,"Ⓞ":2,"❇":3,"✴":3,"☊":5,"🔕":-2,"⬛":-2,"🚞":3,"🍶":3,"🌐":3,"♀":1,"🚅":3,"🚒":-2,"♋":1,"♍":3,"🕝":-2,"ⓐ":5,"📙":1,"Ⓢ":1,"📋":3,"🎱":1,"🐞":1,"🔺":1,"ⓡ":5,"♤":3,"🎯":3,"🔉":3,"↩":5,"🚾":1,"🎣":-4,"🔣":1,"❎":-5,"➥":1,"🎌":5,"◣":1,"⏬":5,"♭":1,"ⓞ":5,"🔳":2,"🏭":2,"🎳":-3,"☚":5,"➽":2,"➫":2,"➖":-5,"꒰":2,"꒱":2,"◝":-3,"📑":5,"ⓧ":5,"🔟":5,"〓":5,"ⓜ":2,"➠":5,"🚆":2,"℅":-5,"☃":2,"🚽":5,"ⓝ":5,"⇦":5,"👲":2,"🚡":-3,"🔬":5,"➗":-3,"📈":2,"⏪":2,"◎":5,"꒦":-5,"📎":5,"⑅":5,"✭":5,"♓":2,"┏":5,"☇":5,"࿎":-5,"👘":5,"↙":5,"Ⓕ":2,"Ⓦ":2,"Ⓟ":2,"🕑":2,"🕛":5,"♈":-5,"↬":5,"✍":5,"🏦":5,"🔻":5,"ⓟ":5,"ⓕ":5,"ⓘ":5,"♿":5,"⇗":5,"⇘":5,"ⓨ":5,"ⓙ":5,"▫":5,"🔇":5,"⌃":-5,"🔖":5,"📜":5,"🚝":5,"┘":-5,"✝":-5,"⍣":-5,"📮":-5,"🕕":-5,"🔯":5,"➸":5,"꒵":5,"🕥":-5,"✽":5,"📼":5,"🕐":-5,"🀄":5,"✬":5,"✫":5,"🕔":-5,"❣":5,"📫":5,"🉐":5,"🈂":-5,"🎰":-5,"҂":-5,"╤":-5,"📔":5}')},3621:function(e){"use strict";e.exports=JSON.parse('{"abandon":-2,"abandoned":-2,"abandons":-2,"abducted":-2,"abduction":-2,"abductions":-2,"abhor":-3,"abhorred":-3,"abhorrent":-3,"abhors":-3,"abilities":2,"ability":2,"aboard":1,"aborted":-1,"aborts":-1,"absentee":-1,"absentees":-1,"absolve":2,"absolved":2,"absolves":2,"absolving":2,"absorbed":1,"abuse":-3,"abused":-3,"abuses":-3,"abusing":-3,"abusive":-3,"accept":1,"acceptable":1,"acceptance":1,"accepted":1,"accepting":1,"accepts":1,"accessible":1,"accident":-2,"accidental":-2,"accidentally":-2,"accidents":-2,"acclaim":2,"acclaimed":2,"accolade":2,"accomplish":2,"accomplished":2,"accomplishes":2,"accomplishment":2,"accomplishments":2,"accusation":-2,"accusations":-2,"accuse":-2,"accused":-2,"accuses":-2,"accusing":-2,"ache":-2,"achievable":1,"aching":-2,"acquit":2,"acquits":2,"acquitted":2,"acquitting":2,"acrimonious":-3,"active":1,"adequate":1,"admire":3,"admired":3,"admires":3,"admiring":3,"admit":-1,"admits":-1,"admitted":-1,"admonish":-2,"admonished":-2,"adopt":1,"adopts":1,"adorable":3,"adoration":3,"adore":3,"adored":3,"adores":3,"adoring":3,"adoringly":3,"advanced":1,"advantage":2,"advantageous":2,"advantageously":2,"advantages":2,"adventure":2,"adventures":2,"adventurous":2,"adversary":-1,"advisable":1,"affected":-1,"affection":3,"affectionate":3,"affectionateness":3,"afflicted":-1,"affordable":2,"affronted":-1,"aficionados":2,"afraid":-2,"aggravate":-2,"aggravated":-2,"aggravates":-2,"aggravating":-2,"aggression":-2,"aggressions":-2,"aggressive":-2,"aggressiveness":-2,"aghast":-2,"agog":2,"agonise":-3,"agonised":-3,"agonises":-3,"agonising":-3,"agonize":-3,"agonized":-3,"agonizes":-3,"agonizing":-3,"agree":1,"agreeable":2,"agreed":1,"agreement":1,"agrees":1,"alarm":-2,"alarmed":-2,"alarmist":-2,"alarmists":-2,"alas":-1,"alert":-1,"alienation":-2,"alive":1,"allegation":-2,"allegations":-2,"allergic":-2,"allow":1,"ally":2,"alone":-2,"altruistic":2,"amaze":2,"amazed":2,"amazes":2,"amazing":4,"ambitious":2,"ambivalent":-1,"amicable":2,"amuse":3,"amused":3,"amusement":3,"amusements":3,"anger":-3,"angered":-3,"angers":-3,"angry":-3,"anguish":-3,"anguished":-3,"animosity":-2,"annoy":-2,"annoyance":-2,"annoyed":-2,"annoying":-2,"annoys":-2,"antagonistic":-2,"anti":-1,"anticipation":1,"anxiety":-2,"anxious":-2,"apathetic":-3,"apathy":-3,"apeshit":-3,"apocalyptic":-2,"apologise":-1,"apologised":-1,"apologises":-1,"apologising":-1,"apologize":-1,"apologized":-1,"apologizes":-1,"apologizing":-1,"apology":-1,"appalled":-2,"appalling":-2,"appealing":2,"appease":2,"appeased":2,"appeases":2,"appeasing":2,"applaud":2,"applauded":2,"applauding":2,"applauds":2,"applause":2,"appreciate":2,"appreciated":2,"appreciates":2,"appreciating":2,"appreciation":2,"apprehensive":-2,"appropriate":2,"appropriately":2,"approval":2,"approved":2,"approves":2,"ardent":1,"arrest":-2,"arrested":-3,"arrests":-2,"arrogant":-2,"arsehole":-4,"ashame":-2,"ashamed":-2,"ass":-4,"assassination":-3,"assassinations":-3,"assault":-2,"assaults":-2,"asset":2,"assets":2,"assfucking":-4,"asshole":-4,"astonished":2,"astound":3,"astounded":3,"astounding":3,"astoundingly":3,"astounds":3,"atrocious":-3,"atrocity":-3,"attack":-1,"attacked":-1,"attacking":-1,"attacks":-1,"attract":1,"attracted":1,"attracting":2,"attraction":2,"attractions":2,"attractive":2,"attractively":2,"attractiveness":2,"attracts":1,"audacious":3,"aura":1,"authority":1,"avenge":-2,"avenged":-2,"avenger":-2,"avengers":-2,"avenges":-2,"avenging":-2,"avert":-1,"averted":-1,"averts":-1,"avid":2,"avoid":-1,"avoided":-1,"avoids":-1,"await":-1,"awaited":-1,"awaits":-1,"award":3,"awarded":3,"awards":3,"awesome":4,"awful":-3,"awkward":-2,"axe":-1,"axed":-1,"backed":1,"backing":2,"backs":1,"bad":-3,"bad luck":-2,"badass":-3,"badly":-3,"badness":-3,"bailout":-2,"balanced":1,"bamboozle":-2,"bamboozled":-2,"bamboozles":-2,"ban":-2,"banish":-1,"bankrupt":-3,"bankruptcy":-3,"bankster":-3,"banned":-2,"barbarian":-2,"barbaric":-2,"barbarous":-2,"bargain":2,"barrier":-2,"bastard":-5,"bastards":-5,"battle":-1,"battled":-1,"battles":-1,"battling":-2,"beaten":-2,"beatific":3,"beating":-1,"beauties":3,"beautiful":3,"beautifully":3,"beautify":3,"beauty":3,"befit":2,"befitting":2,"belittle":-2,"belittled":-2,"beloved":3,"benefactor":2,"benefactors":2,"benefit":2,"benefits":2,"benefitted":2,"benefitting":2,"benevolent":3,"bereave":-2,"bereaved":-2,"bereaves":-2,"bereaving":-2,"best":3,"best damn":4,"betray":-3,"betrayal":-3,"betrayed":-3,"betraying":-3,"betrays":-3,"better":2,"bias":-1,"biased":-2,"big":1,"bitch":-5,"bitches":-5,"bitter":-2,"bitterest":-2,"bitterly":-2,"bizarre":-2,"blackmail":-3,"blackmailed":-3,"blackmailing":-3,"blackmails":-3,"blah":-2,"blame":-2,"blamed":-2,"blames":-2,"blaming":-2,"bless":2,"blesses":2,"blessing":3,"blessings":3,"blind":-1,"bliss":3,"blissful":3,"blithe":2,"bloated":-1,"block":-1,"blockade":-2,"blockbuster":3,"blocked":-1,"blocking":-1,"blocks":-1,"bloody":-3,"blurry":-2,"boastful":-2,"bold":2,"boldly":2,"bomb":-1,"boost":1,"boosted":1,"boosting":1,"boosts":1,"bore":-2,"bored":-2,"boring":-3,"bother":-2,"bothered":-2,"bothers":-2,"bothersome":-2,"boycott":-2,"boycotted":-2,"boycotting":-2,"boycotts":-2,"brainwashing":-3,"brave":2,"braveness":2,"bravery":2,"bravura":3,"breach":-2,"breached":-2,"breaches":-2,"breaching":-2,"breakthrough":3,"breathtaking":5,"bribe":-3,"bribed":-3,"bribes":-3,"bribing":-3,"bright":1,"brightest":2,"brightness":1,"brilliant":4,"brilliance":3,"brilliances":3,"brisk":2,"broke":-1,"broken":-1,"brooding":-2,"brutal":-3,"brutally":-3,"bullied":-2,"bullshit":-4,"bully":-2,"bullying":-2,"bummer":-2,"buoyant":2,"burden":-2,"burdened":-2,"burdening":-2,"burdens":-2,"burglar":-2,"burglary":-2,"calm":2,"calmed":2,"calming":2,"calms":2,"can\'t stand":-3,"cancel":-1,"cancelled":-1,"cancelling":-1,"cancels":-1,"cancer":-1,"capabilities":1,"capability":1,"capable":1,"captivated":3,"care":2,"carefree":1,"careful":2,"carefully":2,"carefulness":2,"careless":-2,"cares":2,"caring":2,"cashing in":-2,"casualty":-2,"catastrophe":-3,"catastrophic":-4,"cautious":-1,"celebrate":3,"celebrated":3,"celebrates":3,"celebrating":3,"celebration":3,"celebrations":3,"censor":-2,"censored":-2,"censors":-2,"certain":1,"chagrin":-2,"chagrined":-2,"challenge":-1,"champion":2,"championed":2,"champions":2,"chance":2,"chances":2,"chaos":-2,"chaotic":-2,"charged":-3,"charges":-2,"charisma":2,"charitable":2,"charm":3,"charming":3,"charmingly":3,"charmless":-3,"chastise":-3,"chastised":-3,"chastises":-3,"chastising":-3,"cheat":-3,"cheated":-3,"cheater":-3,"cheaters":-3,"cheating":-3,"cheats":-3,"cheer":2,"cheered":2,"cheerful":2,"cheerfully":2,"cheering":2,"cheerless":-2,"cheers":2,"cheery":3,"cherish":2,"cherished":2,"cherishes":2,"cherishing":2,"chic":2,"chide":-3,"chided":-3,"chides":-3,"chiding":-3,"childish":-2,"chilling":-1,"choke":-2,"choked":-2,"chokes":-2,"choking":-2,"clarifies":2,"clarity":2,"clash":-2,"classy":3,"clean":2,"cleaner":2,"clear":1,"cleared":1,"clearly":1,"clears":1,"clever":2,"clouded":-1,"clueless":-2,"cock":-5,"cocksucker":-5,"cocksuckers":-5,"cocky":-2,"coerced":-2,"coercion":-2,"collapse":-2,"collapsed":-2,"collapses":-2,"collapsing":-2,"collide":-1,"collides":-1,"colliding":-1,"collision":-2,"collisions":-2,"colluding":-3,"combat":-1,"combats":-1,"comedy":1,"comfort":2,"comfortable":2,"comfortably":2,"comforting":2,"comforts":2,"comic":1,"commend":2,"commended":2,"commit":1,"commitment":2,"commits":1,"committed":1,"committing":1,"compassion":2,"compassionate":2,"compelled":1,"competencies":1,"competent":2,"competitive":2,"complacent":-2,"complain":-2,"complained":-2,"complaining":-2,"complains":-2,"complaint":-2,"complaints":-2,"complicating":-2,"compliment":2,"complimented":2,"compliments":2,"comprehensive":2,"concerned":-2,"conciliate":2,"conciliated":2,"conciliates":2,"conciliating":2,"condemn":-2,"condemnation":-2,"condemned":-2,"condemns":-2,"confidence":2,"confident":2,"confidently":2,"conflict":-2,"conflicting":-2,"conflictive":-2,"conflicts":-2,"confuse":-2,"confused":-2,"confusing":-2,"congrats":2,"congratulate":2,"congratulation":2,"congratulations":2,"consent":2,"consents":2,"consolable":2,"conspiracy":-3,"constipation":-2,"constrained":-2,"contagion":-2,"contagions":-2,"contagious":-1,"contaminant":-2,"contaminants":-2,"contaminate":-2,"contaminated":-2,"contaminates":-2,"contaminating":-2,"contamination":-2,"contaminations":-2,"contempt":-2,"contemptible":-2,"contemptuous":-2,"contemptuously":-2,"contend":-1,"contender":-1,"contending":-1,"contentious":-2,"contestable":-2,"controversial":-2,"controversially":-2,"controversies":-2,"controversy":-2,"convicted":-2,"convince":1,"convinced":1,"convinces":1,"convivial":2,"cool":1,"cool stuff":3,"cornered":-2,"corpse":-1,"corrupt":-3,"corrupted":-3,"corrupting":-3,"corruption":-3,"corrupts":-3,"costly":-2,"courage":2,"courageous":2,"courageously":2,"courageousness":2,"courteous":2,"courtesy":2,"cover-up":-3,"coward":-2,"cowardly":-2,"coziness":2,"cramp":-1,"crap":-3,"crappy":-3,"crash":-2,"crazier":-2,"craziest":-2,"crazy":-2,"creative":2,"crestfallen":-2,"cried":-2,"cries":-2,"crime":-3,"crimes":-3,"criminal":-3,"criminals":-3,"criminate":-3,"criminated":-3,"criminates":-3,"crisis":-3,"critic":-2,"criticise":-2,"criticised":-2,"criticises":-2,"criticising":-2,"criticism":-2,"criticize":-2,"criticized":-2,"criticizes":-2,"criticizing":-2,"critics":-2,"critique":-2,"crowding":-1,"crude":-1,"cruel":-3,"cruelty":-3,"crush":-1,"crushed":-2,"crushes":-1,"crushing":-1,"cry":-1,"crying":-2,"cunning":2,"cunt":-5,"curious":1,"curse":-1,"cut":-1,"cutback":-2,"cutbacks":-2,"cute":2,"cuts":-1,"cutting":-1,"cynic":-2,"cynical":-2,"cynicism":-2,"damage":-3,"damaged":-3,"damages":-3,"damaging":-3,"damn":-2,"damn cute":3,"damn good":4,"damned":-4,"damnit":-4,"danger":-2,"dangerous":-2,"dangerously":-2,"daredevil":2,"daring":2,"darkest":-2,"darkness":-1,"dauntless":2,"dazzling":3,"dead":-3,"deadening":-2,"deadlock":-2,"deadly":-3,"deafening":-1,"dear":2,"dearly":3,"death":-2,"deaths":-2,"debonair":2,"debt":-2,"deceit":-3,"deceitful":-3,"deceive":-3,"deceived":-3,"deceives":-3,"deceiving":-3,"deception":-3,"deceptive":-3,"decisive":1,"dedicated":2,"dedication":2,"defeat":-2,"defeated":-2,"defect":-3,"defective":-3,"defects":-3,"defender":2,"defenders":2,"defenseless":-2,"defer":-1,"deferring":-1,"defiant":-1,"deficient":-2,"deficiency":-2,"deficiencies":-2,"deficit":-2,"deformed":-2,"deformities":-2,"deformity":-2,"defraud":-3,"defrauds":-3,"deft":2,"defunct":-2,"degrade":-2,"degraded":-2,"degrades":-2,"dehumanize":-2,"dehumanized":-2,"dehumanizes":-2,"dehumanizing":-2,"deject":-2,"dejected":-2,"dejecting":-2,"dejects":-2,"delay":-1,"delayed":-1,"delectable":3,"delicious":3,"delight":3,"delighted":3,"delightful":3,"delightfully":3,"delighting":3,"delights":3,"demand":-1,"demanded":-1,"demanding":-1,"demands":-1,"demonstration":-1,"demoralize":-2,"demoralized":-2,"demoralizes":-2,"demoralizing":-2,"denial":-2,"denials":-2,"denied":-2,"denier":-2,"deniers":-2,"denies":-2,"denounce":-2,"denounces":-2,"dent":-2,"deny":-2,"denying":-2,"deplore":-3,"deplored":-3,"deplores":-3,"deploring":-3,"deport":-2,"deported":-2,"deporting":-2,"deports":-2,"deportation":-2,"deportations":-2,"depressed":-2,"depressing":-2,"deprivation":-3,"derail":-2,"derailed":-2,"derails":-2,"derelict":-2,"deride":-2,"derided":-2,"derides":-2,"deriding":-2,"derision":-2,"desirable":2,"desire":1,"desired":2,"desirous":2,"despair":-3,"despairing":-3,"despairs":-3,"desperate":-3,"desperately":-3,"despondent":-3,"destroy":-3,"destroyed":-3,"destroying":-3,"destroys":-3,"destruction":-3,"destructive":-3,"detached":-1,"detain":-2,"detained":-2,"detention":-2,"deteriorate":-2,"deteriorated":-2,"deteriorates":-2,"deteriorating":-2,"determined":2,"deterrent":-2,"detract":-1,"detracted":-1,"detracts":-1,"devastate":-2,"devastated":-2,"devastating":-2,"devastation":-2,"devastations":-2,"devoted":3,"devotion":2,"devotional":2,"diamond":1,"dick":-4,"dickhead":-4,"die":-3,"died":-3,"difficult":-1,"diffident":-2,"dignity":2,"dilemma":-1,"dilligence":2,"dipshit":-3,"dire":-3,"direful":-3,"dirt":-2,"dirtier":-2,"dirtiest":-2,"dirty":-2,"disabilities":-2,"disability":-2,"disabling":-1,"disadvantage":-2,"disadvantaged":-2,"disagree":-2,"disagreeable":-2,"disagreement":-2,"disappear":-1,"disappeared":-1,"disappears":-1,"disappoint":-2,"disappointed":-2,"disappointing":-2,"disappointment":-2,"disappointments":-2,"disappoints":-2,"disapproval":-2,"disapprovals":-2,"disapprove":-2,"disapproved":-2,"disapproves":-2,"disapproving":-2,"disaster":-2,"disasters":-2,"disastrous":-3,"disbelieve":-2,"discard":-1,"discarded":-1,"discarding":-1,"discards":-1,"discernment":2,"discomfort":-2,"disconsolate":-2,"disconsolation":-2,"discontented":-2,"discord":-2,"discounted":-1,"discouraged":-2,"discredited":-2,"discriminate":-2,"discriminated":-2,"discriminates":-2,"discriminating":-2,"discriminatory":-2,"disdain":-2,"disease":-1,"diseases":-1,"disgrace":-2,"disgraced":-2,"disguise":-1,"disguised":-1,"disguises":-1,"disguising":-1,"disgust":-3,"disgusted":-3,"disgustful":-3,"disgusting":-3,"disheartened":-2,"dishonest":-2,"disillusioned":-2,"disinclined":-2,"disjointed":-2,"dislike":-2,"disliked":-2,"dislikes":-2,"dismal":-2,"dismayed":-2,"dismissed":-2,"disorder":-2,"disorders":-2,"disorganized":-2,"disoriented":-2,"disparage":-2,"disparaged":-2,"disparages":-2,"disparaging":-2,"displeased":-2,"displeasure":-2,"disproportionate":-2,"dispute":-2,"disputed":-2,"disputes":-2,"disputing":-2,"disqualified":-2,"disquiet":-2,"disregard":-2,"disregarded":-2,"disregarding":-2,"disregards":-2,"disrespect":-2,"disrespected":-2,"disrupt":-2,"disrupted":-2,"disrupting":-2,"disruption":-2,"disruptions":-2,"disruptive":-2,"disrupts":-2,"dissatisfied":-2,"distasteful":-2,"distinguished":2,"distort":-2,"distorted":-2,"distorting":-2,"distorts":-2,"distract":-2,"distracted":-2,"distraction":-2,"distracts":-2,"distress":-2,"distressed":-2,"distresses":-2,"distressing":-2,"distrust":-3,"distrustful":-3,"disturb":-2,"disturbed":-2,"disturbing":-2,"disturbs":-2,"dithering":-2,"diverting":-1,"dizzy":-1,"dodging":-2,"dodgy":-2,"does not work":-3,"dolorous":-2,"donate":2,"donated":2,"donates":2,"donating":2,"donation":2,"dont like":-2,"doom":-2,"doomed":-2,"doubt":-1,"doubted":-1,"doubtful":-1,"doubting":-1,"doubts":-1,"douche":-3,"douchebag":-3,"dour":-2,"downcast":-2,"downer":-2,"downhearted":-2,"downside":-2,"drag":-1,"dragged":-1,"drags":-1,"drained":-2,"dread":-2,"dreaded":-2,"dreadful":-3,"dreading":-2,"dream":1,"dreams":1,"dreary":-2,"droopy":-2,"drop":-1,"dropped":-1,"drown":-2,"drowned":-2,"drowns":-2,"drudgery":-2,"drunk":-2,"dubious":-2,"dud":-2,"dull":-2,"dumb":-3,"dumbass":-3,"dump":-1,"dumped":-2,"dumps":-1,"dupe":-2,"duped":-2,"dupery":-2,"durable":2,"dying":-3,"dysfunction":-2,"eager":2,"earnest":2,"ease":2,"easy":1,"ecstatic":4,"eerie":-2,"eery":-2,"effective":2,"effectively":2,"effectiveness":2,"effortlessly":2,"elated":3,"elation":3,"elegant":2,"elegantly":2,"embarrass":-2,"embarrassed":-2,"embarrasses":-2,"embarrassing":-2,"embarrassment":-2,"embezzlement":-3,"embittered":-2,"embrace":1,"emergency":-2,"empathetic":2,"empower":2,"empowerment":2,"emptiness":-1,"empty":-1,"enchanted":2,"encourage":2,"encouraged":2,"encouragement":2,"encourages":2,"encouraging":2,"endorse":2,"endorsed":2,"endorsement":2,"endorses":2,"enemies":-2,"enemy":-2,"energetic":2,"engage":1,"engages":1,"engrossed":1,"engrossing":3,"enjoy":2,"enjoyable":2,"enjoyed":2,"enjoying":2,"enjoys":2,"enlighten":2,"enlightened":2,"enlightening":2,"enlightens":2,"ennui":-2,"enrage":-2,"enraged":-2,"enrages":-2,"enraging":-2,"enrapture":3,"enslave":-2,"enslaved":-2,"enslaves":-2,"ensure":1,"ensuring":1,"enterprising":1,"entertaining":2,"enthral":3,"enthusiastic":3,"entitled":1,"entrusted":2,"envies":-1,"envious":-2,"environment-friendly":2,"envy":-1,"envying":-1,"erroneous":-2,"error":-2,"errors":-2,"escape":-1,"escapes":-1,"escaping":-1,"esteem":2,"esteemed":2,"ethical":2,"euphoria":3,"euphoric":4,"evacuate":-1,"evacuated":-1,"evacuates":-1,"evacuating":-1,"evacuation":-1,"evergreen":2,"evergreens":2,"evergreening":-3,"eviction":-1,"evil":-3,"exacerbate":-2,"exacerbated":-2,"exacerbates":-2,"exacerbating":-2,"exaggerate":-2,"exaggerated":-2,"exaggerates":-2,"exaggerating":-2,"exasparate":-2,"exasperated":-2,"exasperates":-2,"exasperating":-2,"excellence":3,"excellent":3,"excite":3,"excited":3,"excitement":3,"exciting":3,"exclude":-1,"excluded":-2,"exclusion":-1,"exclusive":2,"excruciatingly":-1,"excuse":-1,"exempt":-1,"exhausted":-2,"exhilarated":3,"exhilarates":3,"exhilarating":3,"exonerate":2,"exonerated":2,"exonerates":2,"exonerating":2,"expand":1,"expands":1,"expel":-2,"expelled":-2,"expelling":-2,"expels":-2,"expertly":2,"exploit":-2,"exploited":-2,"exploiting":-2,"exploits":-2,"exploration":1,"explorations":1,"expose":-1,"exposed":-1,"exposes":-1,"exposing":-1,"exquisite":3,"extend":1,"extends":1,"extremist":-2,"extremists":-2,"exuberant":4,"exultant":3,"exultantly":3,"fabulous":4,"fabulously":4,"fad":-2,"fag":-3,"faggot":-3,"faggots":-3,"fail":-2,"failed":-2,"failing":-2,"fails":-2,"failure":-2,"failures":-2,"fainthearted":-2,"fair":2,"fairness":2,"faith":1,"faithful":3,"fake":-3,"faker":-3,"fakes":-3,"faking":-3,"fallen":-2,"falling":-1,"false":-1,"falsely":-2,"falsified":-3,"falsify":-3,"fame":1,"famine":-2,"famous":2,"fan":3,"fantastic":4,"farce":-1,"fascinate":3,"fascinated":3,"fascinates":3,"fascinating":3,"fascination":3,"fascist":-2,"fascists":-2,"fatal":-3,"fatalities":-3,"fatality":-3,"fatigue":-2,"fatigued":-2,"fatigues":-2,"fatiguing":-2,"favor":2,"favorable":2,"favorably":2,"favored":2,"favorite":2,"favorited":2,"favorites":2,"favors":2,"favour":2,"favourable":2,"favourably":2,"favoured":2,"favourite":2,"favourited":2,"favourites":2,"favours":2,"fear":-2,"fearful":-2,"fearfully":-2,"fearing":-2,"fearless":2,"fearlessness":2,"fearsome":-2,"fed up":-3,"feeble":-2,"feeling":1,"felonies":-3,"felony":-3,"fertile":2,"fervent":2,"fervid":2,"festive":2,"fever":-2,"fiasco":-3,"fidgety":-2,"fight":-1,"fighting":-2,"fine":2,"fines":-2,"finest":3,"fire":-2,"fired":-2,"firing":-2,"fit":1,"fitness":1,"filth":-2,"filthy":-2,"flagship":2,"flaw":-2,"flawed":-3,"flawless":2,"flawlessly":2,"flaws":-2,"flees":-1,"flop":-2,"flops":-2,"flu":-2,"flustered":-2,"focused":2,"fond":2,"fondness":2,"fool":-2,"foolish":-2,"fools":-2,"forbid":-1,"forbidden":-2,"forbidding":-2,"forced":-1,"foreclosure":-2,"foreclosures":-2,"forefront":1,"forget":-1,"forgetful":-2,"forgettable":-1,"forgive":1,"forgiving":1,"forgot":-1,"forgotten":-1,"fortune":2,"fortunate":2,"fortunately":2,"foul":-3,"frantic":-1,"fraud":-4,"frauds":-4,"fraudster":-4,"fraudsters":-4,"fraudulence":-4,"fraudulent":-4,"freak":-2,"free":1,"freedom":2,"freedoms":2,"frenzy":-3,"fresh":1,"friend":1,"friendliness":2,"friendly":2,"friendship":2,"fright":-2,"frightened":-2,"frightening":-3,"frikin":-2,"frisky":2,"frowning":-1,"fruitless":-2,"frustrate":-2,"frustrated":-2,"frustrates":-2,"frustrating":-2,"frustration":-2,"ftw":3,"fuck":-4,"fucked":-4,"fucker":-4,"fuckers":-4,"fuckface":-4,"fuckhead":-4,"fuckin":-4,"fucking":-4,"fucking amazing":4,"fucking beautiful":4,"fucking cute":4,"fucking fantastic":4,"fucking good":4,"fucking great":4,"fucking hot":2,"fucking love":4,"fucking loves":4,"fucking perfect":4,"fucktard":-4,"fud":-3,"fuked":-4,"fuking":-4,"fulfill":2,"fulfilled":2,"fulfillment":2,"fulfills":2,"fuming":-2,"fun":4,"funeral":-1,"funerals":-1,"funky":2,"funnier":4,"funny":4,"furious":-3,"futile":-2,"gag":-2,"gagged":-2,"gain":2,"gained":2,"gaining":2,"gains":2,"gallant":3,"gallantly":3,"gallantry":3,"game-changing":3,"garbage":-1,"gem":3,"generous":2,"generously":2,"genial":3,"ghastly":-2,"ghost":-1,"giddy":-2,"gift":2,"glad":3,"glamorous":3,"glamourous":3,"glee":3,"gleeful":3,"gloom":-1,"gloomy":-2,"glorious":2,"glory":2,"glum":-2,"god":1,"goddamn":-3,"godsend":4,"gold":2,"good":3,"goodlooking":3,"goodmorning":1,"goodness":3,"goodwill":3,"goofiness":-2,"goofy":-2,"grace":1,"graceful":2,"gracious":3,"grand":3,"grant":1,"granted":1,"granting":1,"grants":1,"grateful":3,"gratification":2,"grave":-2,"gray":-1,"grisly":-2,"gr8":3,"great":3,"greater":3,"greatest":3,"greed":-3,"greedy":-2,"green wash":-3,"green washing":-3,"greenwash":-3,"greenwasher":-3,"greenwashers":-3,"greenwashing":-3,"greet":1,"greeted":1,"greeting":1,"greetings":2,"greets":1,"grey":-1,"grief":-2,"grieved":-2,"grim":-2,"gripping":2,"groan":-2,"groaned":-2,"groaning":-2,"groans":-2,"gross":-2,"growing":1,"growth":2,"growths":2,"gruesome":-3,"guarantee":1,"guilt":-3,"guilty":-3,"gullibility":-2,"gullible":-2,"gun":-1,"ha":2,"hacked":-1,"haha":3,"hahaha":3,"hahahah":3,"hail":2,"hailed":2,"hallelujah":3,"handpicked":1,"handsome":3,"hapless":-2,"haplessness":-2,"happiest":3,"happiness":3,"happy":3,"harass":-3,"harassed":-3,"harasses":-3,"harassing":-3,"harassment":-3,"hard":-1,"hardier":2,"hardship":-2,"hardy":2,"harm":-2,"harmed":-2,"harmful":-2,"harming":-2,"harmony":2,"harmonious":2,"harmoniously":2,"harms":-2,"harried":-2,"harsh":-2,"harsher":-2,"harshest":-2,"harshly":-2,"hate":-3,"hated":-3,"hater":-3,"haters":-3,"hates":-3,"hating":-3,"hatred":-3,"haunt":-1,"haunted":-2,"haunting":1,"haunts":-1,"havoc":-2,"hazardous":-3,"headache":-2,"healthy":2,"heartbreaking":-3,"heartbroken":-3,"heartfelt":3,"heartless":-2,"heartwarming":3,"heaven":2,"heavenly":4,"heavyhearted":-2,"hehe":2,"hell":-4,"hellish":-2,"help":2,"helpful":2,"helping":2,"helpless":-2,"helps":2,"hero":2,"heroes":2,"heroic":3,"hesitant":-2,"hesitate":-2,"hid":-1,"hide":-1,"hideous":-3,"hides":-1,"hiding":-1,"highlight":2,"hilarious":2,"hinder":-2,"hindrance":-2,"hoax":-2,"hollow":-1,"homeless":-2,"homesick":-2,"homicide":-2,"homicides":-2,"honest":2,"honor":2,"honored":2,"honoring":2,"honour":2,"honoured":2,"honouring":2,"hooligan":-2,"hooliganism":-2,"hooligans":-2,"hope":2,"hopeful":2,"hopefully":2,"hopeless":-2,"hopelessness":-2,"hopes":2,"hoping":2,"horrendous":-3,"horrid":-3,"horrible":-3,"horrific":-3,"horrified":-3,"hospitalized":-2,"hostile":-2,"huckster":-2,"hug":2,"huge":1,"hugs":2,"humane":2,"humble":1,"humbug":-2,"humerous":3,"humiliated":-3,"humiliation":-3,"humor":2,"humorous":2,"humour":2,"humourous":2,"hunger":-2,"hurrah":5,"hurt":-2,"hurting":-2,"hurts":-2,"hypocritical":-2,"hysteria":-3,"hysterical":-3,"hysterics":-3,"icky":-3,"idiocy":-3,"idiot":-3,"idiotic":-3,"ignorance":-2,"ignorant":-2,"ignore":-1,"ignored":-2,"ignores":-1,"ill":-2,"ill-fated":-2,"illegal":-3,"illegally":-3,"illegitimate":-3,"illiteracy":-2,"illness":-2,"illnesses":-2,"illogical":-2,"imaginative":2,"imbecile":-3,"immobilized":-1,"immortal":2,"immune":1,"impair":-2,"impaired":-2,"impairing":-2,"impairment":-2,"impairs":-2,"impatient":-2,"impeachment":-3,"impeachments":-3,"impede":-2,"impeded":-2,"impedes":-2,"impeding":-2,"impedingly":-2,"imperfect":-2,"importance":2,"important":2,"impose":-1,"imposed":-1,"imposes":-1,"imposing":-1,"imposter":-2,"impotent":-2,"impress":3,"impressed":3,"impresses":3,"impressive":3,"imprisoned":-2,"imprisonment":-2,"improper":-2,"improperly":-2,"improve":2,"improved":2,"improvement":2,"improves":2,"improving":2,"inability":-2,"inaction":-2,"inadequate":-2,"inadvertently":-2,"inappropriate":-2,"incapable":-2,"incapacitated":-2,"incapacitates":-2,"incapacitating":-2,"incense":-2,"incensed":-2,"incenses":-2,"incensing":-2,"incoherent":-2,"incompetence":-2,"incompetent":-2,"incomplete":-1,"incomprehensible":-2,"inconsiderate":-2,"inconvenience":-2,"inconvenient":-2,"increase":1,"increased":1,"indecisive":-2,"indestructible":2,"indicted":-2,"indifference":-2,"indifferent":-2,"indignant":-2,"indignation":-2,"indoctrinate":-2,"indoctrinated":-2,"indoctrinates":-2,"indoctrinating":-2,"inediable":-2,"inexorable":-3,"inexcusable":-3,"ineffective":-2,"ineffectively":-2,"ineffectual":-2,"inefficiency":-2,"inefficient":-2,"inefficiently":-2,"inept":-2,"ineptitude":-2,"infantile":-2,"infantilized":-2,"infatuated":2,"infatuation":2,"infect":-2,"infected":-2,"infecting":-2,"infection":-2,"infections":-2,"infectious":-2,"infects":-2,"inferior":-2,"infest":-2,"infested":-2,"infesting":-2,"infests":-2,"inflamed":-2,"inflict":-2,"inflicted":-2,"inflicting":-2,"inflicts":-2,"influential":2,"infract":-2,"infracted":-2,"infracting":-2,"infracts":-2,"infringement":-2,"infuriate":-2,"infuriated":-2,"infuriates":-2,"infuriating":-2,"inhibit":-1,"inhuman":-2,"injured":-2,"injuries":-2,"injury":-2,"injustice":-2,"innovate":1,"innovates":1,"innovation":1,"innovative":2,"inoperative":-2,"inquisition":-2,"inquisitive":2,"insane":-2,"insanity":-2,"insecure":-2,"insensitive":-2,"insensitivity":-2,"insignificant":-2,"insipid":-2,"insolvent":-2,"insomnia":-2,"inspiration":2,"inspirational":2,"inspire":2,"inspired":2,"inspires":2,"inspiring":3,"insufficiency":-2,"insufficient":-2,"insufficiently":-2,"insult":-2,"insulted":-2,"insulting":-2,"insults":-2,"intact":2,"integrity":2,"intelligent":2,"intense":1,"interest":1,"interested":2,"interesting":2,"interests":1,"interrogated":-2,"interrupt":-2,"interrupted":-2,"interrupting":-2,"interruption":-2,"interrupts":-2,"intimacy":2,"intimidate":-2,"intimidated":-2,"intimidates":-2,"intimidating":-2,"intimidation":-2,"intransigence":-2,"intransigency":-2,"intricate":2,"intrigues":1,"invasion":-1,"invincible":2,"invite":1,"inviting":1,"invulnerable":2,"irate":-3,"ironic":-1,"irony":-1,"irrational":-1,"irreparable":-2,"irreproducible":-2,"irresistible":2,"irresistibly":2,"irresolute":-2,"irresponsible":-2,"irresponsibly":-2,"irreversible":-1,"irreversibly":-1,"irritate":-3,"irritated":-3,"irritates":-3,"irritating":-3,"isolated":-1,"itchy":-2,"jackass":-4,"jackasses":-4,"jailed":-2,"jaunty":2,"jealous":-2,"jealousy":-2,"jeopardy":-2,"jerk":-3,"jesus":1,"jewel":1,"jewels":1,"jocular":2,"join":1,"joke":2,"jokes":2,"jolly":2,"jovial":2,"joy":3,"joyful":3,"joyfully":3,"joyless":-2,"joyous":3,"jubilant":3,"jumpy":-1,"justice":2,"justifiably":2,"justified":2,"keen":1,"kickback":-3,"kickbacks":-3,"kidnap":-2,"kidnapped":-2,"kidnapping":-2,"kidnappings":-2,"kidnaps":-2,"kill":-3,"killed":-3,"killing":-3,"kills":-3,"kind":2,"kind of":0,"kinder":2,"kindness":2,"kiss":2,"kudos":3,"lack":-2,"lackadaisical":-2,"lag":-1,"lagged":-2,"lagging":-2,"lags":-2,"lame":-2,"landmark":2,"lapse":-1,"lapsed":-1,"laugh":1,"laughed":1,"laughing":1,"laughs":1,"laughting":1,"launched":1,"lawl":3,"lawsuit":-2,"lawsuits":-2,"lazy":-1,"leadership":1,"leading":2,"leak":-1,"leaked":-1,"leave":-1,"legal":1,"legally":1,"lenient":1,"lethal":-2,"lethality":-2,"lethargic":-2,"lethargy":-2,"liar":-3,"liars":-3,"libelous":-2,"lied":-2,"lifeless":-1,"lifesaver":4,"lighthearted":1,"likable":2,"like":2,"likeable":2,"liked":2,"likers":2,"likes":2,"liking":2,"limitation":-1,"limited":-1,"limits":-1,"litigation":-1,"litigious":-2,"lively":2,"livid":-2,"lmao":4,"lmfao":4,"loathe":-3,"loathed":-3,"loathes":-3,"loathing":-3,"loathsome":-3,"lobbied":-2,"lobby":-2,"lobbying":-2,"lobbyist":-2,"lobbyists":-2,"lol":3,"lolol":4,"lololol":4,"lolololol":4,"lonely":-2,"lonesome":-2,"longing":-1,"lool":3,"loom":-1,"loomed":-1,"looming":-1,"looms":-1,"loool":3,"looool":3,"loose":-3,"looses":-3,"loser":-3,"losing":-3,"loss":-3,"losses":-3,"lost":-3,"lousy":-2,"lovable":3,"love":3,"loved":3,"lovelies":3,"lovely":3,"loves":3,"loving":2,"loving-kindness":3,"lowest":-1,"loyal":3,"loyalty":3,"luck":3,"luckily":3,"lucky":3,"lucrative":3,"ludicrous":-3,"lugubrious":-2,"lunatic":-3,"lunatics":-3,"lurk":-1,"lurking":-1,"lurks":-1,"luxury":2,"macabre":-2,"mad":-3,"maddening":-3,"made-up":-1,"madly":-3,"madness":-3,"magnificent":3,"maladaption":-2,"maldevelopment":-2,"maltreatment":-2,"mandatory":-1,"manipulated":-1,"manipulating":-1,"manipulation":-1,"manslaughter":-3,"marvel":3,"marvelous":3,"marvels":3,"masterpiece":4,"masterpieces":4,"matter":1,"matters":1,"mature":2,"meaningful":2,"meaningless":-2,"medal":3,"mediocrity":-3,"meditative":1,"melancholy":-2,"memorable":1,"memoriam":-2,"menace":-2,"menaced":-2,"menaces":-2,"mercy":2,"merry":3,"mesmerizing":3,"mess":-2,"messed":-2,"messing up":-2,"methodical":2,"methodically":2,"mindless":-2,"miracle":4,"mirth":3,"mirthful":3,"mirthfully":3,"misbehave":-2,"misbehaved":-2,"misbehaves":-2,"misbehaving":-2,"misbranding":-3,"miscast":-2,"mischief":-1,"mischiefs":-1,"misclassified":-2,"misclassifies":-2,"misclassify":-2,"misconduct":-2,"misconducted":-2,"misconducting":-2,"misconducts":-2,"miserable":-3,"miserably":-3,"misery":-2,"misfire":-2,"misfortune":-2,"misgiving":-2,"misinformation":-2,"misinformed":-2,"misinterpreted":-2,"mislead":-3,"misleaded":-3,"misleading":-3,"misleads":-3,"misplace":-2,"misplaced":-2,"misplaces":-2,"misplacing":-2,"mispricing":-3,"misread":-1,"misreport":-2,"misreported":-2,"misreporting":-2,"misreports":-2,"misrepresent":-2,"misrepresentation":-2,"misrepresentations":-2,"misrepresented":-2,"misrepresenting":-2,"misrepresents":-2,"miss":-2,"missed":-2,"missing":-2,"mistake":-2,"mistaken":-2,"mistakes":-2,"mistaking":-2,"misunderstand":-2,"misunderstanding":-2,"misunderstands":-2,"misunderstood":-2,"misuse":-2,"misused":-2,"misuses":-2,"misusing":-2,"moan":-2,"moaned":-2,"moaning":-2,"moans":-2,"mock":-2,"mocked":-2,"mocking":-2,"mocks":-2,"modernize":2,"modernized":2,"modernizes":2,"modernizing":2,"mongering":-2,"monopolize":-2,"monopolized":-2,"monopolizes":-2,"monopolizing":-2,"monotone":-1,"moody":-1,"mope":-1,"moping":-1,"moron":-3,"motherfucker":-5,"motherfucking":-5,"motivate":1,"motivated":2,"motivating":2,"motivation":1,"mourn":-2,"mourned":-2,"mournful":-2,"mourning":-2,"mourns":-2,"muddy":-2,"mumpish":-2,"murder":-2,"murderer":-2,"murdering":-3,"murderous":-3,"murders":-2,"murky":-2,"myth":-1,"n00b":-2,"naive":-2,"narcissism":-2,"nasty":-3,"natural":1,"naïve":-2,"needy":-2,"negative":-2,"negativity":-2,"neglect":-2,"neglected":-2,"neglecting":-2,"neglects":-2,"nerves":-1,"nervous":-2,"nervously":-2,"nice":3,"nifty":2,"niggas":-5,"nigger":-5,"no":-1,"no fun":-3,"noble":2,"noblest":2,"noisy":-1,"non-approved":-2,"nonsense":-2,"noob":-2,"nosey":-2,"not good":-2,"not working":-3,"notable":2,"noticeable":2,"notorious":-2,"novel":2,"numb":-1,"nurturing":2,"nuts":-3,"obliterate":-2,"obliterated":-2,"obnoxious":-3,"obscene":-2,"obscenity":-2,"obsessed":2,"obsolete":-2,"obstacle":-2,"obstacles":-2,"obstinate":-2,"obstruct":-2,"obstructed":-2,"obstructing":-2,"obstruction":-2,"obstructs":-2,"odd":-2,"offence":-2,"offences":-2,"offend":-2,"offended":-2,"offender":-2,"offending":-2,"offends":-2,"offense":-2,"offenses":-2,"offensive":-2,"offensively":-2,"offline":-1,"oks":2,"ominous":3,"once-in-a-lifetime":3,"oops":-2,"opportunities":2,"opportunity":2,"oppressed":-2,"oppression":-2,"oppressions":-2,"oppressive":-2,"optimism":2,"optimistic":2,"optionless":-2,"ostracize":-2,"ostracized":-2,"ostracizes":-2,"ouch":-2,"outage":-2,"outages":-2,"outbreak":-2,"outbreaks":-2,"outcry":-2,"outmaneuvered":-2,"outnumbered":-2,"outrage":-3,"outraged":-3,"outrageous":-3,"outreach":2,"outstanding":5,"overjoyed":4,"overload":-1,"overlooked":-1,"overprotective":-2,"overran":-2,"overreact":-2,"overreacted":-2,"overreacting":-2,"overreaction":-2,"overreacts":-2,"oversell":-2,"overselling":-2,"oversells":-2,"oversight":-1,"oversimplification":-2,"oversimplified":-2,"oversimplifies":-2,"oversimplify":-2,"oversold":-2,"overstatement":-2,"overstatements":-2,"overweight":-1,"overwrought":-3,"oxymoron":-1,"pain":-2,"pained":-2,"painful":-2,"panic":-3,"panicked":-3,"panics":-3,"paradise":3,"paradox":-1,"pardon":2,"pardoned":2,"pardoning":2,"pardons":2,"parley":-1,"passion":1,"passionate":2,"passive":-1,"passively":-1,"pathetic":-2,"pay":-1,"peace":2,"peaceful":2,"peacefully":2,"penalize":-2,"penalized":-2,"penalizes":-2,"penalizing":-2,"penalty":-2,"pensive":-1,"perfect":3,"perfected":2,"perfection":3,"perfectly":3,"perfects":2,"peril":-2,"perjury":-3,"perpetrated":-2,"perpetrator":-2,"perpetrators":-2,"perplexed":-2,"persecute":-2,"persecuted":-2,"persecutes":-2,"persecuting":-2,"perturbed":-2,"pervert":-3,"pesky":-2,"pessimism":-2,"pessimistic":-2,"petrified":-2,"philanthropy":2,"phobic":-2,"picturesque":2,"pileup":-1,"pillage":-2,"pique":-2,"piqued":-2,"piss":-4,"pissed":-4,"pissing":-3,"piteous":-2,"pitied":-1,"pity":-2,"plague":-3,"plagued":-3,"plagues":-3,"plaguing":-3,"playful":2,"pleasant":3,"please":1,"pleased":3,"pleasurable":3,"pleasure":3,"plodding":-2,"poignant":2,"pointless":-2,"poised":-2,"poison":-2,"poisoned":-2,"poisons":-2,"polished":2,"polite":2,"politeness":2,"pollutant":-2,"pollute":-2,"polluted":-2,"polluter":-2,"polluters":-2,"pollutes":-2,"pollution":-2,"poor":-2,"poorer":-2,"poorest":-2,"poorly":-2,"popular":3,"popularity":3,"positive":2,"positively":2,"possessive":-2,"post-traumatic":-2,"postpone":-1,"postponed":-1,"postpones":-1,"postponing":-1,"poverty":-1,"powerful":2,"powerless":-2,"praise":3,"praised":3,"praises":3,"praising":3,"pray":1,"praying":1,"prays":1,"prblm":-2,"prblms":-2,"predatory":-2,"prepared":1,"pressure":-1,"pressured":-2,"pretend":-1,"pretending":-1,"pretends":-1,"pretty":1,"prevent":-1,"prevented":-1,"preventing":-1,"prevents":-1,"prick":-5,"prison":-2,"prisoner":-2,"prisoners":-2,"privileged":2,"proactive":2,"problem":-2,"problems":-2,"profit":2,"profitable":2,"profiteer":-2,"profits":2,"progress":2,"prohibit":-1,"prohibits":-1,"prominent":2,"promise":1,"promised":1,"promises":1,"promote":1,"promoted":1,"promotes":1,"promoting":1,"promptly":1,"propaganda":-2,"prosecute":-1,"prosecuted":-2,"prosecutes":-1,"prosecution":-1,"prospect":1,"prospects":1,"prosperity":3,"prosperous":3,"protect":1,"protected":1,"protects":1,"protest":-2,"protesters":-2,"protesting":-2,"protests":-2,"proud":2,"proudly":2,"provoke":-1,"provoked":-1,"provokes":-1,"provoking":-1,"prudence":2,"pseudoscience":-3,"psychopathic":-2,"punish":-2,"punished":-2,"punishes":-2,"punishing":-2,"punitive":-2,"pure":1,"purest":1,"purposeful":2,"pushy":-1,"puzzled":-2,"quaking":-2,"qualities":2,"quality":2,"questionable":-2,"questioned":-1,"questioning":-1,"racism":-3,"racist":-3,"racists":-3,"rage":-2,"rageful":-2,"rainy":-1,"rant":-3,"ranter":-3,"ranters":-3,"rants":-3,"rape":-4,"raped":-4,"rapist":-4,"rapture":2,"raptured":2,"raptures":2,"rapturous":4,"rash":-2,"ratified":2,"reach":1,"reached":1,"reaches":1,"reaching":1,"reassure":1,"reassured":1,"reassures":1,"reassuring":2,"rebel":-2,"rebellion":-2,"rebels":-2,"recession":-2,"reckless":-2,"recognition":2,"recommend":2,"recommended":2,"recommends":2,"redeemed":2,"refine":1,"refined":1,"refines":1,"refreshingly":2,"refuse":-2,"refused":-2,"refuses":-2,"refusing":-2,"regret":-2,"regretful":-2,"regrets":-2,"regretted":-2,"regretting":-2,"reigning":1,"reject":-1,"rejected":-1,"rejecting":-1,"rejection":-2,"rejects":-1,"rejoice":4,"rejoiced":4,"rejoices":4,"rejoicing":4,"relaxed":2,"relentless":-1,"reliability":2,"reliable":2,"reliably":2,"reliant":2,"relieve":1,"relieved":2,"relieves":1,"relieving":2,"relishing":2,"remarkable":2,"remorse":-2,"repellent":-2,"repercussion":-2,"repercussions":-2,"reprimand":-2,"reprimanded":-2,"reprimanding":-2,"reprimands":-2,"repulse":-1,"repulsed":-2,"repulsive":-2,"rescue":2,"rescued":2,"rescues":2,"resentful":-2,"resign":-1,"resigned":-1,"resigning":-1,"resigns":-1,"resolute":2,"resolution":2,"resolve":2,"resolved":2,"resolves":2,"resolving":2,"respect":2,"respected":2,"respects":2,"responsibility":1,"responsible":2,"responsive":2,"restful":2,"restless":-2,"restore":1,"restored":1,"restores":1,"restoring":1,"restrict":-2,"restricted":-2,"restricting":-2,"restriction":-2,"restrictive":-1,"restricts":-2,"retained":-1,"retard":-2,"retarded":-2,"retreat":-1,"revenge":-2,"revengeful":-2,"revered":2,"revive":2,"revives":2,"revolting":-2,"reward":2,"rewarded":2,"rewarding":2,"rewards":2,"rich":2,"richly":2,"ridiculous":-3,"rig":-1,"rigged":-1,"right direction":3,"righteousness":2,"rightful":2,"rightfully":2,"rigorous":3,"rigorously":3,"riot":-2,"riots":-2,"rise":1,"rises":1,"risk":-2,"risks":-2,"risky":-2,"riveting":3,"rob":-2,"robber":-2,"robed":-2,"robing":-2,"robs":-2,"robust":2,"rofl":4,"roflcopter":4,"roflmao":4,"romance":2,"romantical":2,"romantically":2,"rose":1,"rotfl":4,"rotflmfao":4,"rotflol":4,"rotten":-3,"rude":-2,"ruin":-2,"ruined":-2,"ruining":-2,"ruins":-2,"sabotage":-2,"sad":-2,"sadden":-2,"saddened":-2,"sadly":-2,"safe":1,"safely":1,"safer":2,"safety":1,"salient":1,"salute":2,"saluted":2,"salutes":2,"saluting":2,"salvation":2,"sappy":-1,"sarcastic":-2,"satisfied":2,"savange":-2,"savanges":-2,"save":2,"saved":2,"savings":1,"scam":-2,"scams":-2,"scandal":-3,"scandalous":-3,"scandals":-3,"scapegoat":-2,"scapegoats":-2,"scare":-2,"scared":-2,"scar":-2,"scars":-2,"scary":-2,"sceptical":-2,"scold":-2,"scoop":3,"scorn":-2,"scornful":-2,"scream":-2,"screamed":-2,"screaming":-2,"screams":-2,"screwed":-2,"screwed up":-3,"scum":-3,"scumbag":-4,"seamless":2,"seamlessly":2,"secure":2,"secured":2,"secures":2,"sedition":-2,"seditious":-2,"seduced":-1,"self-abuse":-2,"self-confident":2,"self-contradictory":-2,"self-deluded":-2,"selfish":-3,"selfishness":-3,"sentence":-2,"sentenced":-2,"sentences":-2,"sentencing":-2,"serene":2,"settlement":1,"settlements":1,"severe":-2,"severely":-2,"sexist":-2,"sexistic":-2,"sexy":3,"shaky":-2,"shame":-2,"shamed":-2,"shameful":-2,"share":1,"shared":1,"shares":1,"shattered":-2,"shit":-4,"shithead":-4,"shitty":-3,"shock":-2,"shocked":-2,"shocking":-2,"shocks":-2,"shoody":-2,"shoot":-1,"short-sighted":-2,"short-sightedness":-2,"shortage":-2,"shortages":-2,"shrew":-4,"shy":-1,"sick":-2,"sickness":-2,"side-effect":-2,"side-effects":-2,"sigh":-2,"significance":1,"significant":1,"silencing":-1,"silly":-1,"simplicity":1,"sin":-2,"sincere":2,"sincerely":2,"sincerest":2,"sincerity":2,"sinful":-3,"singleminded":-2,"sinister":-2,"sins":-2,"skeptic":-2,"skeptical":-2,"skepticism":-2,"skeptics":-2,"slam":-2,"slash":-2,"slashed":-2,"slashes":-2,"slashing":-2,"slave":-3,"slavery":-3,"slaves":-3,"sleeplessness":-2,"slick":2,"slicker":2,"slickest":2,"slip":-1,"sloppy":-2,"sluggish":-2,"slumping":-1,"slut":-5,"smart":1,"smarter":2,"smartest":2,"smear":-2,"smile":2,"smiled":2,"smiles":2,"smiling":2,"smog":-2,"smuggle":-2,"smuggled":-2,"smuggling":-2,"smuggles":-2,"sneaky":-1,"sneeze":-2,"sneezed":-2,"sneezes":-2,"sneezing":-2,"snub":-2,"snubbed":-2,"snubbing":-2,"snubs":-2,"sobering":1,"solemn":-1,"solid":2,"solidarity":2,"solidified":2,"solidifies":2,"solidify":2,"solidifying":2,"solution":1,"solutions":1,"solve":1,"solved":1,"solves":1,"solving":1,"somber":-2,"some kind":0,"son-of-a-bitch":-5,"soothe":3,"soothed":3,"soothing":3,"sophisticated":2,"sore":-1,"sorrow":-2,"sorrowful":-2,"sorry":-1,"spacious":1,"spam":-2,"spammer":-3,"spammers":-3,"spamming":-2,"spark":1,"sparkle":3,"sparkles":3,"sparkling":3,"spearhead":2,"speculative":-2,"spirit":1,"spirited":2,"spiritless":-2,"spiteful":-2,"splendid":3,"spoiled":-2,"spoilt":-2,"spotless":2,"sprightly":2,"squander":-2,"squandered":-2,"squandering":-2,"squanders":-2,"squelched":-1,"stab":-2,"stabbed":-2,"stable":2,"stabs":-2,"stall":-2,"stalled":-2,"stalling":-2,"stamina":2,"stampede":-2,"stank":-2,"startled":-2,"startling":3,"starve":-2,"starved":-2,"starves":-2,"starving":-2,"steadfast":2,"steal":-2,"stealing":-2,"steals":-2,"stereotype":-2,"stereotyped":-2,"stifled":-1,"stimulate":1,"stimulated":1,"stimulates":1,"stimulating":2,"stingy":-2,"stink":-2,"stinked":-2,"stinker":-2,"stinking":-2,"stinks":-2,"stinky":-2,"stole":-2,"stolen":-2,"stop":-1,"stopped":-1,"stopping":-1,"stops":-1,"stout":2,"straight":1,"strange":-1,"strangely":-1,"strangled":-2,"strength":2,"strengthen":2,"strengthened":2,"strengthening":2,"strengthens":2,"strengths":2,"stress":-1,"stressed":-2,"stressor":-2,"stressors":-2,"stricken":-2,"strike":-1,"strikers":-2,"strikes":-1,"strong":2,"stronger":2,"strongest":2,"struck":-1,"struggle":-2,"struggled":-2,"struggles":-2,"struggling":-2,"stubborn":-2,"stuck":-2,"stunned":-2,"stunning":4,"stupid":-2,"stupidity":-3,"stupidly":-2,"suave":2,"subpoena":-2,"substantial":1,"substantially":1,"subversive":-2,"succeed":3,"succeeded":3,"succeeding":3,"succeeds":3,"success":2,"successful":3,"successfully":3,"suck":-3,"sucks":-3,"sue":-2,"sued":-2,"sueing":-2,"sues":-2,"suffer":-2,"suffered":-2,"sufferer":-2,"sufferers":-2,"suffering":-2,"suffers":-2,"suicidal":-2,"suicide":-2,"suicides":-2,"suing":-2,"suitable":2,"suited":2,"sulking":-2,"sulky":-2,"sullen":-2,"sunshine":2,"super":3,"superb":5,"superior":2,"support":2,"supported":2,"supporter":1,"supporters":1,"supporting":1,"supportive":2,"supports":2,"supreme":4,"survived":2,"surviving":2,"survivor":2,"suspect":-1,"suspected":-1,"suspecting":-1,"suspects":-1,"suspend":-1,"suspended":-1,"suspicious":-2,"sustainability":1,"sustainable":2,"sustainably":2,"swear":-2,"swearing":-2,"swears":-2,"sweet":2,"sweeter":3,"sweetest":3,"swift":2,"swiftly":2,"swindle":-3,"swindles":-3,"swindling":-3,"sympathetic":2,"sympathy":2,"taint":-2,"tainted":-2,"talent":2,"tard":-2,"tarnish":-2,"tarnished":-2,"tarnishes":-2,"tears":-2,"tender":2,"tenderness":2,"tense":-2,"tension":-1,"terrible":-3,"terribly":-3,"terrific":4,"terrifically":4,"terrified":-3,"terror":-3,"terrorist":-2,"terrorists":-2,"terrorize":-3,"terrorized":-3,"terrorizes":-3,"thank":2,"thankful":2,"thanks":2,"thorny":-2,"thoughtful":2,"thoughtless":-2,"threat":-2,"threaten":-2,"threatened":-2,"threatening":-2,"threatens":-2,"threats":-2,"thrilled":5,"thwart":-2,"thwarted":-2,"thwarting":-2,"thwarts":-2,"timid":-2,"timorous":-2,"tired":-2,"tits":-2,"tolerance":2,"tolerant":2,"toothless":-2,"top":2,"tops":2,"torn":-2,"torture":-4,"tortured":-4,"tortures":-4,"torturing":-4,"totalitarian":-2,"totalitarianism":-2,"tout":-2,"touted":-2,"touting":-2,"touts":-2,"toxic":-3,"tragedies":-2,"tragedy":-2,"tragic":-2,"tranquil":2,"transgress":-2,"transgressed":-2,"transgresses":-2,"transgressing":-2,"trap":-1,"trapped":-2,"traps":-1,"trauma":-3,"traumatic":-3,"travesty":-2,"treason":-3,"treasonous":-3,"treasure":2,"treasures":2,"trembling":-2,"tremor":-2,"tremors":-2,"tremulous":-2,"tribulation":-2,"tribute":2,"tricked":-2,"trickery":-2,"triumph":4,"triumphant":4,"troll":-2,"trouble":-2,"troubled":-2,"troubles":-2,"troubling":-2,"true":2,"trust":1,"trusted":2,"trusts":1,"tumor":-2,"twat":-5,"tyran":-3,"tyrannic":-3,"tyrannical":-3,"tyrannically":-3,"tyrans":-3,"ubiquitous":2,"ugh":-2,"ugliness":-3,"ugly":-3,"unable":-2,"unacceptable":-2,"unappeasable":-2,"unappreciated":-2,"unapproved":-2,"unattractive":-2,"unavailable":-1,"unavailing":-2,"unaware":-2,"unbearable":-2,"unbelievable":-1,"unbelieving":-1,"unbiased":2,"uncertain":-1,"unclear":-1,"uncomfortable":-2,"unconcerned":-2,"unconfirmed":-1,"unconvinced":-1,"uncredited":-1,"undecided":-1,"undercooked":-2,"underestimate":-1,"underestimated":-1,"underestimates":-1,"underestimating":-1,"undermine":-2,"undermined":-2,"undermines":-2,"undermining":-2,"underperform":-2,"underperformed":-2,"underperforming":-2,"underperforms":-2,"undeserving":-2,"undesirable":-2,"uneasy":-2,"unemployed":-1,"unemployment":-2,"unequal":-1,"unequaled":2,"unethical":-2,"uneventful":-2,"unfair":-2,"unfavorable":-2,"unfit":-2,"unfitted":-2,"unfocused":-2,"unforgivable":-3,"unforgiving":-2,"unfulfilled":-2,"unfunny":-2,"ungenerous":-2,"ungrateful":-3,"unhappy":-2,"unhappiness":-2,"unhealthy":-2,"unhygienic":-2,"unified":1,"unimaginative":-2,"unimpressed":-2,"uninspired":-2,"unintelligent":-2,"unintentional":-2,"uninvolving":-2,"united":1,"unjust":-2,"unlikely":-1,"unlovable":-2,"unloved":-2,"unmatched":1,"unmotivated":-2,"unoriginal":-2,"unparliamentary":-2,"unpleasant":-2,"unpleasantness":-2,"unprofessional":-2,"unravel":1,"unreleting":-2,"unresearched":-2,"unsafe":-2,"unsatisfied":-2,"unscientific":-2,"unsecured":-2,"unselfish":2,"unsettled":-1,"unsold":-1,"unsophisticated":-2,"unsound":-2,"unstable":-2,"unstoppable":2,"unsuccessful":-2,"unsuccessfully":-2,"unsupported":-2,"unsure":-1,"untarnished":2,"untrue":-2,"unwanted":-2,"unworthy":-2,"uplifting":2,"uproar":-3,"upset":-2,"upsets":-2,"upsetting":-2,"uptight":-2,"urgent":-1,"useful":2,"usefulness":2,"useless":-2,"uselessness":-2,"vague":-2,"validate":1,"validated":1,"validates":1,"validating":1,"vapid":-2,"verdict":-1,"verdicts":-1,"vested":1,"vexation":-2,"vexing":-2,"vibrant":3,"vicious":-2,"victim":-3,"victimization":-3,"victimize":-3,"victimized":-3,"victimizes":-3,"victimizing":-3,"victims":-3,"victor":3,"victors":3,"victory":3,"victories":3,"vigilant":3,"vigor":3,"vile":-3,"vindicate":2,"vindicated":2,"vindicates":2,"vindicating":2,"violate":-2,"violated":-2,"violates":-2,"violating":-2,"violation":-2,"violations":-2,"violence":-3,"violence-related":-3,"violent":-3,"violently":-3,"virtuous":2,"virulent":-2,"vision":1,"visionary":3,"visioning":1,"visions":1,"vitality":3,"vitamin":1,"vitriolic":-3,"vivacious":3,"vividly":2,"vociferous":-1,"vomit":-3,"vomited":-3,"vomiting":-3,"vomits":-3,"vulnerability":-2,"vulnerable":-2,"walkout":-2,"walkouts":-2,"wanker":-3,"want":1,"war":-2,"warfare":-2,"warm":1,"warmhearted":2,"warmness":2,"warmth":2,"warn":-2,"warned":-2,"warning":-3,"warnings":-3,"warns":-2,"waste":-1,"wasted":-2,"wasting":-2,"wavering":-1,"weak":-2,"weakened":-2,"weakness":-2,"weaknesses":-2,"wealth":3,"wealthier":2,"wealthy":2,"weary":-2,"weep":-2,"weeping":-2,"weird":-2,"welcome":2,"welcomed":2,"welcomes":2,"well-being":2,"well-championed":3,"well-developed":2,"well-established":2,"well-focused":2,"well-groomed":2,"well-proportioned":2,"whimsical":1,"whitewash":-3,"whore":-4,"wicked":-2,"widowed":-1,"willingness":2,"win":4,"winner":4,"winning":4,"wins":4,"winwin":3,"wisdom":1,"wish":1,"wishes":1,"wishing":1,"withdrawal":-3,"wits":2,"woebegone":-2,"woeful":-3,"won":3,"wonderful":4,"wonderfully":4,"woo":3,"woohoo":3,"wooo":4,"woow":4,"worn":-1,"worried":-3,"worries":-3,"worry":-3,"worrying":-3,"worse":-3,"worsen":-3,"worsened":-3,"worsening":-3,"worsens":-3,"worshiped":3,"worst":-3,"worth":2,"worthless":-2,"worthy":2,"wow":4,"wowow":4,"wowww":4,"wrathful":-3,"wreck":-2,"wrenching":-2,"wrong":-2,"wrongdoing":-2,"wrongdoings":-2,"wronged":-2,"wrongful":-2,"wrongfully":-2,"wrongly":-2,"wtf":-4,"wtff":-4,"wtfff":-4,"xo":3,"xoxo":3,"xoxoxo":4,"xoxoxoxo":4,"yeah":1,"yearning":1,"yeees":2,"yes":1,"youthful":2,"yucky":-2,"yummy":3,"zealot":-2,"zealots":-2,"zealous":2}')},623:function(e){"use strict";e.exports=JSON.parse('{"cant":1,"can\'t":1,"dont":1,"don\'t":1,"doesnt":1,"doesn\'t":1,"not":1,"non":1,"wont":1,"won\'t":1,"isnt":1,"isn\'t":1}')}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var o=t[n]={id:n,loaded:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e},function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t,r){for(var n=0;n<r.length;n++){var i=r[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,(o=i.key,u=void 0,u=function(t,r){if("object"!==e(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,r||"default");if("object"!==e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(o,"string"),"symbol"===e(u)?u:String(u)),i)}var o,u}var n=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var r,n,i;return r=e,(n=[{key:"getResult",value:function(e,t){throw new Error("The method getResult is not implemented")}},{key:"getScore",value:function(){return 0}},{key:"isApplicable",value:function(e){return!0}}])&&t(r.prototype,n),i&&t(r,i),Object.defineProperty(r,"prototype",{writable:!1}),e}(),i=lodash;function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function u(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,u=void 0,u=function(e,t){if("object"!==o(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===o(u)?u:String(u)),n)}var i,u}var a=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.has=!1,this.score=0,this.maxScore=0,this.text="",this.empty="",this.tooltip="",this}var t,r,n;return t=e,(r=[{key:"hasScore",value:function(){return this.has}},{key:"getScore",value:function(){return this.score}},{key:"setScore",value:function(e){return(0,i.isNumber)(e)&&(this.score=e,this.has=0<e),this}},{key:"setMaxScore",value:function(e){return(0,i.isNumber)(e)&&(this.maxScore=e),this}},{key:"getMaxScore",value:function(){return this.maxScore}},{key:"hasText",value:function(){return""!==this.text}},{key:"getText",value:function(){return this.hasText()?this.text:this.empty}},{key:"setText",value:function(e){return this.text=(0,i.isUndefined)(e)?"":e,this}},{key:"setEmpty",value:function(e){return this.empty=(0,i.isUndefined)(e)?"":e,this}},{key:"hasTooltip",value:function(){return""!==this.tooltip}},{key:"getTooltip",value:function(){return this.tooltip}},{key:"setTooltip",value:function(e){return this.tooltip=(0,i.isUndefined)(e)?"":e,this}}])&&u(t.prototype,r),n&&u(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),s=function(e){return e?(0,i.toLower)((0,i.deburr)((0,i.trim)(e.replace(/[\s\./_]+/g,"-"),"-"))):""},l=function(e){return e.replace(/<\/?[a-z][^>]*?>/gi,"\n")},c=function(e){return e.replace(/&nbsp;|&#160;/gi," ").replace(/\s{2,}/g," ").replace(/\s\./g,".").replace(/^\s+|\s+$/g,"")},d="[^<>&/\\[\\]\0- =]+?",p=new RegExp("\\["+d+"( [^\\]]+?)?\\]","g"),f=new RegExp("\\[/"+d+"\\]","g"),y=function(e){return e.replace(p,"").replace(f,"")},h=function(e){return e.replace(/--|\u2014/g," ")},g=new RegExp(["[","-¿×÷"," -⯿","⸀-⹿","]"].join(""),"g"),m=function(e){return e.replace(g,"")},b=function(e){return e.replace(/<!--[\s\S]*?-->/g,"")},v=function(e){return e.replace(/&\S+?;/g,"")},D="[\\–\\-\\(\\)_\\[\\]’“”\"'.?!:;,¿¡«»‹›—×+&<>]+",w=new RegExp("^"+D),k=new RegExp(D+"$"),F=function(e){return e.replace(w,"").replace(k,"")},E=function(e,t){var r=function(e){if(""===(e=(0,i.flow)([l,b,y,c,v,h,m])(e)))return[];var t=e.split(/\s/g);return t=(0,i.map)(t,(function(e){return F(e)})),(0,i.filter)(t,(function(e){return""!==e.trim()}))}(e);return t=t||!1,0!==r.length&&(!1===t?r:r.slice(0,t))},A={adulthood:!0,advice:!0,agenda:!0,aid:!0,alcohol:!0,ammo:!0,anime:!0,athletics:!0,audio:!0,bison:!0,blood:!0,bream:!0,buffalo:!0,butter:!0,carp:!0,cash:!0,chassis:!0,chess:!0,clothing:!0,cod:!0,commerce:!0,cooperation:!0,corps:!0,debris:!0,diabetes:!0,digestion:!0,elk:!0,energy:!0,equipment:!0,excretion:!0,expertise:!0,flounder:!0,fun:!0,gallows:!0,garbage:!0,graffiti:!0,headquarters:!0,health:!0,herpes:!0,highjinks:!0,homework:!0,housework:!0,information:!0,jeans:!0,justice:!0,kudos:!0,labour:!0,literature:!0,machinery:!0,mackerel:!0,mail:!0,media:!0,mews:!0,moose:!0,music:!0,mud:!0,manga:!0,news:!0,pike:!0,plankton:!0,pliers:!0,police:!0,pollution:!0,premises:!0,rain:!0,research:!0,rice:!0,salmon:!0,scissors:!0,seo:!0,series:!0,sewage:!0,shambles:!0,shrimp:!0,species:!0,staff:!0,swine:!0,tennis:!0,traffic:!0,transportation:!0,trout:!0,tuna:!0,wealth:!0,welfare:!0,whiting:!0,wildebeest:!0,wildlife:!0,wordpress:!0,you:!0},C=[["I","we"],["me","us"],["he","they"],["she","they"],["them","them"],["myself","ourselves"],["yourself","yourselves"],["itself","themselves"],["herself","themselves"],["himself","themselves"],["themself","themselves"],["is","are"],["was","were"],["has","have"],["this","these"],["that","those"],["echo","echoes"],["dingo","dingoes"],["volcano","volcanoes"],["tornado","tornadoes"],["torpedo","torpedoes"],["genus","genera"],["viscus","viscera"],["stigma","stigmata"],["stoma","stomata"],["dogma","dogmata"],["lemma","lemmata"],["schema","schemata"],["anathema","anathemata"],["ox","oxen"],["axe","axes"],["die","dice"],["yes","yeses"],["foot","feet"],["eave","eaves"],["goose","geese"],["tooth","teeth"],["quiz","quizzes"],["human","humans"],["proof","proofs"],["carve","carves"],["valve","valves"],["looey","looies"],["thief","thieves"],["groove","grooves"],["pickaxe","pickaxes"],["whiskey","whiskies"],["thou","you"]],S=[[/[^aeiou]ese$/i,"$0"],[/deer$/i,"$0"],[/fish$/i,"$0"],[/measles$/i,"$0"],[/o[iu]s$/i,"$0"],[/pox$/i,"$0"],[/sheep$/i,"$0"],[/s?$/i,"s"],[/[^\u0000-\u007F]$/i,"$0"],[/([^aeiou]ese)$/i,"$1"],[/(ax|test)is$/i,"$1es"],[/(alias|[^aou]us|t[lm]as|gas|ris)$/i,"$1es"],[/(e[mn]u)s?$/i,"$1s"],[/([^l]ias|[aeiou]las|[ejzr]as|[iu]am)$/i,"$1"],[/(alumn|syllab|octop|vir|radi|nucle|fung|cact|stimul|termin|bacill|foc|uter|loc|strat)(?:us|i)$/i,"$1i"],[/(alumn|alg|vertebr)(?:a|ae)$/i,"$1ae"],[/(seraph|cherub)(?:im)?$/i,"$1im"],[/(her|at|gr)o$/i,"$1oes"],[/(agend|addend|millenni|dat|extrem|bacteri|desiderat|strat|candelabr|errat|ov|symposi|curricul|automat|quor)(?:a|um)$/i,"$1a"],[/(apheli|hyperbat|periheli|asyndet|noumen|phenomen|criteri|organ|prolegomen|hedr|automat)(?:a|on)$/i,"$1a"],[/sis$/i,"ses"],[/(?:(kni|wi|li)fe|(ar|l|ea|eo|oa|hoo)f)$/i,"$1$2ves"],[/([^aeiouy]|qu)y$/i,"$1ies"],[/([^ch][ieo][ln])ey$/i,"$1ies"],[/(x|ch|ss|sh|zz)$/i,"$1es"],[/(matr|cod|mur|sil|vert|ind|append)(?:ix|ex)$/i,"$1ices"],[/\b((?:tit)?m|l)(?:ice|ouse)$/i,"$1ice"],[/(pe)(?:rson|ople)$/i,"$1ople"],[/(child)(?:ren)?$/i,"$1ren"],[/eaux$/i,"$0"],[/m[ae]n$/i,"men"]];function j(e){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},j(e)}function x(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==j(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==j(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===j(o)?o:String(o)),n)}var i,o}var B=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.irregularSingles={},this.irregularPlurals={},this.uncountables=A,this.pluralizationRules=S,C.forEach((function(e){var t=e[0],r=e[1];this.irregularSingles[t]=r,this.irregularPlurals[r]=t}),this)}var t,r,n;return t=e,r=[{key:"get",value:function(e){if(!e.length)return e;var t=e.toLowerCase();if(this.irregularPlurals.hasOwnProperty(t)&&this.restoreCase(e,t),this.irregularSingles.hasOwnProperty(t)&&this.restoreCase(e,this.irregularSingles[t]),this.uncountables.hasOwnProperty(t))return e;for(var r=this.pluralizationRules.length;r--;){var n=this.pluralizationRules[r];if(n[0].test(e))return this.replace(e,n)}return e}},{key:"restoreCase",value:function(e,t){return e===t?t:e===e.toUpperCase()?t.toUpperCase():e[0]===e[0].toUpperCase()?t.charAt(0).toUpperCase()+t.substr(1).toLowerCase():t.toLowerCase()}},{key:"replace",value:function(e,t){var r=arguments,n=this;return e.replace(t[0],(function(i,o){var u=n.interpolate(t[1],r);return""===i?n.restoreCase(e[o-1],u):n.restoreCase(i,u)}))}},{key:"interpolate",value:function(e,t){return e.replace(/\$(\d{1,2})/g,(function(e,r){return t[r]||""}))}}],r&&x(t.prototype,r),n&&x(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),O=B,_=wp.wordcount,P=function(e){return(0,_.count)(e,"words")},R=function(e){var t=[],r=[];e.forEach((function(e){t.push(e.word),r.push(e.plural)}));var n=t.length,o=[];function u(t){e.forEach((function(e){e.plural===e.word||(0,i.includes)(t,e.plural)||o.push(t.join(" ").replace(e.word,e.plural))}))}o.push(t.join(" "));for(var a=0;a<n*n;a++)(0,i.isUndefined)(o[a])||u(o[a].split(" "));return o.push(r.join(" ")),(0,i.uniq)(o)},T=r(8575),z=wp.hooks,N=/href=(["'])([^"']+)\1/i;function q(e){return"#"===e[0]}function I(e){var t=N.exec(e);return null===t?"":t[2]}function M(e,t){var r,n=I(e);return r=function(e){return T.parse(e).protocol}(n),r&&"http:"!==r&&"https:"!==r||q(n)?"other":function(e,t){var r=(0,z.applyFilters)("rankMath_analysis_isInternalLink",null,e,t);if(null!==r)return r;if(!(0,i.includes)(e,"//")&&"/"===e[0])return!0;if(q(e))return!1;var n=T.parse(e,!1,!0);return!n.host||(0,i.includes)(n.host,t)}(n,t)?"internal":"external"}function L(e,t){return e=e.toLowerCase(),(0,i.includes)(e,"dofollow")?"Dofollow":"internal"!==t&&rankMath.noFollowExternalLinks&&!(0,i.includes)(e,"nofollow")?(r=I(e),n=!0,o=T.parse(r,!1,!0),(rankMath.noFollowDomains.length?(rankMath.noFollowDomains.forEach((function(e){(0,i.includes)(o.host,e)&&(n=!1)})),n):rankMath.noFollowExcludeDomains.length&&(n=!1,rankMath.noFollowExcludeDomains.forEach((function(e){(0,i.includes)(o.host,e)&&(n=!0)})),n))?"Dofollow":"Nofollow"):(0,i.includes)(e,"<a")&&(0,i.includes)(e,"rel=")&&(0,i.includes)(e,"nofollow")?"Nofollow":"Dofollow";var r,n,o}var $=function(e){var t=function(e){return e.match(/<a [^>]*href=([\"'])[a-z/]([^\"']+)[^>]*>/gi)||[]}(e),r={total:0,internalTotal:0,internalDofollow:0,internalNofollow:0,externalTotal:0,externalDofollow:0,externalNofollow:0,otherTotal:0,otherDofollow:0,otherNofollow:0,anchors:t};return null===t||(r.total=t.length,t.forEach((function(e){var t=M(e,rankMath.parentDomain),n=L(e,t);r[t+"Total"]++,r[t+n]++}))),r};const U=[];function W(e){let t=-1;if(!e||!Array.isArray(e)&&!e.type)throw new Error("Expected node, not `"+e+"`");if("value"in e)return e.value;const r=(Array.isArray(e)?e:e.children)||U,n=[];for(;++t<r.length;)n[t]=W(r[t]);return n.join("")}const K={}.hasOwnProperty;function H(e){return function(e){if(!e||!e.children)throw new Error("Missing children in `parent` for `modifier`");!function(e,t,r){let n=-1;if(!e)throw new Error("Iterate requires that |this| not be "+e);if(!K.call(e,"length"))throw new Error("Iterate requires that |this| has a `length`");if("function"!=typeof t)throw new TypeError("`callback` must be a function");for(;++n<e.length;){if(!(n in e))continue;const i=t.call(r,e[n],n,e);"number"==typeof i&&(i<0&&(n=0),n=i-1)}}(e.children,t,e)};function t(t,r){return e(t,r,this)}}const V=H((function(e,t,r){const n=r.children[t-1];if(n&&"children"in n&&"children"in e&&e.children.length>0){let i=-1;for(;e.children[++i];){const o=e.children[i];if("WordNode"===o.type)return;if("SymbolNode"===o.type||"PunctuationNode"===o.type){const i=W(o);if(","!==i&&";"!==i)return;return n.children.push(...e.children),n.position&&e.position&&(n.position.end=e.position.end),r.children.splice(t,1),t}}}})),Y=/^([!"').?\u0F3B\u0F3D\u169C\u2019\u201D\u2026\u203A\u203D\u2046\u207E\u208E\u2309\u230B\u232A\u2769\u276B\u276D\u276F\u2771\u2773\u2775\u27C6\u27E7\u27E9\u27EB\u27ED\u27EF\u2984\u2986\u2988\u298A\u298C\u298E\u2990\u2992\u2994\u2996\u2998\u29D9\u29DB\u29FD\u2E03\u2E05\u2E0A\u2E0D\u2E1D\u2E21\u2E23\u2E25\u2E27\u2E29\u2E56\u2E58\u2E5A\u2E5C\u3009\u300B\u300D\u300F\u3011\u3015\u3017\u3019\u301B\u301E\u301F\uFD3E\uFE18\uFE36\uFE38\uFE3A\uFE3C\uFE3E\uFE40\uFE42\uFE44\uFE48\uFE5A\uFE5C\uFE5E\uFF09\uFF3D\uFF5D\uFF60\uFF63\u00BB\]}])\1*$/,G=/^[ \t]*((\r?\n|\r)[\t ]*)+$/,Q=/^([!.?\u2026\u203D]+)$/,J=/^([&'\-.:=?@\u00AD\u00B7\u2010\u2011\u2019\u2027]|_+)$/,Z=/^(?:[\d\u00B2\u00B3\u00B9\u00BC-\u00BE\u0660-\u0669\u06F0-\u06F9\u07C0-\u07C9\u0966-\u096F\u09E6-\u09EF\u09F4-\u09F9\u0A66-\u0A6F\u0AE6-\u0AEF\u0B66-\u0B6F\u0B72-\u0B77\u0BE6-\u0BF2\u0C66-\u0C6F\u0C78-\u0C7E\u0CE6-\u0CEF\u0D58-\u0D5E\u0D66-\u0D78\u0DE6-\u0DEF\u0E50-\u0E59\u0ED0-\u0ED9\u0F20-\u0F33\u1040-\u1049\u1090-\u1099\u1369-\u137C\u16EE-\u16F0\u17E0-\u17E9\u17F0-\u17F9\u1810-\u1819\u1946-\u194F\u19D0-\u19DA\u1A80-\u1A89\u1A90-\u1A99\u1B50-\u1B59\u1BB0-\u1BB9\u1C40-\u1C49\u1C50-\u1C59\u2070\u2074-\u2079\u2080-\u2089\u2150-\u2182\u2185-\u2189\u2460-\u249B\u24EA-\u24FF\u2776-\u2793\u2CFD\u3007\u3021-\u3029\u3038-\u303A\u3192-\u3195\u3220-\u3229\u3248-\u324F\u3251-\u325F\u3280-\u3289\u32B1-\u32BF\uA620-\uA629\uA6E6-\uA6EF\uA830-\uA835\uA8D0-\uA8D9\uA900-\uA909\uA9D0-\uA9D9\uA9F0-\uA9F9\uAA50-\uAA59\uABF0-\uABF9\uFF10-\uFF19]|\uD800[\uDD07-\uDD33\uDD40-\uDD78\uDD8A\uDD8B\uDEE1-\uDEFB\uDF20-\uDF23\uDF41\uDF4A\uDFD1-\uDFD5]|\uD801[\uDCA0-\uDCA9]|\uD802[\uDC58-\uDC5F\uDC79-\uDC7F\uDCA7-\uDCAF\uDCFB-\uDCFF\uDD16-\uDD1B\uDDBC\uDDBD\uDDC0-\uDDCF\uDDD2-\uDDFF\uDE40-\uDE48\uDE7D\uDE7E\uDE9D-\uDE9F\uDEEB-\uDEEF\uDF58-\uDF5F\uDF78-\uDF7F\uDFA9-\uDFAF]|\uD803[\uDCFA-\uDCFF\uDD30-\uDD39\uDE60-\uDE7E\uDF1D-\uDF26\uDF51-\uDF54\uDFC5-\uDFCB]|\uD804[\uDC52-\uDC6F\uDCF0-\uDCF9\uDD36-\uDD3F\uDDD0-\uDDD9\uDDE1-\uDDF4\uDEF0-\uDEF9]|\uD805[\uDC50-\uDC59\uDCD0-\uDCD9\uDE50-\uDE59\uDEC0-\uDEC9\uDF30-\uDF3B]|\uD806[\uDCE0-\uDCF2\uDD50-\uDD59]|\uD807[\uDC50-\uDC6C\uDD50-\uDD59\uDDA0-\uDDA9\uDF50-\uDF59\uDFC0-\uDFD4]|\uD809[\uDC00-\uDC6E]|\uD81A[\uDE60-\uDE69\uDEC0-\uDEC9\uDF50-\uDF59\uDF5B-\uDF61]|\uD81B[\uDE80-\uDE96]|\uD834[\uDEC0-\uDED3\uDEE0-\uDEF3\uDF60-\uDF78]|\uD835[\uDFCE-\uDFFF]|\uD838[\uDD40-\uDD49\uDEF0-\uDEF9]|\uD839[\uDCF0-\uDCF9]|\uD83A[\uDCC7-\uDCCF\uDD50-\uDD59]|\uD83B[\uDC71-\uDCAB\uDCAD-\uDCAF\uDCB1-\uDCB4\uDD01-\uDD2D\uDD2F-\uDD3D]|\uD83C[\uDD00-\uDD0C]|\uD83E[\uDFF0-\uDFF9])+$/,X=/^\d/,ee=/^(?:[a-z\u00B5\u00DF-\u00F6\u00F8-\u00FF\u0101\u0103\u0105\u0107\u0109\u010B\u010D\u010F\u0111\u0113\u0115\u0117\u0119\u011B\u011D\u011F\u0121\u0123\u0125\u0127\u0129\u012B\u012D\u012F\u0131\u0133\u0135\u0137\u0138\u013A\u013C\u013E\u0140\u0142\u0144\u0146\u0148\u0149\u014B\u014D\u014F\u0151\u0153\u0155\u0157\u0159\u015B\u015D\u015F\u0161\u0163\u0165\u0167\u0169\u016B\u016D\u016F\u0171\u0173\u0175\u0177\u017A\u017C\u017E-\u0180\u0183\u0185\u0188\u018C\u018D\u0192\u0195\u0199-\u019B\u019E\u01A1\u01A3\u01A5\u01A8\u01AA\u01AB\u01AD\u01B0\u01B4\u01B6\u01B9\u01BA\u01BD-\u01BF\u01C6\u01C9\u01CC\u01CE\u01D0\u01D2\u01D4\u01D6\u01D8\u01DA\u01DC\u01DD\u01DF\u01E1\u01E3\u01E5\u01E7\u01E9\u01EB\u01ED\u01EF\u01F0\u01F3\u01F5\u01F9\u01FB\u01FD\u01FF\u0201\u0203\u0205\u0207\u0209\u020B\u020D\u020F\u0211\u0213\u0215\u0217\u0219\u021B\u021D\u021F\u0221\u0223\u0225\u0227\u0229\u022B\u022D\u022F\u0231\u0233-\u0239\u023C\u023F\u0240\u0242\u0247\u0249\u024B\u024D\u024F-\u0293\u0295-\u02AF\u0371\u0373\u0377\u037B-\u037D\u0390\u03AC-\u03CE\u03D0\u03D1\u03D5-\u03D7\u03D9\u03DB\u03DD\u03DF\u03E1\u03E3\u03E5\u03E7\u03E9\u03EB\u03ED\u03EF-\u03F3\u03F5\u03F8\u03FB\u03FC\u0430-\u045F\u0461\u0463\u0465\u0467\u0469\u046B\u046D\u046F\u0471\u0473\u0475\u0477\u0479\u047B\u047D\u047F\u0481\u048B\u048D\u048F\u0491\u0493\u0495\u0497\u0499\u049B\u049D\u049F\u04A1\u04A3\u04A5\u04A7\u04A9\u04AB\u04AD\u04AF\u04B1\u04B3\u04B5\u04B7\u04B9\u04BB\u04BD\u04BF\u04C2\u04C4\u04C6\u04C8\u04CA\u04CC\u04CE\u04CF\u04D1\u04D3\u04D5\u04D7\u04D9\u04DB\u04DD\u04DF\u04E1\u04E3\u04E5\u04E7\u04E9\u04EB\u04ED\u04EF\u04F1\u04F3\u04F5\u04F7\u04F9\u04FB\u04FD\u04FF\u0501\u0503\u0505\u0507\u0509\u050B\u050D\u050F\u0511\u0513\u0515\u0517\u0519\u051B\u051D\u051F\u0521\u0523\u0525\u0527\u0529\u052B\u052D\u052F\u0560-\u0588\u10D0-\u10FA\u10FD-\u10FF\u13F8-\u13FD\u1C80-\u1C88\u1D00-\u1D2B\u1D6B-\u1D77\u1D79-\u1D9A\u1E01\u1E03\u1E05\u1E07\u1E09\u1E0B\u1E0D\u1E0F\u1E11\u1E13\u1E15\u1E17\u1E19\u1E1B\u1E1D\u1E1F\u1E21\u1E23\u1E25\u1E27\u1E29\u1E2B\u1E2D\u1E2F\u1E31\u1E33\u1E35\u1E37\u1E39\u1E3B\u1E3D\u1E3F\u1E41\u1E43\u1E45\u1E47\u1E49\u1E4B\u1E4D\u1E4F\u1E51\u1E53\u1E55\u1E57\u1E59\u1E5B\u1E5D\u1E5F\u1E61\u1E63\u1E65\u1E67\u1E69\u1E6B\u1E6D\u1E6F\u1E71\u1E73\u1E75\u1E77\u1E79\u1E7B\u1E7D\u1E7F\u1E81\u1E83\u1E85\u1E87\u1E89\u1E8B\u1E8D\u1E8F\u1E91\u1E93\u1E95-\u1E9D\u1E9F\u1EA1\u1EA3\u1EA5\u1EA7\u1EA9\u1EAB\u1EAD\u1EAF\u1EB1\u1EB3\u1EB5\u1EB7\u1EB9\u1EBB\u1EBD\u1EBF\u1EC1\u1EC3\u1EC5\u1EC7\u1EC9\u1ECB\u1ECD\u1ECF\u1ED1\u1ED3\u1ED5\u1ED7\u1ED9\u1EDB\u1EDD\u1EDF\u1EE1\u1EE3\u1EE5\u1EE7\u1EE9\u1EEB\u1EED\u1EEF\u1EF1\u1EF3\u1EF5\u1EF7\u1EF9\u1EFB\u1EFD\u1EFF-\u1F07\u1F10-\u1F15\u1F20-\u1F27\u1F30-\u1F37\u1F40-\u1F45\u1F50-\u1F57\u1F60-\u1F67\u1F70-\u1F7D\u1F80-\u1F87\u1F90-\u1F97\u1FA0-\u1FA7\u1FB0-\u1FB4\u1FB6\u1FB7\u1FBE\u1FC2-\u1FC4\u1FC6\u1FC7\u1FD0-\u1FD3\u1FD6\u1FD7\u1FE0-\u1FE7\u1FF2-\u1FF4\u1FF6\u1FF7\u210A\u210E\u210F\u2113\u212F\u2134\u2139\u213C\u213D\u2146-\u2149\u214E\u2184\u2C30-\u2C5F\u2C61\u2C65\u2C66\u2C68\u2C6A\u2C6C\u2C71\u2C73\u2C74\u2C76-\u2C7B\u2C81\u2C83\u2C85\u2C87\u2C89\u2C8B\u2C8D\u2C8F\u2C91\u2C93\u2C95\u2C97\u2C99\u2C9B\u2C9D\u2C9F\u2CA1\u2CA3\u2CA5\u2CA7\u2CA9\u2CAB\u2CAD\u2CAF\u2CB1\u2CB3\u2CB5\u2CB7\u2CB9\u2CBB\u2CBD\u2CBF\u2CC1\u2CC3\u2CC5\u2CC7\u2CC9\u2CCB\u2CCD\u2CCF\u2CD1\u2CD3\u2CD5\u2CD7\u2CD9\u2CDB\u2CDD\u2CDF\u2CE1\u2CE3\u2CE4\u2CEC\u2CEE\u2CF3\u2D00-\u2D25\u2D27\u2D2D\uA641\uA643\uA645\uA647\uA649\uA64B\uA64D\uA64F\uA651\uA653\uA655\uA657\uA659\uA65B\uA65D\uA65F\uA661\uA663\uA665\uA667\uA669\uA66B\uA66D\uA681\uA683\uA685\uA687\uA689\uA68B\uA68D\uA68F\uA691\uA693\uA695\uA697\uA699\uA69B\uA723\uA725\uA727\uA729\uA72B\uA72D\uA72F-\uA731\uA733\uA735\uA737\uA739\uA73B\uA73D\uA73F\uA741\uA743\uA745\uA747\uA749\uA74B\uA74D\uA74F\uA751\uA753\uA755\uA757\uA759\uA75B\uA75D\uA75F\uA761\uA763\uA765\uA767\uA769\uA76B\uA76D\uA76F\uA771-\uA778\uA77A\uA77C\uA77F\uA781\uA783\uA785\uA787\uA78C\uA78E\uA791\uA793-\uA795\uA797\uA799\uA79B\uA79D\uA79F\uA7A1\uA7A3\uA7A5\uA7A7\uA7A9\uA7AF\uA7B5\uA7B7\uA7B9\uA7BB\uA7BD\uA7BF\uA7C1\uA7C3\uA7C8\uA7CA\uA7D1\uA7D3\uA7D5\uA7D7\uA7D9\uA7F6\uA7FA\uAB30-\uAB5A\uAB60-\uAB68\uAB70-\uABBF\uFB00-\uFB06\uFB13-\uFB17\uFF41-\uFF5A]|\uD801[\uDC28-\uDC4F\uDCD8-\uDCFB\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC]|\uD803[\uDCC0-\uDCF2]|\uD806[\uDCC0-\uDCDF]|\uD81B[\uDE60-\uDE7F]|\uD835[\uDC1A-\uDC33\uDC4E-\uDC54\uDC56-\uDC67\uDC82-\uDC9B\uDCB6-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDCCF\uDCEA-\uDD03\uDD1E-\uDD37\uDD52-\uDD6B\uDD86-\uDD9F\uDDBA-\uDDD3\uDDEE-\uDE07\uDE22-\uDE3B\uDE56-\uDE6F\uDE8A-\uDEA5\uDEC2-\uDEDA\uDEDC-\uDEE1\uDEFC-\uDF14\uDF16-\uDF1B\uDF36-\uDF4E\uDF50-\uDF55\uDF70-\uDF88\uDF8A-\uDF8F\uDFAA-\uDFC2\uDFC4-\uDFC9\uDFCB]|\uD837[\uDF00-\uDF09\uDF0B-\uDF1E\uDF25-\uDF2A]|\uD83A[\uDD22-\uDD43])/,te=/[\uD800-\uDFFF]/,re=/[!"'-),-/:;?[-\]_{}\u00A1\u00A7\u00AB\u00B6\u00B7\u00BB\u00BF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u201F\u2022-\u2027\u2032-\u203A\u203C-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDEAD\uDF55-\uDF59\uDF86-\uDF89]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5A\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDEB9\uDF3C-\uDF3E]|\uD806[\uDC3B\uDD44-\uDD46\uDDE2\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2\uDF00-\uDF09]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8\uDF43-\uDF4F\uDFFF]|\uD809[\uDC70-\uDC74]|\uD80B[\uDFF1\uDFF2]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A\uDFE2]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/,ne=/[\dA-Za-z\u00AA\u00B2\u00B3\u00B5\u00B9\u00BA\u00BC-\u00BE\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u052F\u0531-\u0556\u0559\u0560-\u0588\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05EF-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u07FD\u0800-\u082D\u0840-\u085B\u0860-\u086A\u0870-\u0887\u0889-\u088E\u0898-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09F4-\u09F9\u09FC\u09FE\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71-\u0B77\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BF2\u0C00-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3C-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C5D\u0C60-\u0C63\u0C66-\u0C6F\u0C78-\u0C7E\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDD\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1-\u0CF3\u0D00-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D63\u0D66-\u0D78\u0D7A-\u0D7F\u0D81-\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECE\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F33\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u137C\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1715\u171F-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u17F0-\u17F9\u180B-\u180D\u180F-\u1819\u1820-\u1878\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ACE\u1B00-\u1B4C\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CD0-\u1CD2\u1CD4-\u1CFA\u1D00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2070\u2071\u2074-\u2079\u207F-\u2089\u2090-\u209C\u20D0-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2150-\u2189\u2460-\u249B\u24EA-\u24FF\u2776-\u2793\u2C00-\u2CE4\u2CEB-\u2CF3\u2CFD\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u3192-\u3195\u31A0-\u31BF\u31F0-\u31FF\u3220-\u3229\u3248-\u324F\u3251-\u325F\u3280-\u3289\u32B1-\u32BF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA672\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA827\uA82C\uA830-\uA835\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD07-\uDD33\uDD40-\uDD78\uDD8A\uDD8B\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0-\uDEFB\uDF00-\uDF23\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC58-\uDC76\uDC79-\uDC9E\uDCA7-\uDCAF\uDCE0-\uDCF2\uDCF4\uDCF5\uDCFB-\uDD1B\uDD20-\uDD39\uDD80-\uDDB7\uDDBC-\uDDCF\uDDD2-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE38-\uDE3A\uDE3F-\uDE48\uDE60-\uDE7E\uDE80-\uDE9F\uDEC0-\uDEC7\uDEC9-\uDEE6\uDEEB-\uDEEF\uDF00-\uDF35\uDF40-\uDF55\uDF58-\uDF72\uDF78-\uDF91\uDFA9-\uDFAF]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDCFA-\uDD27\uDD30-\uDD39\uDE60-\uDE7E\uDE80-\uDEA9\uDEAB\uDEAC\uDEB0\uDEB1\uDEFD-\uDF27\uDF30-\uDF54\uDF70-\uDF85\uDFB0-\uDFCB\uDFE0-\uDFF6]|\uD804[\uDC00-\uDC46\uDC52-\uDC75\uDC7F-\uDCBA\uDCC2\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD44-\uDD47\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDC9-\uDDCC\uDDCE-\uDDDA\uDDDC\uDDE1-\uDDF4\uDE00-\uDE11\uDE13-\uDE37\uDE3E-\uDE41\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3B-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC5E-\uDC61\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB8\uDEC0-\uDEC9\uDF00-\uDF1A\uDF1D-\uDF2B\uDF30-\uDF3B\uDF40-\uDF46]|\uD806[\uDC00-\uDC3A\uDCA0-\uDCF2\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD35\uDD37\uDD38\uDD3B-\uDD43\uDD50-\uDD59\uDDA0-\uDDA7\uDDAA-\uDDD7\uDDDA-\uDDE1\uDDE3\uDDE4\uDE00-\uDE3E\uDE47\uDE50-\uDE99\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC6C\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD8E\uDD90\uDD91\uDD93-\uDD98\uDDA0-\uDDA9\uDEE0-\uDEF6\uDF00-\uDF10\uDF12-\uDF3A\uDF3E-\uDF42\uDF50-\uDF59\uDFB0\uDFC0-\uDFD4]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC40-\uDC55]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDE70-\uDEBE\uDEC0-\uDEC9\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF5B-\uDF61\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE96\uDF00-\uDF4A\uDF4F-\uDF87\uDF8F-\uDF9F\uDFE0\uDFE1\uDFE3\uDFE4\uDFF0\uDFF1]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD833[\uDF00-\uDF2D\uDF30-\uDF46]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44\uDEC0-\uDED3\uDEE0-\uDEF3\uDF60-\uDF78]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A\uDC30-\uDC6D\uDC8F\uDD00-\uDD2C\uDD30-\uDD3D\uDD40-\uDD49\uDD4E\uDE90-\uDEAE\uDEC0-\uDEF9]|\uD839[\uDCD0-\uDCF9\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDCC7-\uDCD6\uDD00-\uDD4B\uDD50-\uDD59]|\uD83B[\uDC71-\uDCAB\uDCAD-\uDCAF\uDCB1-\uDCB4\uDD01-\uDD2D\uDD2F-\uDD3D\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD83C[\uDD00-\uDD0C]|\uD83E[\uDFF0-\uDFF9]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF]|\uDB40[\uDD00-\uDDEF]/,ie=/[\t-\r \u0085\u00A0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,oe=H((function(e,t,r){if("children"in e&&e.children.length>0&&t>0){const n=r.children[t-1],i=e.children[0],o=e.children[1];if(n&&"SentenceNode"===n.type&&("SymbolNode"===i.type||"PunctuationNode"===i.type)&&Y.test(W(i)))return e.children.shift(),n.children.push(i),i.position&&n.position&&(n.position.end=i.position.end),o&&o.position&&e.position&&(e.position.start=o.position.start),t-1}})),ue=H((function(e,t,r){if("SentenceNode"!==e.type)return;const n=e.children;let i=0;for(;++i<n.length-1;){const o=n[i];if("WhiteSpaceNode"!==o.type||W(o).split(/\r\n|\r|\n/).length<3)continue;e.children=n.slice(0,i);const u={type:"SentenceNode",children:n.slice(i+1)},a=n[i-1],s=n[i+1];if(r.children.splice(t+1,0,o,u),e.position&&a.position&&s.position){const t=e.position.end;e.position.end=a.position.end,u.position={start:s.position.start,end:t}}return t+1}})),ae=H((function(e,t,r){if("children"in e){const n=e.children[e.children.length-1];if(n&&"WhiteSpaceNode"===n.type){e.children.pop(),r.children.splice(t+1,0,n);const i=e.children[e.children.length-1];return i&&i.position&&e.position&&(e.position.end=i.position.end),t}}}));function se(e){return function(t){const r=t&&t.children;let n=-1;if(!r)throw new Error("Missing children in `parent` for `visit`");for(;++n in r;)e(r[n],n,t)}}const le=se((function(e,t,r){if("children"in e&&e.children){const n=e.children[0];if(n&&"WhiteSpaceNode"===n.type){e.children.shift(),r.children.splice(t,0,n);const i=e.children[0];i&&i.position&&e.position&&(e.position.start=i.position.start)}}})),ce=H((function(e,t,r){if(t>0&&("SymbolNode"===e.type||"PunctuationNode"===e.type)&&"-"===W(e)){const n=r.children,i=n[t-1],o=n[t+1];if((!o||"WordNode"!==o.type)&&i&&"WordNode"===i.type)return n.splice(t,1),i.children.push(e),i.position&&e.position&&(i.position.end=e.position.end),t}})),de=H((function(e,t,r){const n=r.children[t-1];if(n&&"SentenceNode"===n.type&&"SentenceNode"===e.type){const i=e.children[0];if(i&&"WordNode"===i.type&&X.test(W(i)))return n.children.push(...e.children),r.children.splice(t,1),n.position&&e.position&&(n.position.end=e.position.end),t}})),pe=H((function(e,t,r){if("SentenceNode"===e.type&&t>0){const n=r.children[t-1],i=e.children;if(i.length>0&&"SentenceNode"===n.type){let o=-1;for(;i[++o];){const u=i[o];if("WordNode"===u.type){if(!ee.test(W(u)))return;return n.children.push(...i),r.children.splice(t,1),n.position&&e.position&&(n.position.end=e.position.end),t}if("SymbolNode"===u.type||"PunctuationNode"===u.type)return}}}})),fe=H((function(e,t,r){if("SymbolNode"!==e.type&&"PunctuationNode"!==e.type||"&"!==W(e))return;const n=r.children,i=n[t+1];return t>0&&"WordNode"===n[t-1].type||!i||"WordNode"!==i.type?void 0:(n.splice(t,1),i.children.unshift(e),i.position&&e.position&&(i.position.start=e.position.start),t-1)})),ye=H((function(e,t,r){if(t>0&&"PunctuationNode"===e.type&&"."===W(e)){const n=r.children[t-1];if("WordNode"===n.type&&n.children&&1!==n.children.length&&n.children.length%2!=0){let i=n.children.length,o=!0;for(;n.children[--i];){const e=W(n.children[i]);if(i%2==0){if(e.length>1)return;Z.test(e)||(o=!1)}else if("."!==e){if(i<n.children.length-2)break;return}}if(!o)return r.children.splice(t,1),n.children.push(e),n.position&&e.position&&(n.position.end=e.position.end),t}}})),he=H((function(e,t,r){if(t>0&&("SymbolNode"===e.type||"PunctuationNode"===e.type)){const e=r.children,n=e[t-1];if(n&&"WordNode"===n.type){let r=t-1;const i=[];let o=[];for(;e[++r];){const t=e[r];if("WordNode"===t.type)i.push(...o,...t.children),o=[];else{if("SymbolNode"!==t.type&&"PunctuationNode"!==t.type||!J.test(W(t)))break;o.push(t)}}if(i.length>0){o.length>0&&(r-=o.length),e.splice(t,r-t),n.children.push(...i);const u=i[i.length-1];return n.position&&u.position&&(n.position.end=u.position.end),t}}}})),ge=H((function(e,t,r){const n=r.children,i=n[t-1];if(i&&"WordNode"===i.type&&("SymbolNode"===e.type||"PunctuationNode"===e.type)&&"/"===W(e)){const r=W(i);let o=e;const u=[e];let a=1,s="";const l=n[t+1];if(l&&"WordNode"===l.type&&(s=W(l),o=l,u.push(...l.children),a++),r.length<3&&(!s||s.length<3))return i.children.push(...u),n.splice(t,a),i.position&&o.position&&(i.position.end=o.position.end),t}})),me=H((function(e,t,r){if("children"in e){let n=-1;for(;e.children[++n];)if("WordNode"===e.children[n].type)return;const i=r.children[t-1];if(i&&"children"in i)return i.children.push(...e.children),r.children.splice(t,1),i.position&&e.position&&(i.position.end=e.position.end),t;const o=r.children[t+1];o&&"children"in o&&(o.children.unshift(...e.children),o.position&&e.position&&(o.position.start=e.position.start),r.children.splice(t,1))}})),be=new RegExp("^([0-9]{1,3}|[a-z]|al|ca|cap|cca|cent|cf|cit|con|cp|cwt|ead|etc|ff|fl|ibid|id|nem|op|pro|seq|sic|stat|tem|viz)$"),ve=H((function(e,t,r){if("children"in e&&e.children.length>1){const n=e.children[e.children.length-1];if(n&&("PunctuationNode"===n.type||"SymbolNode"===n.type)&&"."===W(n)){const i=e.children[e.children.length-2];if(i&&"WordNode"===i.type&&be.test(W(i).toLowerCase())){i.children.push(n),e.children.pop(),n.position&&i.position&&(i.position.end=n.position.end);const o=r.children[t+1];if(o&&"SentenceNode"===o.type)return e.children.push(...o.children),r.children.splice(t+1,1),o.position&&e.position&&(e.position.end=o.position.end),t-1}}}})),De=se((function(e,t,r){if("children"in e){let t=e.children.length,r=!1;for(;e.children[--t];){const n=e.children[t];if("SymbolNode"!==n.type&&"PunctuationNode"!==n.type){"WordNode"===n.type&&(r=!0);continue}if(!Q.test(W(n)))continue;if(!r){r=!0;continue}if("."!==W(n))continue;const i=e.children[t-1],o=e.children[t+1];if(i&&"WordNode"===i.type){const r=e.children[t+2];if(o&&r&&"WhiteSpaceNode"===o.type&&"."===W(r))continue;e.children.splice(t,1),i.children.push(n),n.position&&i.position&&(i.position.end=n.position.end),t--}else o&&"WordNode"===o.type&&(e.children.splice(t,1),o.children.unshift(n),n.position&&o.position&&(o.position.start=n.position.start))}}})),we=H((function(e,t,r){if("children"in e&&0===e.children.length)return r.children.splice(t,1),t})),ke=se((function(e,t,r){const n=r.children;!(e.position&&t<1)||r.position&&r.position.start||(Fe(r),r.position.start=e.position.start),!e.position||t!==n.length-1||r.position&&r.position.end||(Fe(r),r.position.end=e.position.end)}));function Fe(e){e.position||(e.position={})}class Ee{constructor(e,t){const r=t||e;this.doc=r?String(r):void 0,this.tokenizeRootPlugins=[...this.tokenizeRootPlugins],this.tokenizeParagraphPlugins=[...this.tokenizeParagraphPlugins],this.tokenizeSentencePlugins=[...this.tokenizeSentencePlugins]}parse(e){return this.tokenizeRoot(e||this.doc)}tokenizeRoot(e){const t={type:"RootNode",children:Ae(this.tokenizeParagraph(e),"WhiteSpaceNode",G)};let r=-1;for(;this.tokenizeRootPlugins[++r];)this.tokenizeRootPlugins[r](t);return t}tokenizeParagraph(e){const t={type:"ParagraphNode",children:Ae(this.tokenizeSentence(e),"PunctuationNode",Q)};let r=-1;for(;this.tokenizeParagraphPlugins[++r];)this.tokenizeParagraphPlugins[r](t);return t}tokenizeSentence(e){const t={type:"SentenceNode",children:this.tokenize(e)};let r=-1;for(;this.tokenizeSentencePlugins[++r];)this.tokenizeSentencePlugins[r](t);return t}tokenize(e){const t=[];if(!e)return t;const r={line:1,column:1,offset:0};let n,i,o=0,u=0,a={...r};for(;u<e.length;){const l=e.charAt(u),c=ie.test(l)?"WhiteSpaceNode":re.test(l)?"PunctuationNode":ne.test(l)?"WordNode":"SymbolNode";o<u&&n&&c&&(n!==c||"WordNode"!==n&&"WhiteSpaceNode"!==n&&l!==i&&!te.test(l))&&(t.push(s(n,e.slice(o,u))),o=u,a={...r}),"\r"===l||"\n"===l&&"\r"!==i?(r.line++,r.column=1):"\n"!==l&&r.column++,r.offset++,n=c,i=l,u++}return n&&o<u&&t.push(s(n,e.slice(o,u))),t;function s(e,t){return"WordNode"===e?{type:"WordNode",children:[{type:"TextNode",value:t,position:{start:a,end:{...r}}}],position:{start:a,end:{...r}}}:{type:e,value:t,position:{start:a,end:{...r}}}}}}function Ae(e,t,r){const n=[];let i=-1,o=0;for(;++i<e.children.length;){const u=e.children[i];if(i===e.children.length-1||u.type===t&&r.test(W(u))){const t={type:e.type,children:e.children.slice(o,i+1)},r=e.children[o],a=u;r.position&&a.position&&(t.position={start:r.position.start,end:a.position.end}),n.push(t),o=i+1}}return n}Ee.prototype.tokenizeSentencePlugins=[fe,ce,he,ge,ye,ke],Ee.prototype.tokenizeParagraphPlugins=[me,oe,pe,de,ve,V,De,le,ae,ue,we,ke],Ee.prototype.tokenizeRootPlugins=[le,ae,we,ke];const Ce=/^(t(?:hurs|bsp|sp)|s(?:e(?:pt|c)|q)|(?:tue|bbl|yd)s|thu|sep|tue|bbl|nov|aug|ju[ln]|(?:ap|h)r|(?:ja|su)n|m(?:ar|on|in)|(?:sa|oc|[kpq])t|g(?:ro|al)|f(?:eb|ri|[lt])|d(?:ec|oz)|wed|l(?:bs|td)|inc?|mi|gr|yd|lb|oz|cu)$/,Se=/^((?:Northant|Derby|S(?:hrop|taff)|W(?:ark|orc)|L(?:in|ei|an)c|C(?:amb|he)|Trea|York|B(?:uck|e(?:rk|d))|(?:Not|Wil)t|H(?:er|[au]n)t|Glo|Pre)s|Northumb|N(?:or(?:thd|f)|e(?:br|v)|atl)|M(?:(?:essr|as|se)s|i(?:ddx|nn|ch|ss)|ont|lle|s(?:gr|s)|ddx|(?:me|r)s?|a[jn]|ex|gr|o|d|e|s|[tx])?|Heref|P(?:enna|[ahot])|D(?:e(?:rbs|[lv])|r)|Westm|(?:Cali|Pro)f|S(?:a(?:lop|sk)|uff|om|e[cn]|[qrxy])|(?:Bldg|Kan)s?|Here|P(?:enn?|k)|St(?:af)?|W(?:isc|arw|yo)|C(?:umb|olo)|B(?:lvd|rig)|(?:Ok|F)la|(?:Pk|[FH])wy|A(?:r(?:iz|k)|lt?a|tty|m[bd])|(?:Cap|S(?:up|g)|On|Ru|U)t|(?:Ter|Cd|D[ou]|Sn|J)r|(?:Oxo|Co[nr]|Ten|Ke|Ge|Ho)n|Wash|N(?:eb|at)|W(?:is|ar|o)|C(?:al|ol?)|Qu[e\u00E9]|I(?:ll|a)|Tex|G(?:ov|a)|Ind|R(?:te|e[pv]|d)|Ida|(?:Yu|Da)k|(?:Or|Av)e|Ssx|Ok|La|Br|Rt|Id|V[at]|Ky|Lt|F[rt])$/,je=/^(ol?)$/,xe=/^(t(?:were|(?:wa|i)s)|cause|e[mr]|im|\d\ds?)$/,Be=/^['’]$/;class Oe extends Ee{}Oe.prototype.tokenizeSentencePlugins=[se((function(e,t,r){if("PunctuationNode"===e.type||"SymbolNode"===e.type){const n=r.children,i=n.length,o=W(e);if("/"===o){const r=n[t-1];r&&"WordNode"===r.type&&"w"===W(r).toLowerCase()&&(n.splice(t,1),r.children.push(e),r.position&&e.position&&(r.position.end=e.position.end))}else if(Be.test(o)){const r=n[t-1];if(t>2&&t<i-1&&"WordNode"===r.type&&"WhiteSpaceNode"===n[t-2].type&&"WhiteSpaceNode"===n[t+1].type&&je.test(W(r).toLowerCase()))return n.splice(t,1),r.children.push(e),void(r.position&&e.position&&(r.position.end=e.position.end));if(t!==i-1&&"WordNode"===n[t+1].type&&(0===t||"WordNode"!==n[t-1].type)){const r=n[t+1],i=W(r).toLowerCase(),o=n[t+2];"WordNode"===r.type&&xe.test(i)?(n.splice(t,1),r.children.unshift(e),r.position&&e.position&&(r.position.start=e.position.start)):"WordNode"===r.type&&"n"===i&&o&&"PunctuationNode"===o.type&&Be.test(W(o))&&(n.splice(t,1),n.splice(t+1,1),r.children.unshift(e),r.children.push(o),r.position&&(e.position&&(r.position.start=e.position.start),o.position&&(r.position.end=o.position.end)))}}}})),...Ee.prototype.tokenizeSentencePlugins],Oe.prototype.tokenizeParagraphPlugins=[H((function(e,t,r){if("children"in e&&e.children){const n=e.children[e.children.length-1],i=e.children[e.children.length-2];if(n&&"PunctuationNode"===n.type&&"."===W(n)&&i&&"WordNode"===i.type){const o=W(i);if(Ce.test(o.toLowerCase())||Se.test(o)){i.children.push(n),e.children.pop(),n.position&&i.position&&(i.position.end=n.position.end);const o=r.children[t+1];if(o&&"SentenceNode"===o.type)return e.children.push(...o.children),r.children.splice(t+1,1),o.position&&e.position&&(e.position.end=o.position.end),t-1}}}})),...Ee.prototype.tokenizeParagraphPlugins];var _e=function(e){if(""===e)return!1;e=function(e){return"."===(e=e.replace(/\b[0-9]+\b/g,""))?"":e}(e);var t=E(e),r=function(e){if(""===(e=(0,i.flow)([l,b,y,c,v,h])(e)))return 0;var t=(new Oe).tokenizeParagraph(e).children;return(0,i.filter)(t,{type:"SentenceNode"}).length}(e),n=t.length;if(0===r||0===n)return!1;var o=function(e){var t=(0,i.map)(e,(function(e){return function(e){return 3>=(e=e.toLowerCase()).length?1:null===(e=e.replace(/(?:[^laeiouy]es|ed|lle|[^laeiouy]e)$/,"").replace(/^y/,"").match(/[aeiouy]{1,2}/g))?0:e.length}(e)}));return(0,i.sum)(t)}(t);return function(e,t,r){return 206.835-t/e*1.015-r/t*84.6}(r,n,o).toFixed(2)},Pe=wp.autop,Re=function(e){return e.replace(/<style[^>]*>([\S\s]*?)<\/style>/gim,"")},Te=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,t)},ze=function(e){return e.replace(/[‘’‛`]/g,"'").replace(/[“”〝〞〟‟„]/g,'"')},Ne=function(e){return e.replace(/&nbsp;/g," ").replace(/\s+/g," ")};function qe(e){return(0,i.isUndefined)(e)||!e?"":(0,i.flow)([Re,Te,l,b,v,c,ze])(e)}function Ie(e){return(0,i.isUndefined)(e)?"":(0,i.flow)([l,c])(e)}var Me=function(e,t){var r=function(e,t){for(var r,n=/<p(?:[^>]+)?>(.*?)<\/p>/gi,o=[];null!==(r=n.exec(e));)o.push(r);return(0,i.map)(o,(function(e){return t?qe(e[1]):e[1]}))}(e=(0,i.flow)([y,b,Pe.autop])(e),t=t||!1);return 0<r.length?r:[t?qe(e):e]},Le=function(e){var t=[];return Me(e).map((function(e){return t.push({wordCount:P(e),text:e})})),(0,i.filter)(t,(function(e){return 0<e.wordCount}))};function $e(e){return $e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$e(e)}function Ue(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==$e(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==$e(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===$e(o)?o:String(o)),n)}var i,o}var We=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.setPaper(t),this.researches={combinations:R,fleschReading:_e,getLinkStats:$,getParagraphs:Le,getWords:E,pluralize:new O,removePunctuation:F,slugify:s,stripTags:l,wordCount:P}}var t,r,n;return t=e,(r=[{key:"setPaper",value:function(e){this.paper=e}},{key:"getResearches",value:function(){return this.researches}},{key:"getResearch",value:function(e){return!(0,i.isUndefined)(e)&&!(0,i.isEmpty)(e)&&!!this.hasResearch(e)&&this.getResearches()[e]}},{key:"hasResearch",value:function(e){return(0,i.has)(this.getResearches(),e)}}])&&Ue(t.prototype,r),n&&Ue(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Ke=wp.i18n;function He(e){return He="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},He(e)}function Ve(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==He(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==He(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===He(o)?o:String(o)),n)}var i,o}function Ye(e,t){return Ye=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ye(e,t)}function Ge(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=Qe(e);if(t){var i=Qe(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===He(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function Qe(e){return Qe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Qe(e)}var Je=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ye(e,t)}(u,e);var t,r,n,o=Ge(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),o.apply(this,arguments)}return t=u,r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Add a few images and/or videos to make your content appealing.","rank-math")).setTooltip((0,Ke.__)("Content with images and/or video feels more inviting to users. It also helps supplement your textual content.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult();return e.hasText()?(r.setScore(this.calculateScore(e)).setText(this.translateScore(r)),r):(e.hasThumbnail()&&r.setScore(1).setText(this.translateScore(r)),r)}},{key:"isApplicable",value:function(e){return e.hasText()||e.hasThumbnail()}},{key:"calculateScore",value:function(e){var t=0;return t+=this.calculateImagesScore(this.getImages(e)),t+=this.calculateVideosScore(this.getVideos(e.getText())),Math.min(this.getScore(),t)}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_contentHasAssets_score",6)}},{key:"calculateImagesScore",value:function(e){var t={0:0,1:1,2:2,3:4};return(0,i.has)(t,e)?t[e]:6}},{key:"calculateVideosScore",value:function(e){var t={0:0,1:1};return(0,i.has)(t,e)?t[e]:2}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("Your content contains images and/or video(s).","rank-math"):(0,Ke.__)("You are not using rich media like images or videos.","rank-math")}},{key:"getImages",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;t=(0,i.isNull)(t)?e.getText():t;var r=[].concat(this.match(t,"<img(?:[^>]+)?>"),this.match(t,"\\[gallery( [^\\]]+?)?\\]"));return e.hasThumbnail()&&r.push(e.getThumbnail()),r.length}},{key:"hasVideoUrl",value:function(e){return this.match(e,/(http:\/\/|https:\/\/|)(player.|www.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com))\/(video\/|embed\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/)}},{key:"getVideosFromIframe",value:function(e){var t=this;return{count:this.match(e,"<iframe(?:[^>]+)?>").filter((function(r){return!!t.hasVideoUrl(r)&&(e=e.replace(r,""),!0)})).length,text:e}}},{key:"getVideosFromVideoTag",value:function(e){var t=this;return{count:this.match(e,"<video(?:[^>]+)?>").filter((function(r){return!!t.hasVideoUrl(r)&&(e=e.replace(r,""),!0)})).length,text:e}}},{key:"getVideosFromShortcodes",value:function(e){var t=this;return{count:this.match(e,"\\[video( [^\\]]+?)?\\]").filter((function(r){return!!t.hasVideoUrl(r)&&(e=e.replace(r,""),!0)})).length,text:e}}},{key:"getVideosByURL",value:function(e){return{count:this.hasVideoUrl(e).length,text:e}}},{key:"getVideos",value:function(e){var t=0,r=this.getVideosFromIframe(e);t+=parseInt(r.count),e=r.text;var n=this.getVideosFromVideoTag(e);t+=parseInt(n.count),e=n.text;var i=this.getVideosFromShortcodes(e);t+=parseInt(i.count),e=i.text;var o=this.getVideosByURL(e);return t+=parseInt(o.count),e=o.text,t}},{key:"match",value:function(e,t){var r=new RegExp(t,"ig"),n=e.match(r);return null===n?[]:n}}],r&&Ve(t.prototype,r),n&&Ve(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),Ze=Je;function Xe(e){return Xe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xe(e)}function et(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==Xe(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Xe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Xe(o)?o:String(o)),n)}var i,o}function tt(e,t){return tt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},tt(e,t)}function rt(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=nt(e);if(t){var i=nt(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===Xe(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function nt(e){return nt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},nt(e)}var it=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tt(e,t)}(o,e);var t,r,n,i=rt(o);function o(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),i.apply(this,arguments)}return t=o,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Add short and concise paragraphs for better readability and UX.","rank-math")).setTooltip((0,Ke.__)("Short paragraphs are easier to read and more pleasing to the eye. Long paragraphs scare the visitor, and they might result to SERPs looking for better readable content.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=t.getResearch("getParagraphs")(e.getText()).some((function(e){return 120<e.wordCount}));return r.setScore(this.calculateScore(n)).setText(this.translateScore(r)),r}},{key:"isApplicable",value:function(e){return e.hasText()}},{key:"calculateScore",value:function(e){return e?null:this.getScore()}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_contentHasShortParagraphs_score",3)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("You are using short paragraphs.","rank-math"):(0,Ke.__)("At least one paragraph is long. Consider using short paragraphs.","rank-math")}}])&&et(t.prototype,r),n&&et(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(n),ot=it;function ut(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=rankMath.links[e]||"";if(!r)return"#";if(!t)return r;var n={utm_source:"Plugin",utm_medium:encodeURIComponent(t),utm_campaign:"WP"};return r+"?"+Object.keys(n).map((function(e){return"".concat(e,"=").concat(n[e])})).join("&")}function at(e){return at="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},at(e)}function st(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==at(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==at(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===at(o)?o:String(o)),n)}var i,o}function lt(e,t){return lt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},lt(e,t)}function ct(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=dt(e);if(t){var i=dt(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===at(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function dt(e){return dt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},dt(e)}var pt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&lt(e,t)}(u,e);var t,r,n,o=ct(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),o.apply(this,arguments)}return t=u,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Use Table of Content to break-down your text.","rank-math")).setTooltip((0,Ke.__)("Table of Contents help break down content into smaller, digestible chunks. It makes reading easier which in turn results in better rankings.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=rankMath.assessor.hasTOCPlugin||(0,i.includes)(e.getTextLower(),"wp-block-rank-math-toc-block");return r.setScore(this.calculateScore(n)).setText(this.translateScore(r)),r}},{key:"isApplicable",value:function(e){return e.hasText()}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_contentHasTOC_score",2)}},{key:"translateScore",value:function(e){var t=ut("toc","Content Analysis");return e.hasScore()?(0,Ke.sprintf)((0,Ke.__)("You seem to be using a %1$s to break-down your text.","rank-math"),'<a href="'+t+'" target="_blank">Table of Contents plugin</a>'):(0,Ke.sprintf)((0,Ke.__)("You don't seem to be using a %1$s.","rank-math"),'<a href="'+t+'" target="_blank">Table of Contents plugin</a>')}}])&&st(t.prototype,r),n&&st(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),ft=pt,yt=function(e){return e.replace(/[\\^$*+?.()|[\]{}]/g,"\\$&")};function ht(e){return ht="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ht(e)}function gt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==ht(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==ht(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===ht(o)?o:String(o)),n)}var i,o}function mt(e,t){return mt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},mt(e,t)}function bt(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=vt(e);if(t){var i=vt(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===ht(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function vt(e){return vt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},vt(e)}var Dt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mt(e,t)}(u,e);var t,r,n,o=bt(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),o.apply(this,arguments)}return t=u,r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Keyword Density is 0. Aim for around 1% Keyword Density.","rank-math")).setTooltip((0,Ke.__)("There is no ideal keyword density percentage, but it should not be too high. The most important thing is to keep the copy natural.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=t.getResearch("wordCount")(e.getTextLower()),o=e.get("keywords");if(!1===n||0===n||(0,i.isEmpty)(o))return r;var u=new RegExp((0,i.map)(o,yt).join("|"),"gi"),a=(Ie(e.getText()).match(u)||[]).length,s=(0,z.applyFilters)("rankMath_analysis_keywordDensity",(a/n*100).toFixed(2),a),l=this.calculateScore(s);return r.setScore(l.score).setText((0,Ke.sprintf)(this.translateScore(l.type),s,a)),r}},{key:"isApplicable",value:function(e){return e.hasText()}},{key:"translateScore",value:function(e){return"low"===e?(0,Ke.__)("Keyword Density is %1$s which is low, the Focus Keyword and combination appears %2$s times.","rank-math"):"high"===e?(0,Ke.__)("Keyword Density is %1$s which is high, the Focus Keyword and combination appears %2$s times.","rank-math"):(0,Ke.__)("Keyword Density is %1$s, the Focus Keyword and combination appears %2$s times.","rank-math")}},{key:"calculateScore",value:function(e){var t=this.getBoundaries();return.5>e?{type:"low",score:t.fail}:2.5<e?{type:"high",score:t.fail}:(0,i.inRange)(e,.5,.75)?{type:"fair",score:t.fair}:(0,i.inRange)(e,.76,1)?{type:"good",score:t.good}:{type:"best",score:t.best}}},{key:"getScore",value:function(){return this.getBoundaries().best}},{key:"getBoundaries",value:function(){return(0,z.applyFilters)("rankMath_analysis_keywordDensity_score",{fail:0,fair:2,good:3,best:6})}}],r&&gt(t.prototype,r),n&&gt(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),wt=Dt;function kt(e){return kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},kt(e)}function Ft(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==kt(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==kt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===kt(o)?o:String(o)),n)}var i,o}function Et(e,t){return Et=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Et(e,t)}function At(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=Ct(e);if(t){var i=Ct(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===kt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function Ct(e){return Ct=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ct(e)}var St=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Et(e,t)}(u,e);var t,r,n,o=At(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),o.apply(this,arguments)}return t=u,r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Use Focus Keyword at the beginning of your content.","rank-math")).setTooltip((0,Ke.__)("The first 10% of the content should contain the Focus Keyword preferably at the beginning.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=t.getResearch("getWords"),o=n(e.getTextLower());if(!1===o)return r;400<o.length&&(o=o.slice(0,Math.floor(.1*o.length))),o=o.join(" ");var u=n(e.getLower("keyword")).join(" "),a=(0,i.includes)(o,u);return r.setScore(this.calculateScore(a)).setText(this.translateScore(r)),r}},{key:"isApplicable",value:function(e){return e.hasKeyword()&&e.hasText()}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_keywordIn10Percent_score",3)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("Focus Keyword appears in the first 10% of the content.","rank-math"):(0,Ke.__)("Focus Keyword doesn't appear at the beginning of your content.","rank-math")}}],r&&Ft(t.prototype,r),n&&Ft(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),jt=St;function xt(e){return xt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xt(e)}function Bt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==xt(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==xt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===xt(o)?o:String(o)),n)}var i,o}function Ot(e,t){return Ot=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ot(e,t)}function _t(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=Pt(e);if(t){var i=Pt(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===xt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function Pt(e){return Pt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Pt(e)}var Rt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ot(e,t)}(u,e);var t,r,n,o=_t(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),o.apply(this,arguments)}return t=u,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Use Focus Keyword in the content.","rank-math")).setTooltip((0,Ke.__)("It is recommended to make the focus keyword appear in the post content too.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=Ie(e.getTextLower()),o=(0,i.includes)(n,e.getLower("keyword"));return r.setScore(this.calculateScore(o)).setText(this.translateScore(r)),r}},{key:"isApplicable",value:function(e){return e.hasKeyword()&&e.hasText()}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_keywordInContent_score",3)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("Focus Keyword found in the content.","rank-math"):(0,Ke.__)("Focus Keyword doesn't appear in the content.","rank-math")}}])&&Bt(t.prototype,r),n&&Bt(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),Tt=Rt;function zt(e){return zt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zt(e)}function Nt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==zt(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==zt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===zt(o)?o:String(o)),n)}var i,o}function qt(e,t){return qt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},qt(e,t)}function It(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=Mt(e);if(t){var i=Mt(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===zt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function Mt(e){return Mt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Mt(e)}var Lt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&qt(e,t)}(u,e);var t,r,n,o=It(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),o.apply(this,arguments)}return t=u,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Add an image with your Focus Keyword as alt text.","rank-math")).setTooltip((0,Ke.__)("It is recommended to add the focus keyword in the alt attribute of one or more images.","rank-math"))}},{key:"getResult",value:function(e){var t=this.newResult(),r=e.getLower("thumbnailAlt"),n=e.getLower("keyword");if(n===r||(0,i.includes)(r,n))return t.setScore(this.calculateScore(!0)).setText(this.translateScore(t)),t;n=ze((0,i.uniq)(n.split(" ")).join(" "));for(var o,u=new RegExp("<img.*?alt=([\"'])(.*?)\\1\\s","g"),a=[],s=e.getTextLower();!(0,i.isNull)(o=u.exec(s));)a.push(o[2]);var l=n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&").replace(/ /g,".*");if(null!==a)for(var c=0;c<a.length;c++)if(null!==a[c].match(new RegExp(l,"gi")))return t.setScore(this.calculateScore(!0)).setText(this.translateScore(t)),t;return u=new RegExp("\\[gallery( [^\\]]+?)?\\]","ig"),null!==s.match(u)&&t.setScore(this.calculateScore(!0)).setText((0,Ke.__)("We detected a gallery in your content & assuming that you added Focus Keyword in alt in at least one of the gallery images.","rank-math")),t}},{key:"isApplicable",value:function(e){return e.hasKeyword()&&(e.hasText()||e.hasThumbnailAltText())}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_keywordInImageAlt_score",2)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("Focus Keyword found in image alt attribute(s).","rank-math"):(0,Ke.__)("Focus Keyword not found in image alt attribute(s).","rank-math")}}])&&Nt(t.prototype,r),n&&Nt(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),$t=Lt;function Ut(e){return Ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ut(e)}function Wt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==Ut(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Ut(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Ut(o)?o:String(o)),n)}var i,o}function Kt(e,t){return Kt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Kt(e,t)}function Ht(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=Vt(e);if(t){var i=Vt(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===Ut(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function Vt(e){return Vt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Vt(e)}var Yt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Kt(e,t)}(u,e);var t,r,n,o=Ht(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),o.apply(this,arguments)}return t=u,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Add Focus Keyword to your SEO Meta Description.","rank-math")).setTooltip((0,Ke.__)("Make sure the focus keyword appears in the SEO description too.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=(0,i.includes)(e.getLower("description"),e.getLower("keyword"));return r.setScore(this.calculateScore(n)).setText(this.translateScore(r)),r}},{key:"isApplicable",value:function(e){return e.hasKeyword()&&e.hasDescription()}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_keywordInMetaDescription_score",2)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("Focus Keyword used inside SEO Meta Description.","rank-math"):(0,Ke.__)("Focus Keyword not found in your SEO Meta Description.","rank-math")}}])&&Wt(t.prototype,r),n&&Wt(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),Gt=Yt;function Qt(e){return Qt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Qt(e)}function Jt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==Qt(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Qt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Qt(o)?o:String(o)),n)}var i,o}function Zt(e,t){return Zt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Zt(e,t)}function Xt(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=er(e);if(t){var i=er(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===Qt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function er(e){return er=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},er(e)}var tr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Zt(e,t)}(u,e);var t,r,n,o=Xt(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),o.apply(this,arguments)}return t=u,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Use Focus Keyword in the URL.","rank-math")).setTooltip((0,Ke.__)("Include the focus keyword in the slug (permalink) of this post.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=e.getUrl().replace(/[-_]/gi,"-"),o=(0,i.includes)(n,e.getKeywordPermalink(t))||(0,i.includes)(n,e.getPermalinkWithStopwords(t));return r.setScore(this.calculateScore(o)).setText(this.translateScore(r)),r}},{key:"isApplicable",value:function(e){return e.hasKeyword()&&e.hasPermalink()}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_keywordInPermalink_score",5)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("Focus Keyword used in the URL.","rank-math"):(0,Ke.__)("Focus Keyword not found in the URL.","rank-math")}}])&&Jt(t.prototype,r),n&&Jt(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),rr=tr;function nr(e){return nr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nr(e)}function ir(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==nr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==nr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===nr(o)?o:String(o)),n)}var i,o}function or(e,t){return or=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},or(e,t)}function ur(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=ar(e);if(t){var i=ar(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===nr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function ar(e){return ar=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ar(e)}var sr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&or(e,t)}(o,e);var t,r,n,i=ur(o);function o(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),i.apply(this,arguments)}return t=o,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Use Focus Keyword in subheading(s) like H2, H3, H4, etc..","rank-math")).setTooltip((0,Ke.__)("It is recommended to add the focus keyword as part of one or more subheadings in the content.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=new RegExp("<h[2-6][^>]*>.*"+yt(e.getLower("keyword"))+".*</h[2-6]>","gi"),i=null!==e.getTextLower().match(n);return r.setScore(this.calculateScore(i)).setText(this.translateScore(r)),r}},{key:"isApplicable",value:function(e){return e.hasKeyword()&&e.hasText()}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_keywordInSubheadings_score",3)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("Focus Keyword found in the subheading(s).","rank-math"):(0,Ke.__)("Focus Keyword not found in subheading(s) like H2, H3, H4, etc..","rank-math")}}])&&ir(t.prototype,r),n&&ir(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(n),lr=sr;function cr(e){return cr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cr(e)}function dr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==cr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==cr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===cr(o)?o:String(o)),n)}var i,o}function pr(e,t){return pr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},pr(e,t)}function fr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=yr(e);if(t){var i=yr(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===cr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function yr(e){return yr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},yr(e)}var hr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pr(e,t)}(u,e);var t,r,n,o=fr(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),o.apply(this,arguments)}return t=u,(r=[{key:"newResult",value:function(e){return(new a).setMaxScore(this.getScore(e.getShortLocale())).setEmpty((0,Ke.__)("Add Focus Keyword to the SEO title.","rank-math")).setTooltip((0,Ke.__)("Make sure the focus keyword appears in the SEO post title too.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(e),n=(0,i.includes)(e.getLower("title"),e.getLower("keyword"));return r.setScore(this.calculateScore(n,e)).setText(this.translateScore(r)),r}},{key:"isApplicable",value:function(e){return e.hasKeyword()&&e.hasTitle()}},{key:"calculateScore",value:function(e,t){return e?this.getScore(t.getShortLocale()):null}},{key:"getScore",value:function(e){var t="en"===e?36:38;return(0,z.applyFilters)("rankMath_analysis_keywordInTitle_score",t)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("Hurray! You're using Focus Keyword in the SEO Title.","rank-math"):(0,Ke.__)("Focus Keyword does not appear in the SEO title.","rank-math")}}])&&dr(t.prototype,r),n&&dr(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),gr=hr,mr=jQuery,br=r.n(mr);function vr(e){return vr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vr(e)}function Dr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ar(n.key),n)}}function wr(e,t){return wr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},wr(e,t)}function kr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=Er(e);if(t){var i=Er(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===vr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Fr(e)}(this,r)}}function Fr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Er(e){return Er=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Er(e)}function Ar(e){var t=function(e,t){if("object"!==vr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==vr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===vr(t)?t:String(t)}var Cr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&wr(e,t)}(u,e);var t,r,n,o=kr(u);function u(){var e,t,r,n;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u);for(var i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];return e=o.call.apply(o,[this].concat(a)),t=Fr(e),n={},(r=Ar(r="keywordsChecked"))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,e}return t=u,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Set a Focus Keyword for this content.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this,n=this.newResult(),o=e.getLower("keyword").trim();return(0,i.isUndefined)(this.keywordsChecked[o])?(this.keywordsChecked[o]=!0,br().ajax({url:rankMath.ajaxurl,type:"GET",data:{keyword:o,action:"rank_math_is_keyword_new",security:rankMath.security,objectID:rankMath.objectID,objectType:rankMath.objectType}}).done((function(e){r.keywordsChecked[o]=e.isNew,n.setText(r.translateScore(o,e.isNew)),n.has=e.isNew,(0,z.doAction)("rankMath_analysis_keywordUsage_updated",o,n)})),n.setText((0,Ke.__)("We are searching in database.","rank-math")),n):(n.has=this.keywordsChecked[o],n.setText(this.translateScore(o,this.keywordsChecked[o])),(0,z.doAction)("rankMath_analysis_keywordUsage_updated",o,n),n)}},{key:"isApplicable",value:function(e){return e.hasKeyword()}},{key:"translateScore",value:function(e,t){return t?(0,Ke.__)("You haven't used this Focus Keyword before.","rank-math"):(0,Ke.sprintf)((0,Ke.__)("You have %1$s this Focus Keyword.","rank-math"),'<a target="_blank" href="'+this.changeKeywordInLink(e)+'">'+(0,Ke.__)("already used","rank-math")+"</a>")}},{key:"changeKeywordInLink",value:function(e){return rankMath.assessor.focusKeywordLink.replace("%focus_keyword%",e).replace("%post_type%",rankMath.objectType).replace("%yaxonomy%",rankMath.objectType)}}])&&Dr(t.prototype,r),n&&Dr(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),Sr=Cr;function jr(e){return jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jr(e)}function xr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==jr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==jr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===jr(o)?o:String(o)),n)}var i,o}function Br(e,t){return Br=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Br(e,t)}function Or(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=_r(e);if(t){var i=_r(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===jr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function _r(e){return _r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_r(e)}var Pr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Br(e,t)}(u,e);var t,r,n,o=Or(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),o.apply(this,arguments)}return t=u,r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty(this.getFilteredText("emptyContent")).setTooltip(this.getFilteredText("tooltipText"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=t.getResearch("wordCount")(e.getTextLower());return!1===n||0===n||r.setScore(this.calculateScore(n)).setText((0,Ke.sprintf)(this.translateScore(r),n)),r}},{key:"isApplicable",value:function(e){return e.hasText()}},{key:"calculateScore",value:function(e){var t=this.getBoundaries(),r=1e5,n=!1;return(0,i.forEach)(t,(function(t){(0,i.inRange)(e,t.boundary,r)&&(n=t),r=t.boundary+1})),!1!==n?n.score:0}},{key:"translateScore",value:function(e){return e.hasScore()?this.getFilteredText("hasScore"):this.getFilteredText("failed")}},{key:"getScore",value:function(){return this.getBoundaries().recommended.score}},{key:"getBoundaries",value:function(){return(0,z.applyFilters)("rankMath_analysis_contentLength_boundaries",{recommended:{boundary:2500,score:8},belowRecommended:{boundary:2e3,score:5},medium:{boundary:1500,score:4},belowMedium:{boundary:1e3,score:3},low:{boundary:600,score:2}})}},{key:"getFilteredText",value:function(e){return(0,z.applyFilters)("rankMath_analysis_contentLength",{hasScore:(0,Ke.__)("Content is %1$d words long. Good job!","rank-math"),failed:(0,Ke.__)("Content is %1$d words long. Consider using at least 600 words.","rank-math"),emptyContent:(0,Ke.sprintf)((0,Ke.__)("Content should be %1$s long.","rank-math"),'<a href="'+ut("content-length","Content Analysis")+'" target="_blank">600-2500 words</a>'),tooltipText:(0,Ke.__)("Minimum recommended content length should be 600 words.","rank-math")})[e]}}],r&&xr(t.prototype,r),n&&xr(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),Rr=Pr;function Tr(e){return Tr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Tr(e)}function zr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==Tr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Tr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Tr(o)?o:String(o)),n)}var i,o}function Nr(e,t){return Nr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Nr(e,t)}function qr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=Ir(e);if(t){var i=Ir(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===Tr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function Ir(e){return Ir=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ir(e)}var Mr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Nr(e,t)}(o,e);var t,r,n,i=qr(o);function o(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),i.apply(this,arguments)}return t=o,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("URL unavailable. Add a short URL.","rank-math")).setTooltip((0,Ke.__)("Permalink should be at most 75 characters long.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=e.getUrl().length;return r.setScore(this.calculateScore(n)).setText((0,Ke.sprintf)(this.translateScore(r),n)),r}},{key:"isApplicable",value:function(e){return e.hasUrl()}},{key:"calculateScore",value:function(e){return 75<e?null:this.getScore()}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_permalinkLength_score",4)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("URL is %1$d characters long. Kudos!","rank-math"):(0,Ke.__)("URL is %1$d characters long. Consider shortening it.","rank-math")}}])&&zr(t.prototype,r),n&&zr(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(n),Lr=Mr;function $r(e){return $r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$r(e)}function Ur(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==$r(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==$r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===$r(o)?o:String(o)),n)}var i,o}function Wr(e,t){return Wr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Wr(e,t)}function Kr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=Hr(e);if(t){var i=Hr(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===$r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function Hr(e){return Hr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Hr(e)}var Vr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Wr(e,t)}(o,e);var t,r,n,i=Kr(o);function o(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),i.apply(this,arguments)}return t=o,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Link out to external resources.","rank-math")).setTooltip((0,Ke.__)("It helps visitors read more about a topic and prevents pogosticking.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=t.getResearch("getLinkStats")(e.getText());return 0===n.total||r.setScore(this.calculateScore(0<n.externalTotal)).setText((0,Ke.sprintf)(this.translateScore(r),n.externalTotal)),r}},{key:"isApplicable",value:function(e){return e.hasText()}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_linksHasExternals_score",4)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("Great! You are linking to external resources.","rank-math"):(0,Ke.__)("No outbound links were found. Link out to external resources.","rank-math")}}])&&Ur(t.prototype,r),n&&Ur(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(n),Yr=Vr;function Gr(e){return Gr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Gr(e)}function Qr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==Gr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Gr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Gr(o)?o:String(o)),n)}var i,o}function Jr(e,t){return Jr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Jr(e,t)}function Zr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=Xr(e);if(t){var i=Xr(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===Gr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function Xr(e){return Xr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Xr(e)}var en=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Jr(e,t)}(o,e);var t,r,n,i=Zr(o);function o(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),i.apply(this,arguments)}return t=o,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Add internal links in your content.","rank-math")).setTooltip((0,Ke.__)("Internal links decrease your bounce rate and improve SEO.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=t.getResearch("getLinkStats")(e.getText());return 0===n.total||r.setScore(this.calculateScore(0<n.internalTotal)).setText((0,Ke.sprintf)(this.translateScore(r),n.internalTotal)),r}},{key:"isApplicable",value:function(e){return e.hasText()}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_linksHasInternal_score",5)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("You are linking to other resources on your website which is great.","rank-math"):(0,Ke.__)("We couldn't find any internal links in your content.","rank-math")}}])&&Qr(t.prototype,r),n&&Qr(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(n),tn=en;function rn(e){return rn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},rn(e)}function nn(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==rn(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==rn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===rn(o)?o:String(o)),n)}var i,o}function on(e,t){return on=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},on(e,t)}function un(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=an(e);if(t){var i=an(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===rn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function an(e){return an=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},an(e)}var sn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&on(e,t)}(o,e);var t,r,n,i=un(o);function o(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),i.apply(this,arguments)}return t=o,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Add DoFollow links pointing to external resources.","rank-math")).setTooltip((0,Ke.__)("PageRank Sculpting no longer works. Your posts should have a mix of nofollow and DoFollow links.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=t.getResearch("getLinkStats")(e.getText());return 0===n.total?(r.setText((0,Ke.__)("Add DoFollow links pointing to external resources.","rank-math")),r):(r.setScore(this.calculateScore(0<n.externalDofollow)).setText((0,Ke.sprintf)(this.translateScore(r),n.externalTotal)),r)}},{key:"isApplicable",value:function(e){return e.hasText()}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_linksNotAllExternals_score",2)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("At least one external link with DoFollow found in your content.","rank-math"):(0,Ke.__)("We found %1$d outbound links in your content and all of them are nofollow.","rank-math")}}])&&nn(t.prototype,r),n&&nn(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(n),ln=sn;function cn(e){return cn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cn(e)}function dn(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==cn(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==cn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===cn(o)?o:String(o)),n)}var i,o}function pn(e,t){return pn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},pn(e,t)}function fn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=yn(e);if(t){var i=yn(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===cn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function yn(e){return yn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},yn(e)}var hn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pn(e,t)}(o,e);var t,r,n,i=fn(o);function o(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),i.apply(this,arguments)}return t=o,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Add a number to your title to improve CTR.","rank-math")).setTooltip((0,Ke.__)("Headlines with numbers are 36% more likely to generate clicks, according to research by Conductor.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=/\d+/.test(e.getTitle());return r.setScore(this.calculateScore(n)).setText(this.translateScore(r)),r}},{key:"isApplicable",value:function(e){return e.hasTitle()}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_titleHasNumber_score",1)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("You are using a number in your SEO title.","rank-math"):(0,Ke.__)("Your SEO title doesn't contain a number.","rank-math")}}])&&dn(t.prototype,r),n&&dn(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(n),gn=hn,mn={};(0,i.isUndefined)(rankMath.assessor)||(0,i.forEach)(rankMath.assessor.diacritics,(function(e,t){return mn[t]=new RegExp(e,"g")}));var bn=function(e){if((0,i.isUndefined)(e))return e;for(var t in mn)e=e.replace(mn[t],t);return e};function vn(e){return vn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vn(e)}function Dn(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==vn(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==vn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===vn(o)?o:String(o)),n)}var i,o}function wn(e,t){return wn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},wn(e,t)}function kn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=Fn(e);if(t){var i=Fn(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===vn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function Fn(e){return Fn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Fn(e)}var En,An=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&wn(e,t)}(u,e);var t,r,n,o=kn(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),o.apply(this,arguments)}return t=u,r=[{key:"newResult",value:function(){return this.hasPowerWords()?(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.sprintf)((0,Ke.__)("Add %s to your title to increase CTR.","rank-math"),'<a href="https://rankmath.com/blog/power-words/" target="_blank">power words</a>')).setTooltip((0,Ke.__)("Power Words are tried-and-true words that copywriters use to attract more clicks.","rank-math")):null}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=e.getLower("title").split(" "),o=rankMath.assessor.powerWords.filter((function(e){return(0,i.indexOf)(n,bn(e))>=0})),u=0<o.length;return r.setScore(this.calculateScore(u)).setText((0,Ke.sprintf)(this.translateScore(r),o.length)),r}},{key:"isApplicable",value:function(e){return this.hasPowerWords()&&e.hasTitle()}},{key:"hasPowerWords",value:function(){return!(0,i.isEmpty)(rankMath.assessor.powerWords)}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_titleHasPowerWords_score",1)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("Your title contains %1$d power word(s). Booyah!","rank-math"):(0,Ke.sprintf)((0,Ke.__)("Your title doesn't contain a %1$s. Add at least one.","rank-math"),'<a href="https://rankmath.com/blog/power-words/" target="_blank">power word</a>')}}],r&&Dn(t.prototype,r),n&&Dn(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),Cn=An,Sn=r(8286),jn=r.n(Sn);function xn(e){return xn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xn(e)}function Bn(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==xn(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==xn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===xn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var On={extras:(En={"a+":5,abound:2,abounds:2,abundance:4,abundant:4,accessable:3,accessible:3,acclaim:2,acclaimed:2,acclamation:2,accolade:3,accolades:3,accommodative:2,accomodative:2,accomplish:5,accomplished:5,accomplishment:5,accomplishments:5,accurate:3,accurately:3,achievable:3,achievement:3,achievements:3,achievible:3,acumen:1,adaptable:1,adaptive:1,adequate:1,adjustable:2,admirable:2,admirably:2,admiration:2,admire:2,admirer:2,admiring:2,admiringly:2,adorable:3,adore:3,adored:3,adorer:3,adoring:3,adoringly:3,adroit:2,adroitly:2,adulate:2,adulation:1,adulatory:1,advanced:3,advantage:3,advantageous:2,advantageously:2,advantages:2,adventuresome:2,adventurous:2,advocate:2,advocated:2,advocates:2,affability:2,affable:2,affably:2,affectation:3,affection:3,affectionate:3,affinity:3,affirm:2,affirmation:2,affirmative:2,affluence:2,affluent:3,afford:2,affordable:2,affordably:2,afordable:2,agile:3,agilely:2,agility:2,agreeable:3,agreeableness:3,agreeably:2,"all-around":2,alluring:3,alluringly:3,altruistic:3,altruistically:3,amaze:4,amazed:4,amazement:4,amazes:4,amazing:4,amazingly:4,ambitious:4,ambitiously:4,ameliorate:4,amenable:3,amenity:3,amiability:3,amiabily:3,amiable:3,amicability:3,amicable:3,amicably:3,amity:2,ample:2,amply:2,amuse:2,amusing:2,amusingly:2,angel:5,angelic:5,apotheosis:1,appeal:4,appealing:4,applaud:4,appreciable:4,appreciate:4,appreciated:4,appreciates:4,appreciative:4,appreciatively:4,appropriate:4,approval:4,approve:4,ardent:1,ardently:1,ardor:1,articulate:1,aspiration:3,aspirations:3,aspire:3,assurance:3,assurances:3,assure:3,assuredly:3,assuring:3,astonish:4,astonished:4,astonishing:4,astonishingly:4,astonishment:4,astound:4,astounded:4,astounding:4,astoundingly:4,astutely:1,attentive:1,attraction:1,attractive:1,attractively:1,attune:1,audible:1,audibly:1,auspicious:1,authentic:1,authoritative:1,autonomous:1,available:1,aver:1,avid:1,avidly:1,award:1,awarded:1,awards:1,awe:5,awed:1,awesome:5,awesomely:4,awesomeness:4,awestruck:4,awsome:4,backbone:3,balanced:3,bargain:3,beauteous:3,beautiful:3,beautifullly:3,beautifully:3,beautify:3,beauty:3,beckon:2,beckoned:1,beckoning:1,beckons:1,believable:3,believeable:3,beloved:3,benefactor:2,beneficent:2,beneficial:2,beneficially:2,beneficiary:2,benefit:3,benefits:3,benevolence:1,benevolent:1,benifits:2,best:5,"best-known":5,"best-performing":5,"best-selling":5,better:5,"better-known":5,"better-than-expected":4,beutifully:5,blameless:4,bless:3,blessing:3,bliss:4,blissful:4,blissfully:4,blithe:1,blockbuster:5,bloom:3,blossom:4,bolster:1,bonny:1,bonus:4,bonuses:4,boom:2,booming:3,boost:4,boundless:4,bountiful:3,brainiest:3,brainy:3,"brand-new":5,brave:3,bravery:3,bravo:2,breakthrough:5,breakthroughs:5,breathlessness:5,breathtaking:5,breathtakingly:5,breeze:4,bright:4,brighten:4,brighter:4,brightest:4,brilliance:4,brilliances:4,brilliant:4,brilliantly:4,brisk:3,brotherly:4,bullish:1,buoyant:1,cajole:1,calm:3,calming:3,calmness:3,capability:4,capable:4,capably:4,captivate:4,captivating:4,carefree:1,cashback:1,cashbacks:1,catchy:1,celebrate:4,celebrated:4,celebration:4,celebratory:4,champ:3,champion:3,charisma:4,charismatic:4,charitable:3,charm:5,charming:5,charmingly:5,chaste:1,cheaper:1,cheapest:1,cheer:4,cheerful:4,cheery:4,cherish:4,cherished:4,cherub:1,chic:1,chivalrous:2,chivalry:2,civility:2,civilize:2,clarity:4,classic:1,classy:1,clean:3,cleaner:3,cleanest:3,cleanliness:3,cleanly:3,clear:3,"clear-cut":3,cleared:3,clearer:3,clearly:3,clears:3,clever:3,cleverly:2,cohere:2,coherence:2,coherent:2,cohesive:2,colorful:3,comely:1,comfort:4,comfortable:4,comfortably:4,comforting:4,comfy:4,commend:1,commendable:1,commendably:1,commitment:3,commodious:1,compact:3,compactly:2,compassion:3,compassionate:4,compatible:3,competitive:3,complement:3,complementary:3,complemented:3,complements:3,compliant:2,compliment:5,complimentary:5,comprehensive:5,conciliate:1,conciliatory:1,concise:2,confidence:3,confident:4,congenial:1,congratulate:5,congratulation:5,congratulations:5,congratulatory:5,conscientious:2,considerate:4,consistent:4,consistently:4,constructive:4,consummate:1,contentment:1,continuity:4,contrasty:1,contribution:2,convenience:3,convenient:3,conveniently:3,convience:3,convienient:3,convient:3,convincing:3,convincingly:3,cool:5,coolest:5,cooperative:3,cooperatively:3,cornerstone:3,correct:5,correctly:5,"cost-effective":3,"cost-saving":3,"counter-attack":2,"counter-attacks":2,courage:3,courageous:3,courageously:3,courageousness:3,courteous:3,courtly:2,covenant:2,cozy:2,creative:2,credence:2,credible:2,crisp:1,crisper:1,cure:4,"cure-all":1,cushy:1,cute:5,cuteness:5,danke:1,danken:1,daring:5,daringly:5,darling:5,dashing:5,dauntless:1,dawn:1,dazzle:5,dazzled:5,dazzling:5,"dead-cheap":1,"dead-on":1,decency:5,decent:5,decisive:5,decisiveness:5,dedicated:5,defeat:1,defeated:1,defeating:1,defeats:1,defender:1,deference:1,deft:1,deginified:1,delectable:1,delicacy:4,delicate:4,delicious:4,delight:4,delighted:4,delightful:4,delightfully:4,delightfulness:4,dependable:2,dependably:2,deservedly:5,deserving:5,desirable:5,desiring:5,desirous:5,destiny:5,detachable:5,devout:5,dexterous:5,dexterously:5,dextrous:5,dignified:5,dignify:5,dignity:5,diligence:3,diligent:3,diligently:3,diplomatic:3,"dirt-cheap":3,distinction:3,distinctive:3,distinguished:3,diversified:3,divine:5,divinely:5,dominate:5,dominated:5,dominates:5,dote:1,dotingly:1,doubtless:1,dreamland:1,dumbfounded:1,dumbfounding:1,"dummy-proof":1,durable:3,dynamic:3,eager:5,eagerly:5,eagerness:5,earnest:5,earnestly:5,earnestness:5,ease:4,eased:4,eases:4,easier:4,easiest:4,easiness:4,easing:4,easy:4,"easy-to-use":5,easygoing:5,ebullience:1,ebullient:1,ebulliently:1,ecenomical:1,economical:5,ecstasies:5,ecstasy:5,ecstatic:5,ecstatically:5,edify:1,educated:5,effective:5,effectively:5,effectiveness:5,effectual:5,efficacious:5,efficient:5,efficiently:5,effortless:5,effortlessly:5,effusion:5,effusive:5,effusively:5,effusiveness:5,elan:2,elate:2,elated:2,elatedly:2,elation:3,electrify:5,elegance:5,elegant:5,elegantly:5,elevate:5,elite:5,eloquence:3,eloquent:3,eloquently:3,embolden:3,eminence:3,eminent:3,empathize:5,empathy:5,empower:5,empowerment:5,enchant:5,enchanted:5,enchanting:5,enchantingly:5,encourage:5,encouragement:5,encouraging:5,encouragingly:5,endear:3,endearing:3,endorse:3,endorsed:3,endorsement:3,endorses:3,endorsing:2,energetic:5,energize:5,"energy-efficient":5,"energy-saving":5,engaging:2,engrossing:2,enhance:5,enhanced:5,enhancement:5,enhances:5,enjoy:5,enjoyable:5,enjoyably:5,enjoyed:5,enjoying:5,enjoyment:5,enjoys:5,enlighten:5,enlightenment:5,enliven:1,ennoble:2,enough:3,enrapt:2,enrapture:1,enraptured:1,enrich:5,enrichment:3,enterprising:3,entertain:3,entertaining:5,entertains:5,enthral:2,enthrall:1,enthralled:1,enthuse:1,enthusiasm:4,enthusiast:4,enthusiastic:4,enthusiastically:3,entice:3,enticed:4,enticing:3,enticingly:4,entranced:3,entrancing:3,entrust:4,enviable:3,enviably:3,envious:3,enviously:3,enviousness:1,envy:1,equitable:1,ergonomical:5,"err-free":1,erudite:1,ethical:4,eulogize:2,euphoria:3,euphoric:3,euphorically:3,evaluative:4,evenly:5,eventful:4,everlasting:5,evocative:2,exalt:1,exaltation:2,exalted:3,exaltedly:3,exalting:3,exaltingly:3,examplar:1,examplary:1,excallent:1,exceed:5,exceeded:5,exceeding:5,exceedingly:5,exceeds:5,excel:5,exceled:5,excelent:5,excellant:5,excelled:5,excellence:5,excellency:5,excellent:5,excellently:5,excels:5,exceptional:5,exceptionally:5,excite:5,excited:5,excitedly:5,excitedness:5,excitement:5,excites:5,exciting:5,excitingly:5,exellent:5,exemplar:3,exemplary:3,exhilarate:3,exhilarating:3,exhilaratingly:3,exhilaration:3,exonerate:3,expansive:3,expeditiously:3,expertly:4,exquisite:1,exquisitely:1,extol:1,extoll:1,extraordinarily:5,extraordinary:5,exuberance:3,exuberant:3,exuberantly:3,exult:1,exultant:1,exultation:1,exultingly:1,"eye-catch":5,"eye-catching":5,eyecatch:5,eyecatching:5,fabulous:1,fabulously:4,facilitate:4,fair:5,fairly:5,fairness:5,faith:4,faithful:4,faithfully:4,faithfulness:4,fame:4,famed:4,famous:4,famously:4,fancier:5,fancinating:5,fancy:5,fanfare:3,fans:3,fantastic:3,fantastically:3,fascinate:5,fascinating:5,fascinatingly:5,fascination:5,fashionable:5,fashionably:5,fast:2,"fast-growing":2,"fast-paced":2,faster:5,fastest:5,"fastest-growing":5,faultless:3,fav:4,fave:4,favor:4,favorable:4,favored:4,favorite:4,favorited:4,favour:4,fearless:5,fearlessly:5,feasible:3,feasibly:3,feat:2,"feature-rich":2,fecilitous:2,feisty:2,felicitate:2,felicitous:2,felicity:3,fertile:3,fervent:3,fervently:3,fervid:3,fervidly:3,fervor:3,festive:3,fidelity:1,fiery:1,fine:5,"fine-looking":5,finely:5,finer:5,finest:5,firmer:4,"first-class":4,"first-in-class":4,"first-rate":5,flashy:5,flatter:4,flattering:4,flatteringly:4,flawless:5,flawlessly:5,flexibility:3,flexible:3,flourish:1,flourishing:1,fluent:2,flutter:2,fond:4,fondly:4,fondness:4,foolproof:4,foremost:3,foresight:5,formidable:4,fortitude:5,fortuitous:5,fortuitously:3,fortunate:5,fortunately:5,fortune:5,fragrant:4,free:5,freed:5,freedom:5,freedoms:5,fresh:5,fresher:5,freshest:5,friendliness:4,friendly:4,frolic:3,frugal:3,fruitful:4,ftw:4,fulfillment:4,fun:5,futurestic:4,futuristic:4,gaiety:1,gaily:1,gain:4,gained:4,gainful:4,gainfully:4,gaining:4,gains:4,gallant:2,gallantly:2,galore:4,geekier:4,geeky:3,gem:4,gems:4,generosity:4,generous:5,generously:5,genial:1,genius:4,gentle:4,gentlest:4,genuine:5,gifted:5,glad:4,gladden:1,gladly:4,gladness:4,glamorous:5,glee:1,gleeful:1,gleefully:1,glimmer:1,glimmering:1,glisten:1,glistening:1,glitter:1,glitz:1,glorify:5,glorious:5,gloriously:5,glory:5,glow:4,glowing:4,glowingly:4,"god-given":3,"god-send":3,godlike:4,godsend:4,gold:3,golden:3,good:5,goodly:5,goodness:4,goodwill:3,goood:5,gooood:5,gorgeous:4,gorgeously:4,grace:4,graceful:4,gracefully:4,gracious:4,graciously:4,graciousness:4,grand:5,grandeur:3,grateful:3,gratefully:3,gratification:3,gratified:3,gratifies:3,gratify:3,gratifying:3,gratifyingly:3,gratitude:5,great:5,greatest:5,greatness:5,grin:3,groundbreaking:3,guarantee:3,guidance:3,guiltless:3,gumption:1,gush:1,gusto:1,gutsy:1,hail:1,halcyon:1,hale:1,hallmark:1,hallmarks:1,hallowed:1,handier:1,handily:1,"hands-down":4,handsome:5,handsomely:5,handy:5,happier:5,happily:5,happiness:5,happy:5,"hard-working":4,hardier:4,hardy:4,harmless:3,harmonious:1,harmoniously:1,harmonize:1,harmony:4,headway:1,heal:5,healthful:5,healthy:4,hearten:2,heartening:2,heartfelt:3,heartily:3,heartwarming:4,heaven:5,heavenly:5,helped:4,helpful:4,helping:4,hero:4,heroic:4,heroically:4,heroine:2,heroize:2,heros:4,"high-quality":5,"high-spirited":3,hilarious:3,holy:3,homage:2,honest:3,honesty:4,honor:3,honorable:3,honored:3,honoring:3,hooray:4,hopeful:4,hospitable:4,hot:2,hotcake:2,hotcakes:3,hottest:4,hug:3,humane:4,humble:3,humility:4,humor:3,humorous:3,humorously:3,humour:3,humourous:3,ideal:4,idealize:3,ideally:3,idol:2,idolize:2,idolized:2,idyllic:1,illuminate:5,illuminati:1,illuminating:4,illumine:2,illustrious:3,ilu:1,imaculate:5,imaginative:5,immaculate:5,immaculately:5,immense:3,impartial:3,impartiality:3,impartially:3,impassioned:3,impeccable:3,impeccably:3,important:4,impress:4,impressed:4,impresses:4,impressive:4,impressively:4,impressiveness:4,improve:4,improved:3,improvement:4,improvements:5,improves:5,improving:3,incredible:4,incredibly:5,indebted:4,individualized:1,indulgence:1,indulgent:1,industrious:1,inestimable:1,inestimably:1,inexpensive:4,infallibility:1,infallible:1,infallibly:1,influential:1,ingenious:2,ingeniously:3,ingenuity:3,ingenuous:3,ingenuously:2,innocuous:3,innovation:4,innovative:4,inpressed:2,insightful:4,insightfully:3,inspiration:4,inspirational:5,inspire:5,inspiring:5,instantly:3,instructive:3,instrumental:3,integral:3,integrated:3,intelligence:3,intelligent:3,intelligible:2,interesting:4,interests:3,intimacy:2,intimate:2,intricate:3,intrigue:2,intriguing:2,intriguingly:2,intuitive:3,invaluable:5,invaluablely:5,inventive:3,invigorate:3,invigorating:2,invincibility:3,invincible:5,inviolable:3,inviolate:3,invulnerable:4,irreplaceable:3,irreproachable:3,irresistible:3,irresistibly:3,"issue-free":3,"jaw-droping":5,"jaw-dropping":5,jollify:3,jolly:3,jovial:2,joy:4,joyful:5,joyfully:5,joyous:4,joyously:4,jubilant:4,jubilantly:3,jubilate:3,jubilation:3,jubiliant:3,judicious:3,justly:3,keen:3,keenly:4,keenness:4,"kid-friendly":3,kindliness:5,kindly:5,kindness:5,knowledgeable:4,kudos:3,"large-capacity":3,laud:3,laudable:4,laudably:4,lavish:5,lavishly:5,"law-abiding":4,lawful:4,lawfully:4,lead:3,leading:3,leads:1,lean:1,led:1,legendary:5,leverage:3,levity:2,liberate:4,liberation:4,liberty:4,lifesaver:5,"light-hearted":3,lighter:3,likable:4,like:5,liked:4,likes:4,liking:5,lionhearted:4,lively:5,logical:4,"long-lasting":5,lovable:4,lovably:5,love:5,loved:5,loveliness:4,lovely:5,lover:4,loves:4,loving:5,"low-cost":2,"low-price":2,"low-priced":2,"low-risk":3,"lower-priced":3,loyal:4,loyalty:4,lucid:2,lucidly:2,luck:4,luckier:4,luckiest:4,luckiness:4,lucky:5,lucrative:3,luminous:3,lush:2,luster:2,lustrous:3,luxuriant:3,luxuriate:3,luxurious:3,luxuriously:4,luxury:5,lyrical:2,magic:3,magical:4,magnanimous:3,magnanimously:3,magnificence:2,magnificent:3,magnificently:3,majestic:3,majesty:3,manageable:2,maneuverable:2,marvel:4,marveled:4,marvelled:4,marvellous:4,marvelous:4,marvelously:5,marvelousness:3,marvels:3,master:5,masterful:5,masterfully:5,masterpiece:5,masterpieces:5,masters:5,mastery:4,matchless:4,mature:3,maturely:2,maturity:3,meaningful:4,memorable:5,merciful:5,mercifully:5,mercy:5,merit:4,meritorious:4,merrily:3,merriment:2,merriness:2,merry:4,mesmerize:3,mesmerized:3,mesmerizes:3,mesmerizing:3,mesmerizingly:3,meticulous:3,meticulously:3,mightily:4,mighty:2,"mind-blowing":5,miracle:5,miracles:5,miraculous:5,miraculously:5,miraculousness:1,modern:3,modest:3,modesty:3,momentous:3,monumental:3,monumentally:3,morality:3,motivated:3,"multi-purpose":3,navigable:3,neat:5,neatest:5,neatly:5,nice:5,nicely:5,nicer:5,nicest:5,nifty:4,nimble:4,noble:4,nobly:4,noiseless:4,"non-violence":2,"non-violent":2,notably:4,noteworthy:4,nourish:4,nourishing:4,nourishment:4,novelty:3,nurturing:3,oasis:3,obsession:3,obsessions:3,obtainable:3,openly:1,openness:1,optimal:3,optimism:3,optimistic:3,opulent:1,orderly:4,originality:4,outdo:4,outdone:4,outperform:4,outperformed:4,outperforming:4,outperforms:4,outshine:5,outshone:5,outsmart:5,outstanding:3,outstandingly:3,outstrip:4,outwit:4,ovation:2,overjoyed:2,overtake:3,overtaken:3,overtakes:4,overtaking:4,overtook:3,overture:3,"pain-free":4,painless:4,painlessly:5,palatial:2,pamper:3,pampered:3,pamperedly:3,pamperedness:2,pampers:2,panoramic:3,paradise:4,paramount:2,pardon:2,passion:2,passionate:2,passionately:2,patience:3,patient:3,patiently:2,patriot:2,patriotic:2,peace:5,peaceable:5,peaceful:5,peacefully:5,peacekeepers:5,peach:3,peerless:3,pep:2,pepped:2,pepping:2,peppy:2,peps:2,perfect:5,perfection:5,perfectly:5,permissible:2,perseverance:2,persevere:1,personages:1,personalized:3,phenomenal:4,phenomenally:4,picturesque:4,piety:3,pinnacle:4,playful:4,playfully:4,pleasant:4,pleasantly:3,pleased:4,pleases:4,pleasing:3,pleasingly:4,pleasurable:4,pleasurably:3,pleasure:3,plentiful:4,pluses:2,plush:1,plusses:1,poetic:1,poeticize:1,poignant:1,poise:1,poised:2,polished:2,polite:4,politeness:4,popular:5,portable:4,posh:3,positive:4,positively:5,positives:5,powerful:5,powerfully:5,praise:5,praiseworthy:5,praising:4,"pre-eminent":2,precious:2,precise:2,precisely:2,preeminent:2,prefer:2,preferable:2,preferably:2,prefered:2,preferes:2,preferring:2,prefers:2,premier:2,prestige:4,prestigious:2,prettily:2,pretty:5,priceless:5,pride:4,principled:2,privilege:2,privileged:2,prize:2,proactive:2,"problem-free":2,"problem-solver":2,prodigious:2,prodigiously:2,prodigy:2,productive:2,productively:2,proficient:2,proficiently:2,profound:3,profoundly:3,profuse:2,profusion:2,progress:2,progressive:2,prolific:2,prominence:2,prominent:2,promise:2,promised:2,promises:2,promising:2,promoter:2,prompt:2,promptly:2,proper:2,properly:2,propitious:2,propitiously:2,pros:2,prosper:2,prosperity:3,prosperous:3,prospros:3,protect:3,protection:4,protective:4,proud:2,proven:2,proves:2,providence:2,proving:2,prowess:2,prudence:2,prudent:2,prudently:2,punctual:2,pure:5,purify:5,purposeful:2,quaint:2,qualified:2,qualify:4,quicker:5,quiet:5,quieter:2,radiance:2,radiant:2,rapid:2,rapport:2,rapt:1,rapture:1,raptureous:1,raptureously:1,rapturous:1,rapturously:1,rational:1,"razor-sharp":2,reachable:1,readable:2,readily:2,ready:5,reaffirm:1,reaffirmation:2,realistic:2,realizable:2,reasonable:2,reasonably:2,reasoned:2,reassurance:2,reassure:2,receptive:2,reclaim:2,recomend:4,recommend:4,recommendation:4,recommendations:4,recommended:5,reconcile:3,reconciliation:3,"record-setting":4,recover:4,recovery:4,rectification:4,rectify:3,rectifying:3,redeem:3,redeeming:3,redemption:1,refine:1,refined:3,refinement:3,reform:4,reformed:4,reforming:3,reforms:3,refresh:3,refreshed:3,refreshing:3,refund:3,refunded:2,regal:2,regally:2,regard:2,rejoice:2,rejoicing:3,rejoicingly:3,rejuvenate:3,rejuvenated:3,rejuvenating:3,relaxed:4,relent:4,reliable:4,reliably:4,relief:4,relish:4,remarkable:3,remarkably:3,remedy:3,remission:3,remunerate:3,renaissance:3,renewed:2,renown:2,renowned:2,replaceable:3,reputable:3,reputation:4,resilient:4,resolute:4,resound:4,resounding:2,resourceful:2,resourcefulness:3,respect:3,respectable:3,respectful:2,respectfully:2,respite:2,resplendent:2,responsibly:2,responsive:4,restful:4,restored:2,restructure:2,restructured:2,restructuring:2,retractable:2,revel:2,revelation:2,revere:2,reverence:2,reverent:2,reverently:2,revitalize:2,revival:2,revive:4,revives:2,revolutionary:2,revolutionize:2,revolutionized:2,revolutionizes:2,reward:5,rewarding:5,rewardingly:5,rich:4,richer:4,richly:4,richness:4,right:4,righten:3,righteous:3,righteously:2,righteousness:2,rightful:2,rightfully:2,rightly:2,rightness:2,"risk-free":2,robust:2,"rock-star":4,"rock-stars":4,rockstar:2,rockstars:2,romantic:2,romantically:2,romanticize:2,roomier:2,roomy:2,rosy:2,safe:4,safely:4,sagacity:2,sagely:2,saint:2,saintliness:2,saintly:2,salutary:2,salute:2,sane:3,satisfactorily:3,satisfactory:3,satisfied:3,satisfies:3,satisfy:3,satisfying:3,satisified:3,saver:4,savings:4,savior:4,savvy:4,scenic:4,seamless:4,seasoned:2,secure:5,securely:2,selective:3,"self-determination":3,"self-respect":5,"self-satisfaction":5,"self-sufficiency":5,"self-sufficient":5,sensation:5,sensational:4,sensationally:4,sensations:4,sensible:4,sensibly:4,sensitive:2,serene:2,serenity:2,sexy:4,sharp:2,sharper:2,sharpest:2,shimmering:2,shimmeringly:2,shine:2,shiny:2,significant:2,silent:3,simpler:3,simplest:2,simplified:2,simplifies:2,simplify:2,simplifying:2,sincere:5,sincerely:2,sincerity:2,skill:4,skilled:2,skillful:2,skillfully:2,slammin:2,sleek:2,slick:2,smart:3,smarter:3,smartest:2,smartly:2,smile:2,smiles:2,smiling:2,smilingly:2,smitten:2,smooth:2,smoother:2,smoothes:2,smoothest:2,smoothly:2,snappy:2,snazzy:2,sociable:2,soft:4,softer:4,solace:2,solicitous:2,solicitously:2,solid:2,solidarity:2,soothe:2,soothingly:2,sophisticated:2,soulful:3,soundly:3,soundness:2,spacious:2,sparkle:2,sparkling:2,spectacular:5,spectacularly:5,speedily:2,speedy:2,spellbind:2,spellbinding:2,spellbindingly:2,spellbound:2,spirited:2,spiritual:2,splendid:2,splendidly:2,splendor:2,spontaneous:2,sporty:1,spotless:2,sprightly:1,stability:2,stabilize:2,stable:4,stainless:2,standout:2,"state-of-the-art":2,stately:2,statuesque:2,staunch:2,staunchly:2,staunchness:2,steadfast:2,steadfastly:2,steadfastness:2,steadiest:3,steadiness:3,steady:5,stellar:5,stellarly:2,stimulate:2,stimulates:2,stimulating:2,stimulative:2,stirringly:1,straighten:1,straightforward:3,streamlined:3,striking:2,strikingly:2,striving:2,strong:4,stronger:4,strongest:4,stunned:4,stunning:5,stunningly:5,stupendous:1,stupendously:1,sturdier:1,sturdy:1,stylish:4,stylishly:4,stylized:4,suave:2,suavely:1,sublime:2,subsidize:1,subsidized:1,subsidizes:1,subsidizing:1,substantive:1,succeed:3,succeeded:4,succeeding:3,succeeds:3,succes:1,success:4,successes:2,successful:2,successfully:4,suffice:2,sufficed:3,suffices:2,sufficient:3,sufficiently:3,suitable:4,sumptuous:1,sumptuously:1,sumptuousness:1,super:5,superb:5,superbly:5,superior:2,superiority:4,supple:2,support:3,supported:3,supporter:3,supporting:3,supportive:3,supports:3,supremacy:4,supreme:4,supremely:4,supurb:4,supurbly:3,surmount:2,surpass:3,surreal:4,survival:2,survivor:2,sustainability:2,sustainable:2,swank:1,swankier:1,swankiest:1,swanky:1,sweeping:2,sweet:4,sweeten:3,sweetheart:5,sweetly:4,sweetness:5,swift:3,swiftness:3,talent:2,talented:2,talents:2,tantalize:1,tantalizing:1,tantalizingly:1,tempt:1,tempting:3,temptingly:3,tenacious:2,tenaciously:2,tenacity:2,tender:4,tenderly:4,terrific:4,terrifically:4,thank:4,thankful:4,thinner:3,thoughtful:2,thoughtfully:2,thoughtfulness:2,thrift:2,thrifty:2,thrill:2,thrilled:2,thrilling:2,thrillingly:2,thrills:2,thrive:2,thriving:2,"thumb-up":2,"thumbs-up":2,tickle:2,tidy:2,"time-honored":1,timely:2,tingle:2,titillate:1,titillating:1,titillatingly:1,togetherness:2,tolerable:3,"toll-free":1,top:4,"top-notch":5,"top-quality":5,topnotch:5,tops:5,tough:2,tougher:3,toughest:4,traction:1,tranquil:1,tranquility:1,transparent:2,treasure:4,tremendously:5,trendy:5,triumph:5,triumphal:5,triumphant:1,triumphantly:1,trivially:1,trophy:1,"trouble-free":2,trump:5,trumpet:1,trust:5,trusted:2,trusting:3,trustingly:2,trustworthiness:2,trustworthy:2,trusty:4,truthful:2,truthfully:2,truthfulness:2,twinkly:2,"ultra-crisp":5,unabashed:2,unabashedly:2,unaffected:1,unassailable:2,unbeatable:2,unbiased:2,unbound:2,uncomplicated:3,unconditional:2,undamaged:3,undaunted:2,understandable:2,undisputable:2,undisputably:2,undisputed:3,unencumbered:1,unequivocal:1,unequivocally:1,unfazed:2,unfettered:1,unforgettable:3,unity:2,unlimited:3,unmatched:2,unparalleled:1,unquestionable:1,unquestionably:1,unreal:3,unrestricted:2,unrivaled:2,unselfish:2,unwavering:3,upbeat:2,upgradable:2,upgradeable:2,upgraded:2,upheld:2,uphold:2,uplift:2,uplifting:2,upliftingly:2,upliftment:2,upscale:2,usable:4,useable:2,useful:5,"user-friendly":2,"user-replaceable":2,valiant:1,valiantly:2,valor:2,valuable:5,variety:2,venerate:2,verifiable:1,veritable:1,versatile:2,versatility:1,vibrant:4,vibrantly:4,victorious:5,victory:5,viewable:2,vigilance:2,vigilant:3,virtue:2,virtuous:2,virtuously:2,visionary:2,vivacious:2,vivid:2,vouch:3,vouchsafe:2,warm:4,warmer:2,warmhearted:2,warmly:4,warmth:5,wealthy:5,welcome:2,well:5,"well-backlit":2,"well-balanced":2,"well-behaved":5,"well-being":2,"well-bred":2,"well-connected":2,"well-educated":2,"well-established":1,"well-informed":1,"well-intentioned":2,"well-known":3,"well-made":5,"well-managed":2,"well-mannered":2,"well-positioned":2,"well-received":2,"well-regarded":2,"well-rounded":2,"well-run":2,"well-wishers":2,wellbeing:2,whoa:2,wholeheartedly:2,wholesome:2,whooa:2,whoooa:2,wieldy:1,willing:2,willingly:2,willingness:3,win:5,windfall:2,winnable:2,winner:2,winners:2,winning:2,wins:2,wisdom:2,wise:2,wisely:2,witty:4,won:5,wonder:2,wonderful:2,wonderfully:2,wonderous:2,wonderously:2,wonders:2,wondrous:2,woo:3,work:2,workable:2,worked:3,works:4,"world-famous":2,worth:2,"worth-while":2,worthiness:2,worthwhile:2,worthy:4,wow:5,wowed:2,wowing:2,wows:2,yay:4,youthful:5,zeal:2,zenith:2,zest:4,zippy:2,"2-faced":-1,"2-faces":-1,abnormal:-5,abolish:-4,abominable:-3,abominably:-3,abominate:-2,abomination:-3,abort:-2,aborted:-3,aborts:-3,abrade:-2,abrasive:-4,abrupt:-2,abruptly:-2,abscond:-4},Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"aborted",-5),"absence",-5),"absent-minded",-5),"absentee",-5),"absurd",-4),"absurdity",-3),"absurdly",-3),"absurdness",-3),"abuse",-4),"abused",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"abuses",-4),"abusive",-3),"abysmal",-2),"abysmally",-1),"abyss",-1),"accidental",-3),"accost",-1),"accursed",-1),"accusation",-5),"accusations",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"accuse",-3),"accuses",-3),"accusing",-2),"accusingly",-4),"acerbate",-1),"acerbic",-1),"acerbically",-1),"ache",-5),"ached",-1),"aches",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"achey",-1),"aching",-5),"acrid",-1),"acridly",-1),"acridness",-1),"acrimonious",-1),"acrimoniously",-1),"acrimony",-1),"adamant",-4),"adamantly",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"addict",-5),"addicted",-2),"addicting",-2),"addicts",-3),"admonish",-4),"admonisher",-2),"admonishingly",-3),"admonishment",-4),"admonition",-3),"adulterate",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"adulterated",-3),"adulteration",-5),"adulterier",-2),"adversarial",-1),"adversary",-1),"adverse",-4),"adversity",-5),"afflict",-1),"affliction",-1),"afflictive",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"affront",-1),"afraid",-5),"aggravate",-4),"aggravating",-4),"aggravation",-5),"aggression",-5),"aggressive",-5),"aggressiveness",-4),"aggressor",-4),"aggrieve",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"aggrieved",-4),"aggrivation",-5),"aghast",-2),"agonies",-4),"agonize",-5),"agonizing",-5),"agonizingly",-4),"agony",-5),"aground",-2),"ail",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"ailing",-1),"ailment",-2),"aimless",-2),"alarm",-1),"alarmed",-1),"alarming",-1),"alarmingly",-1),"alienate",-2),"alienated",-1),"alienation",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"allegation",-2),"allegations",-3),"allege",-1),"allergic",-1),"allergies",-1),"allergy",-1),"aloof",-1),"altercation",-1),"ambiguity",-2),"ambiguous",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"ambivalence",-1),"ambivalent",-1),"ambush",-1),"amiss",-1),"amputate",-3),"anarchism",-1),"anarchist",-4),"anarchistic",-1),"anarchy",-1),"anemic",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"anger",-4),"angrily",-5),"angriness",-5),"angry",-5),"anguish",-3),"animosity",-1),"annihilate",-1),"annihilation",-1),"annoy",-3),"annoyance",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"annoyances",-4),"annoyed",-2),"annoying",-2),"annoyingly",-3),"annoys",-1),"anomalous",-1),"anomaly",-3),"antagonism",-1),"antagonist",-1),"antagonistic",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"antagonize",-1),"anti-",-1),"anti-american",-1),"anti-israeli",-1),"anti-occupation",-1),"anti-proliferation",-1),"anti-semites",-1),"anti-social",-1),"anti-us",-1),"anti-white",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"antipathy",-1),"antiquated",-1),"antithetical",-1),"anxieties",-1),"anxiety",-2),"anxious",-3),"anxiously",-3),"anxiousness",-1),"apathetic",-4),"apathetically",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"apathy",-5),"apocalypse",-5),"apocalyptic",-4),"apologist",-3),"apologists",-2),"appal",-1),"appall",-1),"appalled",-1),"appalling",-1),"appallingly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"apprehension",-1),"apprehensions",-1),"apprehensive",-1),"apprehensively",-1),"arbitrary",-1),"arcane",-1),"archaic",-1),"arduous",-1),"arduously",-1),"argumentative",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"arrogance",-3),"arrogant",-4),"arrogantly",-5),"ashamed",-4),"asinine",-1),"asininely",-1),"asinininity",-1),"askance",-1),"asperse",-1),"aspersion",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"aspersions",-1),"assail",-1),"assassin",-2),"assassinate",-2),"assault",-1),"assult",-1),"astray",-1),"asunder",-1),"atrocious",-1),"atrocities",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"atrocity",-1),"atrophy",-1),"attack",-3),"attacks",-3),"audacious",-3),"audaciously",-2),"audaciousness",-2),"audacity",-2),"audiciously",-1),"austere",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"authoritarian",-1),"autocrat",-1),"autocratic",-1),"avalanche",-2),"avarice",-1),"avaricious",-1),"avariciously",-1),"avenge",-4),"averse",-1),"aversion",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"aweful",-4),"awful",-5),"awfully",-1),"awfulness",-1),"awkward",-3),"awkwardness",-2),"ax",-1),"babble",-1),"back-logged",-1),"back-wood",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"back-woods",-1),"backache",-1),"backaches",-1),"backaching",-1),"backbite",-1),"backbiting",-1),"backward",-1),"backwardness",-1),"backwood",-1),"backwoods",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"bad",-4),"badly",-2),"baffle",-5),"baffled",-4),"bafflement",-1),"baffling",-1),"bait",-1),"balk",-1),"banal",-1),"banalize",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"bane",-1),"banish",-5),"banishment",-4),"bankrupt",-2),"barbarian",-5),"barbaric",-4),"barbarically",-3),"barbarity",-2),"barbarous",-3),"barbarously",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"barren",-3),"baseless",-2),"bash",-1),"bashed",-1),"bashful",-1),"bashing",-1),"bastard",-2),"bastards",-2),"battered",-1),"battering",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"batty",-1),"bearish",-1),"beastly",-1),"bedlam",-1),"bedlamite",-1),"befoul",-1),"beg",-1),"beggar",-1),"beggarly",-1),"begging",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"beguile",-1),"belabor",-1),"belated",-1),"beleaguer",-1),"belie",-2),"belittle",-2),"belittled",-2),"belittling",-2),"bellicose",-2),"belligerence",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"belligerent",-2),"belligerently",-2),"bemoan",-2),"bemoaning",-2),"bemused",-2),"bent",-2),"berate",-2),"bereave",-2),"bereavement",-2),"bereft",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"berserk",-2),"beseech",-1),"beset",-1),"besiege",-1),"besmirch",-1),"bestial",-1),"betray",-1),"betrayal",-1),"betrayals",-1),"betrayer",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"betraying",-1),"betrays",-1),"bewail",-1),"beware",-2),"bewilder",-1),"bewildered",-1),"bewildering",-1),"bewilderingly",-1),"bewilderment",-1),"bewitch",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"bias",-3),"biased",-1),"biases",-1),"bicker",-1),"bickering",-1),"bid-rigging",-1),"bigotries",-1),"bigotry",-4),"bitch",-5),"bitchy",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"biting",-5),"bitingly",-5),"bitter",-5),"bitterly",-5),"bitterness",-5),"bizarre",-1),"blab",-1),"blabber",-1),"blackmail",-1),"blah",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"blame",-3),"blameworthy",-1),"bland",-1),"blandish",-1),"blaspheme",-1),"blasphemous",-1),"blasphemy",-1),"blasted",-1),"blatant",-1),"blatantly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"blather",-1),"bleak",-1),"bleakly",-1),"bleakness",-1),"bleed",-2),"bleeding",-3),"bleeds",-3),"blemish",-1),"blind",-2),"blinding",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"blindingly",-1),"blindside",-1),"blister",-1),"blistering",-1),"bloated",-1),"blockage",-1),"blockhead",-1),"bloodshed",-1),"bloodthirsty",-1),"bloody",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"blotchy",-1),"blow",-3),"blunder",-1),"blundering",-1),"blunders",-1),"blunt",-1),"blur",-1),"bluring",-1),"blurred",-1),"blurring",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"blurry",-1),"blurs",-1),"blurt",-1),"boastful",-1),"boggle",-3),"bogus",-2),"boil",-2),"boiling",-3),"boisterous",-2),"bomb",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"bombard",-2),"bombardment",-1),"bombastic",-1),"bondage",-1),"bonkers",-4),"bore",-1),"bored",-1),"boredom",-2),"bores",-2),"boring",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"botch",-1),"bother",-1),"bothered",-1),"bothering",-1),"bothers",-1),"bothersome",-1),"bowdlerize",-1),"boycott",-3),"braggart",-3),"bragger",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"brainless",-3),"brainwash",-1),"brash",-1),"brashly",-1),"brashness",-1),"brat",-1),"bravado",-1),"brazen",-1),"brazenly",-2),"brazenness",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"breach",-3),"break",-2),"break-up",-2),"break-ups",-1),"breakdown",-4),"breaking",-1),"breaks",-2),"breakup",-2),"breakups",-2),"bribery",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"brimstone",-2),"bristle",-2),"brittle",-3),"broke",-3),"broken",-3),"broken-hearted",-3),"brood",-3),"browbeat",-3),"bruise",-3),"bruised",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"bruises",-2),"bruising",-2),"brusque",-3),"brutal",-3),"brutalising",-3),"brutalities",-2),"brutality",-2),"brutalize",-2),"brutalizing",-2),"brutally",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"brute",-2),"brutish",-1),"bs",-3),"buckle",-1),"bug",-2),"bugging",-5),"buggy",-3),"bugs",-2),"bulkier",-2),"bulkiness",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"bulky",-3),"bulkyness",-1),"bull****",-3),"bull----",-3),"bullies",-2),"bullshit",-5),"bullshyt",-1),"bully",-2),"bullying",-1),"bullyingly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"bum",-1),"bump",-1),"bumped",-3),"bumping",-1),"bumpping",-1),"bumps",-1),"bumpy",-1),"bungle",-1),"bungler",-1),"bungling",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"bunk",-1),"burden",-4),"burdensome",-1),"burdensomely",-1),"burn",-5),"burned",-4),"burning",-1),"burns",-1),"bust",-1),"busts",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"busybody",-1),"butcher",-1),"butchery",-1),"buzzing",-1),"byzantine",-1),"cackle",-1),"calamities",-3),"calamitous",-3),"calamitously",-3),"calamity",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"callous",-2),"calumniate",-1),"calumniation",-1),"calumnies",-1),"calumnious",-1),"calumniously",-1),"calumny",-1),"cancer",-5),"cancerous",-1),"cannibal",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"cannibalize",-5),"capitulate",-1),"capricious",-1),"capriciously",-1),"capriciousness",-1),"capsize",-1),"careless",-4),"carelessness",-5),"caricature",-1),"carnage",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"carp",-1),"cartoonish",-1),"cash-strapped",-1),"castigate",-1),"castrated",-1),"casualty",-3),"cataclysm",-2),"cataclysmal",-2),"cataclysmic",-2),"cataclysmically",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"catastrophe",-4),"catastrophes",-4),"catastrophic",-4),"catastrophically",-1),"catastrophies",-1),"caustic",-1),"caustically",-1),"cautionary",-3),"cave",-1),"censure",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"chafe",-1),"chaff",-1),"chagrin",-1),"challenging",-3),"chaos",-5),"chaotic",-1),"chasten",-1),"chastise",-1),"chastisement",-1),"chatter",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"chatterbox",-1),"cheap",-3),"cheapen",-1),"cheaply",-1),"cheat",-2),"cheated",-3),"cheater",-2),"cheating",-1),"cheats",-1),"checkered",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"cheerless",-1),"cheesy",-1),"chide",-1),"childish",-1),"chill",-1),"chilly",-1),"chintzy",-1),"choke",-1),"choleric",-1),"choppy",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"chore",-1),"chronic",-1),"chunky",-1),"clamor",-1),"clamorous",-1),"clash",-1),"cliche",-1),"cliched",-1),"clique",-1),"clog",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"clogged",-1),"clogs",-1),"cloud",-1),"clouding",-1),"cloudy",-1),"clueless",-1),"clumsy",-3),"clunky",-3),"coarse",-3),"cocky",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"coerce",-2),"coercion",-2),"coercive",-2),"cold",-3),"coldly",-1),"collapse",-1),"collude",-1),"collusion",-1),"combative",-1),"combust",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"comical",-2),"commiserate",-2),"commonplace",-2),"commotion",-3),"commotions",-3),"complacent",-3),"complain",-3),"complained",-4),"complaining",-4),"complains",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"complaint",-4),"complaints",-2),"complex",-2),"complicated",-4),"complication",-4),"complicit",-4),"compulsion",-4),"compulsive",-1),"concede",-1),"conceded",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"conceit",-1),"conceited",-1),"concen",-1),"concens",-2),"concern",-1),"concerned",-1),"concerns",-1),"concession",-1),"concessions",-1),"condemn",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"condemnable",-1),"condemnation",-1),"condemned",-1),"condemns",-1),"condescend",-1),"condescending",-1),"condescendingly",-1),"condescension",-1),"confess",-1),"confession",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"confessions",-2),"confined",-2),"conflict",-2),"conflicted",-2),"conflicting",-2),"conflicts",-2),"confound",-2),"confounded",-2),"confounding",-2),"confront",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"confrontation",-3),"confrontational",-3),"confuse",-5),"confused",-4),"confuses",-4),"confusing",-4),"confusion",-4),"confusions",-4),"congested",-4),"congestion",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"cons",-5),"conscons",-2),"conservative",-1),"conspicuous",-1),"conspicuously",-1),"conspiracies",-1),"conspiracy",-4),"conspirator",-1),"conspiratorial",-1),"conspire",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"consternation",-1),"contagious",-3),"contaminate",-3),"contaminated",-3),"contaminates",-2),"contaminating",-2),"contamination",-1),"contempt",-1),"contemptible",-1),"contemptuous",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"contemptuously",-2),"contend",-2),"contention",-2),"contentious",-2),"contort",-3),"contortions",-3),"contradict",-3),"contradiction",-3),"contradictory",-3),"contrariness",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"contravene",-3),"contrive",-3),"contrived",-3),"controversial",-3),"controversy",-2),"convoluted",-2),"corrode",-2),"corrosion",-3),"corrosions",-3),"corrosive",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"corrupt",-4),"corrupted",-4),"corrupting",-4),"corruption",-4),"corrupts",-4),"corruptted",-4),"costlier",-3),"costly",-3),"counter-productive",-3),"counterproductive",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"coupists",-1),"covetous",-2),"coward",-4),"cowardly",-5),"crabby",-2),"crack",-2),"cracked",-2),"cracks",-1),"craftily",-1),"craftly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"crafty",-1),"cramp",-1),"cramped",-1),"cramping",-1),"cranky",-1),"crap",-4),"crappy",-5),"craps",-3),"crash",-3),"crashed",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"crashes",-2),"crashing",-1),"crass",-1),"craven",-1),"cravenly",-1),"craze",-1),"crazily",-1),"craziness",-1),"crazy",-2),"creak",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"creaking",-3),"creaks",-1),"credulous",-1),"creep",-1),"creeping",-1),"creeps",-1),"creepy",-1),"crept",-1),"crime",-4),"criminal",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"cringe",-2),"cringed",-2),"cringes",-2),"cripple",-2),"crippled",-3),"cripples",-3),"crippling",-4),"crisis",-4),"critic",-4),"critical",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"criticism",-1),"criticisms",-1),"criticize",-1),"criticized",-1),"criticizing",-1),"critics",-3),"cronyism",-3),"crook",-3),"crooked",-3),"crooks",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"crowded",-1),"crowdedness",-1),"crude",-1),"cruel",-5),"crueler",-5),"cruelest",-5),"cruelly",-5),"cruelness",-4),"cruelties",-4),"cruelty",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"crumble",-3),"crumbling",-3),"crummy",-2),"crumple",-2),"crumpled",-2),"crumples",-1),"crush",-3),"crushed",-1),"crushing",-1),"cry",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"culpable",-1),"culprit",-3),"cumbersome",-1),"cunt",-5),"cunts",-5),"cuplrit",-1),"curse",-3),"cursed",-3),"curses",-4),"curt",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"cuss",-3),"cussed",-3),"cutthroat",-2),"cynical",-3),"cynicism",-3),"d*mn",-3),"damage",-3),"damaged",-3),"damages",-3),"damaging",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"damn",-4),"damnable",-5),"damnably",-5),"damnation",-5),"damned",-5),"damning",-5),"damper",-2),"danger",-2),"dangerous",-2),"dangerousness",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"dark",-3),"darken",-2),"darkened",-2),"darker",-2),"darkness",-2),"dastard",-1),"dastardly",-1),"daunt",-1),"daunting",-2),"dauntingly",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"dawdle",-1),"daze",-1),"dazed",-1),"dead",-5),"deadbeat",-5),"deadlock",-3),"deadly",-4),"deadweight",-3),"deaf",-3),"dearth",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"death",-4),"debacle",-1),"debase",-1),"debasement",-1),"debaser",-1),"debatable",-1),"debauch",-1),"debaucher",-1),"debauchery",-1),"debilitate",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"debilitating",-1),"debility",-1),"debt",-2),"debts",-2),"decadence",-1),"decadent",-1),"decay",-4),"decayed",-4),"deceit",-3),"deceitful",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"deceitfully",-3),"deceitfulness",-3),"deceive",-3),"deceiver",-3),"deceivers",-3),"deceiving",-3),"deception",-4),"deceptive",-4),"deceptively",-5),"declaim",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"decline",-2),"declines",-2),"declining",-2),"decrement",-2),"decrepit",-2),"decrepitude",-1),"decry",-1),"defamation",-3),"defamations",-3),"defamatory",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"defame",-3),"defect",-4),"defective",-4),"defects",-4),"defensive",-3),"defiance",-3),"defiant",-3),"defiantly",-3),"deficiencies",-3),"deficiency",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"deficient",-3),"defile",-3),"defiler",-3),"deform",-3),"deformed",-3),"defrauding",-4),"defunct",-2),"defy",-1),"degenerate",-2),"degenerately",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"degeneration",-2),"degradation",-3),"degrade",-3),"degrading",-3),"degradingly",-3),"dehumanization",-2),"dehumanize",-2),"deign",-1),"deject",-1),"dejected",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"dejectedly",-1),"dejection",-1),"delay",-1),"delayed",-1),"delaying",-1),"delays",-1),"delinquency",-1),"delinquent",-1),"delirious",-1),"delirium",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"delude",-1),"deluded",-1),"deluge",-1),"delusion",-1),"delusional",-1),"delusions",-1),"demean",-2),"demeaning",-1),"demise",-3),"demolish",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"demolisher",-3),"demon",-4),"demonic",-4),"demonize",-4),"demonized",-4),"demonizes",-4),"demonizing",-4),"demoralize",-1),"demoralizing",-1),"demoralizingly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"denial",-2),"denied",-2),"denies",-1),"denigrate",-1),"denounce",-1),"dense",-1),"dent",-1),"dented",-1),"dents",-1),"denunciate",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"denunciation",-1),"denunciations",-1),"deny",-2),"denying",-2),"deplete",-1),"deplorable",-1),"deplorably",-1),"deplore",-1),"deploring",-1),"deploringly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"deprave",-1),"depraved",-1),"depravedly",-1),"deprecate",-1),"depress",-4),"depressed",-3),"depressing",-4),"depressingly",-3),"depression",-4),"depressions",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"deprive",-3),"deprived",-3),"deride",-1),"derision",-1),"derisive",-1),"derisively",-1),"derisiveness",-1),"derogatory",-3),"desecrate",-1),"desert",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"desertion",-1),"desiccate",-1),"desiccated",-1),"desititute",-1),"desolate",-1),"desolately",-1),"desolation",-1),"despair",-1),"despairing",-1),"despairingly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"desperate",-2),"desperately",-2),"desperation",-1),"despicable",-2),"despicably",-1),"despise",-1),"despised",-1),"despoil",-1),"despoiler",-1),"despondence",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"despondency",-1),"despondent",-1),"despondently",-1),"despot",-1),"despotic",-1),"despotism",-1),"destabilisation",-1),"destains",-1),"destitute",-1),"destitution",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"destroy",-5),"destroyer",-5),"destruction",-5),"destructive",-5),"desultory",-1),"deter",-2),"deteriorate",-5),"deteriorating",-5),"deterioration",-5),"deterrent",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"detest",-3),"detestable",-3),"detestably",-3),"detested",-3),"detesting",-3),"detests",-3),"detract",-3),"detracted",-2),"detracting",-2),"detraction",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"detracts",-2),"detriment",-2),"detrimental",-2),"devastate",-5),"devastated",-5),"devastates",-5),"devastating",-5),"devastatingly",-5),"devastation",-5),"deviate",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"deviation",-2),"devil",-2),"devilish",-2),"devilishly",-2),"devilment",-2),"devilry",-2),"devious",-3),"deviously",-3),"deviousness",-3),"devoid",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"diabolic",-2),"diabolical",-2),"diabolically",-2),"diametrically",-1),"diappointed",-5),"diatribe",-1),"diatribes",-1),"dick",-5),"dictator",-4),"dictatorial",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"die",-5),"die-hard",-2),"died",-4),"dies",-3),"difficult",-2),"difficulties",-2),"difficulty",-2),"diffidence",-1),"dilapidated",-1),"dilemma",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"dilly-dally",-1),"dim",-1),"dimmer",-1),"din",-1),"ding",-1),"dings",-1),"dinky",-1),"dire",-1),"direly",-1),"direness",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"dirt",-2),"dirtbag",-1),"dirtbags",-1),"dirts",-2),"dirty",-2),"disable",-1),"disabled",-1),"disaccord",-1),"disadvantage",-1),"disadvantaged",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disadvantageous",-1),"disadvantages",-1),"disaffect",-1),"disaffected",-1),"disaffirm",-1),"disagree",-2),"disagreeable",-1),"disagreeably",-1),"disagreed",-2),"disagreeing",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disagreement",-1),"disagrees",-1),"disallow",-4),"disapointed",-5),"disapointing",-5),"disapointment",-5),"disappoint",-5),"disappointed",-4),"disappointing",-4),"disappointingly",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disappointment",-4),"disappointments",-4),"disappoints",-3),"disapprobation",-1),"disapproval",-2),"disapprove",-2),"disapproving",-2),"disarm",-2),"disarray",-1),"disaster",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disasterous",-2),"disastrous",-2),"disastrously",-1),"disavow",-3),"disavowal",-3),"disbelief",-2),"disbelieve",-2),"disbeliever",-2),"disclaim",-1),"discombobulate",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"discomfit",-3),"discomfititure",-1),"discomfort",-1),"discompose",-1),"disconcert",-1),"disconcerted",-1),"disconcerting",-1),"disconcertingly",-1),"disconsolate",-1),"disconsolately",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disconsolation",-1),"discontent",-2),"discontented",-2),"discontentedly",-2),"discontinued",-2),"discontinuity",-1),"discontinuous",-1),"discord",-1),"discordance",-1),"discordant",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"discountenance",-1),"discourage",-1),"discouragement",-1),"discouraging",-1),"discouragingly",-1),"discourteous",-1),"discourteously",-1),"discoutinous",-1),"discredit",-1),"discrepant",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"discriminate",-2),"discrimination",-2),"discriminatory",-2),"disdain",-1),"disdained",-1),"disdainful",-1),"disdainfully",-1),"disfavor",-1),"disgrace",-5),"disgraced",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disgraceful",-5),"disgracefully",-5),"disgruntle",-3),"disgruntled",-3),"disgust",-5),"disgusted",-5),"disgustedly",-5),"disgustful",-5),"disgustfully",-5),"disgusting",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disgustingly",-5),"dishearten",-4),"disheartening",-4),"dishearteningly",-4),"dishonest",-3),"dishonestly",-3),"dishonesty",-3),"dishonor",-3),"dishonorable",-3),"dishonorablely",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disillusion",-2),"disillusioned",-2),"disillusionment",-2),"disillusions",-2),"disinclination",-2),"disinclined",-2),"disingenuous",-2),"disingenuously",-2),"disintegrate",-2),"disintegrated",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disintegrates",-2),"disintegration",-2),"disinterest",-2),"disinterested",-2),"dislike",-3),"disliked",-3),"dislikes",-3),"disliking",-3),"dislocated",-3),"disloyal",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disloyalty",-3),"dismal",-1),"dismally",-1),"dismalness",-1),"dismay",-3),"dismayed",-3),"dismaying",-3),"dismayingly",-3),"dismissive",-2),"dismissively",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disobedience",-2),"disobedient",-2),"disobey",-1),"disoobedient",-1),"disorder",-1),"disordered",-1),"disorderly",-1),"disorganized",-1),"disorient",-1),"disoriented",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disown",-1),"disparage",-1),"disparaging",-1),"disparagingly",-1),"dispensable",-1),"dispirit",-1),"dispirited",-1),"dispiritedly",-1),"dispiriting",-1),"displace",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"displaced",-1),"displease",-1),"displeased",-1),"displeasing",-1),"displeasure",-1),"disproportionate",-1),"disprove",-2),"disputable",-1),"dispute",-3),"disputed",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disquiet",-1),"disquieting",-1),"disquietingly",-1),"disquietude",-1),"disregard",-3),"disregardful",-1),"disreputable",-1),"disrepute",-2),"disrespect",-1),"disrespectable",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disrespectablity",-1),"disrespectful",-1),"disrespectfully",-1),"disrespectfulness",-1),"disrespecting",-1),"disrupt",-4),"disruption",-4),"disruptive",-4),"diss",-1),"dissapointed",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"dissappointed",-5),"dissappointing",-5),"dissatisfaction",-4),"dissatisfactory",-4),"dissatisfied",-4),"dissatisfies",-4),"dissatisfy",-4),"dissatisfying",-4),"dissed",-1),"dissemble",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"dissembler",-1),"dissension",-1),"dissent",-1),"dissenter",-1),"dissention",-1),"disservice",-1),"disses",-1),"dissidence",-1),"dissident",-1),"dissidents",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"dissing",-1),"dissocial",-1),"dissolute",-1),"dissolution",-1),"dissonance",-1),"dissonant",-1),"dissonantly",-1),"dissuade",-1),"dissuasive",-1),"distains",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"distaste",-1),"distasteful",-1),"distastefully",-1),"distort",-1),"distorted",-1),"distortion",-1),"distorts",-1),"distract",-1),"distracting",-1),"distraction",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"distraught",-1),"distraughtly",-1),"distraughtness",-1),"distress",-2),"distressed",-2),"distressing",-2),"distressingly",-1),"distrust",-3),"distrustful",-3),"distrusting",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"disturb",-3),"disturbance",-3),"disturbed",-3),"disturbing",-3),"disturbingly",-3),"disunity",-2),"disvalue",-2),"divergent",-2),"divisive",-2),"divisively",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"divisiveness",-2),"dizzing",-2),"dizzingly",-2),"dizzy",-2),"doddering",-2),"dodgey",-2),"dogged",-2),"doggedly",-2),"dogmatic",-2),"doldrums",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"domineer",-2),"domineering",-2),"donside",-2),"doom",-4),"doomed",-4),"doomsday",-3),"dope",-1),"doubt",-2),"doubtful",-1),"doubtfully",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"doubts",-2),"douchbag",-5),"douchebag",-5),"douchebags",-5),"downbeat",-3),"downcast",-1),"downer",-1),"downfall",-1),"downfallen",-1),"downgrade",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"downhearted",-1),"downheartedly",-1),"downhill",-1),"downside",-3),"downsides",-1),"downturn",-1),"downturns",-1),"drab",-1),"draconian",-1),"draconic",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"drag",-2),"dragged",-1),"dragging",-1),"dragoon",-1),"drags",-1),"drain",-3),"drained",-3),"draining",-3),"drains",-1),"drastic",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"drastically",-1),"drawback",-1),"drawbacks",-1),"dread",-4),"dreadful",-4),"dreadfully",-4),"dreadfulness",-4),"dreary",-2),"dripped",-2),"dripping",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"drippy",-2),"drips",-2),"drones",-2),"droop",-2),"droops",-2),"drop-out",-3),"drop-outs",-3),"dropout",-3),"dropouts",-2),"drought",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"drowning",-3),"drunk",-3),"drunkard",-1),"drunken",-1),"dubious",-1),"dubiously",-1),"dubitable",-1),"dud",-1),"dull",-1),"dullard",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"dumb",-2),"dumbfound",-2),"dump",-2),"dumped",-3),"dumping",-3),"dumps",-3),"dunce",-1),"dungeon",-1),"dungeons",-1),"dupe",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"dust",-2),"dusty",-2),"dwindling",-1),"dying",-1),"earsplitting",-1),"eccentric",-1),"eccentricity",-1),"effigy",-1),"effrontery",-1),"egocentric",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"egomania",-3),"egotism",-1),"egotistical",-1),"egotistically",-1),"egregious",-1),"egregiously",-1),"election-rigger",-1),"elimination",-1),"emaciated",-1),"emasculate",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"embarrass",-1),"embarrassing",-1),"embarrassingly",-1),"embarrassment",-1),"embattled",-1),"embroil",-1),"embroiled",-1),"embroilment",-1),"emergency",-1),"emphatic",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"emphatically",-1),"emptiness",-1),"encroach",-1),"encroachment",-1),"endanger",-3),"enemies",-2),"enemy",-2),"enervate",-1),"enfeeble",-1),"enflame",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"engulf",-1),"enjoin",-1),"enmity",-1),"enrage",-1),"enraged",-1),"enraging",-1),"enslave",-3),"entangle",-1),"entanglement",-1),"entrap",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"entrapment",-1),"envious",-1),"enviously",-1),"enviousness",-1),"epidemic",-4),"equivocal",-1),"erase",-3),"erode",-5),"erodes",-5),"erosion",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"err",-1),"errant",-1),"erratic",-1),"erratically",-1),"erroneous",-1),"erroneously",-1),"error",-3),"errors",-3),"eruptions",-1),"escapade",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"eschew",-1),"estranged",-1),"evade",-1),"evasion",-1),"evasive",-1),"evil",-1),"evildoer",-1),"evils",-1),"eviscerate",-1),"exacerbate",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"exagerate",-3),"exagerated",-3),"exagerates",-3),"exaggerate",-3),"exaggeration",-3),"exasperate",-3),"exasperated",-3),"exasperating",-3),"exasperatingly",-3),"exasperation",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"excessive",-3),"excessively",-2),"exclusion",-2),"excoriate",-2),"excruciating",-2),"excruciatingly",-1),"excuse",-2),"excuses",-2),"execrate",-3),"exhaust",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"exhausted",-3),"exhaustion",-3),"exhausts",-3),"exhorbitant",-2),"exhort",-2),"exile",-2),"exorbitant",-2),"exorbitantance",-2),"exorbitantly",-2),"expel",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"expensive",-4),"expire",-4),"expired",-4),"explode",-4),"exploit",-4),"exploitation",-3),"explosive",-3),"expropriate",-3),"expropriation",-3),"expulse",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"expunge",-1),"exterminate",-3),"extermination",-3),"extinguish",-1),"extort",-5),"extortion",-5),"extraneous",-1),"extravagance",-2),"extravagant",-1),"extravagantly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"extremism",-3),"extremist",-5),"extremists",-5),"eyesore",-3),"f**k",-5),"fabricate",-3),"fabrication",-1),"facetious",-1),"facetiously",-1),"fail",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"failed",-4),"failing",-4),"fails",-4),"failure",-4),"failures",-4),"faint",-2),"fainthearted",-2),"faithless",-2),"fake",-2),"fall",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"fallacies",-2),"fallacious",-1),"fallaciously",-1),"fallaciousness",-1),"fallacy",-3),"fallen",-3),"falling",-3),"fallout",-4),"falls",-3),"false",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"falsehood",-5),"falsely",-4),"falsify",-4),"falter",-1),"faltered",-1),"famine",-5),"famished",-1),"fanatic",-3),"fanatical",-3),"fanatically",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"fanaticism",-2),"fanatics",-2),"fanciful",-2),"far-fetched",-1),"farce",-1),"farcical",-1),"farcical-yet-provocative",-1),"farcically",-1),"farfetched",-1),"fascism",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"fascist",-5),"fastidious",-1),"fastidiously",-1),"fastuous",-1),"fat",-1),"fat-cat",-1),"fat-cats",-1),"fatal",-4),"fatalistic",-4),"fatalistically",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"fatally",-4),"fatcat",-1),"fatcats",-1),"fateful",-3),"fatefully",-3),"fathomless",-1),"fatigue",-4),"fatigued",-4),"fatique",-4),"fatty",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"fatuity",-2),"fatuous",-2),"fatuously",-2),"fault",-4),"faults",-4),"faulty",-4),"fawningly",-1),"faze",-1),"fear",-5),"fearful",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"fearfully",-5),"fears",-5),"fearsome",-5),"feckless",-1),"feeble",-1),"feeblely",-1),"feebleminded",-1),"feign",-1),"feint",-1),"fell",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"felon",-1),"felonious",-1),"ferociously",-1),"ferocity",-1),"fetid",-1),"fever",-3),"feverish",-1),"fevers",-1),"fiasco",-1),"fib",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"fibber",-1),"fickle",-1),"fiction",-3),"fictional",-3),"fictitious",-3),"fidget",-1),"fidgety",-1),"fiend",-1),"fiendish",-1),"fierce",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"figurehead",-1),"filth",-2),"filthy",-2),"finagle",-2),"finicky",-2),"fissures",-2),"fist",-1),"flabbergast",-1),"flabbergasted",-1),"flagging",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"flagrant",-1),"flagrantly",-1),"flair",-1),"flairs",-1),"flak",-1),"flake",-1),"flakey",-1),"flakieness",-1),"flaking",-1),"flaky",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"flare",-1),"flares",-1),"flareup",-1),"flareups",-1),"flat-out",-1),"flaunt",-1),"flaw",-5),"flawed",-5),"flaws",-5),"flee",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"fleed",-2),"fleeing",-2),"fleer",-2),"flees",-2),"fleeting",-3),"flicering",-3),"flicker",-3),"flickering",-3),"flickers",-2),"flighty",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"flimflam",-2),"flimsy",-3),"flirt",-3),"flirty",-3),"floored",-3),"flounder",-3),"floundering",-3),"flout",-3),"fluster",-2),"foe",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"fool",-3),"fooled",-3),"foolhardy",-3),"foolish",-3),"foolishly",-3),"foolishness",-3),"forbid",-3),"forbidden",-2),"forbidding",-2),"forceful",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"foreboding",-1),"forebodingly",-1),"forfeit",-1),"forged",-1),"forgetful",-1),"forgetfully",-1),"forgetfulness",-1),"forlorn",-1),"forlornly",-1),"forsake",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"forsaken",-2),"forswear",-2),"foul",-5),"foully",-5),"foulness",-5),"fractious",-3),"fractiously",-3),"fracture",-3),"fragile",-3),"fragmented",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"frail",-3),"frantic",-3),"frantically",-3),"franticly",-3),"fraud",-3),"fraudulent",-3),"fraught",-3),"frazzle",-2),"frazzled",-2),"freak",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"freaking",-3),"freakish",-3),"freakishly",-3),"freaks",-2),"freeze",-2),"freezes",-2),"freezing",-2),"frenetic",-2),"frenetically",-3),"frenzied",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"frenzy",-3),"fret",-3),"fretful",-3),"frets",-3),"friction",-3),"frictions",-3),"fried",-3),"friggin",-3),"frigging",-3),"fright",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"frighten",-2),"frightening",-2),"frighteningly",-2),"frightful",-2),"frightfully",-2),"frigid",-3),"frost",-3),"frown",-3),"froze",-3),"frozen",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"fruitless",-2),"fruitlessly",-2),"frustrate",-2),"frustrated",-5),"frustrates",-5),"frustrating",-5),"frustratingly",-5),"frustration",-5),"frustrations",-5),"fuck",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"fucking",-5),"fudge",-2),"fugitive",-2),"full-blown",-3),"fulminate",-3),"fumble",-3),"fume",-3),"fumes",-3),"fundamentalism",-2),"funky",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"funnily",-2),"funny",-1),"furious",-2),"furiously",-2),"furor",-2),"fury",-3),"fuss",-3),"fussy",-3),"fustigate",-3),"fusty",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"futile",-3),"futilely",-3),"futility",-3),"fuzzy",-3),"gabble",-2),"gaff",-2),"gaffe",-2),"gainsay",-2),"gainsayer",-2),"gall",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"galling",-2),"gallingly",-2),"galls",-2),"gangster",-5),"gape",-5),"garbage",-5),"garish",-1),"gasp",-1),"gauche",-1),"gaudy",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"gawk",-1),"gawky",-3),"geezer",-1),"genocide",-3),"get-rich",-1),"ghastly",-1),"ghetto",-1),"ghosting",-1),"gibber",-1),"gibberish",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"gibe",-1),"giddy",-1),"gimmick",-3),"gimmicked",-3),"gimmicking",-3),"gimmicks",-3),"gimmicky",-3),"glare",-1),"glaringly",-1),"glib",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"glibly",-1),"glitch",-1),"glitches",-1),"gloatingly",-1),"gloom",-1),"gloomy",-1),"glower",-1),"glum",-1),"glut",-1),"gnawing",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"goad",-1),"goading",-1),"god-awful",-1),"goof",-1),"goofy",-1),"goon",-1),"gossip",-1),"graceless",-1),"gracelessly",-1),"graft",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"grainy",-1),"grapple",-1),"grate",-1),"grating",-1),"gravely",-1),"greasy",-1),"greed",-4),"greedy",-4),"grief",-5),"grievance",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"grievances",-5),"grieve",-5),"grieving",-5),"grievous",-5),"grievously",-5),"grim",-2),"grimace",-2),"grind",-1),"gripe",-1),"gripes",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"grisly",-1),"gritty",-1),"gross",-2),"grossly",-2),"grotesque",-1),"grouch",-1),"grouchy",-1),"groundless",-1),"grouse",-1),"growl",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"grudge",-4),"grudges",-4),"grudging",-4),"grudgingly",-4),"gruesome",-5),"gruesomely",-5),"gruff",-1),"grumble",-1),"grumpier",-1),"grumpiest",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"grumpily",-1),"grumpish",-1),"grumpy",-1),"guile",-1),"guilt",-3),"guiltily",-3),"guilty",-3),"gullible",-3),"gutless",-1),"gutter",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"hack",-2),"hacks",-2),"haggard",-2),"haggle",-2),"hairloss",-1),"halfhearted",-1),"halfheartedly",-1),"hallucinate",-1),"hallucination",-1),"hamper",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"hampered",-1),"handicapped",-1),"hang",-1),"hangs",-1),"haphazard",-1),"hapless",-1),"harangue",-1),"harass",-3),"harassed",-3),"harasses",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"harassment",-3),"harboring",-1),"harbors",-1),"hard",-4),"hard-hit",-3),"hard-line",-3),"hard-liner",-3),"hardball",-3),"harden",-3),"hardened",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"hardheaded",-3),"hardhearted",-3),"hardliner",-3),"hardliners",-3),"hardship",-5),"hardships",-5),"harm",-5),"harmed",-5),"harmful",-5),"harms",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"harpy",-1),"harridan",-1),"harried",-1),"harrow",-1),"harsh",-1),"harshly",-1),"hasseling",-1),"hassle",-3),"hassled",-3),"hassles",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"haste",-5),"hastily",-1),"hasty",-1),"hate",-5),"hated",-5),"hateful",-5),"hatefully",-5),"hatefulness",-5),"hater",-5),"haters",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"hates",-5),"hating",-5),"hatred",-5),"haughtily",-1),"haughty",-1),"haunt",-3),"haunting",-3),"havoc",-1),"hawkish",-1),"haywire",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"hazard",-3),"hazardous",-3),"haze",-1),"hazy",-1),"head-aches",-1),"headache",-2),"headaches",-2),"heartbreaker",-4),"heartbreaking",-4),"heartbreakingly",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"heartless",-4),"heathen",-1),"heavy-handed",-2),"heavyhearted",-2),"heck",-1),"heckle",-1),"heckled",-1),"heckles",-1),"hectic",-1),"hedge",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"hedonistic",-1),"heedless",-1),"hefty",-1),"hegemonism",-1),"hegemonistic",-1),"hegemony",-1),"heinous",-1),"hell",-2),"hell-bent",-2),"hellion",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"hells",-1),"helpless",-3),"helplessly",-3),"helplessness",-1),"heresy",-1),"heretic",-1),"heretical",-1),"hesitant",-1),"hestitant",-1),"hideous",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"hideously",-1),"hideousness",-1),"high-priced",-1),"hiliarious",-1),"hinder",-1),"hindrance",-1),"hiss",-1),"hissed",-1),"hissing",-1),"ho-hum",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"hoard",-1),"hoax",-5),"hobble",-5),"hogs",-1),"hollow",-1),"hoodium",-1),"hoodwink",-1),"hooligan",-1),"hopeless",-2),"hopelessly",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"hopelessness",-2),"horde",-2),"horrendous",-1),"horrendously",-1),"horrible",-1),"horrid",-1),"horrific",-1),"horrified",-1),"horrifies",-1),"horrify",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"horrifying",-1),"horrifys",-1),"hostage",-1),"hostile",-1),"hostilities",-1),"hostility",-1),"hotbeds",-1),"hothead",-1),"hotheaded",-1),"hothouse",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"hubris",-1),"huckster",-1),"hum",-1),"humid",-1),"humiliate",-3),"humiliating",-3),"humiliation",-3),"humming",-1),"hung",-3),"hurt",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"hurted",-5),"hurtful",-5),"hurting",-5),"hurts",-4),"hustler",-1),"hype",-1),"hypocricy",-3),"hypocrisy",-3),"hypocrite",-3),"hypocrites",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"hypocritical",-3),"hypocritically",-3),"hysteria",-2),"hysteric",-2),"hysterical",-2),"hysterically",-2),"hysterics",-2),"idiocies",-1),"idiocy",-1),"idiot",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"idiotic",-3),"idiotically",-3),"idiots",-3),"idle",-1),"ignoble",-1),"ignominious",-3),"ignominiously",-3),"ignominy",-3),"ignorance",-5),"ignorant",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"ignore",-5),"ill-advised",-2),"ill-conceived",-2),"ill-defined",-2),"ill-designed",-2),"ill-fated",-2),"ill-favored",-2),"ill-formed",-2),"ill-mannered",-2),"ill-natured",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"ill-sorted",-2),"ill-tempered",-2),"ill-treated",-2),"ill-treatment",-2),"ill-usage",-2),"ill-used",-2),"illegal",-4),"illegally",-4),"illegitimate",-1),"illicit",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"illiterate",-1),"illness",-1),"illogic",-1),"illogical",-1),"illogically",-1),"illusion",-1),"illusions",-1),"illusory",-1),"imaginary",-1),"imbalance",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"imbecile",-1),"imbroglio",-1),"immaterial",-1),"immature",-1),"imminence",-1),"imminently",-1),"immobilized",-1),"immoderate",-1),"immoderately",-1),"immodest",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"immoral",-1),"immorality",-1),"immorally",-1),"immovable",-1),"impair",-3),"impaired",-1),"impasse",-1),"impatience",-2),"impatient",-1),"impatiently",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"impeach",-1),"impedance",-1),"impede",-1),"impediment",-1),"impending",-1),"impenitent",-1),"imperfect",-1),"imperfection",-1),"imperfections",-1),"imperfectly",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"imperialist",-2),"imperil",-2),"imperious",-1),"imperiously",-1),"impermissible",-1),"impersonal",-1),"impertinent",-1),"impetuous",-1),"impetuously",-1),"impiety",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"impinge",-1),"impious",-1),"implacable",-1),"implausible",-1),"implausibly",-1),"implicate",-1),"implication",-1),"implode",-2),"impolite",-1),"impolitely",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"impolitic",-1),"importunate",-1),"importune",-1),"impose",-1),"imposers",-1),"imposing",-1),"imposition",-1),"impossible",-3),"impossiblity",-3),"impossibly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"impotent",-2),"impoverish",-1),"impoverished",-1),"impractical",-1),"imprecate",-1),"imprecise",-2),"imprecisely",-1),"imprecision",-1),"imprison",-1),"imprisonment",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"improbability",-1),"improbable",-1),"improbably",-1),"improper",-1),"improperly",-1),"impropriety",-1),"imprudence",-1),"imprudent",-1),"impudence",-1),"impudent",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"impudently",-1),"impugn",-1),"impulsive",-3),"impulsively",-3),"impunity",-1),"impure",-1),"impurity",-1),"inability",-1),"inaccuracies",-1),"inaccuracy",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"inaccurate",-1),"inaccurately",-1),"inaction",-1),"inactive",-1),"inadequacy",-1),"inadequate",-1),"inadequately",-1),"inadverent",-1),"inadverently",-1),"inadvisable",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"inadvisably",-1),"inane",-1),"inanely",-1),"inappropriate",-1),"inappropriately",-1),"inapt",-1),"inaptitude",-1),"inarticulate",-1),"inattentive",-1),"inaudible",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"incapable",-1),"incapably",-1),"incautious",-1),"incendiary",-1),"incense",-2),"incessant",-1),"incessantly",-1),"incite",-1),"incitement",-1),"incivility",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"inclement",-1),"incognizant",-1),"incoherence",-1),"incoherent",-1),"incoherently",-1),"incommensurate",-1),"incomparable",-1),"incomparably",-1),"incompatability",-2),"incompatibility",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"incompatible",-2),"incompetence",-2),"incompetent",-2),"incompetently",-2),"incomplete",-3),"incompliant",-3),"incomprehensible",-3),"incomprehension",-3),"inconceivable",-3),"inconceivably",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"incongruous",-3),"incongruously",-3),"inconsequent",-3),"inconsequential",-1),"inconsequentially",-1),"inconsequently",-1),"inconsiderate",-1),"inconsiderately",-1),"inconsistence",-4),"inconsistencies",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"inconsistency",-1),"inconsistent",-4),"inconsolable",-1),"inconsolably",-1),"inconstant",-1),"inconvenience",-1),"inconveniently",-1),"incorrect",-5),"incorrectly",-1),"incorrigible",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"incorrigibly",-1),"incredulous",-1),"incredulously",-1),"inculcate",-1),"indecency",-5),"indecent",-5),"indecently",-5),"indecision",-2),"indecisive",-2),"indecisively",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"indecorum",-1),"indefensible",-1),"indelicate",-1),"indeterminable",-1),"indeterminably",-1),"indeterminate",-1),"indifference",-1),"indifferent",-1),"indigent",-1),"indignant",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"indignantly",-1),"indignation",-1),"indignity",-1),"indiscernible",-1),"indiscreet",-1),"indiscreetly",-1),"indiscretion",-1),"indiscriminate",-2),"indiscriminately",-2),"indiscriminating",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"indistinguishable",-2),"indoctrinate",-1),"indoctrination",-1),"indolent",-1),"indulge",-1),"ineffective",-1),"ineffectively",-1),"ineffectiveness",-1),"ineffectual",-1),"ineffectually",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"ineffectualness",-1),"inefficacious",-1),"inefficacy",-1),"inefficiency",-1),"inefficient",-1),"inefficiently",-1),"inelegance",-1),"inelegant",-1),"ineligible",-1),"ineloquent",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"ineloquently",-1),"inept",-1),"ineptitude",-1),"ineptly",-1),"inequalities",-1),"inequality",-1),"inequitable",-1),"inequitably",-1),"inequities",-1),"inescapable",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"inescapably",-1),"inessential",-1),"inevitable",-1),"inevitably",-1),"inexcusable",-1),"inexcusably",-1),"inexorable",-1),"inexorably",-1),"inexperience",-1),"inexperienced",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"inexpert",-1),"inexpertly",-1),"inexpiable",-1),"inexplainable",-1),"inextricable",-1),"inextricably",-1),"infamous",-1),"infamously",-1),"infamy",-1),"infected",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"infection",-1),"infections",-1),"inferior",-1),"inferiority",-1),"infernal",-1),"infest",-1),"infested",-1),"infidel",-1),"infidels",-1),"infiltrator",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"infiltrators",-1),"infirm",-1),"inflame",-1),"inflammation",-1),"inflammatory",-1),"inflammed",-1),"inflated",-1),"inflationary",-1),"inflexible",-1),"inflict",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"infraction",-1),"infringe",-1),"infringement",-1),"infringements",-1),"infuriate",-2),"infuriated",-2),"infuriating",-2),"infuriatingly",-2),"inglorious",-2),"ingrate",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"ingratitude",-2),"inhibit",-2),"inhibition",-2),"inhospitable",-2),"inhospitality",-2),"inhuman",-1),"inhumane",-3),"inhumanity",-3),"inimical",-1),"inimically",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"iniquitous",-1),"iniquity",-1),"injudicious",-1),"injure",-3),"injurious",-3),"injury",-3),"injustice",-3),"injustices",-1),"innuendo",-1),"inoperable",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"inopportune",-1),"inordinate",-1),"inordinately",-1),"insane",-3),"insanely",-3),"insanity",-3),"insatiable",-1),"insecure",-1),"insecurity",-1),"insensible",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"insensitive",-2),"insensitively",-3),"insensitivity",-3),"insidious",-3),"insidiously",-2),"insignificance",-3),"insignificant",-3),"insignificantly",-3),"insincere",-4),"insincerely",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"insincerity",-4),"insinuate",-2),"insinuating",-1),"insinuation",-1),"insociable",-1),"insolence",-1),"insolent",-1),"insolently",-1),"insolvent",-1),"insouciance",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"instability",-1),"instable",-2),"instigate",-2),"instigator",-2),"instigators",-2),"insubordinate",-4),"insubstantial",-4),"insubstantially",-4),"insufferable",-2),"insufferably",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"insufficiency",-2),"insufficient",-2),"insufficiently",-2),"insular",-1),"insult",-5),"insulted",-1),"insulting",-1),"insultingly",-1),"insults",-1),"insupportable",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"insupportably",-1),"insurmountable",-1),"insurmountably",-1),"insurrection",-1),"intefere",-1),"inteferes",-1),"intense",-1),"interfere",-1),"interference",-1),"interferes",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"intermittent",-1),"interrupt",-1),"interruption",-1),"interruptions",-1),"intimidate",-1),"intimidating",-1),"intimidatingly",-1),"intimidation",-1),"intolerable",-1),"intolerablely",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"intolerance",-1),"intoxicate",-1),"intractable",-1),"intransigence",-1),"intransigent",-1),"intrude",-4),"intrusion",-4),"intrusive",-1),"inundate",-1),"inundated",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"invader",-1),"invalid",-5),"invalidate",-1),"invalidity",-1),"invasive",-1),"invective",-1),"inveigle",-1),"invidious",-1),"invidiously",-1),"invidiousness",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"invisible",-4),"involuntarily",-1),"involuntary",-1),"irascible",-1),"irate",-1),"irately",-1),"ire",-1),"irk",-1),"irked",-1),"irking",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"irks",-1),"irksome",-1),"irksomely",-1),"irksomeness",-1),"irksomenesses",-1),"ironic",-3),"ironical",-3),"ironically",-3),"ironies",-3),"irony",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"irragularity",-1),"irrational",-1),"irrationalities",-1),"irrationality",-1),"irrationally",-1),"irrationals",-1),"irreconcilable",-1),"irrecoverable",-1),"irrecoverableness",-1),"irrecoverablenesses",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"irrecoverably",-1),"irredeemable",-1),"irredeemably",-1),"irreformable",-1),"irregular",-2),"irregularity",-2),"irrelevance",-2),"irrelevant",-2),"irreparable",-2),"irreplacible",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"irrepressible",-2),"irresolute",-2),"irresolvable",-2),"irresponsible",-2),"irresponsibly",-1),"irretating",-1),"irretrievable",-1),"irreversible",-1),"irritable",-1),"irritably",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"irritant",-1),"irritate",-1),"irritated",-1),"irritating",-1),"irritation",-1),"irritations",-1),"isolate",-2),"isolated",-2),"isolation",-2),"issue",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"issues",-1),"itch",-1),"itching",-3),"itchy",-1),"jabber",-1),"jaded",-1),"jagged",-1),"jam",-1),"jarring",-1),"jaundiced",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"jealous",-2),"jealously",-2),"jealousness",-2),"jealousy",-2),"jeer",-1),"jeering",-1),"jeeringly",-1),"jeers",-1),"jeopardize",-1),"jeopardy",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"jerk",-1),"jerky",-1),"jitter",-1),"jitters",-1),"jittery",-1),"job-killing",-1),"jobless",-3),"joke",-1),"joker",-1),"jolt",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"judder",-1),"juddering",-1),"judders",-1),"jumpy",-1),"junk",-3),"junky",-1),"junkyard",-1),"jutter",-1),"jutters",-1),"kaput",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"kill",-5),"killed",-5),"killer",-4),"killing",-4),"killjoy",-4),"kills",-4),"knave",-1),"knife",-1),"knock",-2),"knotted",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"kook",-1),"kooky",-1),"lack",-3),"lackadaisical",-1),"lacked",-1),"lackey",-1),"lackeys",-1),"lacking",-1),"lackluster",-1),"lacks",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"laconic",-1),"lag",-1),"lagged",-1),"lagging",-1),"laggy",-1),"lags",-1),"laid-off",-1),"lambast",-1),"lambaste",-1),"lame",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"lame-duck",-1),"lament",-1),"lamentable",-1),"lamentably",-1),"languid",-1),"languish",-1),"languor",-1),"languorous",-1),"languorously",-1),"lanky",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"lapse",-1),"lapsed",-1),"lapses",-1),"lascivious",-1),"last-ditch",-1),"latency",-1),"laughable",-1),"laughably",-1),"laughingstock",-1),"lawbreaker",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"lawbreaking",-1),"lawless",-1),"lawlessness",-1),"layoff",-1),"layoff-happy",-1),"lazy",-3),"leak",-1),"leakage",-1),"leakages",-1),"leaking",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"leaks",-1),"leaky",-1),"lech",-1),"lecher",-1),"lecherous",-1),"lechery",-1),"leech",-1),"leer",-1),"leery",-1),"left-leaning",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"lemon",-1),"lengthy",-1),"less-developed",-1),"lesser-known",-1),"letch",-1),"lethal",-1),"lethargic",-1),"lethargy",-1),"lewd",-1),"lewdly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"lewdness",-1),"liability",-1),"liable",-2),"liar",-3),"liars",-4),"licentious",-1),"licentiously",-1),"licentiousness",-1),"lie",-3),"lied",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"lier",-3),"lies",-3),"life-threatening",-4),"lifeless",-4),"limit",-3),"limitation",-3),"limitations",-3),"limited",-3),"limits",-3),"limp",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"listless",-1),"litigious",-1),"little-known",-1),"livid",-1),"lividly",-1),"loath",-1),"loathe",-1),"loathing",-4),"loathly",-4),"loathsome",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"loathsomely",-1),"lone",-1),"loneliness",-1),"lonely",-1),"loner",-1),"lonesome",-1),"long-time",-1),"long-winded",-1),"longing",-3),"longingly",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"loophole",-2),"loopholes",-2),"loose",-3),"loot",-1),"lorn",-1),"lose",-5),"loser",-5),"losers",-5),"loses",-5),"losing",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"loss",-5),"losses",-5),"lost",-5),"loud",-2),"louder",-2),"lousy",-2),"loveless",-2),"lovelorn",-2),"low-rated",-2),"lowly",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"ludicrous",-2),"ludicrously",-2),"lugubrious",-2),"lukewarm",-2),"lull",-2),"lumpy",-2),"lunatic",-2),"lunaticism",-2),"lurch",-2),"lure",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"lurid",-1),"lurk",-1),"lurking",-1),"lying",-1),"macabre",-1),"mad",-1),"madden",-1),"maddening",-1),"maddeningly",-1),"madder",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"madly",-3),"madman",-3),"madness",-3),"maladjusted",-3),"maladjustment",-3),"malady",-3),"malaise",-3),"malcontent",-3),"malcontented",-1),"maledict",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"malevolence",-1),"malevolent",-1),"malevolently",-1),"malice",-1),"malicious",-1),"maliciously",-1),"maliciousness",-1),"malign",-2),"malignant",-2),"malodorous",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"maltreatment",-2),"mangle",-2),"mangled",-2),"mangles",-2),"mangling",-2),"mania",-3),"maniac",-3),"maniacal",-3),"manic",-3),"manipulate",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"manipulation",-3),"manipulative",-3),"manipulators",-3),"mar",-1),"marginal",-1),"marginally",-1),"martyrdom",-1),"martyrdom-seeking",-1),"mashed",-1),"massacre",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"massacres",-1),"matte",-1),"mawkish",-1),"mawkishly",-1),"mawkishness",-1),"meager",-1),"meaningless",-4),"meanness",-2),"measly",-2),"meddle",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"meddlesome",-2),"mediocre",-2),"mediocrity",-2),"melancholy",-2),"melodramatic",-2),"melodramatically",-1),"meltdown",-2),"menace",-2),"menacing",-2),"menacingly",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"mendacious",-2),"mendacity",-2),"menial",-2),"merciless",-2),"mercilessly",-1),"mess",-1),"messed",-1),"messes",-1),"messing",-1),"messy",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"midget",-1),"miff",-1),"militancy",-1),"mindless",-1),"mindlessly",-1),"mirage",-1),"mire",-1),"misalign",-1),"misaligned",-1),"misaligns",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"misapprehend",-1),"misbecome",-1),"misbecoming",-1),"misbegotten",-1),"misbehave",-1),"misbehavior",-1),"miscalculate",-1),"miscalculation",-1),"miscellaneous",-1),"mischief",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"mischievous",-1),"mischievously",-1),"misconception",-1),"misconceptions",-3),"miscreant",-3),"miscreants",-3),"misdirection",-3),"miser",-3),"miserable",-3),"miserableness",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"miserably",-3),"miseries",-2),"miserly",-2),"misery",-2),"misfit",-1),"misfortune",-5),"misgiving",-3),"misgivings",-3),"misguidance",-3),"misguide",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"misguided",-3),"mishandle",-3),"mishap",-4),"misinform",-2),"misinformed",-2),"misinterpret",-2),"misjudge",-2),"misjudgment",-3),"mislead",-3),"misleading",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"misleadingly",-3),"mislike",-4),"mismanage",-2),"mispronounce",-2),"mispronounced",-2),"mispronounces",-2),"misread",-3),"misreading",-3),"misrepresent",-2),"misrepresentation",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"miss",-1),"missed",-1),"misses",-1),"misstatement",-1),"mist",-1),"mistake",-3),"mistaken",-3),"mistakenly",-3),"mistakes",-3),"mistified",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"mistress",-1),"mistrust",-1),"mistrustful",-1),"mistrustfully",-1),"mists",-1),"misunderstand",-2),"misunderstanding",-2),"misunderstandings",-2),"misunderstood",-2),"misuse",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"moan",-1),"mobster",-1),"mock",-3),"mocked",-3),"mockeries",-1),"mockery",-1),"mocking",-1),"mockingly",-1),"mocks",-1),"molest",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"molestation",-5),"monotonous",-2),"monotony",-2),"monster",-5),"monstrosities",-2),"monstrosity",-2),"monstrous",-3),"monstrously",-3),"moody",-4),"moot",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"mope",-1),"morbid",-3),"morbidly",-3),"mordant",-2),"mordantly",-1),"moribund",-1),"moron",-4),"moronic",-4),"morons",-4),"mortification",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"mortified",-1),"mortify",-3),"mortifying",-1),"motionless",-1),"motley",-1),"mourn",-4),"mourner",-3),"mournful",-3),"mournfully",-3),"muddle",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"muddy",-1),"mudslinger",-1),"mudslinging",-1),"mulish",-1),"multi-polarization",-1),"mundane",-1),"murder",-3),"murderer",-1),"murderous",-1),"murderously",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"murky",-1),"muscle-flexing",-1),"mushy",-1),"musty",-1),"mysterious",-3),"mysteriously",-3),"mystery",-3),"mystify",-3),"myth",-2),"nag",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"nagging",-2),"naive",-5),"naively",-5),"narrower",-1),"nastily",-1),"nastiness",-1),"nasty",-3),"naughty",-4),"nauseate",-1),"nauseates",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"nauseating",-1),"nauseatingly",-1),"naïve",-3),"nebulous",-1),"nebulously",-1),"needless",-1),"needlessly",-1),"needy",-3),"nefarious",-1),"nefariously",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"negate",-1),"negation",-1),"negative",-5),"negatives",-4),"negativity",-4),"neglect",-4),"neglected",-3),"negligence",-3),"negligent",-3),"nemesis",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"nepotism",-1),"nervous",-4),"nervously",-4),"nervousness",-4),"nettle",-1),"nettlesome",-1),"neurotic",-1),"neurotically",-1),"niggle",-1),"niggles",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"nightmare",-4),"nightmarish",-4),"nightmarishly",-3),"nitpick",-1),"nitpicking",-1),"noise",-3),"noises",-3),"noisier",-1),"noisy",-2),"non-confidence",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"nonexistent",-1),"nonresponsive",-1),"nonsense",-4),"nosey",-3),"notoriety",-1),"notorious",-1),"notoriously",-1),"noxious",-1),"nuisance",-1),"numb",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"obese",-1),"object",-1),"objection",-1),"objectionable",-1),"objections",-1),"oblique",-1),"obliterate",-1),"obliterated",-1),"oblivious",-1),"obnoxious",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"obnoxiously",-1),"obscene",-5),"obscenely",-5),"obscenity",-5),"obscure",-1),"obscured",-1),"obscures",-1),"obscurity",-1),"obsess",-4),"obsessive",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"obsessively",-1),"obsessiveness",-1),"obsolete",-1),"obstacle",-1),"obstinate",-1),"obstinately",-1),"obstruct",-1),"obstructed",-1),"obstructing",-1),"obstruction",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"obstructs",-1),"obtrusive",-1),"obtuse",-1),"occlude",-1),"occluded",-1),"occludes",-1),"occluding",-1),"odd",-3),"odder",-3),"oddest",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"oddities",-1),"oddity",-1),"oddly",-2),"odor",-1),"offence",-3),"offend",-3),"offender",-3),"offending",-3),"offenses",-3),"offensive",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"offensively",-3),"offensiveness",-1),"officious",-1),"ominous",-1),"ominously",-1),"omission",-1),"omit",-1),"one-sided",-1),"onerous",-1),"onerously",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"onslaught",-1),"opinionated",-1),"opponent",-1),"opportunistic",-1),"oppose",-1),"opposition",-1),"oppositions",-1),"oppress",-1),"oppression",-5),"oppressive",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"oppressively",-5),"oppressiveness",-5),"oppressors",-5),"ordeal",-3),"orphan",-2),"ostracize",-1),"outbreak",-1),"outburst",-3),"outbursts",-3),"outcast",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"outcry",-3),"outlaw",-4),"outmoded",-2),"outrage",-5),"outraged",-5),"outrageous",-5),"outrageously",-5),"outrageousness",-5),"outrages",-1),"outsider",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"over-acted",-1),"over-awe",-1),"over-balanced",-1),"over-hyped",-1),"over-priced",-1),"over-valuation",-1),"overact",-2),"overacted",-2),"overawe",-1),"overbalance",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"overbalanced",-1),"overbearing",-1),"overbearingly",-1),"overblown",-1),"overdo",-1),"overdone",-1),"overdue",-1),"overemphasize",-1),"overheat",-1),"overkill",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"overloaded",-1),"overlook",-1),"overpaid",-1),"overpayed",-1),"overplay",-1),"overpower",-1),"overpriced",-1),"overrated",-1),"overreach",-1),"overrun",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"overshadow",-3),"oversight",-1),"oversights",-1),"oversimplification",-1),"oversimplified",-1),"oversimplify",-1),"oversize",-3),"overstate",-1),"overstated",-1),"overstatement",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"overstatements",-1),"overstates",-1),"overtaxed",-1),"overthrow",-1),"overthrows",-1),"overturn",-2),"overweight",-1),"overwhelm",-1),"overwhelmed",-1),"overwhelming",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"overwhelmingly",-1),"overwhelms",-1),"overzealous",-1),"overzealously",-1),"overzelous",-1),"pain",-4),"painful",-4),"painfull",-4),"painfully",-4),"pains",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"pale",-2),"pales",-2),"paltry",-1),"pan",-1),"pandemonium",-1),"pander",-1),"pandering",-1),"panders",-1),"panic",-1),"panick",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"panicked",-3),"panicking",-3),"panicky",-1),"paradoxical",-1),"paradoxically",-1),"paralize",-2),"paralyzed",-2),"paranoia",-3),"paranoid",-3),"parasite",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"pariah",-1),"parody",-4),"partiality",-1),"partisan",-1),"partisans",-1),"passe",-1),"passive",-1),"passiveness",-1),"pathetic",-1),"pathetically",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"patronize",-1),"paucity",-1),"pauper",-1),"paupers",-1),"payback",-1),"peculiar",-1),"peculiarly",-1),"pedantic",-1),"peeled",-1),"peeve",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"peeved",-1),"peevish",-1),"peevishly",-1),"penalize",-3),"penalty",-3),"perfidious",-1),"perfidity",-1),"perfunctory",-1),"peril",-1),"perilous",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"perilously",-1),"perish",-1),"pernicious",-1),"perplex",-1),"perplexed",-1),"perplexing",-1),"perplexity",-1),"persecute",-1),"persecution",-1),"pertinacious",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"pertinaciously",-1),"pertinacity",-1),"perturb",-1),"perturbed",-1),"pervasive",-2),"perverse",-2),"perversely",-1),"perversion",-1),"perversity",-1),"pervert",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"perverted",-1),"perverts",-1),"pessimism",-3),"pessimistic",-3),"pessimistically",-3),"pest",-1),"pestilent",-1),"petrified",-1),"petrify",-1),"pettifog",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"petty",-1),"phobia",-2),"phobic",-1),"phony",-1),"picket",-1),"picketed",-1),"picketing",-1),"pickets",-1),"picky",-1),"pig",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"pigs",-3),"pillage",-1),"pillory",-1),"pimple",-2),"pinch",-1),"pique",-1),"pitiable",-1),"pitiful",-1),"pitifully",-1),"pitiless",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"pitilessly",-1),"pittance",-1),"pity",-3),"plagiarize",-1),"plague",-1),"plasticky",-1),"plaything",-1),"plea",-1),"pleas",-1),"plebeian",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"plight",-1),"plot",-1),"plotters",-1),"ploy",-1),"plunder",-1),"plunderer",-1),"pointless",-1),"pointlessly",-1),"poison",-2),"poisonous",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"poisonously",-2),"pokey",-1),"poky",-1),"polarisation",-3),"polemize",-1),"pollute",-2),"polluter",-2),"polluters",-2),"polution",-2),"pompous",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"poor",-2),"poorer",-2),"poorest",-2),"poorly",-2),"posturing",-1),"pout",-1),"poverty",-4),"powerless",-4),"prate",-1),"pratfall",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"prattle",-1),"precarious",-1),"precariously",-1),"precipitate",-1),"precipitous",-1),"predatory",-1),"predicament",-1),"prejudge",-2),"prejudice",-2),"prejudices",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"prejudicial",-2),"premeditated",-1),"preoccupy",-1),"preposterous",-1),"preposterously",-1),"presumptuous",-1),"presumptuously",-1),"pretence",-1),"pretend",-1),"pretense",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"pretentious",-1),"pretentiously",-1),"prevaricate",-1),"pricey",-1),"pricier",-1),"prick",-1),"prickle",-1),"prickles",-1),"prideful",-1),"prik",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"primitive",-1),"prison",-1),"prisoner",-1),"problem",-3),"problematic",-3),"problems",-3),"procrastinate",-2),"procrastinates",-2),"procrastination",-2),"profane",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"profanity",-1),"prohibit",-3),"prohibitive",-3),"prohibitively",-3),"propaganda",-1),"propagandize",-1),"proprietary",-1),"prosecute",-1),"protest",-3),"protested",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"protesting",-1),"protests",-1),"protracted",-1),"provocation",-1),"provocative",-1),"provoke",-1),"pry",-1),"pugnacious",-1),"pugnaciously",-1),"pugnacity",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"punch",-1),"punish",-3),"punishable",-3),"punitive",-3),"punk",-2),"puny",-2),"puppet",-1),"puppets",-1),"puzzled",-1),"puzzlement",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"puzzling",-1),"quack",-1),"qualm",-1),"qualms",-1),"quandary",-1),"quarrel",-3),"quarrellous",-1),"quarrellously",-1),"quarrels",-1),"quarrelsome",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"quash",-1),"queer",-1),"questionable",-1),"quibble",-1),"quibbles",-1),"quitter",-1),"rabid",-1),"racism",-1),"racist",-5),"racists",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"racy",-2),"radical",-4),"radicalization",-5),"radically",-5),"radicals",-5),"rage",-4),"ragged",-3),"raging",-3),"rail",-1),"raked",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"rampage",-3),"rampant",-3),"ramshackle",-1),"rancor",-1),"randomly",-2),"rankle",-1),"rant",-1),"ranted",-1),"ranting",-1),"rantingly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"rants",-1),"rape",-5),"raped",-5),"raping",-5),"rascal",-4),"rascals",-4),"rash",-3),"rattle",-2),"rattled",-2),"rattles",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"ravage",-3),"raving",-2),"reactionary",-1),"rebellious",-3),"rebuff",-1),"rebuke",-1),"recalcitrant",-1),"recant",-1),"recession",-1),"recessionary",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"reckless",-4),"recklessly",-4),"recklessness",-4),"recoil",-1),"recourses",-1),"redundancy",-1),"redundant",-1),"refusal",-1),"refuse",-4),"refused",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"refuses",-2),"refusing",-2),"refutation",-1),"refute",-1),"refuted",-1),"refutes",-1),"refuting",-1),"regress",-1),"regression",-1),"regressive",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"regret",-4),"regreted",-4),"regretful",-4),"regretfully",-4),"regrets",-4),"regrettable",-4),"regrettably",-4),"regretted",-4),"reject",-5),"rejected",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"rejecting",-5),"rejection",-5),"rejects",-5),"relapse",-5),"relentless",-3),"relentlessly",-3),"relentlessness",-3),"reluctance",-2),"reluctant",-2),"reluctantly",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"remorse",-4),"remorseful",-4),"remorsefully",-4),"remorseless",-4),"remorselessly",-4),"remorselessness",-4),"renounce",-1),"renunciation",-1),"repel",-1),"repetitive",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"reprehensible",-1),"reprehensibly",-1),"reprehension",-1),"reprehensive",-1),"repress",-1),"repression",-1),"repressive",-1),"reprimand",-1),"reproach",-1),"reproachful",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"reprove",-1),"reprovingly",-1),"repudiate",-1),"repudiation",-1),"repugn",-1),"repugnance",-1),"repugnant",-1),"repugnantly",-1),"repulse",-3),"repulsed",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"repulsing",-2),"repulsive",-3),"repulsively",-1),"repulsiveness",-1),"resent",-1),"resentful",-1),"resentment",-1),"resignation",-1),"resigned",-1),"resistance",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"restless",-3),"restlessness",-3),"restrict",-3),"restricted",-3),"restriction",-3),"restrictive",-3),"resurgent",-1),"retaliate",-2),"retaliatory",-2),"retard",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"retarded",-3),"retardedness",-3),"retards",-3),"reticent",-2),"retract",-2),"retreat",-2),"retreated",-2),"revenge",-5),"revengeful",-5),"revengefully",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"revert",-4),"revile",-3),"reviled",-3),"revoke",-3),"revolt",-4),"revolting",-4),"revoltingly",-4),"revulsion",-3),"revulsive",-3),"rhapsodize",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"rhetoric",-3),"rhetorical",-3),"ricer",-2),"ridicule",-2),"ridicules",-2),"ridiculous",-3),"ridiculously",-2),"rife",-1),"rift",-5),"rifts",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"rigid",-3),"rigidity",-2),"rigidness",-2),"rile",-1),"riled",-1),"rip",-4),"rip-off",-3),"ripoff",-3),"ripped",-3),"risk",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"risks",-4),"risky",-4),"rival",-5),"rivalry",-5),"roadblocks",-3),"rocky",-1),"rogue",-3),"rollercoaster",-2),"rot",-2),"rotten",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"rough",-2),"rremediable",-1),"rubbish",-2),"rude",-4),"rue",-1),"ruffian",-1),"ruffle",-1),"ruin",-3),"ruined",-3),"ruining",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"ruinous",-3),"ruins",-3),"rumbling",-2),"rumor",-2),"rumors",-2),"rumours",-2),"rumple",-1),"run-down",-2),"runaway",-2),"rupture",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"rust",-3),"rusts",-3),"rusty",-3),"rut",-2),"ruthless",-2),"ruthlessly",-1),"ruthlessness",-1),"ruts",-1),"sabotage",-3),"sack",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"sacrificed",-3),"sad",-4),"sadden",-4),"sadly",-4),"sadness",-4),"sag",-1),"sagged",-1),"sagging",-1),"saggy",-1),"sags",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"salacious",-1),"sanctimonious",-1),"sap",-1),"sarcasm",-2),"sarcastic",-2),"sarcastically",-2),"sardonic",-4),"sardonically",-4),"sass",-1),"satirical",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"satirize",-1),"savage",-3),"savaged",-3),"savagery",-4),"savages",-4),"scaly",-1),"scam",-5),"scams",-5),"scandal",-5),"scandalize",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"scandalized",-5),"scandalous",-5),"scandalously",-5),"scandals",-5),"scandel",-1),"scandels",-1),"scant",-1),"scapegoat",-1),"scar",-1),"scarce",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"scarcely",-4),"scarcity",-4),"scare",-5),"scared",-5),"scarier",-4),"scariest",-5),"scarily",-4),"scarred",-1),"scars",-1),"scary",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"scathing",-1),"scathingly",-1),"sceptical",-1),"scoff",-1),"scoffingly",-1),"scold",-2),"scolded",-1),"scolding",-1),"scoldingly",-1),"scorching",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"scorchingly",-1),"scorn",-1),"scornful",-1),"scornfully",-1),"scoundrel",-1),"scourge",-1),"scowl",-1),"scramble",-1),"scrambled",-1),"scrambles",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"scrambling",-1),"scrap",-3),"scratch",-3),"scratched",-1),"scratches",-1),"scratchy",-1),"scream",-1),"screech",-1),"screw-up",-1),"screwed",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"screwed-up",-1),"screwy",-1),"scuff",-1),"scuffs",-1),"scum",-2),"scummy",-1),"second-class",-1),"second-tier",-1),"secretive",-1),"sedentary",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"seedy",-1),"seethe",-1),"seething",-1),"self-coup",-1),"self-criticism",-1),"self-defeating",-1),"self-destructive",-1),"self-humiliation",-1),"self-interest",-1),"self-interested",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"self-serving",-1),"selfinterested",-1),"selfish",-4),"selfishly",-4),"selfishness",-4),"semi-retarded",-1),"senile",-5),"sensationalize",-1),"senseless",-3),"senselessly",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"seriousness",-1),"sermonize",-1),"servitude",-1),"set-up",-1),"setback",-3),"setbacks",-3),"sever",-2),"severe",-2),"severity",-2),"sh*t",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"shabby",-1),"shadowy",-1),"shady",-2),"shake",-2),"shaky",-2),"shallow",-2),"sham",-3),"shambles",-3),"shame",-3),"shameful",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"shamefully",-1),"shamefulness",-1),"shameless",-3),"shamelessly",-3),"shamelessness",-3),"shark",-2),"sharply",-2),"shatter",-1),"shemale",-1),"shimmer",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"shimmy",-1),"shipwreck",-2),"shirk",-2),"shirker",-1),"shit",-5),"shiver",-1),"shock",-3),"shocked",-3),"shocking",-3),"shockingly",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"shoddy",-1),"short-lived",-1),"shortage",-3),"shortchange",-1),"shortcoming",-1),"shortcomings",-1),"shortness",-2),"shortsighted",-4),"shortsightedness",-2),"showdown",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"shrew",-1),"shriek",-1),"shrill",-1),"shrilly",-1),"shrivel",-1),"shroud",-1),"shrouded",-1),"shrug",-1),"shun",-1),"shunned",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"sick",-3),"sicken",-3),"sickening",-3),"sickeningly",-1),"sickly",-1),"sickness",-1),"sidetrack",-1),"sidetracked",-1),"siege",-1),"sillily",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"silly",-1),"simplistic",-3),"simplistically",-1),"sin",-3),"sinful",-3),"sinfully",-3),"sinister",-3),"sinisterly",-1),"sink",-2),"sinking",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"skeletons",-2),"skeptic",-3),"skeptical",-3),"skeptically",-3),"skepticism",-3),"sketchy",-4),"skimpy",-2),"skinny",-2),"skittish",-1),"skittishly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"skulk",-1),"slack",-1),"slander",-3),"slanderer",-3),"slanderous",-3),"slanderously",-3),"slanders",-1),"slap",-2),"slashing",-1),"slaughter",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"slaughtered",-5),"slave",-4),"slaves",-4),"sleazy",-3),"slime",-1),"slog",-1),"slogged",-1),"slogging",-1),"slogs",-1),"sloooooooooooooow",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"sloooow",-1),"slooow",-1),"sloow",-1),"sloppily",-1),"sloppy",-1),"sloth",-1),"slothful",-1),"slow",-1),"slow-moving",-1),"slowed",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"slower",-1),"slowest",-1),"slowly",-1),"sloww",-1),"slowww",-1),"slowwww",-1),"slug",-1),"sluggish",-1),"slump",-1),"slumping",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"slumpping",-1),"slur",-1),"slut",-5),"sluts",-5),"sly",-1),"smack",-2),"smallish",-2),"smash",-2),"smear",-1),"smell",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"smelled",-1),"smelling",-1),"smells",-1),"smelly",-1),"smelt",-1),"smoke",-2),"smokescreen",-1),"smolder",-1),"smoldering",-1),"smother",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"smoulder",-1),"smouldering",-1),"smudge",-1),"smudged",-1),"smudges",-1),"smudging",-1),"smug",-1),"smugly",-1),"smut",-1),"smuttier",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"smuttiest",-1),"smutty",-1),"snag",-1),"snagged",-1),"snagging",-1),"snags",-1),"snappish",-1),"snappishly",-1),"snare",-1),"snarky",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"snarl",-1),"sneak",-2),"sneakily",-2),"sneaky",-2),"sneer",-1),"sneering",-1),"sneeringly",-1),"snob",-1),"snobbish",-1),"snobby",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"snobish",-1),"snobs",-1),"snub",-1),"so-cal",-1),"soapy",-1),"sob",-1),"sober",-1),"sobering",-1),"solemn",-1),"solicitude",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"somber",-1),"sore",-1),"sorely",-1),"soreness",-1),"sorrow",-1),"sorrowful",-1),"sorrowfully",-1),"sorry",-1),"sour",-2),"sourly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"spade",-1),"spank",-3),"spendy",-1),"spew",-1),"spewed",-1),"spewing",-1),"spews",-1),"spilling",-1),"spinster",-1),"spiritless",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"spite",-1),"spiteful",-1),"spitefully",-1),"spitefulness",-1),"splatter",-1),"split",-2),"splitting",-1),"spoil",-2),"spoilage",-1),"spoilages",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"spoiled",-1),"spoilled",-1),"spoils",-2),"spook",-1),"spookier",-1),"spookiest",-1),"spookily",-1),"spooky",-1),"spoon-fed",-1),"spoon-feed",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"spoonfed",-2),"sporadic",-1),"spotty",-1),"spurious",-1),"spurn",-1),"sputter",-1),"squabble",-1),"squabbling",-1),"squander",-1),"squash",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"squeak",-1),"squeaks",-1),"squeaky",-1),"squeal",-1),"squealing",-1),"squeals",-1),"squirm",-1),"stab",-2),"stagnant",-1),"stagnate",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"stagnation",-1),"staid",-1),"stain",-1),"stains",-1),"stale",-1),"stalemate",-1),"stall",-1),"stalls",-1),"stammer",-1),"stampede",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"standstill",-1),"stark",-2),"starkly",-2),"startle",-1),"startling",-1),"startlingly",-1),"starvation",-1),"starve",-2),"static",-1),"steal",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"stealing",-3),"steals",-3),"steep",-1),"steeply",-1),"stench",-1),"stereotype",-1),"stereotypical",-1),"stereotypically",-1),"stern",-1),"stew",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"sticky",-2),"stiff",-1),"stiffness",-1),"stifle",-1),"stifling",-1),"stiflingly",-1),"stigma",-1),"stigmatize",-1),"sting",-1),"stinging",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"stingingly",-1),"stingy",-1),"stink",-2),"stinks",-2),"stodgy",-2),"stole",-2),"stolen",-2),"stooge",-2),"stooges",-2),"stormy",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"straggle",-2),"straggler",-2),"strain",-2),"strained",-1),"straining",-1),"strange",-3),"strangely",-3),"stranger",-1),"strangest",-1),"strangle",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"streaky",-1),"strenuous",-1),"stress",-3),"stresses",-1),"stressful",-1),"stressfully",-1),"stricken",-1),"strict",-3),"strictly",-3),"strident",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"stridently",-1),"strife",-1),"strike",-1),"stringent",-1),"stringently",-1),"struck",-3),"struggle",-1),"struggled",-1),"struggles",-1),"struggling",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"strut",-1),"stubborn",-4),"stubbornly",-4),"stubbornness",-4),"stuck",-3),"stuffy",-1),"stumble",-1),"stumbled",-1),"stumbles",-1),"stump",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"stumped",-1),"stumps",-1),"stun",-2),"stunt",-3),"stunted",-3),"stupid",-1),"stupidest",-1),"stupidity",-1),"stupidly",-1),"stupified",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"stupify",-1),"stupor",-1),"stutter",-1),"stuttered",-1),"stuttering",-1),"stutters",-1),"sty",-1),"stymied",-1),"sub-par",-3),"subdued",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"subjected",-1),"subjection",-1),"subjugate",-1),"subjugation",-1),"submissive",-3),"subordinate",-1),"subpoena",-1),"subpoenas",-1),"subservience",-1),"subservient",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"substandard",-1),"subtract",-3),"subversion",-1),"subversive",-1),"subversively",-1),"subvert",-1),"succumb",-2),"suck",-4),"sucked",-3),"sucker",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"sucks",-3),"sucky",-2),"sue",-3),"sued",-3),"sueing",-3),"sues",-1),"suffer",-1),"suffered",-1),"sufferer",-1),"sufferers",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"suffering",-1),"suffers",-1),"suffocate",-1),"sugar-coat",-1),"sugar-coated",-1),"sugarcoated",-1),"suicidal",-5),"suicide",-5),"sulk",-4),"sullen",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"sully",-1),"sunder",-1),"sunk",-3),"sunken",-3),"superficial",-1),"superficiality",-1),"superficially",-1),"superfluous",-1),"superstition",-3),"superstitious",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"suppress",-1),"suppression",-1),"surrender",-1),"susceptible",-1),"suspect",-2),"suspicion",-2),"suspicions",-2),"suspicious",-1),"suspiciously",-1),"swagger",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"swamped",-1),"sweaty",-2),"swelled",-1),"swelling",-1),"swindle",-1),"swipe",-3),"swollen",-1),"symptom",-1),"symptoms",-1),"syndrome",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"taboo",-1),"tacky",-1),"taint",-1),"tainted",-1),"tamper",-1),"tangle",-1),"tangled",-1),"tangles",-1),"tank",-1),"tanked",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"tanks",-1),"tantrum",-1),"tardy",-1),"tarnish",-2),"tarnished",-1),"tarnishes",-1),"tarnishing",-1),"tattered",-1),"taunt",-1),"taunting",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"tauntingly",-1),"taunts",-3),"taut",-1),"tawdry",-1),"taxing",-1),"tease",-1),"teasingly",-1),"tedious",-1),"tediously",-1),"temerity",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"temper",-3),"tempest",-2),"temptation",-1),"tenderness",-1),"tense",-1),"tension",-1),"tentative",-1),"tentatively",-1),"tenuous",-1),"tenuously",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"tepid",-1),"terrible",-5),"terribleness",-5),"terribly",-4),"terror",-4),"terror-genic",-1),"terrorism",-5),"terrorize",-5),"testily",-1),"testy",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"tetchily",-1),"tetchy",-1),"thankless",-2),"thicker",-1),"thirst",-1),"thorny",-4),"thoughtless",-3),"thoughtlessly",-3),"thoughtlessness",-3),"thrash",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"threat",-4),"threaten",-4),"threatening",-4),"threats",-4),"threesome",-3),"throb",-2),"throbbed",-2),"throbbing",-2),"throbs",-2),"throttle",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"thug",-4),"thumb-down",-3),"thumbs-down",-3),"thwart",-2),"time-consuming",-1),"timid",-3),"timidity",-3),"timidly",-3),"timidness",-3),"tin-y",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"tingled",-1),"tingling",-1),"tired",-2),"tiresome",-2),"tiring",-1),"tiringly",-1),"toil",-1),"toll",-1),"top-heavy",-2),"topple",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"torment",-5),"tormented",-5),"torrent",-2),"tortuous",-1),"torture",-4),"tortured",-4),"tortures",-4),"torturing",-4),"torturous",-4),"torturously",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"totalitarian",-2),"touchy",-1),"toughness",-1),"tout",-1),"touted",-1),"touts",-1),"toxic",-4),"traduce",-1),"tragedy",-5),"tragic",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"tragically",-5),"traitor",-4),"traitorous",-1),"traitorously",-1),"tramp",-1),"trample",-1),"transgress",-2),"transgression",-2),"trap",-4),"traped",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"trapped",-4),"trash",-1),"trashed",-1),"trashy",-1),"trauma",-2),"traumatic",-2),"traumatically",-1),"traumatize",-1),"traumatized",-1),"travesties",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"travesty",-1),"treacherous",-1),"treacherously",-1),"treachery",-1),"treason",-1),"treasonous",-1),"trick",-1),"tricked",-1),"trickery",-1),"tricky",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"trivial",-1),"trivialize",-1),"trouble",-2),"troubled",-2),"troublemaker",-1),"troubles",-1),"troublesome",-1),"troublesomely",-1),"troubling",-1),"troublingly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"truant",-1),"tumble",-1),"tumbled",-1),"tumbles",-1),"tumultuous",-1),"turbulent",-1),"turmoil",-1),"twist",-1),"twisted",-1),"twists",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"two-faced",-1),"two-faces",-1),"tyrannical",-4),"tyrannically",-4),"tyranny",-4),"tyrant",-4),"ugh",-1),"uglier",-4),"ugliest",-5),"ugliness",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"ugly",-3),"ulterior",-3),"ultimatum",-3),"ultimatums",-3),"ultra-hardline",-1),"un-viewable",-1),"unable",-1),"unacceptable",-2),"unacceptablely",-2),"unacceptably",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unaccessible",-2),"unaccustomed",-2),"unachievable",-2),"unaffordable",-2),"unappealing",-4),"unattractive",-3),"unauthentic",-3),"unavailable",-3),"unavoidably",-3),"unbearable",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unbearablely",-3),"unbelievable",-3),"unbelievably",-3),"uncaring",-3),"uncertain",-1),"uncivil",-1),"uncivilized",-1),"unclean",-2),"unclear",-2),"uncollectible",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"uncomfortable",-2),"uncomfortably",-2),"uncomfy",-2),"uncompetitive",-2),"uncompromising",-1),"uncompromisingly",-1),"unconfirmed",-1),"unconstitutional",-1),"uncontrolled",-2),"unconvincing",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unconvincingly",-2),"uncooperative",-2),"uncouth",-1),"uncreative",-1),"undecided",-1),"undefined",-3),"undependability",-1),"undependable",-1),"undercut",-2),"undercuts",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"undercutting",-1),"underdog",-1),"underestimate",-1),"underlings",-1),"undermine",-3),"undermined",-3),"undermines",-3),"undermining",-3),"underpaid",-3),"underpowered",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"undersized",-2),"undesirable",-3),"undetermined",-1),"undid",-1),"undignified",-1),"undissolved",-1),"undocumented",-1),"undone",-1),"undue",-1),"unease",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"uneasily",-1),"uneasiness",-1),"uneasy",-1),"uneconomical",-2),"unemployed",-3),"unequal",-1),"unethical",-3),"uneven",-1),"uneventful",-1),"unexpected",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unexpectedly",-1),"unexplained",-1),"unfairly",-1),"unfaithful",-1),"unfaithfully",-1),"unfamiliar",-1),"unfavorable",-1),"unfeeling",-1),"unfinished",-1),"unfit",-2),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unforeseen",-3),"unforgiving",-1),"unfortunate",-5),"unfortunately",-5),"unfounded",-1),"unfriendly",-2),"unfulfilled",-1),"unfunded",-1),"ungovernable",-1),"ungrateful",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unhappily",-4),"unhappiness",-4),"unhappy",-4),"unhealthy",-4),"unhelpful",-3),"unilateralism",-1),"unimaginable",-1),"unimaginably",-1),"unimportant",-1),"uninformed",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"uninsured",-1),"unintelligible",-1),"unintelligile",-1),"unipolar",-1),"unjust",-1),"unjustifiable",-1),"unjustifiably",-1),"unjustified",-1),"unjustly",-1),"unkind",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unkindly",-1),"unknown",-1),"unlamentable",-1),"unlamentably",-1),"unlawful",-2),"unlawfully",-2),"unlawfulness",-2),"unleash",-1),"unlicensed",-1),"unlikely",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unlucky",-1),"unmoved",-3),"unnatural",-2),"unnaturally",-2),"unnecessary",-4),"unneeded",-1),"unnerve",-1),"unnerved",-1),"unnerving",-1),"unnervingly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unnoticed",-2),"unobserved",-3),"unorthodox",-3),"unorthodoxy",-3),"unpleasant",-1),"unpleasantries",-2),"unpopular",-2),"unpredictable",-1),"unprepared",-1),"unproductive",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unprofitable",-1),"unprove",-1),"unproved",-1),"unproven",-1),"unproves",-1),"unproving",-1),"unqualified",-1),"unravel",-1),"unraveled",-1),"unreachable",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unreadable",-1),"unrealistic",-1),"unreasonable",-1),"unreasonably",-1),"unrelenting",-2),"unrelentingly",-2),"unreliability",-2),"unreliable",-2),"unresolved",-2),"unresponsive",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unrest",-3),"unruly",-1),"unsafe",-1),"unsatisfactory",-1),"unsavory",-1),"unscrupulous",-1),"unscrupulously",-1),"unsecure",-2),"unseemly",-1),"unsettle",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unsettled",-3),"unsettling",-3),"unsettlingly",-1),"unskilled",-4),"unsophisticated",-1),"unsound",-1),"unspeakable",-1),"unspeakablely",-1),"unspecified",-1),"unstable",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unsteadily",-1),"unsteadiness",-1),"unsteady",-1),"unsuccessful",-1),"unsuccessfully",-1),"unsupported",-1),"unsupportive",-1),"unsure",-1),"unsuspecting",-1),"unsustainable",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"untenable",-1),"untested",-1),"unthinkable",-1),"unthinkably",-1),"untimely",-1),"untouched",-1),"untrue",-1),"untrustworthy",-1),"untruthful",-1),"unusable",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unusably",-1),"unuseable",-1),"unuseably",-1),"unusual",-1),"unusually",-1),"unviewable",-1),"unwanted",-1),"unwarranted",-1),"unwatchable",-1),"unwelcome",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"unwell",-1),"unwieldy",-1),"unwilling",-1),"unwillingly",-1),"unwillingness",-1),"unwise",-1),"unwisely",-1),"unworkable",-1),"unworthy",-1),"unyielding",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"upbraid",-1),"upheaval",-1),"uprising",-1),"uproar",-3),"uproarious",-1),"uproariously",-1),"uproarous",-1),"uproarously",-1),"uproot",-2),"upset",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"upseting",-1),"upsets",-1),"upsetting",-1),"upsettingly",-1),"urgent",-1),"useless",-2),"usurp",-1),"usurper",-1),"utterly",-1),"vagrant",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"vague",-2),"vagueness",-1),"vain",-1),"vainly",-1),"vanity",-1),"vehement",-1),"vehemently",-1),"vengeance",-4),"vengeful",-4),"vengefully",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"vengefulness",-4),"venom",-3),"venomous",-3),"venomously",-3),"vent",-3),"vestiges",-1),"vex",-1),"vexation",-1),"vexing",-1),"vexingly",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"vibrate",-1),"vibrated",-1),"vibrates",-1),"vibrating",-1),"vibration",-1),"vice",-1),"vicious",-1),"viciously",-1),"viciousness",-1),"victimize",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"vile",-1),"vileness",-1),"vilify",-1),"villainous",-1),"villainously",-1),"villains",-1),"villian",-1),"villianous",-1),"villianously",-1),"villify",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"vindictive",-1),"vindictively",-1),"vindictiveness",-1),"violate",-1),"violation",-1),"violator",-1),"violators",-1),"violent",-1),"violently",-1),"viper",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"virulence",-1),"virulent",-1),"virulently",-1),"virus",-3),"vociferous",-1),"vociferously",-1),"volatile",-1),"volatility",-1),"vomit",-1),"vomited",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"vomiting",-1),"vomits",-1),"vulgar",-1),"vulnerable",-1),"wack",-1),"wail",-1),"wallow",-1),"wane",-1),"waning",-1),"wanton",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"war-like",-3),"warily",-3),"wariness",-3),"warlike",-3),"warned",-2),"warning",-2),"warp",-1),"warped",-1),"wary",-1),"washed-out",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"waste",-1),"wasted",-1),"wasteful",-1),"wastefulness",-1),"wasting",-1),"water-down",-1),"watered-down",-1),"wayward",-1),"weak",-4),"weaken",-4),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"weakening",-3),"weaker",-3),"weakness",-3),"weaknesses",-3),"weariness",-1),"wearisome",-1),"weary",-1),"wedge",-1),"weed",-1),"weep",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"weird",-1),"weirdly",-1),"wheedle",-1),"whimper",-1),"whine",-3),"whining",-1),"whiny",-1),"whips",-1),"whore",-1),"whores",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"wicked",-1),"wickedly",-1),"wickedness",-1),"wild",-1),"wildly",-1),"wiles",-1),"wilt",-1),"wily",-1),"wimpy",-1),"wince",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"wobble",-1),"wobbled",-1),"wobbles",-1),"woe",-1),"woebegone",-1),"woeful",-1),"woefully",-1),"womanizer",-1),"womanizing",-1),"worn",-1),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"worried",-1),"worriedly",-1),"worrier",-1),"worries",-1),"worrisome",-1),"worry",-1),"worrying",-1),"worryingly",-1),"worse",-5),"worsen",-5),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"worsening",-5),"worst",-5),"worthless",-3),"worthlessly",-3),"worthlessness",-3),"wound",-3),"wounds",-3),"wrangle",-2),"wrath",-3),"wreak",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"wreaked",-3),"wreaks",-3),"wreck",-3),"wrest",-2),"wrestle",-2),"wretch",-3),"wretched",-3),"wretchedly",-2),"wretchedness",-2),"wrinkle",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"wrinkled",-3),"wrinkles",-3),"wrip",-1),"wripped",-1),"wripping",-1),"writhe",-1),"wrong",-3),"wrongful",-3),"wrongly",-3),"wrought",-3),Bn(Bn(Bn(Bn(Bn(Bn(Bn(Bn(En,"yawn",-2),"zap",-2),"zapped",-2),"zaps",-1),"zealot",-3),"zealous",-3),"zealously",-3),"zombie",-4))};function _n(e){return _n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_n(e)}function Pn(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==_n(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==_n(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===_n(o)?o:String(o)),n)}var i,o}function Rn(e,t){return Rn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Rn(e,t)}function Tn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=zn(e);if(t){var i=zn(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===_n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function zn(e){return zn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},zn(e)}var Nn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Rn(e,t)}(o,e);var t,r,n,i=Tn(o);function o(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),i.apply(this,arguments)}return t=o,(r=[{key:"newResult",value:function(e){return"en"!==e.getShortLocale()?null:(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Titles with positive or negative sentiment work best for higher CTR.","rank-math")).setTooltip((0,Ke.__)("Headlines with a strong emotional sentiment (positive or negative) tend to receive more clicks.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(e),n=(new(jn())).analyze(e.getLower("title"),On).score;return r.setScore(this.calculateScore(n)).setText(this.translateScore(r)),r}},{key:"isApplicable",value:function(e){return"en"===e.getShortLocale()&&e.hasTitle()}},{key:"calculateScore",value:function(e){return 0!==e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_titleSentiment_score",1)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("Your title has a positive or a negative sentiment.","rank-math"):(0,Ke.sprintf)((0,Ke.__)("Your title doesn't contain a %1$s word.","rank-math"),'<a href="https://rankmath.com/kb/score-100-in-tests/#sentiment-in-a-title" target="_blank">positive or a negative sentiment</a>')}}])&&Pn(t.prototype,r),n&&Pn(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(n),qn=Nn;function In(e){return In="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},In(e)}function Mn(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==In(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==In(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===In(o)?o:String(o)),n)}var i,o}function Ln(e,t){return Ln=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ln(e,t)}function $n(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=Un(e);if(t){var i=Un(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===In(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function Un(e){return Un=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Un(e)}var Wn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ln(e,t)}(o,e);var t,r,n,i=$n(o);function o(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),i.apply(this,arguments)}return t=o,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Use the Focus Keyword near the beginning of SEO title.","rank-math")).setTooltip((0,Ke.__)("The SEO page title should contain the Focus Keyword preferably at the beginning.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=e.getLower("title"),i=n.indexOf(e.getLower("keyword")),o=Math.floor(n.length/2),u=0<=i&&i<o;return r.setScore(this.calculateScore(u)).setText(this.translateScore(r)),r}},{key:"isApplicable",value:function(e){return e.hasKeyword()&&e.hasTitle()}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_titleStartWithKeyword_score",3)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("Focus Keyword used at the beginning of SEO title.","rank-math"):(0,Ke.__)("Focus Keyword doesn't appear at the beginning of SEO title.","rank-math")}}])&&Mn(t.prototype,r),n&&Mn(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(n),Kn=Wn;function Hn(e){return Hn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Hn(e)}function Vn(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==Hn(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Hn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Hn(o)?o:String(o)),n)}var i,o}function Yn(e,t){return Yn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Yn(e,t)}function Gn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=Qn(e);if(t){var i=Qn(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===Hn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function Qn(e){return Qn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Qn(e)}var Jn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Yn(e,t)}(u,e);var t,r,n,o=Gn(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),o.apply(this,arguments)}return t=u,(r=[{key:"newResult",value:function(){var e=(0,i.isUndefined)(rankMath.postType)?"Post":(0,i.startCase)(rankMath.postType);return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.sprintf)((0,Ke.__)("Use %1$s to optimise the  %2$s.","rank-math"),'<a class="rank-math-open-contentai" href="'+ut("content-ai-settings","Content Analysis")+'" target="_blank">Content AI</a>',e))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=!1!==e.get("contentAI");return r.setScore(this.calculateScore(n)).setText(this.translateScore(r)),r}},{key:"isApplicable",value:function(e){return!1!==e.get("contentAI")}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_contentAI",5)}},{key:"translateScore",value:function(e){var t=ut("content-ai-settings","Content Analysis"),r=(0,i.isUndefined)(rankMath.postType)?"Post":(0,i.startCase)(rankMath.postType);return e.hasScore()?(0,Ke.sprintf)((0,Ke.__)("You are using %1$s to optimise this %2$s.","rank-math"),'<a href="'+t+'" target="_blank">Content AI</a>',r):(0,Ke.sprintf)((0,Ke.__)("You are not using %1$s to optimise this %2$s.","rank-math"),'<a class="rank-math-open-contentai" href="'+t+'" target="_blank">Content AI</a>',r)}}])&&Vn(t.prototype,r),n&&Vn(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),Zn=Jn;function Xn(e){return Xn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xn(e)}function ei(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==Xn(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Xn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Xn(o)?o:String(o)),n)}var i,o}function ti(e,t){return ti=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ti(e,t)}function ri(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=ni(e);if(t){var i=ni(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===Xn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function ni(e){return ni=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ni(e)}var ii=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ti(e,t)}(o,e);var t,r,n,i=ri(o);function o(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),i.apply(this,arguments)}return t=o,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("Reviews are disabled on this Product.","rank-math"))}},{key:"getResult",value:function(e,t){var r=this.newResult(),n=rankMath.assessor.isReviewEnabled;return r.setScore(this.calculateScore(n)).setText(this.translateScore(r)),r}},{key:"isApplicable",value:function(){return rankMath.assessor.isReviewEnabled}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_isReviewEnabled_score",2)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("Reviews are enabled for this Product. Good Job!","rank-math"):(0,Ke.__)("Reviews are disabled on this Product.","rank-math")}}])&&ei(t.prototype,r),n&&ei(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(n),oi=ii;function ui(e){return ui="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ui(e)}function ai(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==ui(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==ui(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===ui(o)?o:String(o)),n)}var i,o}function si(e,t){return si=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},si(e,t)}function li(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=ci(e);if(t){var i=ci(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===ui(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function ci(e){return ci=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ci(e)}var di=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&si(e,t)}(u,e);var t,r,n,o=li(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),o.apply(this,arguments)}return t=u,(r=[{key:"newResult",value:function(){return(new a).setMaxScore(this.getScore()).setEmpty((0,Ke.__)("You are not using the Product Schema for this Product.","rank-math"))}},{key:"getResult",value:function(e,t){var r=e.get("schemas"),n=!1;(0,i.isEmpty)(r)||(0,i.forEach)(r,(function(e){(0,i.includes)(["WooCommerceProduct","EDDProduct","Product"],e["@type"])&&(n=!0)}));var o=this.newResult();return o.setScore(this.calculateScore(n)).setText(this.translateScore(o)),o}},{key:"isApplicable",value:function(e){return!(0,i.isEmpty)(e.get("schemas"))}},{key:"calculateScore",value:function(e){return e?this.getScore():null}},{key:"getScore",value:function(){return(0,z.applyFilters)("rankMath_analysis_hasProductSchema_score",2)}},{key:"translateScore",value:function(e){return e.hasScore()?(0,Ke.__)("You are using the Product Schema for this Product","rank-math"):(0,Ke.__)("You are not using the Product Schema for this Product.","rank-math")}}])&&ai(t.prototype,r),n&&ai(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(n),pi=di;function fi(e){return fi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fi(e)}function yi(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(i=n.key,o=void 0,o=function(e,t){if("object"!==fi(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==fi(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===fi(o)?o:String(o)),n)}var i,o}var hi=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.options=t,this.researcher=(0,i.has)(t,"researcher")?t.researcher:new We,this.setAnalyses()}var t,r,n;return t=e,(r=[{key:"analyze",value:function(e){return this.generateResults(this.analyses,e)}},{key:"analyzeSome",value:function(e,t){return this.generateResults((0,i.pick)(this.defaultAnalyses,e),t)}},{key:"generateResults",value:function(e,t){var r=this;return new Promise((function(n){r.results={},r.researcher.setPaper(t),(0,i.forEach)(e,(function(e,n){var i=e.isApplicable(t,r.researcher)?e.getResult(t,r.researcher):e.newResult(t);null!==i&&(r.results[n]=i)})),n(r.results)}))}},{key:"setAnalyses",value:function(){this.defaultAnalyses={contentHasAssets:new Ze,contentHasShortParagraphs:new ot,contentHasTOC:new ft,keywordDensity:new wt,keywordIn10Percent:new jt,keywordInContent:new Tt,keywordInImageAlt:new $t,keywordInMetaDescription:new Gt,keywordInPermalink:new rr,keywordInSubheadings:new lr,keywordInTitle:new gr,keywordNotUsed:new Sr,lengthContent:new Rr,lengthPermalink:new Lr,linksHasExternals:new Yr,linksHasInternal:new tn,linksNotAllExternals:new ln,titleHasNumber:new gn,titleHasPowerWords:new Cn,titleSentiment:new qn,titleStartWithKeyword:new Kn,hasContentAI:new Zn,isReviewEnabled:new oi,hasProductSchema:new pi},this.analyses=this.defaultAnalyses,(0,i.has)(this.options,"analyses")&&!(0,i.isUndefined)(this.options.analyses)&&(this.analyses=(0,i.pick)(this.defaultAnalyses,this.options.analyses))}}])&&yi(t.prototype,r),n&&yi(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),gi=function(e){return(0,i.isUndefined)(e)?e:e.replace(/\xA0/g," ")};function mi(e){return mi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},mi(e)}function bi(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,vi(n.key),n)}}function vi(e){var t=function(e,t){if("object"!==mi(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==mi(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===mi(t)?t:String(t)}var Di=function(){function e(t,r){var n,o,u;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),n=this,u={},(o=vi(o="args"))in n?Object.defineProperty(n,o,{value:u,enumerable:!0,configurable:!0,writable:!0}):n[o]=u,r=r||{},this.args=(0,i.defaults)(r,{title:"",keyword:"",keywords:[],titleWidth:0,url:"",permalink:"",description:"",thumbnail:"",thumbnailAlt:"",locale:"en_US",contentAI:!1,schemas:{}}),this.setText((0,i.isUndefined)(t)?"":t),this.args.shortLocale=this.args.locale.split("_")[0]}var t,r,n;return t=e,r=[{key:"get",value:function(e){return(0,i.has)(this.args,e)?this.args[e]:""}},{key:"getLower",value:function(e){return this.get(e+"Lower")}},{key:"hasKeyword",value:function(){return""!==this.args.keyword}},{key:"getKeyword",value:function(){return this.args.keyword}},{key:"setKeyword",value:function(e){this.args.keyword=gi(bn(e)),this.args.keywordLower=this.args.keyword.toLowerCase(),this.keywordPlurals=!1,this.keywordPermalink=!1,this.keywordPermalinkRaw=!1,this.keywordCombinations=!1}},{key:"setKeywords",value:function(e){this.args.keywords=(0,i.filter)((0,i.map)(e,(function(e){return gi(bn(e)).toLowerCase()})))}},{key:"hasTitle",value:function(){return""!==this.args.title}},{key:"getTitle",value:function(){return this.args.title}},{key:"setTitle",value:function(e){this.args.title=gi(bn(ze(e))),this.args.titleLower=this.args.title.toLowerCase()}},{key:"hasTitleWidth",value:function(){return 0!==this.args.titleWidth}},{key:"getTitleWidth",value:function(){return this.args.titleWidth}},{key:"hasPermalink",value:function(){return""!==this.args.permalink}},{key:"getPermalink",value:function(){return this.args.permalink}},{key:"setPermalink",value:function(e){this.args.permalink=e,this.args.permalinkLower=e.toLowerCase()}},{key:"hasDescription",value:function(){return""!==this.args.description}},{key:"getDescription",value:function(){return this.args.description}},{key:"setDescription",value:function(e){this.args.description=gi(bn(qe(e))),this.args.descriptionLower=this.args.description.toLowerCase()}},{key:"hasText",value:function(){return""!==this.text}},{key:"getText",value:function(){return this.text}},{key:"getTextLower",value:function(){return this.textLower}},{key:"setText",value:function(e){this.text=e||"",this.textLower="",""!==e&&(this.text=gi(bn(function(e){return(0,i.isUndefined)(e)?"":(0,i.flow)([Ne,Re,Te,b,v,c,ze])(e)}(e))),this.textLower=this.text.toLowerCase())}},{key:"hasUrl",value:function(){return""!==this.args.url}},{key:"getUrl",value:function(){return this.args.url}},{key:"setUrl",value:function(e){this.args.url=e}},{key:"hasLocale",value:function(){return""!==this.args.locale}},{key:"getLocale",value:function(){return this.args.locale}},{key:"getShortLocale",value:function(){return this.args.shortLocale}},{key:"hasThumbnail",value:function(){return""!==this.args.thumbnail}},{key:"getThumbnail",value:function(){return this.args.thumbnail}},{key:"setThumbnail",value:function(e){this.args.thumbnail=e}},{key:"hasThumbnailAltText",value:function(){return""!==this.args.thumbnailAlt}},{key:"getThumbnailAltText",value:function(){return this.args.thumbnailAlt}},{key:"setThumbnailAltText",value:function(e){this.args.thumbnailAlt=bn(e),this.args.thumbnailAltLower=e.toLowerCase()}},{key:"getKeywordPermalink",value:function(e){if(!1===this.keywordPermalink){var t=e.getResearch("slugify"),r=e.getResearch("removePunctuation"),n=this.getLower("keyword").replace(/\'/g,"").replace(/[-_.]+/g,"-");this.keywordPermalink=t(r(n)),this.keywordPermalinkRaw=this.keywordPermalink}return this.keywordPermalink}},{key:"getPermalinkWithStopwords",value:function(e){return!1===this.keywordPermalink&&this.getKeywordPermalink(e),this.keywordPermalinkRaw}},{key:"getKeywordCombination",value:function(e){return this.hasKeyword()?(!1===this.keywordCombinations&&this.generateCombinations(e),this.keywordCombinations):[]}},{key:"generateCombinations",value:function(e){var t=this.getLower("keyword"),r=e.getResearch("getWords"),n=e.getResearch("pluralize"),i=e.getResearch("combinations");this.keywordPlurals=new Map,r(t).forEach((function(e,t){this.keywordPlurals.set(t,{word:e,plural:n.get(e)})}),this),this.keywordPermalink=this.getKeywordPermalink(e),this.keywordCombinations=i(this.keywordPlurals)}},{key:"setContentAI",value:function(e){this.args.contentAI=e}},{key:"setSchema",value:function(e){this.args.schemas=e}}],r&&bi(t.prototype,r),n&&bi(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),wi=Di;function ki(e){return ki="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ki(e)}function Fi(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ei(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Fi(Object(r),!0).forEach((function(t){Ai(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fi(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ai(e,t,r){return(t=Si(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ci(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Si(n.key),n)}}function Si(e){var t=function(e,t){if("object"!==ki(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==ki(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ki(t)?t:String(t)}var ji=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.results=new Map}var t,r,n;return t=e,r=[{key:"getResults",value:function(){return Object.fromEntries(this.results)}},{key:"getResult",value:function(e){return this.results.get(this.cleanText(e))}},{key:"getScore",value:function(e){var t=this.cleanText(e);return this.results.has(t)?this.results.get(t).score:0}},{key:"update",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=this.cleanText(e);if(this.results.has(n)){var i=this.results.get(n);t=Ei(Ei({},i.results),t),r=i.isPrimary}this.results.set(n,{results:t,isPrimary:r,score:this.refreshScore(t)})}},{key:"refreshScore",value:function(e){var t=0,r=0,n=rankMath.localeFull.split("_")[0];return(0,i.forEach)(e,(function(e){t+=e.getScore(),r+=e.getMaxScore(n)})),(0,i.round)(t/r*100)}},{key:"deleteResult",value:function(e){this.results.delete(this.cleanText(e))}},{key:"isPrimary",value:function(e){var t=this.cleanText(e);return!!this.results.has(this.cleanText(t))&&this.results.get(this.cleanText(t)).isPrimary}},{key:"cleanText",value:function(e){return gi(e)}}],r&&Ci(t.prototype,r),n&&Ci(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),xi=ji;window.rankMathAnalyzer={Analysis:n,AnalysisResult:a,Analyzer:hi,Paper:wi,Researcher:We,ResultManager:xi}}()}();