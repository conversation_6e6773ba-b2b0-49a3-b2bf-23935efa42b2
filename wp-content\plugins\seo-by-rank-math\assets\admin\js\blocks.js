!function(){var e={6942:function(e,t){var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=i(e,a(n)))}return e}function a(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=i(t,n));return t}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";var e=lodash,t=wp.blocks,r=wp.element.createElement("svg",{viewBox:"0 0 462.03 462.03",xmlns:"http://www.w3.org/2000/svg",width:"20"},wp.element.createElement("g",null,wp.element.createElement("path",{d:"m462 234.84-76.17 3.43 13.43 21-127 81.18-126-52.93-146.26 60.97 10.14 24.34 136.1-56.71 128.57 54 138.69-88.61 13.43 21z"}),wp.element.createElement("path",{d:"m54.1 312.78 92.18-38.41 4.49 1.89v-54.58h-96.67zm210.9-223.57v235.05l7.26 3 89.43-57.05v-181zm-105.44 190.79 96.67 40.62v-165.19h-96.67z"}))),o=wp.i18n,a={attributes:{questions:[{visible:!0,titleWrapper:"div",title:"Question",content:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}]}},i=wp.element,l=wp.components,s=wp.blockEditor;function u(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=rankMath.links[e]||"";if(!n)return"#";if(!t)return n;var r={utm_source:"Plugin",utm_medium:encodeURIComponent(t),utm_campaign:"WP"};return n+"?"+Object.keys(r).map((function(e){return"".concat(e,"=").concat(r[e])})).join("&")}var c=wp.data;function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){h(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function h(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=p(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=p(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var b=(0,c.withSelect)((function(e,t){var n=(0,e("core/block-editor").getSettings)().imageSizes;return f(f({},t),{},{imageSizes:n})}))((function(t){var n=t.imageSizes,r=t.attributes,a=t.setAttributes,i=function(t){return(0,e.map)(t,(function(e){var t=e.name;return{value:e.slug,label:t}}))}(n);return wp.element.createElement(s.InspectorControls,{key:"inspector"},wp.element.createElement(l.PanelBody,{title:(0,o.__)("FAQ Options","rank-math")},wp.element.createElement(l.SelectControl,{label:(0,o.__)("List Style","rank-math"),value:r.listStyle,options:[{value:"",label:(0,o.__)("None","rank-math")},{value:"numbered",label:(0,o.__)("Numbered","rank-math")},{value:"unordered",label:(0,o.__)("Unordered","rank-math")}],onChange:function(e){a({listStyle:e})}}),wp.element.createElement(l.SelectControl,{label:(0,o.__)("Title Wrapper","rank-math"),value:r.titleWrapper,options:[{value:"h2",label:(0,o.__)("H2","rank-math")},{value:"h3",label:(0,o.__)("H3","rank-math")},{value:"h4",label:(0,o.__)("H4","rank-math")},{value:"h5",label:(0,o.__)("H5","rank-math")},{value:"h6",label:(0,o.__)("H6","rank-math")},{value:"p",label:(0,o.__)("P","rank-math")},{value:"div",label:(0,o.__)("DIV","rank-math")}],onChange:function(e){a({titleWrapper:e})}}),wp.element.createElement(l.SelectControl,{label:(0,o.__)("Image Size","rank-math"),value:r.sizeSlug,options:i,onChange:function(e){a({sizeSlug:e})}})),wp.element.createElement(l.PanelBody,{title:(0,o.__)("Styling Options","rank-math")},wp.element.createElement(l.TextControl,{label:(0,o.__)("Title Wrapper CSS Class(es)","rank-math"),value:r.titleCssClasses,onChange:function(e){a({titleCssClasses:e})}}),wp.element.createElement(l.TextControl,{label:(0,o.__)("Content Wrapper CSS Class(es)","rank-math"),value:r.contentCssClasses,onChange:function(e){a({contentCssClasses:e})}}),wp.element.createElement(l.TextControl,{label:(0,o.__)("List CSS Class(es)","rank-math"),value:r.listCssClasses,onChange:function(e){a({listCssClasses:e})}})))})),y=n(6942),d=n.n(y);function v(t,n){var r=(0,c.select)("core").getMedia,o=t?r(t):null;return null===o?null:n?function(t,n){var r=(0,e.get)(t,["media_details","sizes",n,"source_url"]);return r||(0,e.get)(t,["media_details","sizes","full","source_url"])}(o,n):o}var g=(0,c.withSelect)((function(e,t){var n=t.imageID,r=t.sizeSlug;return{imageUrl:n?v(n,r):null}}))((function(e){var t=e.imageUrl;return t?wp.element.createElement("img",{src:t,alt:""}):null})),w=function(e){var t=e.imageID,n=e.sizeSlug,r=e.open,a=e.removeImage,i=e.addButtonLabel,s=void 0===i?(0,o.__)("Add Image","rank-math"):i;return wp.element.createElement("div",{className:"rank-math-media-placeholder"},t>0&&wp.element.createElement(g,{imageID:t,sizeSlug:n}),t>0?wp.element.createElement(l.Button,{icon:"edit",className:"rank-math-replace-image",onClick:r}):wp.element.createElement(l.Button,{onClick:r,className:"rank-math-add-image",isPrimary:!0},s),t>0&&wp.element.createElement(l.Button,{icon:"no-alt",className:"rank-math-delete-image",onClick:a}))},k=wp.hooks;function _(e){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_(e)}function S(e){return function(e){if(Array.isArray(e))return E(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return E(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?E(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function C(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,T(r.key),r)}}function O(e,t,n){return t=P(t),function(e,t){if(t&&("object"==_(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,j()?Reflect.construct(t,n||[],P(e).constructor):t.apply(e,n))}function j(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(j=function(){return!!e})()}function P(e){return P=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},P(e)}function A(e,t){return A=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},A(e,t)}function N(e,t,n){return(t=T(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function T(e){var t=function(e,t){if("object"!=_(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=_(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==_(t)?t:t+""}var x=function(e){function t(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return N(e=O(this,t,[].concat(r)),"toggleVisibility",(function(){var t=e.props,n=t.setAttributes,r=t.index,o=S(e.props.questions);o[r].visible=!e.props.visible,n({questions:o})})),N(e,"deleteQuestion",(function(){var t=e.props,n=t.setAttributes,r=t.index,o=S(e.props.questions);o.splice(r,1),n({questions:o})})),e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&A(e,t)}(t,e),n=t,(r=[{key:"render",value:function(){var e=this,t=this.props,n=t.title,r=t.content,a=t.visible,i=t.imageID,u=t.sizeSlug,c=t.titleWrapper,p=t.titleCssClasses,m=t.contentCssClasses,f=d()("rank-math-question-wrapper",{"question-not-visible":!a});return wp.element.createElement("div",{className:f},wp.element.createElement("div",{className:"rank-math-item-header"},wp.element.createElement(s.RichText,{tagName:c,className:"rank-math-faq-question rank-math-block-title"+p,value:n,onChange:function(t){e.setQuestionProp("title",t)},placeholder:(0,o.__)("Question…","rank-math")}),wp.element.createElement("div",{className:"rank-math-block-actions"},(0,k.applyFilters)("rank_math_block_faq_actions","",this.props,this),wp.element.createElement(l.Button,{className:"rank-math-item-visbility",icon:a?"visibility":"hidden",onClick:this.toggleVisibility,label:(0,o.__)("Hide Question","rank-math"),showTooltip:!0}),wp.element.createElement(l.Button,{icon:"trash",className:"rank-math-item-delete",onClick:this.deleteQuestion,label:(0,o.__)("Delete Question","rank-math"),showTooltip:!0}))),wp.element.createElement("div",{className:"rank-math-item-content"},wp.element.createElement(s.RichText,{tagName:"div",className:"rank-math-faq-answer "+m,value:r,onChange:function(t){e.setQuestionProp("content",t)},placeholder:(0,o.__)("Enter the answer to the question","rank-math")}),wp.element.createElement(s.MediaUpload,{allowedTypes:["image"],multiple:!1,value:i,render:function(t){var n=t.open;return wp.element.createElement(w,{imageID:i,sizeSlug:u,open:n,removeImage:function(){e.setQuestionProp("imageID",0)}})},onSelect:function(t){e.setQuestionProp("imageID",t.id)}})))}},{key:"setQuestionProp",value:function(e,t){var n=this.props,r=n.setAttributes,o=n.index,a=S(this.props.questions);a[o][e]=t,r({questions:a})}}])&&C(n.prototype,r),a&&C(n,a),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,a}(i.Component),D=x,I=function(e){return"".concat(e,"-").concat((new Date).getTime())};function R(e){return R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},R(e)}function q(){return q=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},q.apply(null,arguments)}function z(e){return function(e){if(Array.isArray(e))return B(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return B(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?B(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function B(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function M(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,$(r.key),r)}}function Q(e,t,n){return t=H(t),function(e,t){if(t&&("object"==R(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,W()?Reflect.construct(t,n||[],H(e).constructor):t.apply(e,n))}function W(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(W=function(){return!!e})()}function H(e){return H=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},H(e)}function L(e,t){return L=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},L(e,t)}function $(e){var t=function(e,t){if("object"!=R(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=R(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==R(t)?t:t+""}var F=function(t){function n(){var e,t,r,o;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n);for(var a=arguments.length,i=new Array(a),l=0;l<a;l++)i[l]=arguments[l];return e=Q(this,n,[].concat(i)),t=e,o=function(){var t=z(e.props.attributes.questions);t.push({id:I("faq-question"),title:"",content:"",visible:!0}),e.props.setAttributes({questions:t})},(r=$(r="addNew"))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o,e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&L(e,t)}(n,t),r=n,(a=[{key:"render",value:function(){var e=this,t=this.props,n=t.className,r=t.isSelected,a=this.props.attributes.textAlign;return wp.element.createElement("div",{id:"rank-math-faq",className:"rank-math-block "+n},r&&wp.element.createElement(b,this.props),r&&wp.element.createElement(i.Fragment,null,wp.element.createElement(s.BlockControls,null,wp.element.createElement(s.AlignmentToolbar,{value:a,onChange:function(t){return e.props.setAttributes({textAlign:t})}}))),wp.element.createElement("ul",{style:{textAlign:a}},this.renderQuestions()),wp.element.createElement(l.Button,{isPrimary:!0,onClick:this.addNew},(0,o.__)("Add New FAQ","rank-math")),wp.element.createElement("a",{href:u("faq-schema-block","Add New FAQ"),rel:"noopener noreferrer",target:"_blank",title:(0,o.__)("More Info","rank-math"),className:"rank-math-block-info"},wp.element.createElement(l.Dashicon,{icon:"info"})))}},{key:"renderQuestions",value:function(){var t=this,n=this.props.attributes,r=n.sizeSlug,o=n.titleWrapper,a=n.titleCssClasses,i=n.contentCssClasses,l=this.props.attributes.questions;return(0,e.isEmpty)(l)&&(l=[{id:I("faq-question"),title:"",content:"",visible:!0}]),l.map((function(e,n){return wp.element.createElement("li",{key:e.id},wp.element.createElement(D,q({},e,{index:n,key:e.id+"-question",questions:l,setAttributes:t.props.setAttributes,sizeSlug:r,titleWrapper:o,titleCssClasses:a,contentCssClasses:i})))}))}}])&&M(r.prototype,a),c&&M(r,c),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,a,c}(i.Component),U=F,V={from:[{type:"block",blocks:["yoast/faq-block"],transform:function(e){var n={titleWrapper:"h3",questions:e.questions.map((function(e){return{title:e.jsonQuestion,content:e.jsonAnswer,visible:!0}})),className:e.className};return(0,t.createBlock)("rank-math/faq-block",n)}}]},Y=function(t){var n=t.attributes,r=n.questions,o=n.titleWrapper;return(0,e.isEmpty)(r)?null:wp.element.createElement("div",null,r.map((function(t,n){return(0,e.isEmpty)(t.title)||(0,e.isEmpty)(t.content)||!1===t.visible?null:wp.element.createElement("div",{className:"rank-math-faq-item",key:n},wp.element.createElement(s.RichText.Content,{tagName:o,value:t.title,className:"rank-math-question"}),wp.element.createElement(s.RichText.Content,{tagName:"div",value:t.content,className:"rank-math-answer"}))})))},G={attributes:{steps:[{visible:!0,titleWrapper:"div",title:"Step # 1",content:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}]}};function J(e){return J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},J(e)}function K(e){return function(e){if(Array.isArray(e))return X(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return X(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?X(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function X(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Z(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ae(r.key),r)}}function ee(e,t,n){return t=ne(t),function(e,t){if(t&&("object"==J(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,te()?Reflect.construct(t,n||[],ne(e).constructor):t.apply(e,n))}function te(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(te=function(){return!!e})()}function ne(e){return ne=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ne(e)}function re(e,t){return re=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},re(e,t)}function oe(e,t,n){return(t=ae(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ae(e){var t=function(e,t){if("object"!=J(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=J(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==J(t)?t:t+""}var ie=function(e){function t(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return oe(e=ee(this,t,[].concat(r)),"toggleVisibility",(function(){var t=e.props,n=t.setAttributes,r=t.index,o=K(e.props.steps);o[r].visible=!e.props.visible,n({steps:o})})),oe(e,"deleteStep",(function(){var t=e.props,n=t.setAttributes,r=t.index,o=K(e.props.steps);o.splice(r,1),n({steps:o})})),e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&re(e,t)}(t,e),n=t,(r=[{key:"render",value:function(){var e=this,t=this.props,n=t.title,r=t.content,a=t.visible,i=t.imageID,u=t.sizeSlug,c=t.titleWrapper,p=t.titleCssClasses,m=t.contentCssClasses,f=d()("rank-math-step-wrapper",{"step-not-visible":!a});return wp.element.createElement("div",{className:f},wp.element.createElement("div",{className:"rank-math-item-header"},wp.element.createElement(s.RichText,{tagName:c,className:"rank-math-howto-step-title rank-math-block-title"+p,value:n,onChange:function(t){e.setStepProp("title",t)},placeholder:(0,o.__)("Enter a step title","rank-math")}),wp.element.createElement("div",{className:"rank-math-block-actions"},(0,k.applyFilters)("rank_math_block_howto_actions","",this.props),wp.element.createElement(l.Button,{className:"rank-math-item-visbility",icon:a?"visibility":"hidden",onClick:this.toggleVisibility,title:(0,o.__)("Hide Step","rank-math")}),wp.element.createElement(l.Button,{icon:"trash",className:"rank-math-item-delete",onClick:this.deleteStep,title:(0,o.__)("Delete Step","rank-math")}))),wp.element.createElement(s.MediaUpload,{allowedTypes:["image"],multiple:!1,value:i,render:function(t){var n=t.open;return wp.element.createElement(w,{imageID:i,sizeSlug:u,open:n,addButtonLabel:(0,o.__)("Add Step Image","rank-math"),removeImage:function(){e.setStepProp("imageID",0)}})},onSelect:function(t){e.setStepProp("imageID",t.id)}}),wp.element.createElement(s.RichText,{tagName:"div",className:"rank-math-howto-step-content"+m,value:r,onChange:function(t){e.setStepProp("content",t)},placeholder:(0,o.__)("Enter a step description","rank-math")}))}},{key:"setStepProp",value:function(e,t){var n=this.props,r=n.setAttributes,o=n.index,a=K(this.props.steps);a[o][e]=t,r({steps:a})}}])&&Z(n.prototype,r),a&&Z(n,a),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,a}(i.Component),le=ie;function se(e){return se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},se(e)}function ue(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ce(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ue(Object(n),!0).forEach((function(t){pe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ue(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function pe(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=se(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=se(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==se(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var me=(0,c.withSelect)((function(e,t){var n=(0,e("core/block-editor").getSettings)().imageSizes;return ce(ce({},t),{},{imageSizes:n})}))((function(t){var n=t.imageSizes,r=t.attributes,a=t.setAttributes,i=function(t){return(0,e.map)(t,(function(e){var t=e.name;return{value:e.slug,label:t}}))}(n);return wp.element.createElement(s.InspectorControls,{key:"inspector"},wp.element.createElement(l.PanelBody,{title:(0,o.__)("HowTo Options","rank-math")},wp.element.createElement(l.SelectControl,{label:(0,o.__)("List Style","rank-math"),value:r.listStyle,options:[{value:"",label:(0,o.__)("None","rank-math")},{value:"numbered",label:(0,o.__)("Numbered","rank-math")},{value:"unordered",label:(0,o.__)("Unordered","rank-math")}],onChange:function(e){a({listStyle:e})}}),wp.element.createElement(l.SelectControl,{label:(0,o.__)("Title Wrapper","rank-math"),value:r.titleWrapper,options:[{value:"h2",label:(0,o.__)("H2","rank-math")},{value:"h3",label:(0,o.__)("H3","rank-math")},{value:"h4",label:(0,o.__)("H4","rank-math")},{value:"h5",label:(0,o.__)("H5","rank-math")},{value:"h6",label:(0,o.__)("H6","rank-math")},{value:"p",label:(0,o.__)("P","rank-math")},{value:"div",label:(0,o.__)("DIV","rank-math")}],onChange:function(e){a({titleWrapper:e})}}),wp.element.createElement(l.SelectControl,{label:(0,o.__)("Main Image Size","rank-math"),value:r.mainSizeSlug,options:i,onChange:function(e){a({mainSizeSlug:e})}}),wp.element.createElement(l.SelectControl,{label:(0,o.__)("Image Size","rank-math"),value:r.sizeSlug,options:i,onChange:function(e){a({sizeSlug:e})}})),wp.element.createElement(l.PanelBody,{title:(0,o.__)("Styling Options","rank-math")},wp.element.createElement(l.TextControl,{label:(0,o.__)("Step Title Wrapper CSS Class(es)","rank-math"),value:r.titleCssClasses,onChange:function(e){a({titleCssClasses:e})}}),wp.element.createElement(l.TextControl,{label:(0,o.__)("Step Content Wrapper CSS Class(es)","rank-math"),value:r.contentCssClasses,onChange:function(e){a({contentCssClasses:e})}}),wp.element.createElement(l.TextControl,{label:(0,o.__)("Step List CSS Class(es)","rank-math"),value:r.listCssClasses,onChange:function(e){a({listCssClasses:e})}})))}));function fe(e){return fe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fe(e)}function he(){return he=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},he.apply(null,arguments)}function be(e){return function(e){if(Array.isArray(e))return ye(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ye(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ye(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ye(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function de(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Se(r.key),r)}}function ve(e,t,n){return t=we(t),function(e,t){if(t&&("object"==fe(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,ge()?Reflect.construct(t,n||[],we(e).constructor):t.apply(e,n))}function ge(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(ge=function(){return!!e})()}function we(e){return we=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},we(e)}function ke(e,t){return ke=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ke(e,t)}function _e(e,t,n){return(t=Se(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Se(e){var t=function(e,t){if("object"!=fe(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=fe(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fe(t)?t:t+""}var Ee=function(t){function n(){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),_e(t=ve(this,n,arguments),"addNew",(function(){var n=t.props.attributes.steps,r=(0,e.isEmpty)(n)?[]:be(n);r.push({id:I("howto-step"),title:"",content:"",visible:!0}),t.props.setAttributes({steps:r})})),_e(t,"toggleDuration",(function(){t.props.setAttributes({hasDuration:!t.props.attributes.hasDuration})})),_e(t,"onSelectImage",(function(e){(0,t.props.setAttributes)({imageID:e.id})})),_e(t,"removeImage",(function(){(0,t.props.setAttributes)({imageID:0})}));var r=t.props.attributes.steps;return(0,e.isEmpty)(r)&&t.addNew(),t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ke(e,t)}(n,t),r=n,(a=[{key:"render",value:function(){var e=this,t=this.props,n=t.className,r=t.isSelected,a=t.attributes,u=t.setAttributes,c=a.imageID,p=a.mainSizeSlug,m=a.textAlign;return wp.element.createElement("div",{id:"rank-math-howto",className:"rank-math-block "+n},r&&wp.element.createElement(me,this.props),r&&wp.element.createElement(i.Fragment,null,wp.element.createElement(s.BlockControls,null,wp.element.createElement(s.AlignmentToolbar,{value:m,onChange:function(t){return e.props.setAttributes({textAlign:t})}}))),wp.element.createElement(s.MediaUpload,{allowedTypes:["image"],multiple:!1,value:c,render:function(t){var n=t.open;return wp.element.createElement("div",{className:"rank-math-howto-final-image"},wp.element.createElement(w,{imageID:c,sizeSlug:p,open:n,addButtonLabel:(0,o.__)("Add Final Image","rank-math"),removeImage:e.removeImage}))},onSelect:this.onSelectImage}),wp.element.createElement(s.RichText,{style:{textAlign:m},tagName:"div",className:"rank-math-howto-description",value:a.description,onChange:function(e){u({description:e})},placeholder:(0,o.__)("Enter a main description","rank-math")}),wp.element.createElement("div",{className:"rank-math-howto-duration"},wp.element.createElement("div",{className:"components-base-control rank-math-howto-duration-label"},wp.element.createElement("span",null,(0,o.__)("Duration","rank-math")),wp.element.createElement(l.ToggleControl,{checked:a.hasDuration,onChange:this.toggleDuration})),wp.element.createElement("div",{className:"rank-math-howto-duration-fields"+(a.hasDuration?"":" hidden")},wp.element.createElement(l.TextControl,{value:a.timeLabel,placeholder:(0,o.__)("Total time:","rank-math"),onChange:function(e){u({timeLabel:e})}}),wp.element.createElement(l.TextControl,{type:"number",value:a.days,placeholder:(0,o.__)("DD","rank-math"),onChange:function(e){u({days:e})}}),wp.element.createElement(l.TextControl,{type:"number",value:a.hours,placeholder:(0,o.__)("HH","rank-math"),onChange:function(e){u({hours:e})}}),wp.element.createElement(l.TextControl,{type:"number",value:a.minutes,placeholder:(0,o.__)("MM","rank-math"),onChange:function(e){u({minutes:e})}})),wp.element.createElement("div",{className:"rank-math-howto-duration-instructions"+(a.hasDuration?"":" hidden")},(0,o.__)("Optional, use first field to describe the duration.","rank-math"))),(0,k.applyFilters)("rank_math_block_howto_data","",this.props),wp.element.createElement("ul",{style:{textAlign:m}},this.renderSteps()),wp.element.createElement(l.Button,{isPrimary:!0,onClick:this.addNew},(0,o.__)("Add New Step","rank-math")),wp.element.createElement("a",{href:"http://rankmath.com/blog/howto-schema/",title:(0,o.__)("More Info","rank-math"),target:"_blank",rel:"noopener noreferrer",className:"rank-math-block-info"},wp.element.createElement(l.Dashicon,{icon:"info"})))}},{key:"renderSteps",value:function(){var t=this,n=this.props.attributes,r=n.steps,o=n.sizeSlug,a=n.titleWrapper,i=n.titleCssClasses,l=n.contentCssClasses;return(0,e.isEmpty)(r)?null:r.map((function(e,n){return wp.element.createElement("li",{key:e.id},wp.element.createElement(le,he({},e,{index:n,key:e.id+"-step",steps:r,setAttributes:t.props.setAttributes,sizeSlug:o,titleWrapper:a,titleCssClasses:i,contentCssClasses:l})))}))}}])&&de(r.prototype,a),u&&de(r,u),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,a,u}(i.Component),Ce=Ee,Oe=function(t){var n=t.attributes,r=n.steps,o=n.titleWrapper;return(0,e.isEmpty)(r)?null:wp.element.createElement("div",null,r.map((function(e,t){return!1===e.visible?null:wp.element.createElement("div",{className:"rank-math-howto-step",key:t},e.title&&wp.element.createElement(s.RichText.Content,{tagName:o,value:e.title,className:"rank-math-howto-title"}),e.content&&wp.element.createElement(s.RichText.Content,{tagName:"div",value:e.content,className:"rank-math-howto-content"}))})))},je={from:[{type:"block",blocks:["yoast/how-to-block"],transform:function(e){var n={steps:e.steps.map((function(e){return{visible:!0,id:I("howto-step"),title:e.jsonName,content:e.jsonText}})),titleWrapper:"h3",hasDuration:e.hasDuration,days:e.days,hours:e.hours,minutes:e.minutes,description:e.jsonDescription,className:e.className,listStyle:e.unorderedList?"unordered":""};return(0,t.createBlock)("rank-math/howto-block",n)}}]};function Pe(){return Pe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Pe.apply(null,arguments)}const Ae=Object.create(null);var Ne=wp.compose,Te=wp.apiFetch,xe=n.n(Te),De=wp.url;const Ie={};function Re(e){let{className:t}=e;return(0,i.createElement)(l.Placeholder,{className:t},(0,o.__)("Block rendered as empty."))}function qe(e){let{response:t,className:n}=e;const r=(0,o.sprintf)((0,o.__)("Error loading block: %s"),t.errorMsg);return(0,i.createElement)(l.Placeholder,{className:n},r)}function ze(e){let{children:t,showLoader:n}=e;return(0,i.createElement)("div",{style:{position:"relative"}},n&&(0,i.createElement)("div",{style:{position:"absolute",top:"50%",left:"50%",marginTop:"-9px",marginLeft:"-9px"}},(0,i.createElement)(l.Spinner,null)),(0,i.createElement)("div",{style:{opacity:n?"0.3":1}},t))}function Be(n){const{attributes:r,block:o,className:a,httpMethod:l="GET",urlQueryArgs:s,skipBlockSupportAttributes:u=!1,EmptyResponsePlaceholder:c=Re,ErrorResponsePlaceholder:p=qe,LoadingResponsePlaceholder:m=ze}=n,f=(0,i.useRef)(!0),[h,b]=(0,i.useState)(!1),y=(0,i.useRef)(),[d,v]=(0,i.useState)(null),g=(0,Ne.usePrevious)(n),[w,k]=(0,i.useState)(!1);function _(){var e,n;if(!f.current)return;k(!0);let a=r&&(0,t.__experimentalSanitizeBlockAttributes)(o,r);u&&(a=function(e){const{backgroundColor:t,borderColor:n,fontFamily:r,fontSize:o,gradient:a,textColor:i,className:l,...s}=e,{border:u,color:c,elements:p,spacing:m,typography:f,...h}=(null==e?void 0:e.style)||Ie;return{...s,style:h}}(a));const i="POST"===l,c=i?null:null!==(e=a)&&void 0!==e?e:null,p=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return(0,De.addQueryArgs)(`/wp/v2/block-renderer/${e}`,{context:"edit",...null!==t?{attributes:t}:{},...n})}(o,c,s),m=i?{attributes:null!==(n=a)&&void 0!==n?n:null}:null,h=y.current=xe()({path:p,data:m,method:i?"POST":"GET"}).then((e=>{f.current&&h===y.current&&e&&v(e.rendered)})).catch((e=>{f.current&&h===y.current&&v({error:!0,errorMsg:e.message})})).finally((()=>{f.current&&h===y.current&&k(!1)}));return h}const S=(0,Ne.useDebounce)(_,500);(0,i.useEffect)((()=>()=>{f.current=!1}),[]),(0,i.useEffect)((()=>{void 0===g?_():(0,e.isEqual)(g,n)||S()})),(0,i.useEffect)((()=>{if(!w)return;const e=setTimeout((()=>{b(!0)}),1e3);return()=>clearTimeout(e)}),[w]);const E=!!d,C=""===d,O=null==d?void 0:d.error;return w?(0,i.createElement)(m,Pe({},n,{showLoader:h}),E&&(0,i.createElement)(i.RawHTML,{className:a},d)):C||!E?(0,i.createElement)(c,n):O?(0,i.createElement)(p,Pe({response:d},n)):(0,i.createElement)(i.RawHTML,{className:a},d)}const Me={},Qe=(0,c.withSelect)((e=>{const t=e("core/editor");if(t){const e=t.getCurrentPostId();if(e&&"number"==typeof e)return{currentPostId:e}}return Me}))((e=>{let{urlQueryArgs:t=Me,currentPostId:n,...r}=e;const o=(0,i.useMemo)((()=>n?{post_id:n,...t}:t),[n,t]);return(0,i.createElement)(Be,Pe({urlQueryArgs:o},r))}));window&&window.wp&&window.wp.components&&(window.wp.components.ServerSideRender=(0,i.forwardRef)(((e,t)=>(function(e,t={}){const{since:n,version:r,alternative:o,plugin:a,link:i,hint:l}=t,s=`${e} is deprecated${n?` since version ${n}`:""}${r?` and will be removed${a?` from ${a}`:""} in version ${r}`:""}.${o?` Please use ${o} instead.`:""}${i?` See: ${i}`:""}${l?` Note: ${l}`:""}`;s in Ae||((0,k.doAction)("deprecated",e,t,s),console.warn(s),Ae[s]=!0)}("wp.components.ServerSideRender",{version:"6.2",since:"5.3",alternative:"wp.serverSideRender"}),(0,i.createElement)(Qe,Pe({},e,{ref:t}))))));var We,He=Qe;(0,t.updateCategory)("rank-math-blocks",{icon:r}),We=[(0,o.__)("FAQ","rank-math"),(0,o.__)("Frequently Asked Questions","rank-math"),(0,o.__)("Schema","rank-math"),(0,o.__)("SEO","rank-math"),(0,o.__)("Structured Data","rank-math"),(0,o.__)("Yoast","rank-math"),(0,o.__)("Rank Math","rank-math"),(0,o.__)("Block","rank-math"),(0,o.__)("Markup","rank-math"),(0,o.__)("Rich Snippet","rank-math")],(0,t.registerBlockType)("rank-math/faq-block",{title:(0,o.__)("FAQ by Rank Math","rank-math"),description:(0,o.__)("Easily add Schema-ready, SEO-friendly, Frequently Asked Questions to your content.","rank-math"),category:"rank-math-blocks",icon:"editor-ul",keywords:We,attributes:{listStyle:{type:"string"},sizeSlug:{type:"string",default:"thumbnail"},titleWrapper:{type:"string",default:"h3"},questions:{type:"array"},textAlign:{type:"string",default:""},listCssClasses:{type:"string",default:""},titleCssClasses:{type:"string",default:""},contentCssClasses:{type:"string",default:""}},example:a,edit:U,save:Y,transforms:V}),function(){var e=[(0,o.__)("HowTo","rank-math"),(0,o.__)("Schema","rank-math"),(0,o.__)("SEO","rank-math"),(0,o.__)("Structured Data","rank-math"),(0,o.__)("Yoast","rank-math"),(0,o.__)("Rank Math","rank-math"),(0,o.__)("Block","rank-math"),(0,o.__)("Markup","rank-math"),(0,o.__)("Rich Snippet","rank-math")],n=(0,k.applyFilters)("rank_math_block_howto_attributes",{hasDuration:{type:"boolean"},days:{type:"string"},hours:{type:"string"},minutes:{type:"string"},description:{type:"string"},steps:{type:"array"},sizeSlug:{type:"string",default:"full"},imageID:{type:"integer"},mainSizeSlug:{type:"string",default:"full"},titleWrapper:{type:"string",default:"h3"},textAlign:{type:"string",default:""},timeLabel:{type:"string"},listStyle:{type:"string"},listCssClasses:{type:"string",default:""},titleCssClasses:{type:"string",default:""},contentCssClasses:{type:"string",default:""}});(0,t.registerBlockType)("rank-math/howto-block",{title:(0,o.__)("HowTo by Rank Math","rank-math"),description:(0,o.__)("Easily add Schema-ready, SEO-friendly, HowTo block to your content.","rank-math"),category:"rank-math-blocks",icon:"editor-ol",supports:{multiple:!1},keywords:e,attributes:n,example:G,edit:Ce,save:Oe,transforms:je})}(),"undefined"==typeof rankMath||(0,e.isUndefined)(rankMath.canUser)||!rankMath.canUser.snippet||(0,e.isUndefined)(rankMath.schemas)||function(){var n=[(0,o.__)("Schema","rank-math"),(0,o.__)("Markup","rank-math"),(0,o.__)("Structured Data","rank-math"),(0,o.__)("Rich Snippet","rank-math"),(0,o.__)("SEO","rank-math"),(0,o.__)("Rank Math","rank-math"),(0,o.__)("Yoast","rank-math")];(0,t.registerBlockType)("rank-math/rich-snippet",{title:(0,o.__)("Schema by Rank Math","rank-math"),description:(0,o.__)("Add the Schema generated by Rank Math anywhere on your page using this Block.","rank-math"),category:"rank-math-blocks",icon:r,keywords:n,attributes:(0,k.applyFilters)("rank_math_block_schema_attributes",{post_id:{default:rankMath.objectID}}),edit:function(t){var n=t.setAttributes,r=t.attributes,a=[];return(0,e.forEach)(r,(function(t,i){"post_id"!==i?"className"!==i&&a.push(wp.element.createElement(l.TextControl,{key:i,label:(0,o.__)((0,e.startCase)(i),"rank-math"),value:r[i],type:"string",onChange:function(e){var t={};t[i]=e,n(t)}})):a.push(wp.element.createElement(l.TextControl,{key:i,label:(0,o.__)((0,e.startCase)(i),"rank-math"),value:r[i],type:"number",min:1,step:1,onChange:function(e){var t={};t[i]=e||rankMath.objectID,n(t)}}))})),wp.element.createElement("div",null,wp.element.createElement(s.InspectorControls,{key:"inspector"},a),wp.element.createElement(He,{block:"rank-math/rich-snippet",attributes:r}))}})}()}()}();