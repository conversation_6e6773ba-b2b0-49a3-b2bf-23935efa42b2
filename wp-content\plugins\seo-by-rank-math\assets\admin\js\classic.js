!function(){"use strict";var t={n:function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,{a:r}),r},d:function(e,r){for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r:function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{deleteSchema:function(){return Ne},lockModifiedDate:function(){return pt},refreshResults:function(){return ve},resetDirtyMetadata:function(){return ft},resetRedirection:function(){return Et},resetStore:function(){return dt},saveSchema:function(){return Ue},saveTemplate:function(){return Fe},schemaUpdated:function(){return Oe},setEditingSchemaId:function(){return je},setEditorTab:function(){return Ae},setTemplateTab:function(){return Me},setVersion:function(){return be},toggleFrontendScore:function(){return at},toggleIsDiviPageSettingsBarActive:function(){return ke},toggleIsDiviRankMathModalActive:function(){return we},toggleLoaded:function(){return me},toggleSchemaEditor:function(){return Ie},toggleSchemaTemplates:function(){return Ee},toggleSnippetEditor:function(){return Ht},updateAIScore:function(){return Le},updateAdvancedRobots:function(){return ct},updateAnalysisScore:function(){return ot},updateAppData:function(){return et},updateAppUi:function(){return rt},updateBreadcrumbTitle:function(){return lt},updateCanonicalUrl:function(){return ut},updateDescription:function(){return St},updateEditSchema:function(){return Re},updateEditSchemas:function(){return Ce},updateFacebookDescription:function(){return mt},updateFacebookHasOverlay:function(){return vt},updateFacebookImage:function(){return yt},updateFacebookImageID:function(){return gt},updateFacebookImageOverlay:function(){return bt},updateFacebookTitle:function(){return ht},updateFeaturedImage:function(){return Pt},updateHasRedirect:function(){return Ot},updateHighlightedParagraphs:function(){return _e},updateKeywords:function(){return nt},updatePermalink:function(){return wt},updatePillarContent:function(){return it},updatePostID:function(){return kt},updatePrimaryTermID:function(){return Tt},updateRedirection:function(){return Dt},updateRedirectionItem:function(){return It},updateRobots:function(){return st},updateSchemas:function(){return xe},updateSelectedKeyword:function(){return ye},updateSerpDescription:function(){return Gt},updateSerpSlug:function(){return zt},updateSerpTitle:function(){return Kt},updateSnippetPreviewType:function(){return Bt},updateSocialTab:function(){return ge},updateTitle:function(){return _t},updateTwitterAppCountry:function(){return he},updateTwitterAppDescription:function(){return ie},updateTwitterAppGoogleplayID:function(){return pe},updateTwitterAppGoogleplayName:function(){return fe},updateTwitterAppGoogleplayUrl:function(){return de},updateTwitterAppIpadID:function(){return ce},updateTwitterAppIpadName:function(){return se},updateTwitterAppIpadUrl:function(){return le},updateTwitterAppIphoneID:function(){return ae},updateTwitterAppIphoneName:function(){return oe},updateTwitterAppIphoneUrl:function(){return ue},updateTwitterAuthor:function(){return qt},updateTwitterCardType:function(){return Wt},updateTwitterDescription:function(){return Jt},updateTwitterHasOverlay:function(){return Xt},updateTwitterImage:function(){return Qt},updateTwitterImageID:function(){return Yt},updateTwitterImageOverlay:function(){return Zt},updateTwitterPlayerSize:function(){return ee},updateTwitterPlayerStreamCtype:function(){return ne},updateTwitterPlayerStreamUrl:function(){return re},updateTwitterPlayerUrl:function(){return te},updateTwitterTitle:function(){return $t},updateTwitterUseFacebook:function(){return Vt}});var r={};t.r(r),t.d(r,{appData:function(){return Ve},appUi:function(){return Qe}});var n={};t.r(n),t.d(n,{getAdvancedRobots:function(){return ir},getAnalysisScore:function(){return tr},getAppData:function(){return Xe},getBreadcrumbTitle:function(){return or},getCanonicalUrl:function(){return ar},getDescription:function(){return Er},getDirtyMetadata:function(){return Ze},getEditSchemas:function(){return $r},getEditingSchema:function(){return Jr},getEditorTab:function(){return Yr},getFacebookAuthor:function(){return kr},getFacebookDescription:function(){return br},getFacebookHasOverlay:function(){return Sr},getFacebookImage:function(){return _r},getFacebookImageID:function(){return wr},getFacebookImageOverlay:function(){return Pr},getFacebookTitle:function(){return vr},getFeaturedImage:function(){return Or},getFeaturedImageHtml:function(){return jr},getHighlightedParagraphs:function(){return gr},getKeywords:function(){return er},getPermalink:function(){return Ir},getPillarContent:function(){return rr},getPostID:function(){return Tr},getPreviewSchema:function(){return qr},getPrimaryTermID:function(){return Ar},getRedirectionID:function(){return Mr},getRedirectionItem:function(){return Rr},getRedirectionType:function(){return Cr},getRedirectionUrl:function(){return xr},getRichSnippets:function(){return ur},getRobots:function(){return nr},getSchemas:function(){return Wr},getSelectedKeyword:function(){return pr},getSerpDescription:function(){return Kr},getSerpSlug:function(){return Lr},getSerpTitle:function(){return Fr},getShowScoreFrontend:function(){return cr},getSnippetPreviewType:function(){return Gr},getSocialTab:function(){return fr},getTemplateTab:function(){return Qr},getTitle:function(){return Dr},getTwitterAppCountry:function(){return vn},getTwitterAppDescription:function(){return cn},getTwitterAppGoogleplayID:function(){return mn},getTwitterAppGoogleplayName:function(){return yn},getTwitterAppGoogleplayUrl:function(){return gn},getTwitterAppIpadID:function(){return fn},getTwitterAppIpadName:function(){return dn},getTwitterAppIpadUrl:function(){return hn},getTwitterAppIphoneID:function(){return sn},getTwitterAppIphoneName:function(){return ln},getTwitterAppIphoneUrl:function(){return pn},getTwitterAuthor:function(){return rn},getTwitterCardType:function(){return Zr},getTwitterDescription:function(){return en},getTwitterHasOverlay:function(){return on},getTwitterImage:function(){return an},getTwitterImageID:function(){return nn},getTwitterImageOverlay:function(){return un},getTwitterPlayerSize:function(){return kn},getTwitterPlayerStream:function(){return wn},getTwitterPlayerStreamCtype:function(){return _n},getTwitterPlayerUrl:function(){return bn},getTwitterTitle:function(){return tn},getTwitterUseFacebook:function(){return Xr},hasRedirect:function(){return Ur},hasSchemaUpdated:function(){return Vr},isDiviPageSettingsBarActive:function(){return mr},isDiviRankMathModalActive:function(){return yr},isLoaded:function(){return lr},isModifiedDateLocked:function(){return sr},isPro:function(){return hr},isRefreshing:function(){return dr},isSchemaEditorOpen:function(){return Hr},isSchemaTemplatesOpen:function(){return Br},isSnippetEditorOpen:function(){return zr}});var i=jQuery,a=t.n(i),o=lodash,u=wp.hooks,c=wp.data,s=wp.element,l=rankMathAnalyzer,p=wp.i18n;var f=wp.apiFetch,d=t.n(f),h={"&amp;":"&","&quot;":'"',"&#39;":"'"},m=/&(?:amp|quot|#(0+)?39);/g,y=RegExp(m.source);var g=function(t){return t&&y.test(t)?t.replace(m,(function(t){return h[t]||"'"})):t||""},v=function(t){return t.replace(/<\/?[a-z][^>]*?>/gi,"\n")},b=function(t){return t.replace(/<style[^>]*>([\S\s]*?)<\/style>/gim,"")},k=function(t){return t.replace(/&nbsp;|&#160;/gi," ").replace(/\s{2,}/g," ").replace(/\s\./g,".").replace(/^\s+|\s+$/g,"")},w=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return t.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,e)},_=function(t){return t.replace(/[‘’‛`]/g,"'").replace(/[“”〝〞〟‟„]/g,'"')},S=function(t){return t.replace(/<!--[\s\S]*?-->/g,"")},P=function(t){return t.replace(/&\S+?;/g,"")};function T(t){return(0,o.isUndefined)(t)||!t?"":(0,o.flow)([b,w,v,S,P,k,_])(t)}var D={};(0,o.isUndefined)(rankMath.assessor)||(0,o.forEach)(rankMath.assessor.diacritics,(function(t,e){return D[e]=new RegExp(t,"g")}));var I=function(t){if((0,o.isUndefined)(t))return t;for(var e in D)t=t.replace(D[e],e);return t},E=wp.compose,O=wp.components;function j(t){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},j(t)}function A(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function M(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?A(Object(r),!0).forEach((function(e){C(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function C(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==j(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==j(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===j(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var x=(0,E.compose)((0,c.withSelect)((function(t){return{lock:t("rank-math").isModifiedDateLocked()}})),(0,c.withDispatch)((function(t){return{onChange:function(e){t("rank-math").lockModifiedDate(e);var r=wp.data.select("core/editor");if(!(0,o.isUndefined)(r)&&!(0,o.isUndefined)(r.getEditedPostAttribute("meta"))){var n=M(M({},r.getEditedPostAttribute("meta")),{},{rank_math_lock_modified_date:e});t("core/editor").editPost({meta:n})}}}})))((function(t){var e=t.lock,r=t.onChange;return wp.element.createElement("div",null,wp.element.createElement(O.ToggleControl,{label:(0,p.__)("Lock Modified Date","rank-math"),checked:e,onChange:function(t){return r(t)}}))}));function R(t){return R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},R(t)}function U(){U=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,o=Object.create(a.prototype),u=new j(n||[]);return i(o,"_invoke",{value:D(t,r,u)}),o}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var f="suspendedStart",d="suspendedYield",h="executing",m="completed",y={};function g(){}function v(){}function b(){}var k={};s(k,o,(function(){return this}));var w=Object.getPrototypeOf,_=w&&w(w(A([])));_&&_!==r&&n.call(_,o)&&(k=_);var S=b.prototype=g.prototype=Object.create(k);function P(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function T(t,e){function r(i,a,o,u){var c=p(t[i],t,a);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==R(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,o,u)}),(function(t){r("throw",t,o,u)})):e.resolve(l).then((function(t){s.value=t,o(s)}),(function(t){return r("throw",t,o,u)}))}u(c.arg)}var a;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return a=a?a.then(i,i):i()}})}function D(e,r,n){var i=f;return function(a,o){if(i===h)throw new Error("Generator is already running");if(i===m){if("throw"===a)throw o;return{value:t,done:!0}}for(n.method=a,n.arg=o;;){var u=n.delegate;if(u){var c=I(u,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===f)throw i=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=h;var s=p(e,r,n);if("normal"===s.type){if(i=n.done?m:d,s.arg===y)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(i=m,n.method="throw",n.arg=s.arg)}}}function I(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,I(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var a=p(i,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var o=a.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function A(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(R(e)+" is not iterable")}return v.prototype=b,i(S,"constructor",{value:b,configurable:!0}),i(b,"constructor",{value:v,configurable:!0}),v.displayName=s(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,c,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},P(T.prototype),s(T.prototype,u,(function(){return this})),e.AsyncIterator=T,e.async=function(t,r,n,i,a){void 0===a&&(a=Promise);var o=new T(l(t,r,n,i),a);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},P(S),s(S,c,"Generator"),s(S,o,(function(){return this})),s(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=A,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return u.type="throw",u.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],u=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;O(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:A(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function N(t,e,r,n,i,a,o){try{var u=t[a](o),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,i)}function F(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var a=t.apply(e,r);function o(t){N(a,n,i,o,u,"next",t)}function u(t){N(a,n,i,o,u,"throw",t)}o(void 0)}))}}function L(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==R(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==R(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===R(a)?a:String(a)),n)}var i,a}var K=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.analyzer=new l.Analyzer({i18n:p,analyses:rankMath.assessor.researchesTests}),this.dataCollector=e,this.promises=[],this.hooks(),this.registerRefresh(),this.saveEvent()}var e,r,n;return e=t,r=[{key:"hooks",value:function(){this.updateKeywordResult=this.updateKeywordResult.bind(this),this.sanitizeData=this.sanitizeData.bind(this),this.addScoreElem=this.addScoreElem.bind(this),this.addLockModifiedDate=this.addLockModifiedDate.bind(this),(0,u.addAction)("rankMath_analysis_keywordUsage_updated","rank-math",this.updateKeywordResult),(0,u.addFilter)("rank_math_sanitize_meta_value","rank-math",this.sanitizeData),(0,u.addFilter)("rank_math_sanitize_data","rank-math",this.sanitizeData),(0,u.addAction)("rank_math_loaded","rank-math",this.addLockModifiedDate,11),(0,u.addAction)("rank_math_loaded","rank-math",this.addScoreElem,11)}},{key:"addScoreElem",value:function(){var t=this;rankMath.showScore&&rankMath.canUser.analysis&&rankMath.canUser.general&&(this.scoreElem=a()('<div class="misc-pub-section rank-math-seo-score"></div>'),a()("#misc-publishing-actions").append(this.scoreElem),setTimeout((function(){t.scoreText='<span class="score-text"><span class="score-icon"><i class="rm-icon-rank-math"></i></span> SEO: <strong>'+p.__("Not available","rank-math")+"</strong></span>",t.scoreElem.html(t.scoreText),t.scoreText=t.scoreElem.find("strong"),t.fkScoreText='<span class="score-text">'+p.__("Not available","rank-math")+"</span>",t.fkScoreElem=a()('<div class="rank-math-seo-score below-focus-keyword">'+t.fkScoreText+"</div>"),t.fkScoreText=t.fkScoreElem.find("span"),a()("#rank-math-metabox-wrapper .rank-math-focus-keyword").find("tags").parent("div").append(t.fkScoreElem),t.updateScore=t.updateScore.bind(t),t.updateScore(),(0,u.addAction)("rank_math_refresh_results","rank-math",t.updateScore)}),1500))}},{key:"addLockModifiedDate",value:function(){var t=a()("#misc-publishing-actions");t.length&&rankMath.showLockModifiedDate&&(t.append('<div class="misc-pub-section rank-math-lock-modified-date" id="rank-math-lock-modified-date"></div>'),(0,s.createRoot)(document.getElementById("rank-math-lock-modified-date")).render(wp.element.createElement(x,null)))}},{key:"updateScore",value:function(){var t,e=rankMathEditor.resultManager.getScore(rankMathEditor.getPrimaryKeyword()),r=100<(t=e)?"bad-fk dark":80<t?"good-fk":50<t?"ok-fk":"bad-fk";this.scoreElem.removeClass("loading bad-fk ok-fk good-fk"),this.fkScoreElem.removeClass("loading bad-fk ok-fk good-fk"),this.scoreElem.addClass(r),this.fkScoreElem.addClass(r),this.scoreText.html(e+" / 100"),this.fkScoreText.html(e+" / 100")}},{key:"sanitizeData",value:function(t,e){return"schemas"===e||(0,o.isObject)(t)||(0,o.isEmpty)(t)?t:(r=t,(0,o.isUndefined)(r)?"":(0,o.flow)([b,w,v,S])(r));var r}},{key:"getPaper",value:function(t,e){var r=this.dataCollector.getData(),n=new l.Paper("",{locale:rankMath.localeFull});n.setTitle((0,c.select)("rank-math").getSerpTitle()),n.setPermalink(r.slug),n.setDescription((0,c.select)("rank-math").getSerpDescription()),n.setUrl(r.permalink),n.setText(g((0,u.applyFilters)("rank_math_content",r.content))),n.setKeyword(t),n.setKeywords(e),n.setSchema((0,c.select)("rank-math").getSchemas()),(0,o.isUndefined)(r.featuredImage)||(n.setThumbnail(r.featuredImage.source_url),n.setThumbnailAltText(I(r.featuredImage.alt_text)));var i=(0,c.select)("rank-math-content-ai");if(!(0,o.isEmpty)(i)){var a=i.getData(),s=i.getScore();n.setContentAI(s||!(0,o.isEmpty)(a.keyword))}return n}},{key:"registerRefresh",value:function(){var t=this;this.refresh=(0,o.debounce)((function(e){if(!1!==(0,c.select)("rank-math").isLoaded()){var r=(0,c.select)("rank-math").getKeywords().split(","),n=[];(0,u.doAction)("rank_math_"+e+"_refresh"),r.map((function(e,i){var a=t.getPaper(I(e),r),o=0===i?rankMath.assessor.researchesTests:t.filterTests(t.getSecondaryKeywordTests());n.push(t.analyzer.analyzeSome(o,a).then((function(t){rankMathEditor.resultManager.update(a.getKeyword(),t,0===i),0===i&&(0,c.dispatch)("rank-math").updateAnalysisScore(rankMathEditor.resultManager.getScore(a.getKeyword()))}))),Promise.all(n).then((function(){(0,c.dispatch)("rank-math").refreshResults(),t.refreshResults()}))}))}}),500)}},{key:"updateKeywordResult",value:function(t,e){rankMathEditor.resultManager.update(t,{keywordNotUsed:e}),t===rankMathEditor.getSelectedKeyword().toLowerCase()&&(0,c.dispatch)("rank-math").refreshResults()}},{key:"getSecondaryKeywordTests",value:function(){return["keywordInContent","lengthContent","keywordInSubheadings","keywordDensity","lengthPermalink","linksHasExternals","linksNotAllExternals","linksHasInternal","titleSentiment","titleHasPowerWords","titleHasNumber","contentHasTOC","contentHasShortParagraphs","contentHasAssets"]}},{key:"getPrimaryKeyword",value:function(){var t=(0,c.select)("rank-math").getKeywords();return I(t.split(",")[0])}},{key:"getSelectedKeyword",value:function(){var t=(0,c.select)("rank-math").getKeywords(),e=(0,c.select)("rank-math").getSelectedKeyword(),r=""!==e.data.value?e.data.value:t.split(",")[0];return I(r)}},{key:"getResearch",value:function(t){return this.analyzer.researcher.getResearch(t)}},{key:"refreshResults",value:function(){(0,u.doAction)("rank_math_refresh_results")}},{key:"filterTests",value:function(t){return(0,o.intersection)(t,rankMath.assessor.researchesTests)}},{key:"saveEvent",value:function(){var t=this;if(!(0,o.isUndefined)(this.dataCollector.form)&&!(0,o.isUndefined)(this.dataCollector.updateBtn)){var e=!0;this.dataCollector.form.on("submit",(function(r){if(e&&!(0,o.isUndefined)(r.originalEvent)){r.preventDefault();var n=t.dataCollector.updateBtn.data("disable")&&t.dataCollector.saveDraft.length?t.dataCollector.saveDraft:t.dataCollector.updateBtn;n.addClass("disabled").parent().find(".spinner").addClass("is-active"),e=!1;var i=t.saveMeta(),a=t.saveSchemas(i),u=t.saveRedirection(a);return Promise.all([i,a,u]).then((function(){n.removeClass("disabled").trigger("click")})).catch((function(){n.removeClass("disabled").trigger("click")})),!1}}))}}},{key:"saveMeta",value:function(){return new Promise((function(t){var e=(0,c.select)("rank-math").getDirtyMetadata();(0,o.isEmpty)(e)?t(!0):(d()({method:"POST",path:"rankmath/v1/updateMeta",data:{objectID:rankMath.objectID,objectType:rankMath.objectType,meta:e,content:rankMathEditor.assessor.dataCollector.getContent()}}).then((function(){t(!0)})).catch((function(){t(!0)})),(0,c.dispatch)("rank-math").resetDirtyMetadata())}))}},{key:"saveRedirection",value:function(t){return new Promise(function(){var e=F(U().mark((function e(r){var n,i,a;return U().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t;case 2:if(n=(0,c.select)("rank-math").getRedirectionItem(),!(0,o.isEmpty)(n)){e.next=6;break}return r(!0),e.abrupt("return");case 6:n.objectID=window.rankMath.objectID,n.objectType=window.rankMath.objectType,n.redirectionSources=rankMathEditor.assessor.dataCollector.getData("permalink"),i=(0,c.dispatch)("rank-math"),a=(0,c.dispatch)("core/notices"),i.resetRedirection(),d()({method:"POST",path:"rankmath/v1/updateRedirection",data:n}).then((function(t){"delete"===t.action?(a.createInfoNotice(t.message,{id:"redirectionNotice"}),i.updateRedirection("redirectionID",0)):"update"===t.action?a.createInfoNotice(t.message,{id:"redirectionNotice"}):"new"===t.action&&(i.updateRedirection("redirectionID",t.id),a.createSuccessNotice(t.message,{id:"redirectionNotice"})),setTimeout((function(){a.removeNotice("redirectionNotice")}),2e3),r(!0)})).catch((function(){r(!0)}));case 13:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())}},{key:"saveSchemas",value:function(t){return new Promise(function(){var e=F(U().mark((function e(r){var n;return U().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t;case 2:if(n=(0,c.select)("rank-math").getSchemas(),!(0,o.isEmpty)(n)&&(0,c.select)("rank-math").hasSchemaUpdated()){e.next=6;break}return r(!0),e.abrupt("return");case 6:d()({method:"POST",path:"rankmath/v1/updateSchemas",data:{objectID:rankMath.objectID,objectType:rankMath.objectType,schemas:n}}).then((function(){r(!0)})).catch((function(){r(!0)}));case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())}}],r&&L(e.prototype,r),n&&L(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),z=K,G=function(){return!((0,o.isNil)(window.wp)||(0,o.isNil)(wp.data)||(0,o.isNil)(wp.data.select("core/editor"))||!window.document.body.classList.contains("block-editor-page")||!(0,o.isFunction)(wp.data.select("core/editor").getEditedPostAttribute))},H=function(){return!(0,o.isNull)(document.getElementById("site-editor"))&&(0,o.endsWith)(wp.data.select("core/edit-site").getEditedPostId(),"//home")};function B(t){return B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},B(t)}function V(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==B(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==B(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===B(a)?a:String(a)),n)}var i,a}var W=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),(rankMath.is_front_page||H())&&((0,u.addFilter)("rankMath_analysis_contentLength","rank-math",this.contentLength),(0,u.addFilter)("rankMath_analysis_contentLength_boundaries","rank-math",this.contentLengthBoundary))}var e,r,n;return e=t,(r=[{key:"contentLength",value:function(t){return{hasScore:t.hasScore,failed:(0,p.__)("Content is %1$d words long. Consider using at least 300 words.","rank-math"),tooltipText:(0,p.__)("Minimum recommended content length should be 300 words.","rank-math"),emptyContent:(0,p.sprintf)((0,p.__)("Content should be %1$s long.","rank-math"),'<a href="https://rankmath.com/kb/score-100-in-tests/?utm_source=Plugin&utm_campaign=WP#overall-content-length" target="_blank">'+(0,p.__)("300 words","rank-math")+"</a>")}}},{key:"contentLengthBoundary",value:function(){return{recommended:{boundary:299,score:8},belowRecommended:{boundary:200,score:5},low:{boundary:50,score:2}}}}])&&V(e.prototype,r),n&&V(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();function $(t){return $="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$(t)}function J(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==$(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==$(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===$(a)?a:String(a)),n)}var i,a}var q=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.methods=[],this.init=this.init.bind(this),this.refresh=this.refresh.bind(this),(0,u.addAction)("rank_math_loaded","rank-math",this.init)}var e,r,n;return e=t,r=[{key:"init",value:function(){this.methods.length&&rankMathEditor.refresh("content")}},{key:"registerPlugin",value:function(){console.warn("RankMathApp.registerPlugin is deprecated.")}},{key:"refresh",value:function(t){console.warn("RankMathApp.refresh is deprecated, use rankMathEditor.refresh()"),this.methods.push(t)}},{key:"reloadPlugin",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"content";console.warn("RankMathApp.reloadPlugin is deprecated, use rankMathEditor.refresh()"),this.refresh(e)}}],r&&J(e.prototype,r),n&&J(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),Y=q;function Q(t){return Q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Q(t)}function X(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Z(n.key),n)}}function Z(t){var e=function(t,e){if("object"!==Q(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Q(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Q(e)?e:String(e)}var tt=new(function(){function t(){var e,r,n;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e=this,n=null,(r=Z(r="map"))in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n}var e,r,n;return e=t,(r=[{key:"swap",value:function(t,e){var r=this;if(!(t=t||""))return"";var n=new RegExp(/%(([a-z0-9_-]+)\(([^)]*)\)|[^\s]+)%/,"giu");return t.replace(" %page%","").replace("%sep% %sep%","%sep%").replace(n,(function(t){return r.replace(e,t)})).trim()}},{key:"replace",value:function(t,e){var r=e.toLowerCase().slice(1,-1);return["term_description","user_description"].includes(r)?"undefined"!=typeof tinymce&&void 0!==tinymce.editors.rank_math_description_editor?tinymce.editors.rank_math_description_editor.getContent():a()("#description").val():r.includes("customfield(")?(r=r.replace("customfield(","").replace(")",""))in rankMath.customFields?rankMath.customFields[r]:"":(t=t||this.getMap(),(r="seo_description"===(r="seo_title"===(r=r.includes("(")?r.split("(")[0]:r)?"title":r)?"excerpt":r)in t?t[r]:"")}},{key:"getMap",value:function(){var t=this;return null!==this.map||(this.map={},a().each(rankMath.variables,(function(e,r){e=e.toLowerCase().replace(/%+/g,"").split("(")[0],t.map[e]=r.example}))),this.map}},{key:"setVariable",value:function(t,e){null!==this.map?this.map[t]=e:void 0!==rankMath.variables[t]&&(rankMath.variables[t].example=e)}}])&&X(e.prototype,r),n&&X(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}());function et(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return e=(0,u.applyFilters)("rank_math_sanitize_data",e,t,r),null!==n&&(n=(0,u.applyFilters)("rank_math_sanitize_meta_value",n,t,r)),n=null===n?e:n,(0,u.doAction)("rank_math_data_changed",t,e,r),{type:"RANK_MATH_APP_DATA",key:t,value:e,metaKey:r,metaValue:n}}function rt(t,e){return(0,u.doAction)("rank_math_update_app_ui",t,e),{type:"RANK_MATH_APP_UI",key:t,value:e}}function nt(t){return tt.setVariable("focuskw",t.split(",")[0]),rankMathEditor.refresh("keyword"),et("keywords",t,"rank_math_focus_keyword")}function it(t){return et("pillarContent",t,"rank_math_pillar_content",!0===t?"on":"off")}function at(t){return et("showScoreFrontend",t,"rank_math_dont_show_seo_score",!0===t?"off":"on")}function ot(t){return et("score",t,"rank_math_seo_score")}function ut(t){return et("canonicalUrl",t,"rank_math_canonical_url")}function ct(t){return et("advancedRobots",t,"rank_math_advanced_robots")}function st(t){return et("robots",t,"rank_math_robots",Object.keys(t))}function lt(t){return et("breadcrumbTitle",t,"rank_math_breadcrumb_title")}function pt(t){return et("lockModifiedDate",t,"rank_math_lock_modified_date")}function ft(){return et("dirtyMetadata",{})}function dt(t){return{type:"RESET_STORE",value:t}}function ht(t){return et("facebookTitle",t,"rank_math_facebook_title")}function mt(t){return et("facebookDescription",t,"rank_math_facebook_description")}function yt(t){return et("facebookImage",t,"rank_math_facebook_image")}function gt(t){return et("facebookImageID",t,"rank_math_facebook_image_id")}function vt(t){return et("facebookHasOverlay",t,"rank_math_facebook_enable_image_overlay",!0===t?"on":"off")}function bt(t){return et("facebookImageOverlay",t,"rank_math_facebook_image_overlay")}function kt(t){return rankMath.objectID=t,et("postID",t)}function wt(t){return et("permalink",t,"permalink")}function _t(t){return et("title",t,"rank_math_title")}function St(t){return et("description",t,"rank_math_description")}function Pt(t){return et("featuredImage",t)}function Tt(t,e){return et("primaryTerm",parseInt(t),"rank_math_primary_"+e)}function Dt(t,e){return et(t,e)}function It(t){return rt("redirectionItem",t)}function Et(){return rt("redirectionItem",{})}function Ot(t){return rt("hasRedirect",t)}var jt=wp.autop,At="[^<>&/\\[\\]\0- =]+?",Mt=new RegExp("\\["+At+"( [^\\]]+?)?\\]","g"),Ct=new RegExp("\\[/"+At+"\\]","g"),xt=function(t){return t.replace(Mt,"").replace(Ct,"")},Rt=function(t,e){var r=function(t,e){for(var r,n=/<p(?:[^>]+)?>(.*?)<\/p>/gi,i=[];null!==(r=n.exec(t));)i.push(r);return(0,o.map)(i,(function(t){return e?T(t[1]):t[1]}))}(t=(0,o.flow)([xt,S,jt.autop])(t),e=e||!1);return 0<r.length?r:[e?T(t):t]},Ut=document.createElement("div");function Nt(t){return t&&"string"==typeof t&&(t=t.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,"").replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gim,""),Ut.innerHTML=t,t=Ut.textContent,Ut.textContent=""),t}var Ft=function(t,e){return t=(t=T(t)).replace(/\r?\n|\r/g," "),e?(0,o.truncate)(t,{length:e,separator:" "}):t},Lt=function(t){if((0,o.isEmpty)(t))return"";t=w(t),t=b(t),t=(0,o.unescape)(t).replace(/\[caption[^\]]*\](.*)\[\/caption\]/g,"");var e=(0,o.filter)(Rt(t,!0),(function(t){return""!==t.trim()}));if(!e.length)return"";var r=rankMathEditor.getPrimaryKeyword();if(""!==r){var n=(0,o.filter)(e,(function(t){return(0,o.includes)(t.toLowerCase(),r.toLowerCase())}));if(0<n.length)return Ft(n[0],160)}return Ft(e[0],160)};function Kt(t){return t=tt.swap(""!==t?t:rankMath.assessor.serpData.titleTemplate),rankMathEditor.refresh("title"),rt("serpTitle",Nt(t))}function zt(t){return t=""!==t?t:rankMathEditor.assessor.dataCollector.getSlug(),rankMathEditor.refresh("permalink"),rt("serpSlug",t)}function Gt(t){return t=tt.swap(function(t){var e=rankMathEditor.assessor.dataCollector.getData(),r=e.excerpt,n=Lt(e.content),i=(0,o.isUndefined)(r)||(0,o.isEmpty)(r)?n:(0,o.unescape)(r);if(tt.setVariable("excerpt",i),tt.setVariable("seo_description",i),""!==(t=Nt((0,u.applyFilters)("rankMath/description",t))))return T(t);if(!(0,o.isUndefined)(r)&&!(0,o.isEmpty)(r))return T(r);var a=(0,o.unescape)(rankMath.assessor.serpData.descriptionTemplate);return(0,o.isUndefined)(a)||""===a?n:T(a)}(t)),rankMathEditor.refresh("description"),rt("serpDescription",t)}function Ht(t){return rt("isSnippetEditorOpen",t)}function Bt(t){return rt("snippetPreviewType",t)}function Vt(t){return et("twitterUseFacebook",t,"rank_math_twitter_use_facebook",!0===t?"on":"off")}function Wt(t){return et("twitterCardType",t,"rank_math_twitter_card_type")}function $t(t){return et("twitterTitle",t,"rank_math_twitter_title")}function Jt(t){return et("twitterDescription",t,"rank_math_twitter_description")}function qt(t){return et("twitterAuthor",t,"rank_math_twitter_author")}function Yt(t){return et("twitterImageID",t,"rank_math_twitter_image_id")}function Qt(t){return et("twitterImage",t,"rank_math_twitter_image")}function Xt(t){return et("twitterHasOverlay",t,"rank_math_twitter_enable_image_overlay",!0===t?"on":"off")}function Zt(t){return et("twitterImageOverlay",t,"rank_math_twitter_image_overlay")}function te(t){return et("twitterPlayerUrl",t,"rank_math_twitter_player_url")}function ee(t){return et("twitterPlayerSize",t,"rank_math_twitter_player_size")}function re(t){return et("twitterPlayerStream",t,"rank_math_twitter_player_stream")}function ne(t){return et("twitterPlayerStreamCtype",t,"rank_math_twitter_player_stream_ctype")}function ie(t){return et("twitterAppDescription",t,"rank_math_twitter_app_description")}function ae(t){return et("twitterAppIphoneID",t,"rank_math_twitter_app_iphone_id")}function oe(t){return et("twitterAppIphoneName",t,"rank_math_twitter_app_iphone_name")}function ue(t){return et("twitterAppIphoneUrl",t,"rank_math_twitter_app_iphone_url")}function ce(t){return et("twitterAppIpadID",t,"rank_math_twitter_app_ipad_id")}function se(t){return et("twitterAppIpadName",t,"rank_math_twitter_app_ipad_name")}function le(t){return et("twitterAppIpadUrl",t,"rank_math_twitter_app_ipad_url")}function pe(t){return et("twitterAppGoogleplayID",t,"rank_math_twitter_app_googleplay_id")}function fe(t){return et("twitterAppGoogleplayName",t,"rank_math_twitter_app_googleplay_name")}function de(t){return et("twitterAppGoogleplayUrl",t,"rank_math_twitter_app_googleplay_url")}function he(t){return et("twitterAppCountry",t,"rank_math_twitter_app_country")}function me(t){return rt("isLoaded",t)}function ye(t){return rt("selectedKeyword",t)}function ge(t){return rt("socialTab",t)}function ve(){return rt("refreshResults",Date.now())}function be(){return rt("isPro",!0)}function ke(t){return rt("isDiviPageSettingsBarActive",t)}function we(t){return rt("isDiviRankMathModalActive",t)}function _e(t){return rt("highlightedParagraphs",t)}function Se(t){return Se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Se(t)}function Pe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Te(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Pe(Object(r),!0).forEach((function(e){De(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Pe(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function De(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Se(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Se(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Se(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ie(t){return rt("isSchemaEditorOpen",t)}function Ee(t){return rt("isSchemaTemplatesOpen",t)}function Oe(t){return rt("schemaUpdated",t)}function je(t){return rt("editingSchemaId",t)}function Ae(t){return rt("editorTab",t)}function Me(t){return rt("templateTab",t)}function Ce(t){return rt("editSchemas",t)}function xe(t){return et("schemas",t)}function Re(t,e){var r=Te({},(0,c.select)("rank-math").getEditSchemas());return r[t]=e,rt("editSchemas",r)}function Ue(t,e){var r=Te({},(0,c.select)("rank-math").getSchemas());return r[t]=e,et("schemas",r)}function Ne(t){var e=Te({},(0,c.select)("rank-math").getSchemas());return delete e[t],(0,u.doAction)("rank_math_schema_trash",t),et("schemas",e,"rank_math_delete_"+t,"")}function Fe(t,e,r){return d()({method:"POST",path:"rankmath/v1/saveTemplate",data:{schema:t,postId:r}}).then((function(r){e({loading:!1,showNotice:!0,postId:r.id}),setTimeout((function(){e({showNotice:!1}),(0,o.get)(rankMath,"isTemplateScreen",!1)&&(document.title=(0,p.__)("Edit Schema","rank-math"),window.history.pushState(null,"",r.link.replace(/&amp;/g,"&")))}),2e3),rankMath.schemaTemplates.push({schema:t,title:t.metadata.title,type:t["@type"]})})),e({loading:!0}),{type:"DONT_WANT_TO_DO_SOMETHING"}}function Le(t){return et("contentAIScore",t,"rank_math_contentai_score",t)}function Ke(t){return Ke="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ke(t)}function ze(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ge(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ze(Object(r),!0).forEach((function(e){He(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ze(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function He(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Ke(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Ke(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Ke(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Be=function(t){var e=t.assessor.serpData,r=t.assessor.hasRedirection;return{postID:null,title:e.title?e.title:e.titleTemplate,description:e.description,keywords:e.focusKeywords?e.focusKeywords:"",pillarContent:e.pillarContent,featuredImage:"",permalink:!1,primaryTerm:e.primaryTerm,robots:e.robots,advancedRobots:e.advancedRobots,canonicalUrl:e.canonicalUrl,breadcrumbTitle:e.breadcrumbTitle,showScoreFrontend:e.showScoreFrontend,lockModifiedDate:e.lockModifiedDate,redirectionID:r?(0,o.get)(t.assessor,"redirection.id",""):"",redirectionType:r?(0,o.get)(t.assessor,"redirection.header_code",""):"",redirectionUrl:r?(0,o.get)(t.assessor,"redirection.url_to",""):"",facebookTitle:e.facebookTitle,facebookImage:e.facebookImage,facebookImageID:e.facebookImageID,facebookAuthor:e.facebookAuthor,facebookDescription:e.facebookDescription,facebookHasOverlay:e.facebookHasOverlay,facebookImageOverlay:e.facebookImageOverlay,twitterTitle:e.twitterTitle,twitterImage:e.twitterImage,twitterAuthor:e.twitterAuthor,twitterImageID:e.twitterImageID,twitterCardType:e.twitterCardType,twitterUseFacebook:e.twitterUseFacebook,twitterDescription:e.twitterDescription,twitterHasOverlay:e.twitterHasOverlay,twitterImageOverlay:e.twitterImageOverlay,twitterPlayerUrl:e.twitterPlayerUrl,twitterPlayerSize:e.twitterPlayerSize,twitterPlayerStream:e.twitterPlayerStream,twitterPlayerStreamCtype:e.twitterPlayerStreamCtype,twitterAppDescription:e.twitterAppDescription,twitterAppIphoneName:e.twitterAppIphoneName,twitterAppIphoneID:e.twitterAppIphoneID,twitterAppIphoneUrl:e.twitterAppIphoneUrl,twitterAppIpadName:e.twitterAppIpadName,twitterAppIpadID:e.twitterAppIpadID,twitterAppIpadUrl:e.twitterAppIpadUrl,twitterAppGoogleplayName:e.twitterAppGoogleplayName,twitterAppGoogleplayID:e.twitterAppGoogleplayID,twitterAppGoogleplayUrl:e.twitterAppGoogleplayUrl,twitterAppCountry:e.twitterAppCountry,schemas:(0,o.get)(t,"schemas",{}),score:0,dirtyMetadata:{}}};function Ve(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Be(rankMath),e=arguments.length>1?arguments[1]:void 0,r=Ge({},t.dirtyMetadata);return!1!==e.metaKey&&(r[e.metaKey]=e.metaValue),"RESET_STORE"===e.type?Ge({},Be(e.value)):"RANK_MATH_APP_DATA"===e.type?"dirtyMetadata"===e.key?Ge(Ge({},t),{},{dirtyMetadata:e.value}):Ge(Ge({},t),{},He(He({},e.key,e.value),"dirtyMetadata",r)):t}function We(t){return We="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},We(t)}function $e(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Je(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?$e(Object(r),!0).forEach((function(e){qe(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):$e(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function qe(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==We(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==We(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===We(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ye={isLoaded:!1,isPro:!1,selectedKeyword:{tag:"",index:0,data:{value:""}},hasRedirect:rankMath.assessor.hasRedirection&&!(0,o.isEmpty)((0,o.get)(rankMath.assessor,"redirection.id",""))&&!(0,o.isEmpty)((0,o.get)(rankMath.assessor,"redirection.url_to","")),serpTitle:"",serpSlug:"",serpDescription:(0,o.get)(rankMath.assessor,"serpData.description",""),isSnippetEditorOpen:!1,snippetPreviewType:"",refreshResults:"",redirectionItem:{},socialTab:"facebook",highlightedParagraphs:[],editorTab:"",templateTab:"",editSchemas:{},editingSchemaId:"",isSchemaEditorOpen:!1,isSchemaTemplatesOpen:!1,schemaUpdated:!1,isDiviRankMathModalActive:!1,isDiviPageSettingsBarActive:!1};function Qe(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Ye,e=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===e.type?Je(Je({},t),{},qe({},e.key,e.value)):t}function Xe(t){return t.appData}function Ze(t){return t.appData.dirtyMetadata}function tr(t){return t.appData.score}function er(t){return t.appData.keywords}function rr(t){return t.appData.pillarContent}function nr(t){return t.appData.robots}function ir(t){return t.appData.advancedRobots}function ar(t){return t.appData.canonicalUrl}function or(t){return t.appData.breadcrumbTitle}function ur(t){return"todo"}function cr(t){return t.appData.showScoreFrontend}function sr(t){return t.appData.lockModifiedDate}function lr(t){return t.appUi.isLoaded}function pr(t){return t.appUi.selectedKeyword}function fr(t){return t.appUi.socialTab}function dr(t){return t.appUi.refreshResults}function hr(t){return t.appUi.isPro}function mr(t){return t.appUi.isDiviPageSettingsBarActive}function yr(t){return t.appUi.isDiviRankMathModalActive}function gr(t){return t.appUi.highlightedParagraphs}function vr(t){return t.appData.facebookTitle}function br(t){return t.appData.facebookDescription}function kr(t){return t.appData.facebookAuthor}function wr(t){return t.appData.facebookImageID}function _r(t){return t.appData.facebookImage}function Sr(t){return t.appData.facebookHasOverlay}function Pr(t){return""!==t.appData.facebookImageOverlay?t.appData.facebookImageOverlay:"play"}function Tr(t){return t.appData.postID}function Dr(t){return t.appData.title}function Ir(t){return t.appData.permalink}function Er(t){return t.appData.description}function Or(t){return t.appData.featuredImage}function jr(t){var e=t.appData.featuredImage;return'<img src="'.concat(e.source_url,'" alt="').concat(e.alt_text,'" >')}function Ar(t){return t.appData.primaryTerm}function Mr(t){return String(t.appData.redirectionID)}function Cr(t){return t.appData.redirectionType}function xr(t){return t.appData.redirectionUrl}function Rr(t){return t.appUi.redirectionItem}function Ur(t){return t.appUi.hasRedirect}var Nr=wp.url;function Fr(t){return Nt(t.appUi.serpTitle)}function Lr(t){return(0,Nr.safeDecodeURIComponent)(t.appUi.serpSlug)}function Kr(t){return t.appUi.serpDescription}function zr(t){return t.appUi.isSnippetEditorOpen}function Gr(t){return t.appUi.snippetPreviewType}function Hr(t){return t.appUi.isSchemaEditorOpen}function Br(t){return t.appUi.isSchemaTemplatesOpen}function Vr(t){return t.appUi.schemaUpdated}function Wr(t){return t.appData.schemas}function $r(t){return t.appUi.editSchemas}function Jr(t){return{id:t.appUi.editingSchemaId,data:t.appUi.editSchemas[t.appUi.editingSchemaId]}}function qr(t){return t.appData.schemas[t.appUi.editingSchemaId]}function Yr(t){return t.appUi.editorTab}function Qr(t){return t.appUi.templateTab}function Xr(t){return t.appData.twitterUseFacebook}function Zr(t){return t.appData.twitterCardType}function tn(t){return t.appData.twitterTitle}function en(t){return t.appData.twitterDescription}function rn(t){return t.appData.twitterAuthor}function nn(t){return t.appData.twitterImageID}function an(t){return t.appData.twitterImage}function on(t){return t.appData.twitterHasOverlay}function un(t){return""!==t.appData.twitterImageOverlay?t.appData.twitterImageOverlay:"play"}function cn(t){return t.appData.twitterAppDescription}function sn(t){return t.appData.twitterAppIphoneID}function ln(t){return t.appData.twitterAppIphoneName}function pn(t){return t.appData.twitterAppIphoneUrl}function fn(t){return t.appData.twitterAppIpadID}function dn(t){return t.appData.twitterAppIpadName}function hn(t){return t.appData.twitterAppIpadUrl}function mn(t){return t.appData.twitterAppGoogleplayID}function yn(t){return t.appData.twitterAppGoogleplayName}function gn(t){return t.appData.twitterAppGoogleplayUrl}function vn(t){return t.appData.twitterAppCountry}function bn(t){return t.appData.twitterPlayerUrl}function kn(t){return t.appData.twitterPlayerSize}function wn(t){return t.appData.twitterPlayerStream}function _n(t){return t.appData.twitterPlayerStreamCtype}(0,c.registerStore)("rank-math",{reducer:(0,c.combineReducers)(r),selectors:n,actions:e});function Sn(t){return Sn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sn(t)}function Pn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==Sn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Sn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===Sn(a)?a:String(a)),n)}var i,a}var Tn=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.updateBtn=a()("#publish"),this.form=a()("#post"),this._data={id:!1,slug:!1,permalink:!1,content:!1,title:!1,excerpt:!1,featuredImage:!1,description:""},this.refresh=this.refresh.bind(this),(0,u.addAction)("rank_math_loaded","rank-math",this.refresh),this.setup(),this.init()}var e,r,n;return e=t,r=[{key:"init",value:function(){var t=this;this.elemTitle.on("input",(0,o.debounce)((function(){t.handleTitleChange(t.elemTitle.val())}),500)).trigger("input"),this.elemDescription.on("input",(0,o.debounce)((function(){t.handleExcerptChange(t.elemDescription.val())}),500)).trigger("input"),this.elemSlug.on("input",(0,o.debounce)((function(){rankMathEditor.updatePermalink(t.elemSlug.val())}),500))}},{key:"collectData",value:function(){this._data={id:this.getPostID(),slug:this.getSlug(),permalink:this.getPermalink(),content:this.getContent(),title:this.getTitle(),excerpt:this.getExcerpt(),featuredImage:this.getFeaturedImage()}}},{key:"getPostID",value:function(){return rankMath.objectID}},{key:"getTitle",value:function(){return this.elemTitle.val()}},{key:"getExcerpt",value:function(){var t=this.elemDescription.val();return tt.setVariable("excerpt",t),tt.setVariable("excerpt_only",t),tt.setVariable("wc_shortdesc",t),tt.setVariable("seo_description",t),t}},{key:"getPermalink",value:function(){return this.getSlug()?rankMath.permalinkFormat.replace(/%(postname|pagename|term|author)%/,this.getSlug()):""}},{key:"getSlug",value:function(){return this.elemSlug.val()}},{key:"getFeaturedImage",value:function(){return this.featuredImage}},{key:"refresh",value:function(){this.collectData(),(0,c.dispatch)("rank-math").toggleLoaded(!0),rankMathEditor.refresh("init")}},{key:"handleSlugChange",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];("auto-draft"!==this.getStatus()||e)&&this.elemSlug.val(t),a()("#editable-post-name").text(t),a()("#editable-post-name-full").text(t),this._data.slug=this.getSlug(),this._data.permalink=this.getPermalink(),(0,c.dispatch)("rank-math").updateSerpSlug(t),rankMathEditor.refresh("permalink")}},{key:"handleTitleChange",value:function(t){tt.setVariable("title",t),tt.setVariable("term",t),tt.setVariable("author",t),(0,c.dispatch)("rank-math").updateSerpTitle((0,c.select)("rank-math").getTitle()),rankMathEditor.refresh("title")}},{key:"handleExcerptChange",value:function(){this._data.excerpt=this.getExcerpt(),(0,c.dispatch)("rank-math").updateSerpDescription((0,c.select)("rank-math").getDescription()),rankMathEditor.refresh("content")}},{key:"handleFeaturedImageChange",value:function(){this._data.featuredImage=this.getFeaturedImage(),(0,c.dispatch)("rank-math").updateFeaturedImage(this.getFeaturedImage()),rankMathEditor.refresh("featuredImage")}},{key:"handleContentChange",value:function(){this._data.excerpt=this.getExcerpt(),this._data.content=this.getContent(),(0,c.dispatch)("rank-math").updateSerpDescription((0,c.select)("rank-math").getDescription()),rankMathEditor.refresh("content")}},{key:"getData",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=t?this._data[t]:this._data;return(0,u.applyFilters)("rank_math_dataCollector_data",e)}},{key:"updateData",value:function(t,e){this._data[t]=e}},{key:"isTinymce",value:function(){return"undefined"!=typeof tinymce}},{key:"getStatus",value:function(){var t=!(0,o.isUndefined)(this.postStatus)&&this.postStatus.length?this.postStatus.val():"";return(0,o.isUndefined)(t)?"":t}}],r&&Pn(e.prototype,r),n&&Pn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),Dn=Tn;function In(t){return In="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},In(t)}function En(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==In(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==In(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===In(a)?a:String(a)),n)}var i,a}function On(t,e){return On=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},On(t,e)}function jn(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=An(t);if(e){var i=An(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===In(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function An(t){return An=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},An(t)}var Mn=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&On(t,e)}(c,t);var e,r,n,i=jn(c);function c(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,c),i.apply(this,arguments)}return e=c,(r=[{key:"setup",value:function(){this.elemSlug=a()("#post_name"),this.elemTitle=a()("#title"),this.elemDescription=a()("#excerpt"),this.elemContent=a()("#content"),this.saveDraft=a()("#save-post"),this.postStatus=a()("#original_post_status"),this.assessThumbnail=this.assessThumbnail.bind(this),(0,u.addAction)("rank_math_updated_featured_image","rank-math",this.assessThumbnail),this.events()}},{key:"getContent",value:function(){if(null!==this.elemContent&&0!==this.elemContent.length)return this.isTinymce()&&tinymce.activeEditor&&!(0,o.isNull)(tinymce.get("content"))?tinymce.get("content").getContent():this.elemContent.val()}},{key:"getSlug",value:function(){var t=""===this.elemSlug.val()&&a()("#editable-post-name-full").length?a()("#editable-post-name-full").text():this.elemSlug.val();return(0,o.isUndefined)(t)?"":t}},{key:"events",value:function(){var t=this;this.elemContent.on("input change",(0,o.debounce)((function(){t.handleContentChange()}),500)).trigger("change"),a()(window).on("load",(function(){t.isTinymce()&&(tinymce.activeEditor&&!(0,o.isUndefined)(tinymce.editors.content)&&tinymce.editors.content.on("keyup change",(0,o.debounce)((function(){t.handleContentChange()}),500)),tinymce.editors&&!(0,o.isUndefined)(tinymce.editors.excerpt)&&tinymce.editors.excerpt.on("keyup change",(0,o.debounce)((function(){tinymce.editors.excerpt.save(),t.handleExcerptChange()}),500)))})),a()(document).on("ajaxComplete",(function(e,r,n){var i="/admin-ajax.php";if(i===n.url.substr(-15)){var o="";"string"==typeof n.data&&-1!==n.data.indexOf("action=sample-permalink")&&(o=""===r.responseText?t.elemTitle.val():a()("<div>"+r.responseText+"</div>").find("#editable-post-name-full").text(),rankMathEditor.updatePermalink(o))}})),this.saveDraft.on("click",(function(){t.updateBtn.attr("data-disable","true")}))}},{key:"assessThumbnail",value:function(t){this.featuredImage={source_url:t.src,alt_text:t.alt},this.handleFeaturedImageChange(t)}}])&&En(e.prototype,r),n&&En(e,n),Object.defineProperty(e,"prototype",{writable:!1}),c}(Dn),Cn=Mn;function xn(t){return xn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xn(t)}function Rn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==xn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==xn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===xn(a)?a:String(a)),n)}var i,a}function Un(t,e){return Un=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Un(t,e)}function Nn(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Fn(t);if(e){var i=Fn(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===xn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function Fn(t){return Fn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Fn(t)}var Ln=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Un(t,e)}(u,t);var e,r,n,i=Nn(u);function u(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),i.apply(this,arguments)}return e=u,(r=[{key:"setup",value:function(){this.updateBtn=a()('.edit-tag-actions input[type="submit"]'),this.form=a()("#edittag"),this.elemSlug=a()("#slug"),this.elemTitle=a()("#name"),this.elemDescription=a()("#rank_math_description_editor"),this.events()}},{key:"getContent",value:function(){return null===this.elemDescription||0===this.elemDescription.length?"":this.isTinymce()&&tinymce.activeEditor&&"rank_math_description_editor"===tinymce.activeEditor.id?tinymce.activeEditor.getContent():this.elemDescription.val()}},{key:"events",value:function(){var t=this;a()(window).on("load",(function(){t.isTinymce()&&tinymce.activeEditor&&!(0,o.isUndefined)(tinymce.editors.rank_math_description_editor)&&tinymce.editors.rank_math_description_editor.on("keyup change",(0,o.debounce)((function(){t.handleContentChange()}),500))}))}}])&&Rn(e.prototype,r),n&&Rn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),u}(Dn),Kn=Ln;function zn(t){return zn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zn(t)}function Gn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==zn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==zn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===zn(a)?a:String(a)),n)}var i,a}function Hn(t,e){return Hn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Hn(t,e)}function Bn(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Vn(t);if(e){var i=Vn(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===zn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function Vn(t){return Vn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Vn(t)}var Wn=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Hn(t,e)}(o,t);var e,r,n,i=Bn(o);function o(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,o),i.apply(this,arguments)}return e=o,(r=[{key:"setup",value:function(){this.updateBtn=a()("#submit"),this.form=a()("#your-profile"),this.elemSlug=a()("#user_login"),this.elemTitle=a()("#display_name"),this.elemDescription=a()("#description")}},{key:"getContent",value:function(){return this.elemDescription.val()}}])&&Gn(e.prototype,r),n&&Gn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),o}(Dn),$n=Wn,Jn=wp.plugins,qn=wp.editPost;function Yn(t){return Yn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yn(t)}function Qn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Xn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Qn(Object(r),!0).forEach((function(e){ti(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Qn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Zn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ei(n.key),n)}}function ti(t,e,r){return(e=ei(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ei(t){var e=function(t,e){if("object"!==Yn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Yn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Yn(e)?e:String(e)}var ri=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),ti(this,"initialize",!1),ti(this,"isSavingRedirection",!1),ti(this,"isSavingSchemas",!1),ti(this,"oldSlug",!1),this._data={id:!1,slug:!1,permalink:!1,content:!1,title:!1,excerpt:!1,featuredImage:!1},this.refresh=this.refresh.bind(this),this.isSavingPost=this.isSavingPost.bind(this),this.getPostAttribute=this.getPostAttribute.bind(this),this.subscribeToGutenberg(),this.lockModifiedDate()}var e,r,n;return e=t,(r=[{key:"lockModifiedDate",value:function(){rankMath.showLockModifiedDate&&(0,Jn.registerPlugin)("rank-math-lock-last-modified",{render:function(){return wp.element.createElement(qn.PluginPostStatusInfo,null,wp.element.createElement(x,null))}})}},{key:"collectGutenbergData",value:function(){return G()?(this._coreEditorSelect||(this._coreEditorSelect=(0,c.select)("core/editor")),!1===this.oldSlug&&""!==this.getSlug()&&(this.oldSlug=this.getSlug()),{id:this.getPostID(),slug:this.getSlug(),permalink:this.getPermalink(),content:this.getPostAttribute("content"),title:this.getPostAttribute("title"),excerpt:this.getPostAttribute("excerpt"),featuredImage:this.getFeaturedImage()}):{}}},{key:"getPostID",value:function(){return this._coreEditorSelect.getCurrentPostId()}},{key:"getPermalink",value:function(){if(H())return rankMath.homeUrl;if("auto-draft"===this.getPostAttribute("status"))return"";var t=this.getPostAttribute("generated_slug");return"auto-draft"!==t&&"en"===rankMath.locale||(t=""),(0,Nr.safeDecodeURIComponent)(this._coreEditorSelect.getPermalink())}},{key:"getSlug",value:function(){var t=this.getPostAttribute("generated_slug");return"auto-draft"!==t&&"en"===rankMath.locale||(t=""),(0,Nr.safeDecodeURIComponent)(this.getPostAttribute("slug")||t)}},{key:"getFeaturedImage",value:function(){var t=this.getPostAttribute("featured_media");if(this.isValidMediaId(t)){var e=(0,c.select)("core").getMedia(t);if(!(0,o.isUndefined)(e))return e}}},{key:"isValidMediaId",value:function(t){return"number"==typeof t&&0<t}},{key:"getPostAttribute",value:function(t){return G()?(this._coreEditorSelect||(this._coreEditorSelect=(0,c.select)("core/editor")),this._coreEditorSelect.getEditedPostAttribute(t)):""}},{key:"subscribeToGutenberg",value:function(){this.subscriber=(0,o.debounce)(this.refresh,500),(0,c.subscribe)(this.subscriber),(0,c.subscribe)(this.isSavingPost)}},{key:"refresh",value:function(){var t=Xn({},this._data);this._data=this.collectGutenbergData(),this.handleEditorChange(t)}},{key:"isSavingPost",value:function(){var t=(0,c.select)("core/editor");if(!t.isAutosavingPost()&&t.isSavingPost()){var e=(0,c.select)("rank-math").getDirtyMetadata();(0,o.isEmpty)(e)||(d()({method:"POST",path:"rankmath/v1/updateMeta",data:{objectID:rankMath.objectID,objectType:rankMath.objectType,meta:e,content:(0,u.applyFilters)("rank_math_content",this.getPostAttribute("content"))}}).then((function(t){(0,u.doAction)("rank_math_metadata_updated",t)})),(0,c.dispatch)("rank-math").resetDirtyMetadata()),"publish"===this.getPostAttribute("status")&&(this.saveRedirection(),this.autoCreateRedirectionNotice()),this.saveSchemas()}}},{key:"saveSchemas",value:function(){var t=this;if(!this.isSavingSchemas){var e=(0,c.select)("rank-math").getSchemas();if(!(0,o.isEmpty)(e)&&(0,c.select)("rank-math").hasSchemaUpdated()){this.isSavingSchemas=!0;var r=(0,c.select)("rank-math").getEditSchemas();d()({method:"POST",path:"rankmath/v1/updateSchemas",data:{objectID:this.getPostID(),objectType:rankMath.objectType,schemas:e}}).then((function(n){var i=Xn({},e),a=Xn({},r);(0,o.isEmpty)(n)||(0,o.map)(n,(function(t,e){i["schema-"+t]=Xn({},i[e]),a["schema-"+t]=Xn({},a[e]),delete i[e],delete a[e]})),(0,c.dispatch)("rank-math").updateSchemas(i),(0,c.dispatch)("rank-math").updateEditSchemas(a),setTimeout((function(){(0,c.dispatch)("rank-math").schemaUpdated(!1),(0,u.doAction)("rank_math_schema_changed"),t.isSavingSchemas=!1}),2e3)}))}}}},{key:"saveRedirection",value:function(){var t=this;if(!this.isSavingRedirection){var e=(0,c.select)("rank-math").getRedirectionItem();if(!(0,o.isEmpty)(e)){this.isSavingRedirection=!0,e.objectID=this.getPostID(),e.redirectionSources=this.getPermalink();var r=(0,c.dispatch)("rank-math"),n=(0,c.dispatch)("core/notices");r.resetRedirection(),d()({method:"POST",path:"rankmath/v1/updateRedirection",data:e}).then((function(e){"delete"===e.action?(n.createInfoNotice(e.message,{id:"redirectionNotice"}),r.updateRedirection("redirectionID",0)):"update"===e.action?n.createInfoNotice(e.message,{id:"redirectionNotice"}):"new"===e.action&&(r.updateRedirection("redirectionID",e.id),n.createSuccessNotice(e.message,{id:"redirectionNotice"})),setTimeout((function(){t.isSavingRedirection=!1,n.removeNotice("redirectionNotice")}),2e3)}))}}}},{key:"autoCreateRedirectionNotice",value:function(){if(rankMath.assessor.hasRedirection&&(0,o.get)(rankMath,["assessor","autoCreateRedirection"],!1)&&!(0,c.select)("core/editor").isEditedPostNew()&&!1!==this.oldSlug){var t=this.getSlug();if(this.oldSlug!==t){var e=(0,c.dispatch)("core/notices");this.oldSlug=t,e.createSuccessNotice((0,p.__)("Auto redirection created.","rank-math"),{id:"redirectionAutoCreationNotice"}),setTimeout((function(){e.removeNotice("redirectionAutoCreationNotice")}),2e3)}}}},{key:"handleEditorChange",value:function(t){var e=this,r={id:"handleIDChange",slug:"handleSlugChange",title:"handleTitleChange",excerpt:"handleExcerptChange",content:"handleContentChange",featuredImage:"handleFeaturedImageChange"};if(t.id)return this.initialize?void(0,o.forEach)(r,(function(r,n){e._data[n]!==t[n]&&e[r](e._data[n])})):(this.initialize=!0,(0,o.forEach)(r,(function(t,r){e[t](e._data[r])})),void rankMathEditor.refresh("init"));(0,c.dispatch)("rank-math").refreshResults()}},{key:"handleIDChange",value:function(t){H()&&(t=0),(0,c.dispatch)("rank-math").updatePostID(t),(0,c.dispatch)("rank-math").toggleLoaded(!0),(0,u.doAction)("rank_math_id_changed")}},{key:"handleSlugChange",value:function(){""!==this.getSlug()&&!1===this.oldSlug&&(this.oldSlug=this.getSlug()),rankMathEditor.refresh("permalink")}},{key:"handleTitleChange",value:function(t){tt.setVariable("title",t),tt.setVariable("term",t),tt.setVariable("author",t),(0,c.dispatch)("rank-math").updateSerpTitle((0,c.select)("rank-math").getTitle()),rankMathEditor.refresh("title")}},{key:"handleExcerptChange",value:function(t){tt.setVariable("excerpt",t),tt.setVariable("excerpt_only",t),tt.setVariable("wc_shortdesc",t),tt.setVariable("seo_description",t),(0,c.dispatch)("rank-math").updateSerpDescription((0,c.select)("rank-math").getDescription())}},{key:"handleFeaturedImageChange",value:function(t){(0,c.dispatch)("rank-math").updateFeaturedImage(t),rankMathEditor.refresh("featuredImage")}},{key:"handleContentChange",value:function(){(0,c.dispatch)("rank-math").updateSerpDescription((0,c.select)("rank-math").getDescription()),rankMathEditor.refresh("content")}},{key:"getData",value:function(){return(0,u.applyFilters)("rank_math_dataCollector_data",this._data)}}])&&Zn(e.prototype,r),n&&Zn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),ni=ri;function ii(t){return ii="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ii(t)}function ai(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==ii(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==ii(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===ii(a)?a:String(a)),n)}var i,a}function oi(t,e,r){return e&&ai(t.prototype,e),r&&ai(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}var ui=oi((function t(){if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),"post"===rankMath.objectType&&rankMath.postSettings.linkSuggestions){a().fn.extend({insertLink:function(t,e){var r=this[0],n="";if(r.selectionStart||"0"===r.selectionStart){var i=r.selectionStart,a=r.selectionEnd,o=r.scrollTop;n='<a href="'+t+'">'+r.value.substring(i,a)+"</a>",i===a&&(n='<a href="'+t+'">'+e+"</a>"),r.value=r.value.substring(0,i)+n+r.value.substring(a,r.value.length),r.focus(),r.selectionStart=i+n.length,r.selectionEnd=i+n.length,r.scrollTop=o}else n='<a href="'+t+'">'+e+"</a>",r.value+=n,r.focus()}});if("function"==typeof ClipboardJS&&a()(".suggestion-copy").on("click",(function(t){t.preventDefault(),new ClipboardJS(".suggestion-copy");var e=a()(this).parent().next(".suggestion-title"),r=e.html();e.text("Link Copied"),setTimeout((function(){e.html(r)}),1500)})),"object"!==("undefined"==typeof tinymce?"undefined":ii(tinymce)))return!0;var e=null,r=null;a()(".suggestion-insert").on("click",(function(t){t.preventDefault();var n,i,o,u,s=a()(this);if(s.hasClass("clicked"))return!0;if(null!==tinymce.activeEditor&&!0!==tinymce.activeEditor.isHidden()&&"content"===tinymce.activeEditor.id){e=tinymce.activeEditor,r=a()(e.getBody());var l=e.selection.getContent()||"";if(r.find("a[data-mce-selected]").length){var p=(n="",i="",o=e.selection.getStart(),(u=e.dom.getParent(o,"a[href]"))||(i=e.selection.getContent({format:"raw"}))&&-1!==i.indexOf("</a>")&&((n=i.match(/href="([^">]+)"/))&&n[1]&&(u=e.$('a[href="'+n[1]+'"]',o)[0]),u&&e.selection.select(u)),u);e.dom.setAttribs(p,{href:s.data("url")}),a()(p).text()!==l&&e.insertContent(l)}else l.length?e.insertContent('<a href="'+s.data("url")+'">'+l+"</a>"):e.insertContent('<a href="'+s.data("url")+'">'+s.data("text")+"</a>")}else if(G()){var f=(0,c.select)("core/block-editor").getSelectedBlock().clientId,d=(0,c.select)("core/block-editor").getSelectionStart().offset,h=(0,c.select)("core/block-editor").getSelectionEnd().offset;if(document.getSelection){var m=document.getSelection();if(m.rangeCount){var y=m.getRangeAt(0),g=a()("#block-"+f).text().substring(d,h),v=document.createElement("a");v.href=s.data("url"),v.innerText=""!==g?g:s.data("text"),y.deleteContents(),y.insertNode(v);var b=m.focusNode.innerHTML;(0,c.dispatch)("core/block-editor").updateBlock(f,{attributes:{content:b}})}}}var k=s.closest(".suggestion-item").find(".suggestion-title"),w=k.html();k.text("Link Inserted"),s.addClass("clicked"),setTimeout((function(){var t,e,r,n;k.html(w),s.removeClass("clicked"),!0===rankMath.postSettings.useFocusKeyword&&(e=s,r=(t=k).data("fkcount")||0,n=t.data("fk"),(r+=1)===n.length&&(r=0),t.find(">a").text(n[r]),t.data("fkcount",r),e.data("text",n[r]))}),1500)})),a()("#rank_math_metabox_link_suggestions").find("h2").append(a()("#rank-math-link-suggestions-tooltip").html())}}));function ci(t){return ci="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ci(t)}function si(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==ci(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==ci(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===ci(a)?a:String(a)),n)}var i,a}function li(t,e,r){return e&&si(t.prototype,e),r&&si(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}var pi=li((function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),"post"===rankMath.objectType&&!1!==rankMath.assessor.primaryTaxonomy&&a()("[data-primary-term]").each((function(){var t=a()(this),e=t.val(),r=t.data("primary-term"),n=a()("#"+r+"div"),i=a()("#"+r+"checklist"),o=i.find("li"),u=i.find('input[type="checkbox"]:checked');o.addClass("rank-math-primary-term-li"),o.find("input").each((function(){var t=a()(this);t.parent().append('<span class="rank-math-tooltip"><input class="rank-math-make-primary" value="'+t.val()+'" type="radio" name="rank_math_primary_'+r+'"><span>'+(0,p.__)("Make Term Primary","rank-math")+"</span></span>")})),u.each((function(){var t=a()(this),r=t.closest("li");r.addClass("term-checked"),e===t.attr("value")&&(r.addClass("term-marked-primary"),r.find(">label .rank-math-make-primary").prop("checked",!0))})),n.on("click",'input[type="checkbox"]',(function(){var e=a()(this).closest("li");if(e.toggleClass("term-checked"),1===i.find("li.term-checked").length||e.hasClass("term-marked-primary")&&!e.hasClass("term-checked")){var r=i.find("li.term-checked:first > label .rank-math-make-primary");0<r.length?r.trigger("click"):(o.removeClass("term-marked-primary"),o.find('input[type="radio"]').prop("checked",!1),t.val(""))}})),n.on("click",".rank-math-make-primary",(function(){var e=a()(this),r=e.closest("li");o.removeClass("term-marked-primary"),r.addClass("term-marked-primary"),t.val(e.val())})),i.on("wpListAddEnd",(function(){i.find("li:not(.rank-math-primary-term-li)").addClass("rank-math-primary-term-li term-checked").find("input").each((function(){var t=a()(this);t.parent().append('<span class="rank-math-tooltip"><input class="rank-math-make-primary" value="'+t.val()+'" type="radio" name="rank_math_primary_'+r+'"><span>'+(0,p.__)("Make Term Primary","rank-math")+"</span></span>")}))}))}))}));function fi(t){return fi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fi(t)}function di(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==fi(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==fi(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===fi(a)?a:String(a)),n)}var i,a}var hi=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.image_src="",this.image_alt="",this.getFeaturedImage(),this.setImage(),this.removeImage(),G()&&this.gutenbergImage()}var e,r,n;return e=t,(r=[{key:"setImage",value:function(){var t=this;if(!(0,o.isUndefined)(wp.media)){var e=wp.media.featuredImage.frame();e.on("select",(function(){var r=a()("#rank_math_post_thumbnail"),n=e.state().get("selection").first().toJSON();t.sizeWarning(n),1>r.length&&(r=a()('<img id="rank_math_post_thumbnail" />'),a()(".facebook-thumbnail",".rank-math-social-preview-image").before(r)),"large"in n.sizes?r.attr("src",n.sizes.large.url):r.attr("src",n.sizes.full.url),t.setFeaturedImage(n)}))}}},{key:"removeImage",value:function(){var t=this;a()("#postimagediv").on("click","#remove-post-thumbnail",(function(){a()("#rank_math_image_warning").remove(),a()("#rank_math_post_thumbnail").remove(),t.setFeaturedImage("")}))}},{key:"gutenbergImage",value:function(){var t,e,r=this;wp.data.subscribe((function(){var n=wp.data.select("core/editor").getEditedPostAttribute("featured_media");r.isValidMediaId(n)&&(t=wp.data.select("core").getMedia(n),(0,o.isUndefined)(t)||t!==e&&(e=t,r.setFeaturedImage({url:t.guid.rendered,alt:t.alt_text})))}))}},{key:"setFeaturedImage",value:function(t){var e={src:(0,o.get)(t,"url",""),alt:(0,o.get)(t,"alt","")};(0,u.doAction)("rank_math_updated_featured_image",e)}},{key:"sizeWarning",value:function(t){if(a()("#rank_math_image_warning").remove(),!(200<t.width&&200<t.height)){var e=a()("#postimagediv").find(".hndle");a()('<div id="rank_math_image_warning" class="notice notice-error notice-alt"><p>'+rankMath.featuredImageNotice+"</p></div>").insertAfter(e)}}},{key:"getFeaturedImage",value:function(){var t=a()("#postimagediv img");if(!t||t.length){var e={url:t[0].src,alt:t[0].alt};this.setFeaturedImage(e)}}},{key:"isValidMediaId",value:function(t){return"number"==typeof t&&0<t}}])&&di(e.prototype,r),n&&di(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),mi=(0,O.createSlotFill)("RankMathAfterEditor"),yi=mi.Fill,gi=mi.Slot,vi=function(t){var e=t.children,r=t.className;return wp.element.createElement(yi,null,wp.element.createElement(O.PanelRow,{className:r},e))};vi.Slot=gi;var bi=vi,ki=(0,O.createSlotFill)("RankMathAdvancedTab"),wi=ki.Fill,_i=ki.Slot,Si=function(t){var e=t.children,r=t.className;return wp.element.createElement(wi,null,wp.element.createElement(O.PanelRow,{className:r},e))};Si.Slot=_i;var Pi=Si,Ti=(0,O.createSlotFill)("RankMathAfterFocusKeyword"),Di=Ti.Fill,Ii=Ti.Slot,Ei=function(t){var e=t.children,r=t.className;return wp.element.createElement(Di,null,wp.element.createElement("div",{className:r},e))};Ei.Slot=Ii;var Oi=Ei;function ji(t){return ji="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ji(t)}function Ai(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==ji(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==ji(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===ji(a)?a:String(a)),n)}var i,a}var Mi=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,r,n;return e=t,r=[{key:"setup",value:function(){this.registerSlots=this.registerSlots.bind(this),(0,u.addAction)("rank_math_loaded","rank-math",this.registerSlots,0),this.resultManager=new l.ResultManager,this.assessor=new z(this.getCollector()),new W,this.registerComponents=this.registerComponents.bind(this),(0,u.addAction)("rank_math_loaded","rank-math",this.registerComponents,11)}},{key:"registerSlots",value:function(){this.RankMathAfterEditor=bi,this.RankMathAfterFocusKeyword=Oi,this.RankMathAdvancedTab=Pi,this.slots={AfterEditor:bi,AfterFocusKeyword:Oi,AdvancedTab:Pi}}},{key:"registerComponents",value:function(){this.components={},this.components.linkSuggestions=new ui,this.components.primaryTerm=new pi,this.components.featuredImage=new hi,rankMathAdmin.variableInserter(!1),setTimeout((function(){(0,s.createRoot)(document.getElementById("rank-math-metabox-wrapper")).render((0,s.createElement)((0,u.applyFilters)("rank_math_app",{})))}),1e3)}},{key:"refresh",value:function(t){this.assessor.refresh(t)}},{key:"updatePermalink",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.assessor.dataCollector.handleSlugChange(t,e),(0,c.dispatch)("rank-math").updatePermalink(t)}},{key:"updatePermalinkSanitize",value:function(t){t=this.assessor.getResearch("slugify")(t),(0,c.dispatch)("rank-math").updatePermalink(t)}},{key:"getPrimaryKeyword",value:function(){return I(this.assessor.getPrimaryKeyword())}},{key:"getSelectedKeyword",value:function(){var t=this.assessor.getSelectedKeyword();return(0,o.isUndefined)(t)?"":I(t)}},{key:"getCollector",value:function(){return"post"===rankMath.objectType?G()?new ni:new Cn(this):"term"===rankMath.objectType?new Kn:"user"===rankMath.objectType?new $n:void 0}}],r&&Ai(e.prototype,r),n&&Ai(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();a()((function(){window.rankMathEditor=new Mi,window.rankMathEditor.setup(),window.RankMathApp=new Y})),a()(window).on("load",(function(){a().when(a().ready).then((function(){(0,u.doAction)("rank_math_loaded")}))}))}();