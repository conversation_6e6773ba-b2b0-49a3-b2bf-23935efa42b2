!function(){"use strict";var e={n:function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,{a:n}),n},d:function(t,n){for(var a in n)e.o(n,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:n[a]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}},t=jQuery,n=e.n(t),a=wp.i18n;function r(e,t,a){return n().ajax({url:rankMath.ajaxurl,type:a||"POST",dataType:"json",data:n().extend(!0,{action:"rank_math_"+e,security:rankMath.security},t)})}function o(e,t,a,r){t=t||"error",r=r||!1;var o=n()('<div class="notice notice-'+t+' is-dismissible"><p>'+e+"</p></div>").hide();a.next(".notice").remove(),a.after(o),o.slideDown(),n()(document).trigger("wp-updates-notice-added"),n()("html,body").animate({scrollTop:o.offset().top-50},"slow"),r&&setTimeout((function(){o.fadeOut((function(){o.remove()}))}),r)}var i=lodash,c=wp.hooks;function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function l(){l=function(){return t};var e,t={},n=Object.prototype,a=n.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function h(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{h({},"")}catch(e){h=function(e,t,n){return e[t]=n}}function p(e,t,n,a){var o=t&&t.prototype instanceof b?t:b,i=Object.create(o.prototype),c=new j(a||[]);return r(i,"_invoke",{value:E(e,n,c)}),i}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var f="suspendedStart",v="suspendedYield",m="executing",y="completed",g={};function b(){}function k(){}function w(){}var S={};h(S,i,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(I([])));x&&x!==n&&a.call(x,i)&&(S=x);var C=w.prototype=b.prototype=Object.create(S);function D(e){["next","throw","return"].forEach((function(t){h(e,t,(function(e){return this._invoke(t,e)}))}))}function A(e,t){function n(r,o,i,c){var l=d(e[r],e,o);if("throw"!==l.type){var u=l.arg,h=u.value;return h&&"object"==s(h)&&a.call(h,"__await")?t.resolve(h.__await).then((function(e){n("next",e,i,c)}),(function(e){n("throw",e,i,c)})):t.resolve(h).then((function(e){u.value=e,i(u)}),(function(e){return n("throw",e,i,c)}))}c(l.arg)}var o;r(this,"_invoke",{value:function(e,a){function r(){return new t((function(t,r){n(e,a,t,r)}))}return o=o?o.then(r,r):r()}})}function E(t,n,a){var r=f;return function(o,i){if(r===m)throw new Error("Generator is already running");if(r===y){if("throw"===o)throw i;return{value:e,done:!0}}for(a.method=o,a.arg=i;;){var c=a.delegate;if(c){var s=L(c,a);if(s){if(s===g)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(r===f)throw r=y,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r=m;var l=d(t,n,a);if("normal"===l.type){if(r=a.done?y:v,l.arg===g)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(r=y,a.method="throw",a.arg=l.arg)}}}function L(t,n){var a=n.method,r=t.iterator[a];if(r===e)return n.delegate=null,"throw"===a&&t.iterator.return&&(n.method="return",n.arg=e,L(t,n),"throw"===n.method)||"return"!==a&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+a+"' method")),g;var o=d(r,t.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,g;var i=o.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,g):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function I(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,o=function n(){for(;++r<t.length;)if(a.call(t,r))return n.value=t[r],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(s(t)+" is not iterable")}return k.prototype=w,r(C,"constructor",{value:w,configurable:!0}),r(w,"constructor",{value:k,configurable:!0}),k.displayName=h(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,h(e,u,"GeneratorFunction")),e.prototype=Object.create(C),e},t.awrap=function(e){return{__await:e}},D(A.prototype),h(A.prototype,c,(function(){return this})),t.AsyncIterator=A,t.async=function(e,n,a,r,o){void 0===o&&(o=Promise);var i=new A(p(e,n,a,r),o);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},D(C),h(C,u,"Generator"),h(C,i,(function(){return this})),h(C,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var a in t)n.push(a);return n.reverse(),function e(){for(;n.length;){var a=n.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=I,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(P),!t)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(a,r){return c.type="throw",c.arg=t,n.next=a,r&&(n.method="next",n.arg=e),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=a.call(i,"catchLoc"),l=a.call(i,"finallyLoc");if(s&&l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),P(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var a=n.completion;if("throw"===a.type){var r=a.arg;P(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,a){return this.delegate={iterator:I(t),resultName:n,nextLoc:a},"next"===this.method&&(this.arg=e),g}},t}function u(e,t,n,a,r,o,i){try{var c=e[o](i),s=c.value}catch(e){return void n(e)}c.done?t(s):Promise.resolve(s).then(a,r)}function h(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,p(a.key),a)}}function p(e){var t=function(e,t){if("object"!==s(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==s(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===s(t)?t:String(t)}var d,f=function(){function e(){var t,n,a;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,a="",(n=p(n="response"))in t?Object.defineProperty(t,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[n]=a}var t,o,s,d,f;return t=e,o=[{key:"checkAll",value:function(){var e=this,t=n()("#setting-panel-analytics .cmb2-id-check-all-services:not(.done), #cmb2-metabox-rank-math-wizard .cmb2-id-check-all-services:not(.done)");t.length>0&&"0"===t.val()&&(t.addClass("done"),r("google_check_all_services").done((function(t){e.response=t,e.fillSelect(),e.accordions.removeClass("locked")})))}},{key:"events",value:function(){var e=this;this.saveChanges=!1,this.accountSelect=n()(".site-analytics-account"),this.profileSelect=n()(".site-console-profile"),this.propertySelect=n()(".site-analytics-property"),this.viewSelect=n()(".site-analytics-view"),this.adsenseSelect=n()(".site-adsense-account"),this.accordions=n()(".rank-math-accordion"),this.countryConsole=n()("#site-console-country"),this.countryAnalytics=n()("#site-analytics-country"),this.testConnectionButton=n()(".rank-math-test-connection-google"),n()(".cmb2_select").on("select2:open",(function(){document.querySelector(".select2-search__field").focus()})),this.accountSelect.on("change",(function(){var t=parseInt(e.accountSelect.val());0!==t?e.fillPropertySelect(t):e.propertySelect.html('<option value="0">Select Property</option>')})),this.profileSelect.on("change",(function(){0!==parseInt(e.profileSelect.val())&&document.getElementById("enable-index-status").removeAttribute("disabled")})),n()([".rank-math-wizard-body--analytics .form-footer.rank-math-ui .button-primary",".rank-math_page_rank-math-options-general .form-footer.rank-math-ui .button-primary"].join(", ")).on("click",(function(t){e.submitButtonHandler(t)})),this.testConnectionButton.on("click",(function(t){t.preventDefault(),e.testConnections(t)})),this.propertySelect.on("change",(function(){"create-ga4-property"!==e.propertySelect.val()?(e.response.type=(0,i.get)(e.response.accounts,[e.accountSelect.val(),"properties",e.propertySelect.val(),"type"],{}),"GA4"!==e.response.type&&(e.response.type="GA3",e.response.views=(0,i.get)(e.response.accounts,[e.accountSelect.val(),"properties",e.propertySelect.val(),"profiles"],{})),"GA4"===e.response.type?e.createNewDataStream():e.fillViewSelect()):e.createNewProperty()})),this.viewSelect.on("change",(function(e){var t=n()(e.target).find(":selected");t.data("measurement-id")&&(n()("#rank-math-analytics-measurement-id").val(t.data("measurement-id")),n()("#rank-math-analytics-stream-name").val(t.text()))})),n()(".rank-math-disconnect-google").on("click",(function(e){e.preventDefault(),confirm(rankMath.confirmDisconnect)&&r("disconnect_google").done((function(){window.location.reload()}))}))}},{key:"createNewProperty",value:function(){var e=this;confirm((0,a.__)("Are you sure, you want to create a new GA4 Property?","rank-math"))?r("create_ga4_property",{accountID:this.accountSelect.val()},"post").done((function(t){if(t.error)return e.propertySelect.val(e.propertySelect.find("option:first").val()),void alert(t.error);e.propertySelect.append('<option value="'+t.id+'">'+t.name+"</option>"),e.propertySelect.val(t.id),e.createNewDataStream(),e.response.type="GA4"})):this.propertySelect.val(this.propertySelect.find("option:first").val())}},{key:"createNewDataStream",value:function(){var e=this;this.viewSelect.html(""),this.viewSelect.prop("disabled",!0),r("get_ga4_data_streams",{propertyID:this.propertySelect.val()},"post").done((function(t){t.error?console.error(t.error):(e.response.views=t.streams,e.fillViewSelect(),e.viewSelect.trigger("change"))}))}},{key:"submitButtonHandler",value:(d=l().mark((function e(t){var r,o,i,c,s,u;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n()(t.target),t.preventDefault(),!r.hasClass("disabled")){e.next=4;break}return e.abrupt("return");case 4:if(n()("#setting-panel-analytics:visible").length&&!n()("#setting-panel-analytics .connect-wrap").length){e.next=7;break}return r.off("click").trigger("click"),e.abrupt("return");case 7:return r.addClass("disabled").val((0,a.__)("Saving…","rank-math")),e.next=10,this.saveConsole();case 10:return o=e.sent,e.next=13,this.saveAnalytics();case 13:return i=e.sent,e.next=16,this.saveAdsense();case 16:c=e.sent,n()(".rank-math-accordion").find(".rank-math-notice").remove(),s="",u="",o.success?i.success?c.success||(s=n()(".rank-math-connect-adsense .rank-math-accordion-content"),u=c.error):(s=n()(".rank-math-connect-analytics .rank-math-accordion-content"),u=i.error):(s=n()(".rank-math-connect-search-console .rank-math-accordion-content"),u=o.error),o.success&&i.success&&c.success?r.off("click").trigger("click"):(s.append('<div class="rank-math-notice notice notice-error"><p>'+u+"</p></div>"),n()("html, body").animate({scrollTop:s.offset().top},2e3),r.removeClass("disabled").val((0,a.__)("Save Changes","rank-math")));case 22:case"end":return e.stop()}}),e,this)})),f=function(){var e=this,t=arguments;return new Promise((function(n,a){var r=d.apply(e,t);function o(e){u(r,n,a,o,i,"next",e)}function i(e){u(r,n,a,o,i,"throw",e)}o(void 0)}))},function(e){return f.apply(this,arguments)})},{key:"testConnections",value:function(e){e.preventDefault(),(0,c.applyFilters)("rank_math_test_connections",[{class:".rank-math-connect-search-console",canTest:rankMath.isConsoleConnected,action:"check_console_request"},{class:".rank-math-connect-analytics",canTest:rankMath.isAnalyticsConnected,action:"check_analytics_request"}],this).forEach((function(e){if(e.canTest){var t=n()(e.class).find(".rank-math-connection-status-wrap");t.html('<svg class="rank-math-spinner" viewBox="0 0 100 100" width="16" height="16" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false"><circle cx="50" cy="50" r="50" vector-effect="non-scaling-stroke"></circle><path d="m 50 0 a 50 50 0 0 1 50 50" vector-effect="non-scaling-stroke"></path></svg>'),r(e.action,{},"post").done((function(e){e.success?t.html('<span class="rank-math-connection-status rank-math-connection-status-success" title="'+(0,a.__)("Connected","rank-math")+'"></span>'):t.html('<span class="rank-math-connection-status rank-math-connection-status-error" title="'+(0,a.__)("Some permissions are missing, please reconnect","rank-math")+'"></span>')}))}}))}},{key:"saveConsole",value:function(){if(0===parseInt(this.profileSelect.val()))return{success:!0};var e={profile:this.profileSelect.val(),country:this.countryConsole.val(),enableIndexStatus:n()("#enable-index-status").is(":checked")},t=n()("#console_caching_control");return t.length>0&&(e.days=t.val()),r("save_analytic_profile",e,"post").done((function(e){return e}))}},{key:"saveAnalytics",value:function(){var e={accountID:this.accountSelect.val(),propertyID:this.propertySelect.val(),viewID:this.viewSelect.val(),country:this.countryAnalytics.val(),installCode:n()("#install-code").is(":checked"),anonymizeIP:n()("#anonymize-ip").is(":checked"),localGAJS:n()("#local-ga-js").is(":checked"),excludeLoggedin:n()("#exclude-loggedin").is(":checked"),measurementID:n()("#rank-math-analytics-measurement-id").val(),streamName:n()("#rank-math-analytics-stream-name").val()};if(0===parseInt(e.accountID)||0===parseInt(e.propertyID))return{success:!0};var t=n()("#console_caching_control");return t.length>0&&(e.days=t.val()),r("save_analytic_options",e,"post").done((function(e){return e}))}},{key:"saveAdsense",value:function(){var e={accountID:this.adsenseSelect.val()};return e.accountID?(0,c.applyFilters)("rank_math_save_adsense_account",e):{success:!0}}},{key:"fillSelect",value:function(){var e=this,t=this.response,n=t.inSearchConsole,a=t.isVerified;this.fillProfileSelect(),this.fillAccountSelect(),(0,c.doAction)("rank_math_fill_select",this),n||r("add_site_console").done((function(t){e.response.sites=t.sites,e.fillProfileSelect()})),n&&!a&&r("verify_site_console"),this.saveChanges=!1}},{key:"fillAccountSelect",value:function(){var e=this,t=this.response.accounts;(0,i.map)(t,(function(t,n){e.accountSelect.append('<option value="'+n+'">'+t.name+" ("+n+")</option>")})),this.accountSelect.data("selected")?this.accountSelect.val(this.accountSelect.data("selected")):(this.accountSelect.prop("disabled",!1),this.countryAnalytics.prop("disabled",!1),this.accountSelect.select2(),this.countryAnalytics.select2()),this.accountSelect.trigger("change")}},{key:"fillPropertySelect",value:function(e){var t=this,n=this.response,r=n.accounts,o=n.homeUrl,c=((null==r?void 0:r[e])||{}).properties;this.propertySelect.html('<option value="0">Select Property</option>'),this.propertySelect.append('<option value="create-ga4-property">'+(0,a.__)("Create new GA4 Property","rank-math")+"</option>"),(0,i.map)(c,(function(e){var n=e.url===o?' selected="selected"':"";"GA4"===e.type&&t.propertySelect.append("<option ".concat(n,' value="').concat(e.id,'">').concat(e.name," (").concat(e.id,")</option>"))})),this.propertySelect.data("selected")?this.propertySelect.val(this.propertySelect.data("selected")):(this.propertySelect.prop("disabled",!1),this.propertySelect.select2()),this.propertySelect.trigger("change")}},{key:"fillProfileSelect",value:function(){var e=this,t=this.response,n=t.sites,a=t.homeUrl,r=!1;(0,i.map)(n,(function(t,n){r=n===a?' selected="selected"':"",e.profileSelect.append("<option"+r+' value="'+n+'">'+t+"</option>")})),this.profileSelect.data("selected")&&this.profileSelect.closest(".rank-math-accordion").addClass("connected"),this.profileSelect.prop("disabled",!1),this.countryConsole.prop("disabled",!1),this.profileSelect.select2(),this.countryConsole.select2()}},{key:"fillViewSelect",value:function(){var e=this,t=this.response,n=t.views,r="GA4"===t.type?(0,a.__)("Data Stream","rank-math"):(0,a.__)("View","rank-math");this.viewSelect.prev("label").text(r),(0,i.map)(n,(function(t){var n=(0,i.isUndefined)(t.measurementId)?"":t.measurementId;e.viewSelect.append('<option value="'+t.id+'" data-measurement-id="'+n+'">'+t.name+"</option>")})),this.viewSelect.data("selected")&&this.viewSelect.val(this.viewSelect.data("selected")),this.viewSelect.prop("disabled",!1),this.viewSelect.select2()}}],o&&h(t.prototype,o),s&&h(t,s),Object.defineProperty(t,"prototype",{writable:!1}),e}(),v=new f;d=n(),String.prototype.format||(String.prototype.format=function(){var e=arguments;return this.replace(/{(\d+)}/g,(function(t,n){return void 0!==e[n]?e[n]:t}))}),String.prototype.trimRight=function(e){return void 0===e&&(e="s"),this.replace(new RegExp("["+e+"]+$"),"")},d((function(){window.rankMathAdmin={init:function(){this.misc(),this.tabs(),this.dependencyManager(),-1!==Object.values(window.rankMath.modules).indexOf("analytics")&&(v.events(),v.checkAll())},misc:function(){void 0!==n().fn.select2&&d("[data-s2]").select2(),d(".cmb-group-text-only,.cmb-group-fix-me").each((function(){var e=d(this),t=e.find(".cmb-repeatable-group"),n=t.find("> .cmb-row:eq(0) > .cmb-th");e.prepend('<div class="cmb-th"><label>'+n.find("h2").text()+"</label></div>"),t.find(".cmb-add-row").append('<span class="cmb2-metabox-description">'+n.find("p").text()+"</span>"),n.parent().remove()})),d(".rank-math-collapsible-trigger").on("click",(function(e){e.preventDefault();var t=d(this),n=d("#"+t.data("target"));t.toggleClass("open"),n.toggleClass("open")}));var e=d("#rank_math_rich_snippet"),t=e.find("option[value=review]"),a=e.val();t&&(t.prop("disabled",!0),"review"===a&&d(".cmb2-id-rank-math-review-schema-notice").removeClass("hidden"),e.on("change",(function(){null!==e.val()&&"review"!==e.val()&&d(".cmb2-id-rank-math-review-schema-notice").addClass("hidden")})))},searchConsole1:function(){var e=d(".cmb2-id-console-authorization-code"),t=d("#console_authorization_code"),n=d("#gsc-dp-info"),i=d("#console_profile"),c=i.parent().find(".rank-math-refresh"),s=t.parent(),l=d("body").hasClass("rank-math-wizard-body--analytics")?d("> p:first-of-type",".cmb-form"):d("h1",".rank-math-wrap-settings");s.on("click",".rank-math-deauthorize-account",(function(n){n.preventDefault();var s=d(this);if(s.prop("disabled",!0),t.data("authorized"))return r("search_console_deauthentication").always((function(){s.prop("disabled",!1)})).done((function(){t.val(""),t.show(),t.data("authorized",!1),e.removeClass("authorized").addClass("unauthorized"),s.html((0,a.__)("Authorize","rank-math")),i.prop("disabled",!0),c.prop("disabled",!0)})),!1;t.addClass("input-loading"),r("search_console_authentication",{code:t.val()}).always((function(){s.prop("disabled",!1),t.removeClass("input-loading")})).done((function(n){n&&!n.success&&o(n.error,"error",l),n&&n.success&&(t.hide(),t.data("authorized",!0),s.html("De-authorize Account"),c.trigger("click"),i.removeAttr("disabled"),e.removeClass("unauthorized authorizing").addClass("authorized"))}))})),i.on("change",(function(){null!==i.val()&&0===i.val().indexOf("sc-domain:")?n.removeClass("hidden"):n.addClass("hidden")})).change(),c.on("click",(function(e){e.preventDefault(),c.prop("disabled",!0),i.addClass("input-loading"),r("search_console_get_profiles").always((function(){c.prop("disabled",!1),d(".console-cache-update-manually").prop("disabled",!1),i.removeClass("input-loading")})).done((function(e){if(e&&!e.success&&o(e.error,"error",l),e&&e.success){var t=e.selected||i.val();i.html(""),d.each(e.profiles,(function(e,t){i.append('<option value="'+e+'">'+t+"</option>")})),i.val(t||Object.keys(e.profiles)[0]),c.removeClass("hidden")}}))}))},dependencyManager:function(){var e=this,t=d(".cmb-form, .rank-math-metabox-wrap");d(".cmb-repeat-group-wrap",t).each((function(){var e=d(this),t=e.next(".rank-math-cmb-dependency.hidden");t.length&&e.find("> .cmb-td").append(t)})),d(".rank-math-cmb-dependency",t).each((function(){e.loopDependencies(d(this))})),d("input, select",t).on("change",(function(){var t=d(this).attr("name");d('span[data-field="'+t+'"]').each((function(){e.loopDependencies(d(this).closest(".rank-math-cmb-dependency"))}))}))},checkDependency:function(e,t,n){return"string"==typeof t&&t.includes(",")&&"="===n?t.includes(e):"string"==typeof t&&t.includes(",")&&"!="===n?!t.includes(e):"="===n&&e===t||"=="===n&&e===t||">="===n&&e>=t||"<="===n&&e<=t||">"===n&&e>t||"<"===n&&e<t||"!="===n&&e!==t},loopDependencies:function(e){var t=this,n=e.data("relation"),a=null;e.find("span").each((function(){var e=d(this),r=d("[name='"+e.data("field")+"']"),o=r.val();r.is(":radio")&&(o=r.filter(":checked").val()),r.is(":checkbox")&&(o=r.filter(":checked").val());var i=t.checkDependency(o,e.data("value"),e.data("comparison"));if("or"===n&&i)return a=!0,!1;"and"===n&&(a=(null===a||a)&&i)}));var r=e.closest(".rank-math-cmb-group");r.length||(r=e.closest(".cmb-row")),a?r.slideDown(300):r.hide()},tabs:function(){var e,t;t=e||".rank-math-box-tabs",n()(t).children().on("click",(function(e){e.preventDefault();var t=n()(this),a=t.attr("href").substr(1);t.addClass("active-tab").siblings().removeClass("active-tab"),n()("#"+a).addClass("active-tab").siblings().removeClass("active-tab")}));var a=d(".rank-math-tabs-navigation");a.length&&a.each((function(){var e=d(this),t=e.closest(".rank-math-tabs"),n=d(">a",e),r=d(">.rank-math-tabs-content>.rank-math-tab",t),o=e.data("active-class")||"active",i=e.hasClass("before-header");n.on("click",(function(){var e=d(this),a=d(e.attr("href"));if(n.removeClass(o),r.hide(),i){var c=a.find(".cmb-type-title.tab-header").clone();c.addClass("before-header-title"),d(".before-header-title").remove(),t.prepend(c)}return e.addClass(o),a.show(),!1}));var c=window.location.hash||window.localStorage.getItem(t.attr("id"));null===c?n.eq(0).trigger("click"):(c=d('a[href="'+c+'"]',e)).length?c.trigger("click"):n.eq(0).trigger("click"),a.next().css("min-height",e.outerHeight())}))},variableInserter:function(e){var t=d("input[type=text], textarea",".rank-math-supports-variables");if(e=void 0===e||e,t.length){var n,r=this,o=d("body");if(t.attr("autocomplete","off"),t.wrap('<div class="rank-math-variables-wrap"/>'),d(".rank-math-variables-wrap").append('<a href="#" class="rank-math-variables-button button button-secondary"><span class="dashicons dashicons-arrow-down-alt2"></span></a>'),e){var i=rankMath.capitalizeTitle?"capitalize":"";d(".rank-math-variables-wrap").after('<div class="rank-math-variables-preview '+i+'" data-title="'+(0,a.__)("Example","rank-math")+'"/>'),t.on("rank_math_variable_change input",(function(e){var t=d(e.currentTarget),n=r.replaceVariables(t.val());60<n.length&&0<=t.attr("name").indexOf("title")?n=n.substring(0,60)+"...":160<n.length&&0<=t.attr("name").indexOf("description")&&(n=n.substring(0,160)+"...");var a=d("<textarea/>").html(n).val();t.parent().next(".rank-math-variables-preview").text(a)})),t.trigger("rank_math_variable_change")}var c=d("<ul/>"),s=d('<div class="rank-math-variables-dropdown"><input type="text" placeholder="'+(0,a.__)("Search &hellip;","rank-math")+'"></div>');d.each(rankMath.variables,(function(){c.append('<li data-var="%'+this.variable+'%" data-example="'+this.example+'"><strong>'+this.name+"</strong><span>"+this.description+"</span></li>")})),s.append(c),d(".rank-math-variables-wrap:eq(0)").append(s);var l=d(".rank-math-variables-button, .rank-math-variables-button *, .rank-math-variables-dropdown, .rank-math-variables-dropdown *");o.on("click",(function(e){d(e.target).is(l)||f()}));var u=s.find("input"),h=s.find("li");o.on("click",".rank-math-variables-button",(function(e){e.preventDefault();var t=d(this);t.after(s),h.show(),void 0!==(n=t.prev().data("exclude-variables"))&&(n=n.split(","),p()),s.show(),u.val("").focus()})),s.on("click","li",(function(e){e.preventDefault();var t=d(this),n=t.closest(".rank-math-variables-wrap").find("input,textarea");n.val(d.trim(n.val())+" "+t.data("var")),n.trigger("rank_math_variable_change").trigger("input"),f()})),s.on("keyup","input",(function(e){e.preventDefault();var t=d(this).val().toLowerCase();if(2>t.length)return h.show(),void p();h.hide().each((function(){var e=d(this);-1!==e.text().toLowerCase().indexOf(t)&&e.show()})),p()}))}function p(){void 0!==n&&n.forEach((function(e){s.find('[data-var="%'+e+'%"]').hide()}))}function f(){n=void 0,s.hide()}},replaceVariables:function(e){return d.each(rankMath.variables,(function(t){if(!this.example)return!0;var n=new RegExp("\\([a-z]+\\)","g");t=t.replace(n,"\\(.*?\\)"),e=e.replace(new RegExp("%+"+t+"%+","g"),this.example)})),e}},window.rankMathAdmin.init()}))}();