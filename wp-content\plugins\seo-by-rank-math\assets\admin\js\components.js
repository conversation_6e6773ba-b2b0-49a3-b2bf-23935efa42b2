!function(){var t={4184:function(t,e){var n;!function(){"use strict";var o={}.hasOwnProperty;function r(){for(var t=[],e=0;e<arguments.length;e++){var n=arguments[e];if(n){var a=typeof n;if("string"===a||"number"===a)t.push(n);else if(Array.isArray(n)){if(n.length){var i=r.apply(null,n);i&&t.push(i)}}else if("object"===a){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){t.push(n.toString());continue}for(var l in n)o.call(n,l)&&n[l]&&t.push(l)}}}return t.join(" ")}t.exports?(r.default=r,t.exports=r):void 0===(n=function(){return r}.apply(e,[]))||(t.exports=n)}()},2853:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,':root{--rankmath-wp-adminbar-height: 0}.rank-math-button.components-button{display:inline-flex !important;height:auto;min-height:30px;padding:0 20px;border:1px solid #7f868d;border-radius:3px;color:#6b7278;background:#f8f9fa;font-size:14px;line-height:38px;vertical-align:top;transition:.05s ease-in-out}.rank-math-button.components-button:active:not(:disabled),.rank-math-button.components-button.button-secondary:active:not(:disabled),.rank-math-button.components-button.is-destructive:not(.is-primary):not(.is-secondary):not(.is-tertiary):not(.is-link):active:not(:disabled){border-color:#8c8f94;box-shadow:none;background:#f6f7f7}.rank-math-button.components-button:focus:not(:disabled),.rank-math-button.components-button:hover:not(:disabled),.rank-math-button.components-button.button-secondary:hover:not(:disabled),.rank-math-button.components-button.button-secondary:focus:not(:disabled),.rank-math-button.components-button.is-destructive:not(.is-primary):not(.is-secondary):not(.is-tertiary):not(.is-link):hover:not(:disabled),.rank-math-button.components-button.is-destructive:not(.is-primary):not(.is-secondary):not(.is-tertiary):not(.is-link):focus:not(:disabled){border-color:#069de3;color:#069de3;background:#f8f9fa}.rank-math-button.components-button:focus:not(:disabled){box-shadow:0 0 0 1px #069de3;outline-width:2px;outline-offset:0}.rank-math-button.components-button:disabled{opacity:1;pointer-events:none}.rank-math-button.components-button.has-icon i,.rank-math-button.components-button.has-icon svg,.rank-math-button.components-button.has-icon .dashicons{margin:0 !important}.rank-math-button.components-button.has-icon i,.rank-math-button.components-button.has-icon .dashicons{font-size:20px !important}.rank-math-button.components-button.has-icon .dashicons{display:inline-flex;justify-content:center;align-items:center;width:20px !important;height:20px !important}.rank-math-button.components-button.has-icon svg{width:20px;height:20px}.rank-math-button.components-button.has-icon svg path{fill:currentColor;fill-opacity:1}.rank-math-button.components-button.has-icon.has-text{gap:5px}.rank-math-button.components-button.is-small{min-height:26px;padding:0 8px;font-size:11px;line-height:2.18}.rank-math-button.components-button.is-small.has-icon.has-text{gap:5px}.rank-math-button.components-button.is-small.has-icon.has-text i,.rank-math-button.components-button.is-small.has-icon.has-text svg,.rank-math-button.components-button.is-small.has-icon.has-text .dashicons{margin:0 !important}.rank-math-button.components-button.is-small.has-icon.has-text i,.rank-math-button.components-button.is-small.has-icon.has-text .dashicons{font-size:13px !important}.rank-math-button.components-button.is-small.has-icon.has-text .dashicons{display:inline-flex;justify-content:center;align-items:center;width:13px !important;height:13px !important}.rank-math-button.components-button.is-small.has-icon.has-text svg{width:13px;height:13px}.rank-math-button.components-button.is-small.has-icon.has-text svg path{fill:currentColor;fill-opacity:1}.rank-math-button.components-button.is-large{min-height:32px;padding:0 12px;line-height:2.30769231}.rank-math-button.components-button.is-xlarge{height:46px;font-size:16px;line-height:44px}.rank-math-button.components-button.is-destructive:not(.is-primary):not(.is-secondary):not(.is-tertiary):not(.is-link){border-color:#ee6a5e;box-shadow:none;color:#ee6a5e;background:rgba(0,0,0,0)}.rank-math-button.components-button.is-destructive:not(.is-primary):not(.is-secondary):not(.is-tertiary):not(.is-link):hover:not(:disabled),.rank-math-button.components-button.is-destructive:not(.is-primary):not(.is-secondary):not(.is-tertiary):not(.is-link):focus:not(:disabled){box-shadow:0 0 0 1px #ee6a5e}.rank-math-button.components-button.is-destructive:not(.is-primary):not(.is-secondary):not(.is-tertiary):not(.is-link):active:not(:focus):not(:disabled){color:#ee6a5e}.rank-math-button.components-button.button-animate,.rank-math-button.components-button.button-primary{border-color:#069de3;color:#fff;background:#069de3}.rank-math-button.components-button.button-animate:hover:not(:disabled),.rank-math-button.components-button.button-primary:hover:not(:disabled){color:#fff}.rank-math-button.components-button.button-animate:active:not(:disabled),.rank-math-button.components-button.button-primary:active:not(:disabled){border-color:#135e96;background:#135e96}.rank-math-button.components-button.button-animate:hover:not(:disabled),.rank-math-button.components-button.button-animate:focus:not(:disabled),.rank-math-button.components-button.button-primary:hover:not(:disabled),.rank-math-button.components-button.button-primary:focus:not(:disabled){border-color:#08a7f1;background:#08a7f1}.rank-math-button.components-button.button-animate:focus:not(:disabled),.rank-math-button.components-button.button-primary:focus:not(:disabled){color:#fff;box-shadow:0 0 0 1px #fff,0 0 0 3px #069de3}.rank-math-button.components-button.button-animate:disabled,.rank-math-button.components-button.button-primary:disabled{border-color:#069de3 !important;color:#9fdffc !important;background:#069de3 !important}.rank-math-button.components-button.button-primary-outline{border-color:#2271b1;color:#2271b1;background:#f6f7f7}.rank-math-button.components-button.button-primary-outline:hover:not(:disabled){border-color:#0a4b78;background:#f0f0f1}.rank-math-button.components-button.button-primary-outline:active:not(:disabled){border-color:#8c8f94}.rank-math-button.components-button.button-primary-outline:disabled{border-color:#2271b1 !important;color:#2271b1 !important;background:#f6f7f7 !important}.rank-math-button.components-button.button-primary-outline:hover:not(:disabled),.rank-math-button.components-button.button-primary-outline:focus:not(:disabled){color:#0a4b78}.rank-math-button.components-button.button-primary-outline:focus:not(:disabled){border-color:#3582c4;box-shadow:0 0 0 1px #3582c4;color:#0a4b78;background:#f6f7f7}.rank-math-button.components-button.button-primary-outline.is-destructive{color:#d63638}.rank-math-button.components-button.button-primary-outline.is-destructive:hover:not(:focus){box-shadow:none}.rank-math-button.components-button.button-primary-outline.is-destructive:focus:not(:disabled){background:#f6f7f7}.rank-math-button.components-button.button-animate:disabled,.rank-math-button.components-button.button-secondary:disabled{color:#a7aaad !important;background-color:#f6f7f7 !important;border-color:#dcdcde !important}.rank-math-button.components-button.button-secondary,.rank-math-button.components-button.button-secondary:active:not(:focus):not(:disabled){color:#6b7278}.rank-math-button.components-button.button-secondary{border-color:#7f868d;box-shadow:none;background:#f8f9fa}.rank-math-button.components-button.button-secondary:hover:not(:disabled),.rank-math-button.components-button.button-secondary:active:not(:disabled){box-shadow:none}.rank-math-button.components-button.button-secondary:focus:not(:disabled){box-shadow:0 0 0 1px #069de3}.rank-math-button.components-button.button-link{height:auto;min-height:0;padding:0;border:0;border-radius:0;color:#2271b1;background:none;line-height:1.3;transition:.05s ease-in-out}.rank-math-button.components-button.button-link:hover:not(:disabled),.rank-math-button.components-button.button-link:active:not(:disabled){color:#135e96;background:none}.rank-math-button.components-button.button-link:focus:not(:disabled){box-shadow:0 0 0 1px #4f94d4,0 0 2px 1px rgba(79,148,212,.8);outline-width:1px;color:#043959}.rank-math-button.components-button.button-green,.rank-math-button.components-button.button-green:focus:not(:disabled){color:#fff}.rank-math-button.components-button.button-green{display:inline-block;width:auto;max-width:100%;padding:0 2rem;margin:0 auto;border-color:#10ac84;background:#10ac84;font-weight:500}.rank-math-button.components-button.button-green:active:not(:disabled){color:#1e1e1e}.rank-math-button.components-button.button-green:hover:not(:disabled),.rank-math-button.components-button.button-green:focus:not(:disabled){border-color:#0f9e79;color:#fff;background:#0f9e79}.rank-math-button.components-button.button-green:focus:not(:disabled){box-shadow:0 0 0 1px #fff,0 0 0 3px #0f9e79}.rank-math-button.components-button.button-remove-group,.rank-math-button.components-button.button-remove-group:active{color:#a00}.rank-math-button.components-button.button-remove-group{padding:0 10px;border:0;box-shadow:none;background:rgba(0,0,0,0)}.rank-math-button.components-button.button-remove-group:focus:not(:disabled),.rank-math-button.components-button.button-remove-group:hover:not(:disabled){box-shadow:0 0 0 1px #dc3232}.rank-math-button.components-button.button-animate{gap:8px;position:relative;transform:perspective(1px) translateZ(0);height:auto;min-height:30px;padding:0 40px;border-radius:82px;font-size:24px;line-height:82px;text-align:center}.rank-math-button.components-button.button-animate:disabled{opacity:1}.rank-math-button.components-button.button-animate:not(:disabled)::before{position:absolute;top:-6px;right:-6px;bottom:-6px;left:-6px;content:"";border:#bcdbea solid 6px;border-radius:55px;animation:ripple-out 1s linear infinite}.rank-math-button.components-button.button-start-new-chat{width:16.5rem;height:42px;line-height:15px;font-weight:500}.rank-math-button.components-button.button-start-new-chat.has-icon.has-text{justify-content:center;gap:12px;padding-left:16px;padding-right:16px}.rank-math-button.components-button.button-start-new-chat.has-icon.has-text i{font-size:14px}@media screen and (max-width: 782px){.rank-math-button.components-button.is-small,.rank-math-button.components-button.is-large{min-height:40px;padding:0 14px;margin-bottom:4px;font-size:14px;line-height:2.71428571;vertical-align:middle}.rank-math-button.components-button.button-animate{font-size:15px;padding:0 20px}}@keyframes ripple-out{100%{top:-20px;right:-20px;bottom:-20px;left:-20px;opacity:0}}',""]),e.Z=i},4679:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a),l=n(1667),c=n.n(l),s=new URL(n(5132),n.b),u=i()(r()),m=c()(s);u.push([t.id,":root{--rankmath-wp-adminbar-height: 0}.rank-math-checkbox-control .components-base-control__field{margin:0}.rank-math-checkbox-control .components-base-control__field .components-flex{align-items:center}.rank-math-checkbox-control .components-base-control__field .components-checkbox-control__input-container{width:auto;height:auto;margin-right:4px;aspect-ratio:auto}.rank-math-checkbox-control .components-base-control__field .components-checkbox-control__input-container .components-checkbox-control__indeterminate{display:none}.rank-math-checkbox-control .components-base-control__field .components-checkbox-control__input-container .components-checkbox-control__checked{display:none}.rank-math-checkbox-control .components-base-control__field .components-checkbox-control__input-container .components-checkbox-control__input{display:inline-block;width:1rem;min-width:1rem;height:1rem;padding:0 !important;margin:0 5px 0 0;border:1px solid #8c8f94;border-radius:4px;box-shadow:inset 0 1px 2px rgba(0,0,0,.1);outline:0;color:#50575e;background:#fff;line-height:0;text-align:center;vertical-align:-3px;clear:none;transition:.05s border-color ease-in-out}@media screen and (max-width: 782px){.rank-math-checkbox-control .components-base-control__field .components-checkbox-control__input-container .components-checkbox-control__input{width:1.5625rem;height:1.5625rem;max-width:1.5625rem}}.rank-math-checkbox-control .components-base-control__field .components-checkbox-control__input-container .components-checkbox-control__input:disabled{border-color:rgba(255,255,255,.75);box-shadow:inset 0 1px 2px rgba(0,0,0,.04);color:rgba(44,51,56,.5);opacity:.7;background:rgba(255,255,255,.5);pointer-events:none}.rank-math-checkbox-control .components-base-control__field .components-checkbox-control__input-container .components-checkbox-control__input:disabled+label{pointer-events:none}.rank-math-checkbox-control .components-base-control__field .components-checkbox-control__input-container .components-checkbox-control__input:focus,.rank-math-checkbox-control .components-base-control__field .components-checkbox-control__input-container .components-checkbox-control__input:hover:not(:disabled){box-shadow:0 0 0 1px #2271b1}.rank-math-checkbox-control .components-base-control__field .components-checkbox-control__input-container .components-checkbox-control__input:focus{border-color:#2271b1;outline:2px solid rgba(0,0,0,0)}.rank-math-checkbox-control .components-base-control__field .components-checkbox-control__input-container .components-checkbox-control__input:checked::before{float:left;content:url("+m+');width:1.3125rem;height:1.3125rem;margin:-0.1875rem 0 0 -0.25rem}@media screen and (max-width: 782px){.rank-math-checkbox-control .components-base-control__field .components-checkbox-control__input-container .components-checkbox-control__input:checked::before{width:1.875rem;height:1.875rem;margin-left:-0.3125rem;margin-right:-0.3125rem}}.rank-math-checkbox-control .components-base-control__field .components-checkbox-control__label{color:#242628;font-size:14px;line-height:16px;vertical-align:middle;cursor:pointer}.rank-math-checkbox-control.is-disabled .components-base-control__field label{pointer-events:none}.rank-math-checkbox-control.metabox.is-indeterminate{display:inline-block;width:49%;margin:1px 0 5px 0}.rank-math-checkbox-control.metabox .components-base-control__field .components-checkbox-control__input:hover:not(:focus){box-shadow:inset 0 1px 2px rgba(0,0,0,.1)}.rank-math-checkbox-control.metabox .components-base-control__field .components-checkbox-control__input:checked{border-color:#069de3;box-shadow:none;background:#069de3}.rank-math-checkbox-control.metabox .components-base-control__field .components-checkbox-control__input:checked:hover{box-shadow:none}.rank-math-checkbox-control.metabox .components-base-control__field .components-checkbox-control__input:checked::before{content:"";color:#fff;font-family:"dashicons";font-size:20px;line-height:20px}@media screen and (max-width: 782px){.rank-math-checkbox-control.metabox .components-base-control__field .components-checkbox-control__input:checked::before{width:1.875rem;height:1.875rem;margin:2px -0.3125rem;font-size:30px}}',""]),e.Z=u},1403:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,':root{--rankmath-wp-adminbar-height: 0}.field-big-labels .rank-math-checkbox-list{padding:0;margin:0}.field-big-labels .rank-math-checkbox-list::before{display:table;content:""}.field-big-labels .rank-math-checkbox-list li{float:left;padding:0 8px;margin:0 0 15px;width:50%;box-sizing:border-box}.field-big-labels .rank-math-checkbox-list li .rank-math-checkbox-control{width:100% !important;margin:0 !important}.field-big-labels .rank-math-checkbox-list li .rank-math-checkbox-control .components-base-control__field{position:relative}.field-big-labels .rank-math-checkbox-list li .rank-math-checkbox-control .components-base-control__field .components-checkbox-control__label{display:block;width:100%;padding:10px 12px 10px 38px;border:1px solid #c3c4c7;border-radius:3px;background:#f8f9fa;cursor:pointer}.field-big-labels .rank-math-checkbox-list li .rank-math-checkbox-control .components-base-control__field .components-checkbox-control__label:hover{border-color:#7f868d}@media screen and (max-width: 782px){.field-big-labels .rank-math-checkbox-list li .rank-math-checkbox-control .components-base-control__field .components-checkbox-control__label{padding-left:50px}}.field-big-labels .rank-math-checkbox-list li .rank-math-checkbox-control .components-base-control__field .components-checkbox-control__input-container{position:absolute;top:50%;left:13px;transform:translateY(-50%);width:fit-content;height:fit-content}.field-big-labels .rank-math-checkbox-list li .rank-math-checkbox-control.is-disabled .components-checkbox-control__label{opacity:.7}',""]),e.Z=i},2548:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,":root{--rankmath-wp-adminbar-height: 0}.rank-math-radio-control .components-radio-control__input[type=radio]{display:inline-block;width:1rem;min-width:1rem;height:1rem;padding:0 !important;margin:0 5px 0 0;border:1px solid #8c8f94;border-radius:4px;box-shadow:inset 0 1px 2px rgba(0,0,0,.1);outline:0;color:#50575e;background:#fff;line-height:0;text-align:center;vertical-align:-3px;clear:none;transition:.05s border-color ease-in-out;margin-right:9px;border-radius:50%;line-height:.71428571}@media screen and (max-width: 782px){.rank-math-radio-control .components-radio-control__input[type=radio]{width:1.5625rem;height:1.5625rem;max-width:1.5625rem}}.rank-math-radio-control .components-radio-control__input[type=radio]:checked::before{position:unset;transform:none;width:.5rem;height:.5rem;margin:.1875rem;border:none;background-color:#3582c4}.rank-math-radio-control .components-radio-control__input[type=radio]:disabled{border-color:rgba(255,255,255,.75);box-shadow:inset 0 1px 2px rgba(0,0,0,.04);color:rgba(44,51,56,.5);opacity:.7;background:rgba(255,255,255,.5);pointer-events:none}.rank-math-radio-control .components-radio-control__input[type=radio]:disabled+label{pointer-events:none}.rank-math-radio-control label{color:#242628;font-size:14px;line-height:16px;vertical-align:middle;cursor:pointer}.rank-math-radio-control.metabox .components-radio-control__input[type=radio],.rank-math-radio-control.metabox .components-radio-control__input[type=radio]:disabled{border-color:#7f868d;box-shadow:0 0 0 1px #7f868d}.rank-math-radio-control.metabox .components-radio-control__input[type=radio]:checked{border-color:#069de3;box-shadow:0 0 0 1px #069de3}.rank-math-radio-control.metabox .components-radio-control__input[type=radio]:checked::before{background:#069de3}@media screen and (max-width: 782px){.rank-math-radio-control .components-radio-control__input[type=radio]:checked::before{transform:translate(5px, 5px)}}",""]),e.Z=i},6236:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,':root{--rankmath-wp-adminbar-height: 0}.rank-math-toggle-control .components-base-control__field{margin-bottom:0}.rank-math-toggle-control .components-base-control__field .components-flex{gap:8px}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle{height:unset;margin-right:0}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle__input:focus+.components-form-toggle__track{box-shadow:none;outline:none}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle__input:focus+.components-form-toggle__track:hover:not(:disabled),.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle__input:focus+.components-form-toggle__track:focus:not(:disabled),.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle__input:focus+.components-form-toggle__track:active:not(:disabled){box-shadow:none;outline:none}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle__track{width:50px;height:24px;border:2px solid #6c7781;border-radius:34px}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle__thumb{top:50%;left:5px;transform:translateY(-50%);width:14px;height:14px;border:none;background-color:#6c7781}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle::after{content:"";position:absolute;top:50%;right:6px;transform:translateY(-50%);width:8px;height:8px;border:3px solid #6c7781;border-radius:50%;box-sizing:border-box}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle.is-checked .components-form-toggle__track{border-color:#069de3;background-color:#069de3}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle.is-checked .components-form-toggle__thumb{right:5px;left:auto;background-color:#fff}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle.is-checked::after{right:auto;left:11px;width:3px;height:8px;border:none;border-radius:1px;background:#fff}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle.is-disabled{opacity:1;pointer-events:none}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle.is-disabled.is-checked::after{background:#b5bfc9}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle.is-disabled .components-form-toggle__track{border-color:#b5bfc9;background:rgba(0,0,0,0)}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle.is-disabled .components-form-toggle__thumb{background:#b5bfc9}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle.is-disabled::after{border-color:#b5bfc9}.rank-math-toggle-control .components-base-control__field .components-flex .components-form-toggle.is-disabled+label{pointer-events:none}.rank-math-toggle-control .components-base-control__field .components-flex .components-toggle-control__label{color:#242628;font-size:14px;line-height:16px;vertical-align:middle;cursor:pointer}.rank-math-toggle-control.hide-label .components-toggle-control__label{display:none}',""]),e.Z=i},2408:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,':root{--rankmath-wp-adminbar-height: 0}.rank-math-date-picker{display:inline-block;position:relative}.rank-math-date-picker .rank-math-text-control.components-base-control.regular-text{position:relative;width:120px}.rank-math-date-picker .components-datetime__date{position:absolute;left:2px;border:1px solid #dfdfdf;box-shadow:0 3px 6px rgba(0,0,0,.075);background:#fff;opacity:0;z-index:-1;transition:all .2s ease-in-out}.rank-math-date-picker .components-datetime__date:focus-within{opacity:1;z-index:1}.rank-math-date-picker .components-datetime__date>.components-flex{margin-bottom:0;background:#00a0d2}.rank-math-date-picker .components-datetime__date>.components-flex h3,.rank-math-date-picker .components-datetime__date>.components-flex button{color:#fff}.rank-math-date-picker .components-datetime__date>.components-flex h3{font-size:14px}.rank-math-date-picker .components-datetime__date>.components-flex button{box-shadow:none;outline:none}.rank-math-date-picker .components-datetime__date>.components-flex button:hover{opacity:.7}.rank-math-date-picker .components-datetime__date>.css-1srrseb{justify-items:inherit;gap:0;position:relative}.rank-math-date-picker .components-datetime__date>.css-1srrseb>div{padding:10px;color:#fff;background:#32373c;line-height:1.4em;z-index:20}.rank-math-date-picker .components-datetime__date>.css-1srrseb button{width:100%;height:100%;border-radius:0;box-shadow:none;padding:5px 10px;border-top:1px solid #f4f4f4;border-right:1px solid #f4f4f4;color:#444;line-height:1.4em;z-index:20}.rank-math-date-picker .components-datetime__date>.css-1srrseb button:hover:not(:disabled),.rank-math-date-picker .components-datetime__date>.css-1srrseb button:focus:not(:disabled),.rank-math-date-picker .components-datetime__date>.css-1srrseb button[aria-label*=Selected]:not(:disabled){color:#fff;background:#0073aa}.rank-math-date-picker .components-datetime__date>.css-1srrseb button:disabled,.rank-math-date-picker .components-datetime__date>.css-1srrseb button[aria-label*=Selected]:disabled{color:rgba(68,68,68,.5);opacity:1}.rank-math-date-picker .components-datetime__date>.css-1srrseb button[aria-label*=Selected]:disabled{background:#f0f0c0}.rank-math-date-picker .components-datetime__date>.css-1srrseb button:first-of-type{border-left:1px solid #f4f4f4}.rank-math-date-picker .components-datetime__date>.css-1srrseb::after,.rank-math-date-picker .components-datetime__date>.css-1srrseb::before{content:"";display:block;position:absolute;top:38px;bottom:0;background:#f4f4f4;z-index:10}.rank-math-date-picker .components-datetime__date>.css-1srrseb::after{left:0;width:42px}.rank-math-date-picker .components-datetime__date>.css-1srrseb::before{right:0;width:42px}.rank-math-date-picker .components-datetime__date::after{content:"";display:block;position:absolute;bottom:28px;left:0;right:0;height:1px;background:#f4f4f4}.rank-math-date-picker.hide-date-picker .components-datetime__date{opacity:0;z-index:-1}.rank-math-date-picker.show-date-picker .components-datetime__date{opacity:1;z-index:1}.rank-math-date-picker+.field-description{display:inline-block;margin-left:6px}',""]),e.Z=i},4258:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,":root{--rankmath-wp-adminbar-height: 0}.rank-math-text-control.components-base-control .components-base-control__field{margin-bottom:0}.rank-math-text-control.components-base-control .components-base-control__field .components-text-control__input{min-height:30px;padding:0 8px;margin:0 1px;font-size:14px;line-height:2;border-radius:4px;border:1px solid #8c8f94;box-shadow:0 0 0 rgba(0,0,0,0);color:#2c3338;background-color:#fff}.rank-math-text-control.components-base-control .components-base-control__field .components-text-control__input:read-only{background-color:#f0f0f1}.rank-math-text-control.components-base-control .components-base-control__field .components-text-control__input:focus{background:#fff}.rank-math-text-control.components-base-control .components-base-control__field .components-text-control__input:focus{border-color:#2271b1;box-shadow:0 0 0 1px #2271b1;outline:2px solid rgba(0,0,0,0)}.rank-math-text-control.components-base-control .components-base-control__field .components-text-control__input:disabled{border-color:rgba(255,255,255,.75);box-shadow:inset 0 1px 2px rgba(0,0,0,.04);color:rgba(44,51,56,.5);background:rgba(255,255,255,.5)}.rank-math-text-control.components-base-control.regular-text{width:100%}.rank-math-text-control.components-base-control.regular-text .components-base-control__field .components-text-control__input{width:100%;height:40px;padding:.75em .6em;border-color:#7f868d;transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s}.rank-math-text-control.components-base-control.regular-text .components-base-control__field .components-text-control__input:hover:not(:disabled){border-color:#069de3;outline:none}.rank-math-text-control.components-base-control.regular-text .components-base-control__field .components-text-control__input:focus:not(:disabled){box-shadow:0 0 0 1px #069de3;border-color:#069de3;outline:none;background-color:#fff}.rank-math-text-control.components-base-control.no-value .invalid{background:pink}.rank-math-text-control.components-base-control:not(.no-value) .invalid{background-color:#fff;border-color:#8c8f94 !important}.rank-math-text-control.components-base-control:not(.no-value) .invalid:focus{border-color:#2271b1 !important}.rank-math-text-control.components-base-control:not(.no-value) .invalid+span{display:none}@media screen and (max-width: 782px){.rank-math-text-control.components-base-control .components-base-control__field .components-text-control__input{min-height:40px;padding:3px 10px;font-size:16px}}",""]),e.Z=i},5912:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,':root{--rankmath-wp-adminbar-height: 0}.rank-math-textarea-control.components-base-control .components-base-control__field{margin:0}.rank-math-textarea-control.components-base-control .components-textarea-control__input{padding:2px 6px;font-size:14px;line-height:1.42857143;overflow:auto;resize:vertical;border-radius:4px;border:1px solid #8c8f94;box-shadow:0 0 0 rgba(0,0,0,0);color:#2c3338;background-color:#fff}.rank-math-textarea-control.components-base-control .components-textarea-control__input:read-only{background-color:#f0f0f1}.rank-math-textarea-control.components-base-control .components-textarea-control__input:focus{border-color:#2271b1;box-shadow:0 0 0 1px #2271b1;outline:2px solid rgba(0,0,0,0)}.rank-math-textarea-control.components-base-control .components-textarea-control__input:disabled{border-color:rgba(255,255,255,.75);box-shadow:inset 0 1px 2px rgba(0,0,0,.04);color:rgba(44,51,56,.5);background:rgba(255,255,255,.5)}.rank-math-textarea-control.components-base-control.metabox .components-textarea-control__input,.rank-math-textarea-control.components-base-control.code-box .components-textarea-control__input{display:block;width:100%;height:40px;padding:.75em .6em;border-color:#7f868d;transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s;height:auto;resize:none}.rank-math-textarea-control.components-base-control.metabox .components-textarea-control__input:hover:not(:disabled),.rank-math-textarea-control.components-base-control.code-box .components-textarea-control__input:hover:not(:disabled){border-color:#069de3;outline:none}.rank-math-textarea-control.components-base-control.metabox .components-textarea-control__input:focus:not(:disabled),.rank-math-textarea-control.components-base-control.code-box .components-textarea-control__input:focus:not(:disabled){box-shadow:0 0 0 1px #069de3;border-color:#069de3;outline:none;background-color:#fff}.rank-math-textarea-control.components-base-control.code-box .components-textarea-control__input,.rank-math-textarea-control.components-base-control.code-box .components-textarea-control__input:focus{display:block;width:100%;border-color:#32344b;color:#bbbec5;background:#32344b;resize:none}.rank-math-textarea-control.components-base-control.code-box .components-textarea-control__input::placeholder,.rank-math-textarea-control.components-base-control.code-box .components-textarea-control__input:focus::placeholder{color:#63686f}.rank-math-textarea-control.components-base-control.code .components-textarea-control__input{font-family:"Consolas","Monaco",monospace}',""]),e.Z=i},3326:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,":root{--rankmath-wp-adminbar-height: 0}.rank-math-table{width:100%;border:1px solid #c3c4c7;border-collapse:collapse;box-shadow:0 1px 1px rgba(0,0,0,.04);font-size:14px}.rank-math-table *{word-wrap:break-word}.rank-math-table thead{border-bottom:1px solid #c3c4c7;background:#fff}.rank-math-table tbody tr:nth-child(2n+1){background:#f6f7f7}.rank-math-table td,.rank-math-table th{padding:15px 10px;text-align:left;vertical-align:top}.rank-math-table th{color:#2c3338;line-height:1.4em;font-weight:600;vertical-align:top;text-transform:capitalize}.rank-math-table td{color:#50575e;line-height:1.3;vertical-align:middle}.rank-math-table.small td,.rank-math-table.small th{padding:8px 10px;font-size:13px;line-height:1.5em}",""]),e.Z=i},6185:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,':root{--rankmath-wp-adminbar-height: 0}.rank-math-rating{display:flex;flex-direction:row;align-items:center;justify-content:space-between;width:316px;height:60px;border:1px solid #e2e4e7;border-radius:6px;background:#fff}.rank-math-rating__stars{margin:auto}.rank-math-rating__stars .components-button{height:fit-content;box-shadow:none;outline:none;color:#aaa}.rank-math-rating__stars .components-button:hover:not(:disabled),.rank-math-rating__stars .components-button:focus:not(:disabled),.rank-math-rating__stars .components-button:active:not(:disabled){box-shadow:none;outline:none}.rank-math-rating__stars .components-button.has-icon{min-width:fit-content;padding:0}.rank-math-rating__stars .components-button .dashicons{width:40px !important;height:40px !important;margin:0;font-size:40px !important}.rank-math-rating__stars .components-button:hover,.rank-math-rating__stars .components-button.highlighted{color:#f9cb12}.rank-math-rating__face{display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;width:70px;height:100%;border-left:1px solid #e2e4e7;background:#f0f2f4}.rank-math-rating__face .smiley{position:relative;width:40px;height:40px;padding:5px;border-radius:100%;box-sizing:border-box;background:linear-gradient(135deg, #ffe919 0%, #fbc000 100%)}.rank-math-rating__face .smiley .eyes{width:100%;padding:0 5px;margin-top:15%;box-sizing:border-box;transition:all 300ms cubic-bezier(0.645, 0.045, 0.355, 1)}.rank-math-rating__face .smiley .eyes .eye{position:relative;float:left;width:8px;height:8px;border-radius:100%;background:#b57700}.rank-math-rating__face .smiley .eyes .eye:nth-of-type(2){float:right}.rank-math-rating__face .smiley .eyes .eye::after{display:block;content:"";position:absolute;top:-15px;left:5px;transform:rotate(0deg);height:0;width:0;background:#fed800;transition:all 300ms cubic-bezier(0.645, 0.045, 0.355, 1)}.rank-math-rating__face .smiley .eyes .eye:first-of-type::after{right:5px;left:auto;transform:rotate(0deg)}.rank-math-rating__face .smiley .mouth{position:absolute;bottom:18%;left:50%;width:60%;height:30%;margin-left:-30%;border-top-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:40px;border-bottom-left-radius:40px;box-sizing:border-box;background:#b57700;transition:all 300ms cubic-bezier(0.645, 0.045, 0.355, 1)}.rank-math-rating__face .smiley.angry .eyes{margin-top:35%}.rank-math-rating__face .smiley.angry .eye::after{top:-3px;left:-5px;transform:rotate(-35deg);width:120%;height:50%;border-radius:0}.rank-math-rating__face .smiley.angry .eye:first-of-type::after{right:-5px;left:auto;transform:rotate(35deg)}.rank-math-rating__face .smiley.angry .mouth{bottom:18%;left:50%;width:40%;height:20%;margin-left:-20%;border-bottom:0;border-top-left-radius:100%;border-top-right-radius:100%;border-bottom-right-radius:8px;border-bottom-left-radius:8px}.rank-math-rating__face .smiley.angry .eyes,.rank-math-rating__face .smiley.angry .mouth{animation:move-angry-head .6s}.rank-math-rating__face .smiley.normal .eyes{margin-top:30%}.rank-math-rating__face .smiley.normal .eye{height:8px;margin-top:0;animation:blink .6s}.rank-math-rating__face .smiley.normal .mouth{bottom:25%;width:40%;height:10%;margin-left:-20%;border-top-left-radius:40px;border-top-right-radius:40px;border-bottom-right-radius:40px;border-bottom-left-radius:40px}.rank-math-rating__face .smiley.happy .eyes{animation:move-eyes-down .8s}.rank-math-rating__face .smiley.happy .eye:nth-of-type(2){height:8px;margin-top:0;animation:wink .8s}@keyframes wink{0%{height:8px;margin-top:0}30%{height:4px;margin-top:4px}70%{height:4px;margin-top:4px}100%{height:8px;margin-top:0}}@keyframes move-eyes-down{0%{margin-top:15%}35%{margin-top:19%}65%{margin-top:19%}100%{margin-top:15%}}@keyframes move-angry-head{0%{transform:translateX(0%)}20%{transform:translateX(-20%)}40%{transform:translateX(15%)}60%{transform:translateX(-10%)}80%{transform:translateX(5%)}100%{transform:translateX(0%)}}',""]),e.Z=i},5772:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,":root{--rankmath-wp-adminbar-height: 0}.rank-math-tooltip-container{display:inline;vertical-align:middle}.rank-math-tooltip-container.is-block{display:block}.rank-math-tooltip-container .dashicons-editor-help{color:#b5bfc9;height:20px;line-height:18px}.rank-math-tooltip-container:hover .dashicons-editor-help{color:#069de3}.rank-math-tooltip-popover{transition:all .3s ease-in-out;z-index:-1;opacity:0 !important;pointer-events:none}.rank-math-tooltip-popover.is-visible{margin:0 !important;z-index:1000000;opacity:1 !important}.rank-math-tooltip-popover.top,.rank-math-tooltip-popover.top-end,.rank-math-tooltip-popover.top-start,.rank-math-tooltip-popover.bottom,.rank-math-tooltip-popover.bottom-end .rank-math-tooltip-popover.bottom-start{margin-top:10px}.rank-math-tooltip-popover.left,.rank-math-tooltip-popover.left-end,.rank-math-tooltip-popover.left-start,.rank-math-tooltip-popover.right,.rank-math-tooltip-popover.right-end,.rank-math-tooltip-popover.right-start{margin-left:10px}.rank-math-tooltip-popover .components-popover__content{width:200px;padding:8px 10px;border-radius:3px;box-shadow:none;outline:none;color:#fff;background:#555d66;font-size:11px;font-weight:400;line-height:1.5;text-align:center;white-space:normal}.rank-math-tooltip-popover .components-popover__arrow .components-popover__triangle-bg{fill:#555d66}.rank-math-tooltip-popover .components-popover__arrow .components-popover__triangle-border{stroke:rgba(0,0,0,0)}.rank-math-tooltip-popover .components-popover__arrow::before{background:#555d66}",""]),e.Z=i},388:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,':root{--rankmath-wp-adminbar-height: 0}.media-status .img-status{display:inline-block;position:relative;width:auto;margin-right:10px;vertical-align:middle;clear:none}.media-status .img-status:hover img{border-color:#fc948b;box-shadow:0 0 0 1px #fc948b}.media-status .img-status:hover .remove-file-button{opacity:1}.media-status .img-status img{max-width:350px;height:auto;margin:15px 0 0 0;border:5px solid #fff;border-radius:2px;box-shadow:inset 0 0 15px rgba(0,0,0,.3),inset 0 0 0 1px rgba(0,0,0,.05);outline:1px solid #e9e9e9;background:#eee;background-image:linear-gradient(45deg, #d0d0d0 25%, transparent 25%, transparent 75%, #d0d0d0 75%, #d0d0d0),linear-gradient(45deg, #d0d0d0 25%, transparent 25%, transparent 75%, #d0d0d0 75%, #d0d0d0);background-position:0 0,10px 10px;background-size:20px 20px;cursor:pointer}.media-status .img-status .remove-file-button,.media-status .img-status .remove-file-button:hover:not(:disabled){background-color:#fc948b}.media-status .img-status .remove-file-button{display:flex;align-items:center;justify-content:center;position:absolute;top:16px;right:1px;left:auto;width:38px;height:38px;border:0;border-radius:0;opacity:0;line-height:38px}.media-status .img-status .remove-file-button:hover:not(:disabled){border:0}.media-status .img-status .remove-file-button:focus{box-shadow:none;outline:none}.media-status .img-status .remove-file-button::before{display:inline-block;content:"";color:#fff;font-family:"dashicons";font-size:20px;font-weight:400;font-style:normal;text-align:center;vertical-align:top;text-decoration:none}',""]),e.Z=i},5399:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a),l=n(1667),c=n.n(l),s=new URL(n(9394),n.b),u=new URL(n(866),n.b),m=i()(r()),p=c()(s),b=c()(u);m.push([t.id,":root{--rankmath-wp-adminbar-height: 0}.components-base-control__field.css-1t5ousf{margin-bottom:0}.rank-math-select-control.rank-math-select-control.rank-math-select-control{width:fit-content;max-width:25rem;border-radius:3px;gap:0}.rank-math-select-control.rank-math-select-control.rank-math-select-control .components-input-control-suffix-wrapper{display:none}.rank-math-select-control.rank-math-select-control.rank-math-select-control .components-input-control__label{color:#242628;font-size:14px;line-height:16px;vertical-align:middle;cursor:pointer;font-weight:400;line-height:1.4em;text-transform:inherit}.rank-math-select-control.rank-math-select-control.rank-math-select-control .components-select-control__input{min-height:30px;padding:0 24px 0 8px;border-color:#8c8f94;border-radius:3px;color:#2c3338;background:#fff url("+p+") no-repeat right 5px top 55%;background-size:16px 16px;font-size:14px;line-height:2;vertical-align:middle}.rank-math-select-control.rank-math-select-control.rank-math-select-control .components-select-control__input:hover{color:#2271b1}.rank-math-select-control.rank-math-select-control.rank-math-select-control .components-select-control__input:focus{border-color:#2271b1;color:#0a4b78}.rank-math-select-control.rank-math-select-control.rank-math-select-control:focus-within .components-select-control__input,.rank-math-select-control.rank-math-select-control.rank-math-select-control:focus-within .components-input-control__backdrop{border-color:#2271b1 !important}.rank-math-select-control.rank-math-select-control.rank-math-select-control:focus-within .components-input-control__backdrop{box-shadow:0 0 0 1px #2271b1 !important}.rank-math-select-control.rank-math-select-control.rank-math-select-control.is-disabled .components-input-control__container{opacity:.7}.rank-math-select-control.rank-math-select-control.rank-math-select-control.is-disabled .components-select-control__input{color:#a7aaad;border-color:#dcdcde;background-color:#f6f7f7;background-image:url("+b+");text-shadow:0 1px 0 #fff;cursor:default}.rank-math-select-control.rank-math-select-control.rank-math-select-control.is-metabox{width:100%}.rank-math-select-control.rank-math-select-control.rank-math-select-control.is-metabox .components-select-control__input{min-height:40px;padding:0px 22px 0px .6em}.rank-math-select-control.rank-math-select-control.rank-math-select-control.is-metabox:hover .components-select-control__input,.rank-math-select-control.rank-math-select-control.rank-math-select-control.is-metabox:focus-within .components-select-control__input{color:#2271b1}.rank-math-select-control.rank-math-select-control.rank-math-select-control.is-metabox:hover .components-input-control__backdrop,.rank-math-select-control.rank-math-select-control.rank-math-select-control.is-metabox:focus-within .components-input-control__backdrop{border-color:#069de3 !important}.rank-math-select-control.rank-math-select-control.rank-math-select-control.is-metabox:focus-within .components-input-control__backdrop{box-shadow:0 0 0 1px #069de3 !important}@media screen and (max-width: 782px){.rank-math-select-control.rank-math-select-control.rank-math-select-control{width:100%;max-width:100%}.rank-math-select-control.rank-math-select-control.rank-math-select-control .components-select-control__input{min-height:40px;padding:5px 24px 5px 8px;font-size:16px;line-height:1.625}}",""]),e.Z=m},719:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,":root{--rankmath-wp-adminbar-height: 0}.rank-math-select-variable{position:relative;max-width:25rem;background-color:#fff}.rank-math-select-variable[aria-disabled=true]{pointer-events:none}.rank-math-select-variable .select-input{position:relative}.rank-math-select-variable .select-input .components-text-control__input{padding-right:50px}.rank-math-select-variable .select-input .rank-math-text-control+.rank-math-button{top:1px;right:0;bottom:1px;border-width:0 0 0 1px;border-radius:0 3px 3px 0}.rank-math-select-variable .select-input .rank-math-textarea-control+.rank-math-button{top:auto;right:1px;bottom:1px;min-height:40px;border-width:1px 0 0 1px;border-radius:3px 0 3px 0}.rank-math-select-variable .select-input .rank-math-button{position:absolute;height:auto;padding:0 10px;box-shadow:none;color:#858b90;line-height:40px}.rank-math-select-variable .select-menu{position:absolute;top:48px;right:0;z-index:999;overflow:auto;width:80%;max-width:450px;height:220px;border:1px solid #b5bfc9;border-radius:6px;background:#fff}.rank-math-select-variable .select-menu.is-textarea-menu{top:70px}.rank-math-select-variable .select-menu .components-search-control .components-base-control__field{margin-bottom:0}.rank-math-select-variable .select-menu .components-search-control .components-base-control__field .components-input-control__container{background-color:rgba(0,0,0,0)}.rank-math-select-variable .select-menu .components-search-control .components-base-control__field .components-input-control__container .components-input-control__input{border-radius:4px;border:1px solid #8c8f94;box-shadow:0 0 0 rgba(0,0,0,0);color:#2c3338;background-color:#fff;width:calc(100% - 20px);max-width:100%;height:34px;min-height:30px;padding:0 8px;margin:10px;border-color:#b5bfc9;background:#fff;font-size:14px;line-height:2}.rank-math-select-variable .select-menu .components-search-control .components-base-control__field .components-input-control__container .components-input-control__input:focus{border-color:#2271b1;box-shadow:0 0 0 1px #2271b1;outline:2px solid rgba(0,0,0,0)}.rank-math-select-variable .select-menu .components-search-control .components-base-control__field .components-input-control__container .components-input-control__input:disabled{border-color:rgba(255,255,255,.75);box-shadow:inset 0 1px 2px rgba(0,0,0,.04);color:rgba(44,51,56,.5);background:rgba(255,255,255,.5)}.rank-math-select-variable .select-menu .components-search-control .components-base-control__field .components-input-control__container .components-input-control__input:focus{border-color:#069de3;box-shadow:0 0 0 1px #069de3 !important;outline:2px solid rgba(0,0,0,0);background-color:#fff}.rank-math-select-variable .select-menu .components-search-control .components-base-control__field .components-input-control__container .components-input-control__suffix{display:none}.rank-math-select-variable .select-menu .components-search-control .components-base-control__field .components-input-control__container .components-input-control__backdrop{border:0;box-shadow:none;outline:none}.rank-math-select-variable .select-menu ul{margin:0}.rank-math-select-variable .select-menu ul li{position:relative;margin:0;padding:10px;cursor:pointer}.rank-math-select-variable .select-menu ul li:not(:last-child){border-bottom:1px solid #b5bfc9}.rank-math-select-variable .select-menu ul li div:first-of-type{display:flex;flex-direction:row;align-items:center;justify-content:space-between}.rank-math-select-variable .select-menu ul li div:first-of-type p,.rank-math-select-variable .select-menu ul li div:first-of-type h1{margin:0}.rank-math-select-variable .select-menu ul li div:first-of-type p,.rank-math-select-variable .select-menu ul li div:first-of-type h1{font-size:12px;line-height:16px}.rank-math-select-variable .select-menu ul li div:first-of-type h1{font-weight:600}.rank-math-select-variable .select-menu ul li div:first-of-type p{display:inline-block;padding:.25em .4em;margin-top:3px;border-radius:.25rem;background-color:#f0f2f4;font-size:12px;line-height:16px}.rank-math-select-variable .select-menu ul li .description{display:block;color:#7f868d;font-style:italic}.rank-math-select-variable .select-menu ul li:hover{background:#f8f9fa}",""]),e.Z=i},3539:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,':root{--rankmath-wp-adminbar-height: 0}.rank-math-select-with-searchbox{position:relative;max-width:25rem;background-color:#fff}.rank-math-select-with-searchbox label{color:#242628;font-size:14px;line-height:16px;vertical-align:middle;cursor:pointer;line-height:1.4em}.rank-math-select-with-searchbox .components-button.is-secondary{display:flex;justify-content:space-between;width:100%;height:40px;padding:.75em .6em;border-color:#7f868d;transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s;border-width:1px;border-style:solid;border-radius:4px;box-sizing:border-box;box-shadow:none;color:#444;background-color:#fff;line-height:18px}.rank-math-select-with-searchbox .components-button.is-secondary:hover:not(:disabled){border-color:#069de3;outline:none}.rank-math-select-with-searchbox .components-button.is-secondary:focus:not(:disabled){box-shadow:0 0 0 1px #069de3;border-color:#069de3;outline:none;background-color:#fff}.rank-math-select-with-searchbox .components-button.is-secondary::after{content:"";margin-left:auto;color:#888;font-size:18px;font-family:"dashicons"}.rank-math-select-with-searchbox .components-button.is-secondary:hover:not(:focus):not(:disabled),.rank-math-select-with-searchbox .components-button.is-secondary:active:not(:focus):not(:disabled){box-shadow:none;color:currentColor;background-color:#fff}.rank-math-select-with-searchbox .components-button.is-secondary:focus:not(:disabled){color:#444;background-color:#fff}.rank-math-select-with-searchbox .components-button.is-secondary:disabled{color:rgba(44,51,56,.5);pointer-events:none}.rank-math-select-with-searchbox .components-button.is-secondary:disabled::after{color:currentColor}.rank-math-select-with-searchbox .components-button.is-secondary[aria-expanded=true]{border-bottom-right-radius:0;border-bottom-left-radius:0}.rank-math-select-with-searchbox .components-button.is-secondary[aria-expanded=true]::after{transform:rotate(180deg)}.rank-math-select-with-searchbox .select-menu{display:block;position:absolute;left:0;z-index:1051;width:100%;border:1px solid #7f868d;border-top:none;border-radius:4px;border-top-left-radius:0;border-top-right-radius:0;box-sizing:border-box;background-color:#fff}.rank-math-select-with-searchbox .select-menu .components-search-control .components-base-control__field{margin-bottom:0}.rank-math-select-with-searchbox .select-menu .components-search-control .components-base-control__field .components-input-control__container{background-color:rgba(0,0,0,0)}.rank-math-select-with-searchbox .select-menu .components-search-control .components-base-control__field .components-input-control__container .components-input-control__input{border-radius:4px;border:1px solid #8c8f94;box-shadow:0 0 0 rgba(0,0,0,0);color:#2c3338;background-color:#fff;width:calc(100% - 8px);max-width:100%;height:34px;min-height:30px;padding:0 8px;margin:4px;border-color:#b5bfc9;background:#fff;font-size:14px;line-height:2}.rank-math-select-with-searchbox .select-menu .components-search-control .components-base-control__field .components-input-control__container .components-input-control__input:focus{border-color:#2271b1;box-shadow:0 0 0 1px #2271b1;outline:2px solid rgba(0,0,0,0)}.rank-math-select-with-searchbox .select-menu .components-search-control .components-base-control__field .components-input-control__container .components-input-control__input:disabled{border-color:rgba(255,255,255,.75);box-shadow:inset 0 1px 2px rgba(0,0,0,.04);color:rgba(44,51,56,.5);background:rgba(255,255,255,.5)}.rank-math-select-with-searchbox .select-menu .components-search-control .components-base-control__field .components-input-control__container .components-input-control__input:focus{border-color:#069de3;box-shadow:0 0 0 1px #069de3 !important;outline:2px solid rgba(0,0,0,0);background-color:#fff}.rank-math-select-with-searchbox .select-menu .components-search-control .components-base-control__field .components-input-control__container .components-input-control__suffix{display:none}.rank-math-select-with-searchbox .select-menu .components-search-control .components-base-control__field .components-input-control__container .components-input-control__backdrop{border:0;box-shadow:none;outline:none}.rank-math-select-with-searchbox .select-menu ul{margin:0;max-height:200px;overflow-y:auto}.rank-math-select-with-searchbox .select-menu ul li{padding:6px;margin-bottom:6px;font-size:13px;cursor:pointer}.rank-math-select-with-searchbox .select-menu ul li:hover{color:#fff;background-color:#5897fb}.rank-math-select-with-searchbox .select-menu .no-results{display:block;margin:6px;font-size:13px}',""]),e.Z=i},4465:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,":root{--rankmath-wp-adminbar-height: 0}.components-base-control .components-base-control__field .rank-math-toggle-group-control.components-toggle-group-control{display:inline-block;width:fit-content;padding:5px 0 0 5px;margin:0;border:1px solid #b5bfc9;border-radius:4px;background:#fff}.components-base-control .components-base-control__field .rank-math-toggle-group-control.components-toggle-group-control:before{display:none}.components-base-control .components-base-control__field .rank-math-toggle-group-control.components-toggle-group-control:focus-within{box-shadow:none;outline:none}.components-base-control .components-base-control__field .rank-math-toggle-group-control.components-toggle-group-control .components-toggle-group-control-option-base{min-height:36px;margin-right:5px;margin-bottom:5px;padding:0 15px;border:1px solid #dadfe4;border-radius:4px;box-sizing:border-box;color:#666d73;white-space:wrap;transition:none}.components-base-control .components-base-control__field .rank-math-toggle-group-control.components-toggle-group-control .components-toggle-group-control-option-base:hover{border-color:#069de3;color:#069de3}.components-base-control .components-base-control__field .rank-math-toggle-group-control.components-toggle-group-control .components-toggle-group-control-option-base[aria-checked=true],.components-base-control .components-base-control__field .rank-math-toggle-group-control.components-toggle-group-control .components-toggle-group-control-option-base[aria-checked=true]:hover{border-color:#069de3;color:#fff;background-color:#069de3}.components-base-control .components-base-control__field .rank-math-toggle-group-control.components-toggle-group-control .components-toggle-group-control-option-base>div{font-size:14px;font-weight:500}.components-base-control .components-base-control__field .rank-math-toggle-group-control.components-toggle-group-control .components-toggle-group-control-option-base+div{display:none}.components-base-control .components-base-control__field .rank-math-toggle-group-control.components-toggle-group-control[aria-disabled=true]{opacity:.4}.components-base-control .components-base-control__field .css-zjik7{display:none}",""]),e.Z=i},7269:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,":root{--rankmath-wp-adminbar-height: 0}.rank-math-status-anchor-tag.components-button.is-secondary{height:22px;padding:4px 6px;border-radius:2px;box-shadow:none;outline:none;font-size:11px;line-height:13px}.rank-math-status-anchor-tag.components-button.is-secondary:hover:not(:disabled),.rank-math-status-anchor-tag.components-button.is-secondary:focus:not(:disabled),.rank-math-status-anchor-tag.components-button.is-secondary:active:not(:disabled){box-shadow:none;outline:none}.rank-math-status-anchor-tag.components-button.is-secondary.is-good{color:#55a853;background-color:rgba(85,168,83,.1)}.rank-math-status-anchor-tag.components-button.is-secondary.is-good:active{color:#fff;background-color:#7ac979}.rank-math-status-anchor-tag.components-button.is-secondary.is-good:hover,.rank-math-status-anchor-tag.components-button.is-secondary.is-good:focus{color:#3d993a;background-color:rgba(85,168,83,.15)}.rank-math-status-anchor-tag.components-button.is-secondary.is-neutral{color:#df962c;background-color:rgba(223,150,44,.1)}.rank-math-status-anchor-tag.components-button.is-secondary.is-neutral:active{color:#fff;background-color:#ffbe5f}.rank-math-status-anchor-tag.components-button.is-secondary.is-neutral:hover,.rank-math-status-anchor-tag.components-button.is-secondary.is-neutral:focus{color:#d68915;background-color:rgba(223,150,44,.15)}.rank-math-status-anchor-tag.components-button.is-secondary.is-bad{color:#e37a73;background-color:rgba(227,122,115,.1)}.rank-math-status-anchor-tag.components-button.is-secondary.is-bad:active{color:#fff;background-color:#f29c96}.rank-math-status-anchor-tag.components-button.is-secondary.is-bad:hover,.rank-math-status-anchor-tag.components-button.is-secondary.is-bad:focus{color:#d9655d;background-color:rgba(227,122,115,.15)}.rank-math-status-anchor-tag.components-button.is-secondary.is-default{color:#757575;background-color:rgba(0,0,0,.05)}.rank-math-status-anchor-tag.components-button.is-secondary.is-default:active{color:#fff;background-color:#383838}.rank-math-status-anchor-tag.components-button.is-secondary.is-default:hover,.rank-math-status-anchor-tag.components-button.is-secondary.is-default:focus{color:#383838;background-color:rgba(0,0,0,.1)}",""]),e.Z=i},3328:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,":root{--rankmath-wp-adminbar-height: 0}.rank-math-status-button.components-button{min-height:32px;padding:0 12px;border-radius:4px;font-size:14px;font-weight:500;line-height:2.30769231}.rank-math-status-button.components-button.has-icon.has-text svg{width:14px;height:14px;margin-right:5px}.rank-math-status-button.components-button.is-connected{border:1px solid #58bb58;color:#fff;background-color:#58bb58}.rank-math-status-button.components-button.is-connected svg path{fill:#fff}.rank-math-status-button.components-button.is-disconnected{border:1px solid #ee6a5e;color:#fff;background-color:#ee6a5e}.rank-math-status-button.components-button.is-disconnected svg path{fill:#fff}.rank-math-status-button.components-button.is-disconnect:hover{border-color:#ee6a5e;box-shadow:none;color:#fff;background:#ee6a5e}.rank-math-status-button.components-button.is-connected,.rank-math-status-button.components-button.is-disconnected{pointer-events:none}",""]),e.Z=i},185:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,":root{--rankmath-wp-adminbar-height: 0}.rank-math-status-list{display:flex;gap:8px;width:16.5rem}.rank-math-status-list:active .rank-math-status-list__description-icon{color:#fff;background-color:#383838}.rank-math-status-list__icon{display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;width:14px;height:14px;border-radius:8px;transform:translateY(5px)}.rank-math-status-list__description{flex:1 1 0;color:#2f2f2f;font-size:13px;font-weight:400;line-height:20px}.rank-math-status-list__description a{display:inline-block;visibility:hidden;transform:translateY(1px)}.rank-math-status-list__description a:hover svg{border-radius:6px;background-color:#383838}.rank-math-status-list__description a:hover svg path:not(:last-of-type){fill:#fff}.rank-math-status-list:hover .rank-math-status-list__description a{visibility:visible}.rank-math-status-list.is-good .rank-math-status-list__icon{color:#3d993a;background-color:rgba(85,168,83,.15)}.rank-math-status-list.is-good .rank-math-status-list__icon svg path{fill:#3d993a}.rank-math-status-list.is-good:hover .rank-math-status-list__icon{color:#fff;background-color:#7ac979}.rank-math-status-list.is-good:hover .rank-math-status-list__icon svg path{fill:#fff}.rank-math-status-list.is-neutral .rank-math-status-list__icon{color:#d18e08;background-color:rgba(223,150,44,.15)}.rank-math-status-list.is-neutral .rank-math-status-list__icon svg path{fill:#d18e08}.rank-math-status-list.is-neutral:hover .rank-math-status-list__icon{color:#fff;background-color:#ffbe5f}.rank-math-status-list.is-neutral:hover .rank-math-status-list__icon svg path{fill:#fff}.rank-math-status-list.is-bad .rank-math-status-list__icon{color:#d9655d;background-color:rgba(227,122,115,.15)}.rank-math-status-list.is-bad .rank-math-status-list__icon svg path{fill:#d9655d}.rank-math-status-list.is-bad:hover .rank-math-status-list__icon{color:#fff;background-color:#f29c96}.rank-math-status-list.is-bad:hover .rank-math-status-list__icon svg path{fill:#fff}",""]),e.Z=i},5073:function(t,e,n){"use strict";var o=n(8081),r=n.n(o),a=n(3645),i=n.n(a)()(r());i.push([t.id,":root{--rankmath-wp-adminbar-height: 0}.rank-math-tab-panel{margin-top:20px}.rank-math-tab-panel>.components-tab-panel__tabs,.rank-math-tab-panel>.components-tab-panel__tab-content{position:relative}.rank-math-tab-panel>.components-tab-panel__tabs{z-index:10;flex-wrap:wrap}.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item{height:auto;padding:0 15px;border:1px solid #b5bfc9;border-right:0;color:#5b646c;background:#f0f2f4;font-size:15px;font-weight:500;line-height:39px}.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item>a{color:inherit;text-decoration:none}.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item::before,.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item::after{display:none}.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item:first-child{border-radius:4px 0 0 0}.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item:last-child{border-right:1px solid #b5bfc9;border-radius:0 4px 0 0}.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item i{margin-right:9px}.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item:hover,.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item.is-active{color:#1a1e22;background:#f8f9fa}.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item.is-active{border-bottom-color:rgba(0,0,0,0)}.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item.is-external a{margin:0 -15px;padding:0 15px;box-shadow:none}.rank-math-tab-panel>.components-tab-panel__tabs[aria-orientation=horizontal]+.components-tab-panel__tab-content{padding:30px 20px 5.5rem 20px;margin-left:-20px;margin-top:-1px;border-top:1px solid #b5bfc9;border-bottom:1px solid #b5bfc9;background:#f8f9fa}@media screen and (max-width: 768px){.rank-math-tab-panel>.components-tab-panel__tabs{flex-direction:column;border:1px solid #b5bfc9;border-bottom:0;border-radius:4px 4px 0 0}.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item{border:0;border-bottom:1px solid #b5bfc9}.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item:last-child{border-right:0}.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item.is-active{border-bottom-color:#b5bfc9}}@media screen and (max-width: 368px){.rank-math-tab-panel>.components-tab-panel__tabs{margin-left:-20px;border-radius:0}.rank-math-tab-panel>.components-tab-panel__tabs .components-tab-panel__tabs-item{flex:auto;border-radius:0;border-right:0}}",""]),e.Z=i},3645:function(t){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n="",o=void 0!==e[5];return e[4]&&(n+="@supports (".concat(e[4],") {")),e[2]&&(n+="@media ".concat(e[2]," {")),o&&(n+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),n+=t(e),o&&(n+="}"),e[2]&&(n+="}"),e[4]&&(n+="}"),n})).join("")},e.i=function(t,n,o,r,a){"string"==typeof t&&(t=[[null,t,void 0]]);var i={};if(o)for(var l=0;l<this.length;l++){var c=this[l][0];null!=c&&(i[c]=!0)}for(var s=0;s<t.length;s++){var u=[].concat(t[s]);o&&i[u[0]]||(void 0!==a&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=a),n&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=n):u[2]=n),r&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=r):u[4]="".concat(r)),e.push(u))}},e}},1667:function(t){"use strict";t.exports=function(t,e){return e||(e={}),t?(t=String(t.__esModule?t.default:t),/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),e.hash&&(t+=e.hash),/["'() \t\n]|(%20)/.test(t)||e.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t):t}},8081:function(t){"use strict";t.exports=function(t){return t[1]}},3379:function(t){"use strict";var e=[];function n(t){for(var n=-1,o=0;o<e.length;o++)if(e[o].identifier===t){n=o;break}return n}function o(t,o){for(var a={},i=[],l=0;l<t.length;l++){var c=t[l],s=o.base?c[0]+o.base:c[0],u=a[s]||0,m="".concat(s," ").concat(u);a[s]=u+1;var p=n(m),b={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==p)e[p].references++,e[p].updater(b);else{var d=r(b,o);o.byIndex=l,e.splice(l,0,{identifier:m,updater:d,references:1})}i.push(m)}return i}function r(t,e){var n=e.domAPI(e);n.update(t);return function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;n.update(t=e)}else n.remove()}}t.exports=function(t,r){var a=o(t=t||[],r=r||{});return function(t){t=t||[];for(var i=0;i<a.length;i++){var l=n(a[i]);e[l].references--}for(var c=o(t,r),s=0;s<a.length;s++){var u=n(a[s]);0===e[u].references&&(e[u].updater(),e.splice(u,1))}a=c}}},569:function(t){"use strict";var e={};t.exports=function(t,n){var o=function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(t){n=null}e[t]=n}return e[t]}(t);if(!o)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");o.appendChild(n)}},9216:function(t){"use strict";t.exports=function(t){var e=document.createElement("style");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},3565:function(t,e,n){"use strict";t.exports=function(t){var e=n.nc;e&&t.setAttribute("nonce",e)}},7795:function(t){"use strict";t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var e=t.insertStyleElement(t);return{update:function(n){!function(t,e,n){var o="";n.supports&&(o+="@supports (".concat(n.supports,") {")),n.media&&(o+="@media ".concat(n.media," {"));var r=void 0!==n.layer;r&&(o+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),o+=n.css,r&&(o+="}"),n.media&&(o+="}"),n.supports&&(o+="}");var a=n.sourceMap;a&&"undefined"!=typeof btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),e.styleTagTransform(o,t,e.options)}(e,t,n)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(e)}}}},4589:function(t){"use strict";t.exports=function(t,e){if(e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},9394:function(t){"use strict";t.exports="data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23555%22%2F%3E%3C%2Fsvg%3E"},866:function(t){"use strict";t.exports="data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23a0a5aa%22%2F%3E%3C%2Fsvg%3E"},5132:function(t){"use strict";t.exports="data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20viewBox%3D%270%200%2020%2020%27%3E%3Cpath%20d%3D%27M14.83%204.89l1.34.94-5.81%208.38H9.02L5.78%209.67l1.34-1.25%202.57%202.4z%27%20fill%3D%27%233582c4%27%2F%3E%3C%2Fsvg%3E"}},e={};function n(o){var r=e[o];if(void 0!==r)return r.exports;var a=e[o]={id:o,exports:{}};return t[o](a,a.exports,n),a.exports}n.m=t,n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var o in e)n.o(e,o)&&!n.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.b=document.baseURI||self.location.href,n.nc=void 0,function(){"use strict";var t=jQuery,e=n.n(t),o=n(4184),r=n.n(o),a=wp.components,i=wp.element,l=n(3379),c=n.n(l),s=n(7795),u=n.n(s),m=n(569),p=n.n(m),b=n(3565),d=n.n(b),f=n(9216),h=n.n(f),g=n(4589),y=n.n(g),v=n(2853),k={};k.styleTagTransform=y(),k.setAttributes=d(),k.insert=p().bind(null,"head"),k.domAPI=u(),k.insertStyleElement=h();c()(v.Z,k),v.Z&&v.Z.locals&&v.Z.locals;function x(t){return x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},x(t)}var w=["icon","variant","onClick","children","disabled","className","size"];function _(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function O(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?_(Object(n),!0).forEach((function(e){j(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function j(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==x(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==x(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===x(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function S(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var E=(0,i.forwardRef)((function(t,e){var n=t.icon,o=t.variant,i=t.onClick,l=t.children,c=t.disabled,s=t.className,u=t.size,m=void 0===u?"default":u,p=S(t,w);s=r()("button",s,"rank-math-button",o?"button-".concat(o):"",{"is-xlarge":"xlarge"===m,"is-large":"large"===m,"button-secondary":"remove-group"===o,"button-primary":"start-new-chat"===o});var b=O(O({},p),{},{ref:e,size:m,icon:n,variant:o,onClick:i,children:l,disabled:c,className:s,"aria-disabled":c});return wp.element.createElement(a.Button,b)})),P=n(4679),N={};N.styleTagTransform=y(),N.setAttributes=d(),N.insert=p().bind(null,"head"),N.domAPI=u(),N.insertStyleElement=h();c()(P.Z,N),P.Z&&P.Z.locals&&P.Z.locals;function C(t){return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},C(t)}var A=["label","checked","variant","disabled","onChange","className","indeterminate"];function I(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function T(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?I(Object(n),!0).forEach((function(e){D(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):I(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function D(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==C(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==C(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===C(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Z(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var z=function(t){var e=t.label,n=t.checked,o=t.variant,i=t.disabled,l=t.onChange,c=t.className,s=t.indeterminate,u=Z(t,A);c=r()(o,c,"rank-math-checkbox-control",{"is-indeterminate":s,"is-disabled":i});var m=T(T({},u),{},{label:e,checked:n,disabled:i,onChange:l,className:c,indeterminate:s,__nextHasNoMarginBottom:!0});return wp.element.createElement(a.CheckboxControl,m)},M=lodash,L=wp.i18n,R=n(1403),F={};F.styleTagTransform=y(),F.setAttributes=d(),F.insert=p().bind(null,"head"),F.domAPI=u(),F.insertStyleElement=h();c()(R.Z,F),R.Z&&R.Z.locals&&R.Z.locals;var U=["id","label"];function B(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function H(){return H=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},H.apply(this,arguments)}function $(t){return function(t){if(Array.isArray(t))return W(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return W(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return W(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function W(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var G=n(2548),X={};X.styleTagTransform=y(),X.setAttributes=d(),X.insert=p().bind(null,"head"),X.domAPI=u(),X.insertStyleElement=h();c()(G.Z,X),G.Z&&G.Z.locals&&G.Z.locals;function Y(t){return Y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Y(t)}var q=["label","variant","options","selected","disabled","onChange","className"];function Q(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function V(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Q(Object(n),!0).forEach((function(e){J(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Q(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function J(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==Y(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==Y(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Y(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function K(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var tt=n(6236),et={};et.styleTagTransform=y(),et.setAttributes=d(),et.insert=p().bind(null,"head"),et.domAPI=u(),et.insertStyleElement=h();c()(tt.Z,et),tt.Z&&tt.Z.locals&&tt.Z.locals;function nt(t){return nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},nt(t)}var ot=["label","onChange","disabled","checked","className"];function rt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function at(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?rt(Object(n),!0).forEach((function(e){it(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):rt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function it(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==nt(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==nt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===nt(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function lt(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var ct=wp.compose,st=wp.data;function ut(t){return ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ut(t)}var mt=["id","type","content","Component","isDisabled"];function pt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function bt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?pt(Object(n),!0).forEach((function(e){dt(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):pt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function dt(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==ut(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==ut(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===ut(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function ft(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var ht=(0,ct.compose)((0,st.withSelect)((function(t,e){var n=t("rank-math-settings").getAppData();return{field:e.field,settingType:e.settingType,settings:n}})),(0,st.withDispatch)((function(t,e){var n=e.settings,o=e.settingType;return{updateSetting:function(e,r){n[o][e]=r,t("rank-math-settings").updateSettings(n)}}})))((function(t){var e,n=t.field,o=t.settingType,r=t.settings,a=n.id,l=n.type,c=n.content,s=n.Component,u=n.isDisabled,m=ft(n,mt),p=(null===(e=r[o])||void 0===e?void 0:e[a])||"",b=function(e){return t.updateSetting(a,e)},d=function(){var t={toggle:"checked",checkbox:"checked"}[l]||"value",e=(0,M.includes)(["component","group"],l);return bt(bt({},m),{},dt(dt({id:a},t,m.value||p),"onChange",m.onChange||!u&&b),e&&{settingType:o})},f={file:window.rankMathComponents.UploadFile,text:window.rankMathComponents.TextControl,select:window.rankMathComponents.SelectControl,toggle:window.rankMathComponents.ToggleControl,select_search:window.rankMathComponents.SelectWithSearch,multicheck:window.rankMathComponents.CheckboxList,multicheck_inline:window.rankMathComponents.CheckboxList,radio_inline:window.rankMathComponents.ToggleGroupControl,repeatable_group:window.rankMathComponents.RepeatableGroup,group:window.rankMathComponents.Group,checkbox:window.rankMathComponents.CheckboxControl}[l];return f?wp.element.createElement(f,d()):"component"===l?wp.element.createElement(s,d()):"raw"===l?(0,i.createElement)(c):null}));function gt(t){return gt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gt(t)}function yt(){return yt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},yt.apply(this,arguments)}function vt(t){return function(t){if(Array.isArray(t))return kt(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return kt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return kt(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function kt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function xt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function wt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?xt(Object(n),!0).forEach((function(e){_t(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):xt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function _t(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==gt(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==gt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===gt(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Ot=n(4258),jt={};jt.styleTagTransform=y(),jt.setAttributes=d(),jt.insert=p().bind(null,"head"),jt.domAPI=u(),jt.insertStyleElement=h();c()(Ot.Z,jt),Ot.Z&&Ot.Z.locals&&Ot.Z.locals;function St(t){return St="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},St(t)}var Et=["value","onChange","className","type","variant"];function Pt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function Nt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Pt(Object(n),!0).forEach((function(e){Ct(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Pt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Ct(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==St(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==St(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===St(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function At(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var It=function(t){var e=t.value,n=t.onChange,o=t.className,i=t.type,l=void 0===i?"text":i,c=t.variant,s=void 0===c?"regular-text":c,u=At(t,Et);o=r()(s,o,"rank-math-text-control",{"no-value":!(null!=e&&e.trim())});var m=Nt(Nt({},u),{},{type:l,value:e,onChange:n,className:o,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0});return wp.element.createElement(a.TextControl,m)};function Tt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Dt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Dt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Dt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var Zt=function(){var t=Tt((0,i.useState)(!1),2),e=t[0],n=t[1],o=(0,i.useRef)(null);return(0,i.useEffect)((function(){var t=function(t){var e=t.target;e&&o.current&&!o.current.contains(e)&&n(!1)};return document.addEventListener("mousedown",t),function(){document.removeEventListener("mousedown",t)}}),[]),[e,n,o]},zt=n(2408),Mt={};Mt.styleTagTransform=y(),Mt.setAttributes=d(),Mt.insert=p().bind(null,"head"),Mt.domAPI=u(),Mt.insertStyleElement=h();c()(zt.Z,Mt),zt.Z&&zt.Z.locals&&zt.Z.locals;var Lt=["value","onChange","inputProps"];function Rt(){return Rt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},Rt.apply(this,arguments)}function Ft(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Ut(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ut(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ut(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function Bt(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var Ht=n(5912),$t={};$t.styleTagTransform=y(),$t.setAttributes=d(),$t.insert=p().bind(null,"head"),$t.domAPI=u(),$t.insertStyleElement=h();c()(Ht.Z,$t),Ht.Z&&Ht.Z.locals&&Ht.Z.locals;function Wt(t){return Wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wt(t)}var Gt=["help","value","label","disabled","placeholder","className","onChange","variant","rows","cols"];function Xt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function Yt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Xt(Object(n),!0).forEach((function(e){qt(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Xt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function qt(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==Wt(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==Wt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Wt(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Qt(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var Vt=function(t){var e=t.help,n=t.value,o=t.label,i=t.disabled,l=t.placeholder,c=t.className,s=t.onChange,u=t.variant,m=t.rows,p=void 0===m?4:m,b=t.cols,d=void 0===b?30:b,f=Qt(t,Gt);c=r()(u,c,"rank-math-textarea-control");var h=Yt(Yt({},f),{},{rows:p,cols:d,help:e,value:n,label:o,onChange:s,disabled:i,placeholder:l,className:c});return wp.element.createElement(a.TextareaControl,h)},Jt=n(3326),Kt={};Kt.styleTagTransform=y(),Kt.setAttributes=d(),Kt.insert=p().bind(null,"head"),Kt.domAPI=u(),Kt.insertStyleElement=h();c()(Jt.Z,Kt),Jt.Z&&Jt.Z.locals&&Jt.Z.locals;var te=["fields","size","className","th"];function ee(){return ee=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},ee.apply(this,arguments)}function ne(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function oe(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return re(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return re(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function re(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function ae(t){return ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ae(t)}var ie=function(t){return(0,i.isValidElement)(t)?t:"object"===ae(t)?(0,M.map)((0,M.entries)(t),(function(t,e){var n=oe(t,2),o=n[0],r=n[1];return wp.element.createElement(i.Fragment,{key:e},o,": ",r)})):wp.element.createElement(i.RawHTML,null,t)};function le(t){return le="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},le(t)}var ce=["icon","children","onRemove","className","actions","status","variant","politeness","isDismissible"];function se(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function ue(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?se(Object(n),!0).forEach((function(e){me(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):se(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function me(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==le(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==le(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===le(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function pe(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var be=function(t){var e=t.icon,n=t.children,o=t.onRemove,i=t.className,l=t.actions,c=void 0===l?[]:l,s=t.status,u=void 0===s?"info":s,m=t.variant,p=void 0===m?"default":m,b=t.politeness,d=void 0===b?"polite":b,f=t.isDismissible,h=void 0!==f&&f,g=pe(t,ce);i=r()(i,"rank-math-notice",{"has-icon":e,"is-alt":"alt"===p});var y=ue(ue({},g),{},{status:u,actions:c,onRemove:o,className:i,politeness:d,isDismissible:h});return wp.element.createElement(a.Notice,y,e&&wp.element.createElement("span",{className:"rank-math-notice__icon"},e),n)};function de(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=rankMath.links[t]||"";if(!n)return"#";if(!e)return n;var o={utm_source:"Plugin",utm_medium:encodeURIComponent(e),utm_campaign:"WP"};return n+"?"+Object.keys(o).map((function(t){return"".concat(t,"=").concat(o[t])})).join("&")}var fe=["className"];function he(){return he=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},he.apply(this,arguments)}function ge(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var ye=n(6185),ve={};ve.styleTagTransform=y(),ve.setAttributes=d(),ve.insert=p().bind(null,"head"),ve.domAPI=u(),ve.insertStyleElement=h();c()(ye.Z,ve),ye.Z&&ye.Z.locals&&ye.Z.locals;var ke=["value","onChange","className"];function xe(){return xe=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},xe.apply(this,arguments)}function we(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return _e(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _e(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _e(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function Oe(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var je=wp.url,Se=n(5772),Ee={};Ee.styleTagTransform=y(),Ee.setAttributes=d(),Ee.insert=p().bind(null,"head"),Ee.domAPI=u(),Ee.insertStyleElement=h();c()(Se.Z,Ee),Se.Z&&Se.Z.locals&&Se.Z.locals;function Pe(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Ne(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ne(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ne(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var Ce=function(t){var e=t.text,n=t.children,o=t.className,l=t.isBlock,c=void 0!==l&&l,s=t.placement,u=void 0===s?"top":s,m=Pe((0,i.useState)(!1),2),p=m[0],b=m[1];o=r()(o,"rank-math-tooltip-container",{"is-inline":c});var d=r()(u,"rank-math-tooltip-popover",{"is-visible":p}),f=function(){b((function(t){return!t}))};return wp.element.createElement("div",{className:o,onMouseEnter:f,onMouseLeave:f},n,e&&wp.element.createElement(a.Popover,{offset:8,shift:!0,noArrow:!1,placement:u,className:d},e))},Ae=n(388),Ie={};Ie.styleTagTransform=y(),Ie.setAttributes=d(),Ie.insert=p().bind(null,"head"),Ie.domAPI=u(),Ie.insertStyleElement=h();c()(Ae.Z,Ie),Ae.Z&&Ae.Z.locals&&Ae.Z.locals;var Te=n(5399),De={};De.styleTagTransform=y(),De.setAttributes=d(),De.insert=p().bind(null,"head"),De.domAPI=u(),De.insertStyleElement=h();c()(Te.Z,De),Te.Z&&Te.Z.locals&&Te.Z.locals;function Ze(t){return Ze="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ze(t)}var ze=["value","options","onChange","className","disabled","variant","disabledOptions"];function Me(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function Le(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Me(Object(n),!0).forEach((function(e){Re(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Me(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Re(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==Ze(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==Ze(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Ze(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Fe(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Ue(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ue(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ue(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function Be(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var He=n(719),$e={};$e.styleTagTransform=y(),$e.setAttributes=d(),$e.insert=p().bind(null,"head"),$e.domAPI=u(),$e.insertStyleElement=h();c()(He.Z,$e),He.Z&&He.Z.locals&&He.Z.locals;function We(t){return We="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},We(t)}var Ge=["as","value","label","style","options","onChange","width","className","disabled"];function Xe(){return Xe=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},Xe.apply(this,arguments)}function Ye(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function qe(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Ye(Object(n),!0).forEach((function(e){Qe(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ye(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Qe(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==We(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==We(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===We(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Ve(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Je(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Je(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Je(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function Ke(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var tn=(0,i.forwardRef)((function(t,e){var n=t.as,o=t.value,l=(t.label,t.style),c=t.options,s=t.onChange,u=t.width,m=void 0===u?"100%":u,p=t.className,b=void 0===p?"":p,d=t.disabled,f=void 0!==d&&d,h=Ke(t,Ge),g=Ve((0,i.useState)(""),2),y=g[0],v=g[1],k=Ve(Zt(),3),x=k[0],w=k[1],_=k[2],O=null!=e?e:_,j=function(){var t=(0,M.lowerCase)(y);return(0,M.filter)(c,(function(e){var n=e.name,o=e.variable,r=e.description;return(0,M.includes)((0,M.lowerCase)(n),t)||(0,M.includes)((0,M.lowerCase)(o),t)||(0,M.includes)((0,M.lowerCase)(r),t)}))},S={value:o,onChange:s,disabled:f},P=qe(qe({},h),{},{ref:O,style:qe({width:m},l),"aria-disabled":f,className:"rank-math-select-variable ".concat(b)});return wp.element.createElement("div",P,wp.element.createElement("div",{className:"select-input","aria-expanded":x},"textarea"===n?wp.element.createElement(Vt,Xe({rows:2,variant:"metabox"},S)):wp.element.createElement(It,Xe({variant:"regular-text"},S)),wp.element.createElement(E,{variant:"secondary",disabled:f,icon:wp.element.createElement(a.Icon,{icon:"arrow-down-alt2"}),onClick:function(){return w((function(t){return!t}))}})),x&&wp.element.createElement("div",{className:r()("select-menu",{"is-textarea-menu":"textarea"===n})},wp.element.createElement(a.SearchControl,{value:y,onChange:v,placeholder:(0,L.__)("Search …","rank-math"),autoFocus:!0}),j.length>0?wp.element.createElement("ul",{tabIndex:"-1",role:"listbox","aria-hidden":"false","aria-expanded":x},(0,M.map)(j,(function(t){var e=t.name,n=t.variable,r=t.description;return wp.element.createElement("li",{role:"option",key:n,"aria-hidden":"true","data-value":n,onClick:function(){return t=n,s("".concat(o," ").concat(t)),w(!1),void v("");var t}},wp.element.createElement("div",null,wp.element.createElement("h1",null,e),wp.element.createElement("p",null,n)),wp.element.createElement(i.RawHTML,{className:"description"},r))}))):wp.element.createElement("span",{className:"no-results"})))})),en=n(3539),nn={};nn.styleTagTransform=y(),nn.setAttributes=d(),nn.insert=p().bind(null,"head"),nn.domAPI=u(),nn.insertStyleElement=h();c()(en.Z,nn),en.Z&&en.Z.locals&&en.Z.locals;function on(t){return on="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},on(t)}var rn=["value","style","label","options","onChange","width","className","disabled"];function an(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function ln(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?an(Object(n),!0).forEach((function(e){cn(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):an(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function cn(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==on(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==on(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===on(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function sn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return un(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return un(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function un(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function mn(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var pn=(0,i.forwardRef)((function(t,e){var n=t.value,o=t.style,r=t.label,l=t.options,c=t.onChange,s=t.width,u=void 0===s?"100%":s,m=t.className,p=void 0===m?"":m,b=t.disabled,d=void 0!==b&&b,f=mn(t,rn),h=sn((0,i.useState)(""),2),g=h[0],y=h[1],v=sn(Zt(),3),k=v[0],x=v[1],w=v[2],_=null!=e?e:w,O=l[n],j=(0,M.map)((0,M.entries)(l),(function(t){var e=sn(t,2);return{key:e[0],name:e[1]}})),S=(0,M.filter)(j,(function(t){var e=t.name;return(0,M.includes)((0,M.lowerCase)(e),(0,M.lowerCase)(g))})),E=ln(ln({},f),{},{ref:_,style:ln({width:u},o),className:"rank-math-select-with-searchbox ".concat(p)});return(0,i.useEffect)((function(){if(k&&_.current){var t=_.current.querySelector('.select-menu ul li[aria-selected="true"]');t&&t.scrollIntoView({behavior:"auto",block:"nearest"})}}),[k]),wp.element.createElement("div",E,r&&wp.element.createElement("label",{htmlFor:"select-menu"},r),wp.element.createElement(a.Button,{variant:"secondary",disabled:d,"aria-expanded":k,onClick:function(){return x((function(t){return!t}))}},wp.element.createElement("span",{dangerouslySetInnerHTML:{__html:O}})),k&&wp.element.createElement("div",{className:"select-menu"},wp.element.createElement(a.SearchControl,{value:g,onChange:y,placeholder:null,autoFocus:!0}),S.length>0?wp.element.createElement("ul",{tabIndex:"-1",role:"listbox","aria-hidden":"false","aria-expanded":k},(0,M.map)(S,(function(t){var e=t.key,o=t.name;return wp.element.createElement("li",{key:e,role:"option","aria-hidden":"true","aria-selected":e===n,onClick:function(){return c(e),x(!1),void y("")}},wp.element.createElement("span",{dangerouslySetInnerHTML:{__html:o}}))}))):wp.element.createElement("span",{className:"no-results"},(0,L.__)("No results found","rank-math"))))})),bn=n(4465),dn={};dn.styleTagTransform=y(),dn.setAttributes=d(),dn.insert=p().bind(null,"head"),dn.domAPI=u(),dn.insertStyleElement=h();c()(bn.Z,dn),bn.Z&&bn.Z.locals&&bn.Z.locals;function fn(t){return fn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fn(t)}var hn=["value","options","onChange","children","className","width","disabled"];function gn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return yn(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return yn(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yn(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function vn(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function kn(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?vn(Object(n),!0).forEach((function(e){xn(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):vn(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function xn(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==fn(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==fn(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===fn(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function wn(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var _n=n(7269),On={};On.styleTagTransform=y(),On.setAttributes=d(),On.insert=p().bind(null,"head"),On.domAPI=u(),On.insertStyleElement=h();c()(_n.Z,On),_n.Z&&_n.Z.locals&&_n.Z.locals;function jn(t){return jn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jn(t)}var Sn=["className","children","href","severity"];function En(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function Pn(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?En(Object(n),!0).forEach((function(e){Nn(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):En(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Nn(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==jn(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==jn(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===jn(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Cn(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var An={status:{good:wp.element.createElement("svg",{width:"9",height:"8",viewBox:"0 0 9 8",fill:"none",xmlns:"http://www.w3.org/2000/svg"}," ",wp.element.createElement("path",{d:"M2.43293 6.20687L7.72329 0.916504L8.6001 1.79331L3.30974 7.08367L2.43293 6.20687Z",fill:"white"})," ",wp.element.createElement("path",{d:"M3.30974 7.08367L0.600098 4.37403L1.4769 3.49723L4.18653 6.20686L3.30974 7.08367Z",fill:"white"})),neutral:wp.element.createElement("svg",{width:"3",height:"8",viewBox:"0 0 3 8",fill:"none",xmlns:"http://www.w3.org/2000/svg"}," ",wp.element.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.935694 0L1.05966 4.56408H2.53953L2.66349 0H0.935694Z",fill:"white"})," ",wp.element.createElement("path",{d:"M1.79575 8C1.23014 8 0.780762 7.55837 0.780762 6.99276C0.780762 6.42716 1.23014 5.99328 1.79575 5.99328C2.37684 5.99328 2.81848 6.42716 2.81848 6.99276C2.81848 7.55837 2.37684 8 1.79575 8Z",fill:"white"})," "),bad:wp.element.createElement("svg",{width:"8",height:"8",viewBox:"0 0 8 8",fill:"none",xmlns:"http://www.w3.org/2000/svg"},wp.element.createElement("path",{d:"M4 3.22222L1.27778 0.5L0.5 1.27778L3.22222 4L0.5 6.72222L1.27778 7.5L4 4.77778L6.72222 7.5L7.5 6.72222L4.77778 4L7.5 1.27778L6.72222 0.5L4 3.22222Z",fill:"white"}))},statusIcons:{connected:wp.element.createElement("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"}," ",wp.element.createElement("path",{d:"M13.2561 2.93135C13.0592 2.73447 12.753 2.73447 12.5561 2.93135L5.11858 10.1501L1.44358 6.54072C1.2467 6.34385 0.940452 6.36572 0.743577 6.54072C0.546702 6.7376 0.568577 7.04385 0.743577 7.24072L4.61545 11.0032C4.7467 11.1345 4.9217 11.2001 5.11858 11.2001C5.31545 11.2001 5.46858 11.1345 5.6217 11.0032L13.2561 3.5876C13.453 3.43447 13.453 3.12822 13.2561 2.93135Z",fill:"white"})," "),disconnected:wp.element.createElement("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"}," ",wp.element.createElement("path",{d:"M6.5499 5.9999L10.7437 1.80615C10.8983 1.65146 10.8983 1.41084 10.7437 1.25615C10.589 1.10146 10.3483 1.10146 10.1937 1.25615L5.9999 5.4499L1.80615 1.25615C1.65146 1.10146 1.41084 1.10146 1.25615 1.25615C1.10146 1.41084 1.10146 1.65146 1.25615 1.80615L5.4499 5.9999L1.25615 10.1937C1.10146 10.3483 1.10146 10.589 1.25615 10.7437C1.3249 10.8124 1.42803 10.864 1.53115 10.864C1.63428 10.864 1.7374 10.8296 1.80615 10.7437L5.9999 6.5499L10.1937 10.7437C10.2624 10.8124 10.3655 10.864 10.4687 10.864C10.5718 10.864 10.6749 10.8296 10.7437 10.7437C10.8983 10.589 10.8983 10.3483 10.7437 10.1937L6.5499 5.9999Z",fill:"#868686"})," ")}},In=n(3328),Tn={};Tn.styleTagTransform=y(),Tn.setAttributes=d(),Tn.insert=p().bind(null,"head"),Tn.domAPI=u(),Tn.insertStyleElement=h();c()(In.Z,Tn),In.Z&&In.Z.locals&&In.Z.locals;function Dn(t){return Dn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dn(t)}var Zn=["children","className","onClick","status"];function zn(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function Mn(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?zn(Object(n),!0).forEach((function(e){Ln(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):zn(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Ln(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==Dn(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==Dn(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Dn(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Rn(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var Fn=n(185),Un={};Un.styleTagTransform=y(),Un.setAttributes=d(),Un.insert=p().bind(null,"head"),Un.domAPI=u(),Un.insertStyleElement=h();c()(Fn.Z,Un),Fn.Z&&Fn.Z.locals&&Fn.Z.locals;var Bn=["status","href","className","description"];function Hn(){return Hn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},Hn.apply(this,arguments)}function $n(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var Wn=n(5073),Gn={};Gn.styleTagTransform=y(),Gn.setAttributes=d(),Gn.insert=p().bind(null,"head"),Gn.domAPI=u(),Gn.insertStyleElement=h();c()(Wn.Z,Gn),Wn.Z&&Wn.Z.locals&&Wn.Z.locals;function Xn(t){return Xn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xn(t)}var Yn=["className"];function qn(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function Qn(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==Xn(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==Xn(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Xn(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Vn(t,e){if(null==t)return{};var n,o,r=function(t,e){if(null==t)return{};var n,o,r={},a=Object.keys(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(o=0;o<a.length;o++)n=a[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var Jn=function(t){return wp.element.createElement("div",{className:"serp-preview"},wp.element.createElement("div",{className:"serp-preview-body"},wp.element.createElement("div",{className:"serp-url-wrapper"},wp.element.createElement("img",{src:t.favicon,width:16,height:16,className:"serp-favicon",alt:"favicon"}),wp.element.createElement("span",{className:"serp-url"},t.url)),wp.element.createElement("h5",{className:"serp-title"},t.title),wp.element.createElement("p",{className:"serp-description"},t.description)))},Kn=function(t){var e=t.size,n=t.strokeWidth,o=t.value,r=t.max,a=(e-n)/2,i="0 0 ".concat(e," ").concat(e),l=a*Math.PI*2,c=l-l*o/r;return wp.element.createElement("svg",{width:e,height:e,viewBox:i},wp.element.createElement("circle",{fill:"none",stroke:"#e9e9ea",cx:e/2,cy:e/2,r:a,strokeWidth:"".concat(n,"px")}),wp.element.createElement("circle",{className:"circle-progress",fill:"none",stroke:o>70?"#10ac84":o>50?"#ff9f43":"#ed5e5e",strokeLinecap:"round",strokeLinejoin:"round",strokeDasharray:l,strokeDashoffset:c,cx:e/2,cy:e/2,r:a,strokeWidth:"".concat(n,"px"),transform:"rotate(-90 ".concat(e/2," ").concat(e/2,")"),style:{animation:"dashAnimation 1s ease-in-out forwards"}}),wp.element.createElement("style",null,"\n\t\t\t\t\t@keyframes dashAnimation {\n\t\t\t\t\t\tfrom {\n\t\t\t\t\t\t\tstroke-dashoffset: ".concat(l,";\n\t\t\t\t\t\t}\n\t\t\t\t\t\tto {\n\t\t\t\t\t\t\tstroke-dashoffset: ").concat(c,";\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t")))},to=function(t){var e=t.metrices,n=t.date,o=t.serpData,r=e.percent,a=e.statuses,i=e.total-a.info,l=[{status:"good",value:a.ok,label:(0,L.__)("Passed Tests","rank-math")},{status:"average",value:a.warning,label:(0,L.__)("Warnings","rank-math")},{status:"bad",value:a.fail,label:(0,L.__)("Failed Tests","rank-math")}];return wp.element.createElement("div",{className:"rank-math-result-graphs rank-math-box"},n&&wp.element.createElement("div",{className:"rank-math-analysis-date"},wp.element.createElement("span",null,(0,L.__)("Last checked: ","rank-math")),n.date," ",(0,L.__)(" at ","rank-math")," ",n.time),wp.element.createElement("div",{className:"three-col"},wp.element.createElement("div",{className:"graphs-main"},wp.element.createElement("div",{id:"rank-math-circle-progress"},wp.element.createElement(Kn,{max:100,size:207,value:Math.abs(r),strokeWidth:15}),wp.element.createElement("div",{className:"result-main-score"},wp.element.createElement("strong",null,Math.abs(r),"/100"),wp.element.createElement("div",null,(0,L.__)("SEO Score","rank-math"))))),wp.element.createElement("div",{className:"graphs-side"},wp.element.createElement("ul",{className:"chart"},(0,M.map)(l,(function(t){var e=t.status,n=t.value,o=t.label;return wp.element.createElement("li",{key:e,className:"chart-bar-".concat(e)},wp.element.createElement("div",{className:"result-score"},wp.element.createElement("div",null,o),wp.element.createElement("strong",null,Math.abs(n),"/",Math.abs(i))),wp.element.createElement("div",{className:"chart-bar"},wp.element.createElement("span",{style:{width:Math.abs((0,M.round)(n/i*100))+"%"}})))})))),o&&wp.element.createElement(Jn,o)))},eo=wp.hooks,no=function(t){return{ok:"dashicons dashicons-yes status-ok",fail:"dashicons dashicons-no-alt status-fail",warning:"dashicons dashicons-warning status-warning",info:"dashicons dashicons-info status-info"}[t]||""},oo=function(t){return{ok:(0,L.__)("Passed","rank-math"),fail:(0,L.__)("Failed","rank-math"),warning:(0,L.__)("Warning","rank-math"),info:(0,L.__)("Info","rank-math")}[t]||""};function ro(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return ao(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ao(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ao(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var io=function(t){return(0,M.includes)(["links_ratio","keywords_meta","page_objects"],t)},lo=function(t){var e=t.data,n=t.test_id;if("common_keywords"===n)return function(t){var e=(0,M.max)((0,M.values)(t));return wp.element.createElement("div",{className:"wp-tag-cloud"},(0,M.map)((0,M.entries)(t),(function(t,n){var o=ro(t,2),r=o[0],a=o[1]/e*12+10;return a=(0,M.round)(a,2),wp.element.createElement("span",{key:n,className:"keyword-cloud-item",style:{fontSize:"".concat(a,"px")}},r)})))}(e);if(function(t){return(0,M.includes)(["img_alt","minify_css","minify_js","active_plugins","h1_heading","h2_headings"],t)}(n)||io(n))return function(t,e){return wp.element.createElement("ul",{className:"info-list"},(0,M.map)((0,M.entries)(e),(function(e,n){var o=ro(e,2),r=o[0],a=o[1];return a=(0,M.isArray)(a)?(0,M.join)(a,", "):a,io(t)?wp.element.createElement("li",{key:n},wp.element.createElement("strong",null,r,": "),a):wp.element.createElement("li",{key:n},(0,M.isString)(r)&&!(0,M.isNumber)(Number(r))?wp.element.createElement(React.Fragment,null,r," (",a,")"):a)})))}(n,e);if((0,M.includes)(["title_length","description_length","canonical"],n)){var o=(0,M.isArray)(e)?(0,M.join)(e,", "):e;return wp.element.createElement("code",{className:"full-width"},o)}};function co(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return so(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return so(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function so(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var uo=function(t){var e=t.result,n=co((0,i.useState)(!1),2),o=n[0],r=n[1],a=e.status,l=e.title,c=e.tooltip,s=e.kb_link,u=e.fix,m=e.message,p=e.data,b=s||"https://rankmath.com/kb/seo-analysis",d=(0,M.includes)(["fail","warning"],a)&&u;return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"row-title"},e&&function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"info";return wp.element.createElement("div",{className:"status-icon ".concat(no(t)),title:oo(t)})}(a),wp.element.createElement("h3",null,wp.element.createElement("span",{dangerouslySetInnerHTML:{__html:l}}),c&&wp.element.createElement(Ce,{text:c},wp.element.createElement("a",{href:s,target:"_blank",rel:"noreferrer"},wp.element.createElement("em",{className:"dashicons-before dashicons-editor-help"}))))),wp.element.createElement("div",{className:"row-description"},wp.element.createElement("div",{className:"row-content"},d&&wp.element.createElement(E,{variant:"secondary",size:"small",className:"result-action",onClick:function(){return r(!o)}},(0,L.__)("How to fix","rank-math")),wp.element.createElement(i.RawHTML,null,m),o&&wp.element.createElement("div",{className:"how-to-fix-wrapper"},wp.element.createElement("div",{className:"analysis-test-how-to-fix"},wp.element.createElement(i.RawHTML,null,u),!/<\/a><\/p>$/i.test(u.trim())&&wp.element.createElement("p",null,wp.element.createElement(E,{variant:"link",href:b,target:"_blank",className:"analysis-read-more",rel:"noreferrer"},(0,L.__)("Read more","rank-math"))))),wp.element.createElement("div",{className:"clear"}),p&&!(0,M.isEmpty)(p)&&lo(e))))};function mo(t){return function(t){if(Array.isArray(t))return po(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return po(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return po(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function po(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var bo=function(t){var e=t.statuses,n=e.ok,o=e.info,r=e.warning,a=e.fail;return[{name:"all",title:wp.element.createElement(React.Fragment,null,(0,L.__)("All","rank-math"),wp.element.createElement("span",{className:"rank-math-result-filter-count"},t.total-o)),className:"rank-math-result-filter rank-math-result-filter-all",results:t}].concat(mo(n>0?[{name:"ok",title:wp.element.createElement(React.Fragment,null,(0,L.__)("Passed Tests","rank-math"),wp.element.createElement("span",{className:"rank-math-result-filter-count"},n)),className:"rank-math-result-filter rank-math-result-filter-passed",results:t}]:[]),mo(r>0?[{name:"warning",title:wp.element.createElement(React.Fragment,null,(0,L.__)("Warnings","rank-math"),wp.element.createElement("span",{className:"rank-math-result-filter-count"},r)),className:"rank-math-result-filter rank-math-result-filter-warnings",results:t}]:[]),mo(a>0?[{name:"fail",title:wp.element.createElement(React.Fragment,null,(0,L.__)("Failed Tests","rank-math"),wp.element.createElement("span",{className:"rank-math-result-filter-count"},a)),className:"rank-math-result-filter rank-math-result-filter-failed",results:t}]:[]))},fo=function(t){return{priority:(0,L.__)("Priority","rank-math"),advanced:(0,L.__)("Advanced SEO","rank-math"),basic:(0,L.__)("Basic SEO","rank-math"),performance:(0,L.__)("Performance","rank-math"),security:(0,L.__)("Security","rank-math")}[t]||""};function ho(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return go(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return go(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function go(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var yo=function(t){var e=ho((0,i.useState)("all"),2),n=e[0],o=e[1];return wp.element.createElement(a.TabPanel,{className:"seo-analysis-results",tabs:bo(t.metrices),initialTabName:"all",onSelect:function(t){return o(t)}},(function(){return(0,M.map)((0,M.entries)(t.results),(function(t,e){var o=ho(t,2),r=o[0],a=o[1],i=function(t,e){return(0,M.map)(e,(function(e,n){return"all"!==t&&t!==e.status?null:wp.element.createElement("div",{key:n,className:"table-row rank-math-result-status-".concat(e.status),"data-status":e.status},wp.element.createElement(uo,{result:e}))}))}(n,a);if(!(0,M.isEmpty)((0,M.compact)(i)))return wp.element.createElement("div",{key:e,className:"rank-math-result-table rank-math-result-category-".concat(r)},wp.element.createElement("div",{className:"category-title"},fo(r)),(0,eo.applyFilters)("rank_math_analysis_category_notice","",r),i)}))}))};function vo(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return ko(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ko(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ko(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}window.rankMathComponents={Button:E,CheckboxControl:z,CheckboxList:function(t){var e=t.value,n=void 0===e?[]:e,o=t.options,r=t.onChange,a=t.toggleAll,i=t.variant,l=void 0===i?"metabox":i,c=function(t){if(!(0,M.find)(o,(function(e){return e.id===t})).disabled){var e=(0,M.includes)(n,t)?(0,M.filter)(n,(function(e){return e!==t})):[].concat($(n),[t]);r(e)}};return wp.element.createElement(React.Fragment,null,a&&wp.element.createElement(E,H({onClick:function(){var t=(0,M.filter)(o,(function(t){return!t.disabled})),e=(0,M.map)(t,(function(t){return t.id})),a=(0,M.map)((0,M.filter)(o,(function(t){return t.disabled})),(function(t){return t.id})),i=(0,M.every)(e,(function(t){return(0,M.includes)(n,t)}))?(0,M.filter)(n,(function(t){return(0,M.includes)(a,t)})):[].concat($(n),$((0,M.filter)(e,(function(t){return!(0,M.includes)(n,t)}))));r(i)},className:"field-multicheck-toggle",children:(0,L.__)("Select / Deselect All","rank-math")},a)),wp.element.createElement("ul",{className:"rank-math-checkbox-list"},(0,M.map)(o,(function(t){var e=t.id,o=t.label,r=B(t,U);return wp.element.createElement("li",{key:e},wp.element.createElement(z,H({},r,{variant:l,checked:(0,M.includes)(n,e),onChange:function(){return c(e)},label:wp.element.createElement("span",{dangerouslySetInnerHTML:{__html:o}})})))}))))},RadioControl:function(t){var e=t.label,n=t.variant,o=t.options,i=t.selected,l=t.disabled,c=t.onChange,s=t.className,u=void 0===s?"":s,m=K(t,q);u=r()(u,n,"rank-math-radio-control");var p=V(V({},m),{},{label:e,options:o,selected:i,onChange:c,disabled:l,className:u});return wp.element.createElement(a.RadioControl,p)},ToggleControl:function(t){var e=t.label,n=t.onChange,o=t.disabled,r=t.checked,i=t.className,l=void 0===i?"":i,c=at(at({},lt(t,ot)),{},{label:e,onChange:n,disabled:o,checked:r,__nextHasNoMarginBottom:!0,className:"rank-math-toggle-control ".concat(l," ").concat(e?"":"hide-label")});return wp.element.createElement(a.ToggleControl,c)},Group:function(t){var e=t.fields,n=t.settingType;return(0,M.map)(e,(function(t,e){return wp.element.createElement(ht,{key:e,settingType:n,field:t})}))},RepeatableGroup:function(t){var e=t.id,n=t.value,o=t.onChange,r=t.options,a=t.fields,l=t.default,c=void 0===l?{}:l,s=r.addButton,u=r.removeButton,m=function(){var t=(0,M.concat)(n,[c]);o(t)},p=function(t,e,r){var a=(0,M.map)(n,(function(n,o){return e===o?wt(wt({},n),{},_t({},r,t)):n}));o(a)};return(0,i.useEffect)((function(){0===n.length&&m()}),[n]),wp.element.createElement(React.Fragment,null,(0,M.map)(n,(function(t,r){return wp.element.createElement("div",{key:r,className:"field-repeatable-grouping"},(0,M.map)(a,(function(a){var i="".concat(e,"_").concat(r,"_").concat(a.id),l=wt(wt({},a),{},{id:i,value:t[a.id]||"",onChange:function(t){return p(t,r,a.id)},onPaste:function(t){return function(t,e,r){if(!n[e][r]){t.preventDefault();var a=t.clipboardData.getData("text"),i=(0,M.filter)((0,M.split)(a,"\n"),Boolean);if(!(i.length<1)){var l=vt(n);l[e]=wt(wt({},l[e]),{},_t({},r,i[0]));var c=(0,M.map)((0,M.slice)(i,1),(function(t){return wt(wt({},l[e]),{},_t({},r,t))})),s=e+1;o([].concat(vt((0,M.slice)(l,0,s)),vt(c),vt((0,M.slice)(l,s))))}}}(t,r,a.id)}});return wp.element.createElement("div",{key:i,className:"group-item group-item-".concat(a.type)},wp.element.createElement(ht,{field:l}))})),u&&wp.element.createElement(E,yt({},u,{variant:"remove-group",onClick:function(){return t=r,e=(0,M.filter)(n,(function(e,n){return n!==t})),void o(e);var t,e}})))})),s&&wp.element.createElement(E,yt({onClick:m},s)))},DatePicker:function(t){var e=t.value,n=t.onChange,o=t.inputProps,r=Bt(t,Lt),i=Ft(Zt(),3),l=i[0],c=i[1],s=i[2];return wp.element.createElement("div",{ref:s,className:"rank-math-date-picker ".concat(l?"show-date-picker":"hide-date-picker")},wp.element.createElement(It,Rt({},o,{value:e,onChange:n,onClick:function(){return c(!0)},onKeyDown:function(t){(0,M.includes)(["0","1","2","3","4","5","6","7","8","9","-","Backspace","ArrowLeft","ArrowRight"],t.key)||t.preventDefault()},onBlur:function(t){var e=t.target.value;if(/^\d{4}-\d{2}-\d{2}$/.test(e)){var r=Ft((0,M.map)((0,M.split)(e,"-"),Number),3),a=r[0],i=r[1],l=r[2],c=new Date(a,i-1,l);c.getFullYear()===a&&c.getMonth()===i-1&&c.getDate()===l?o.onBlur(t):n("")}else n("")}})),wp.element.createElement(a.DatePicker,Rt({},r,{onChange:function(t){var e=t.split("T")[0];c(!1),n(e)}})))},TextareaControl:Vt,TextControl:It,Table:function(t){var e=t.fields,n=t.size,o=t.className,a=t.th,i=ne(t,te);return o=r()("rank-math-table",o,n),wp.element.createElement("table",ee({className:o},i),wp.element.createElement("tbody",null,(0,M.map)((0,M.entries)(e),(function(t,e){return wp.element.createElement("tr",{key:e},(0,M.map)(t,(function(t,e){return a&&0===e?wp.element.createElement("th",{key:e}," ",ie(t)," "):wp.element.createElement("td",{key:e}," ",ie(t)," ")})))}))))},Notice:be,InvalidSiteUrlNotice:function(t){if(!t.isSiteUrlValid)return wp.element.createElement(be,{status:"warning",className:"notice-connect-disabled"},wp.element.createElement(i.RawHTML,null,(0,L.sprintf)((0,L.__)("Rank Math cannot be connected because your site URL doesn't appear to be a valid URL. If the domain name contains special characters, please make sure to use the encoded version in the %1$s &amp; %2$s fields on the %3$s page.","rank-math"),"<strong>".concat((0,L.__)("WordPress Address (URL)","rank-math"),"</strong>"),"<strong>".concat((0,L.__)("Site Address (URL)","rank-math"),"</strong>"),'<a href="'.concat(window.location.origin,'/wp-admin/options-general.php" target="_blank">').concat((0,L.__)("WordPress General Settings","rank-math"),"</a>"))))},PrivacyBox:function(t){var e=t.className,n=void 0===e?"":e,o=ge(t,fe);return wp.element.createElement("div",he({},o,{id:"rank-math-pro-cta",className:"rank-math-privacy-box ".concat(n)}),wp.element.createElement("div",{className:"rank-math-cta-table"},wp.element.createElement("div",{className:"rank-math-cta-body less-padding"},wp.element.createElement("i",{className:"dashicons dashicons-lock"}),wp.element.createElement("p",{dangerouslySetInnerHTML:{__html:(0,L.sprintf)((0,L.__)("We do not store any of the data from your Google account on our servers, everything is processed & stored on your server. We take your privacy extremely seriously and ensure it is never misused. %s","rank-math"),'<a href="'.concat(de("usage-policy","Analytics Privacy Notice"),'" target="_blank" rel="noopener noreferrer">').concat((0,L.__)("Learn more.","rank-math"),"</a>"))}}))))},ProBadge:function(t){var e=t.href;return wp.element.createElement("span",{className:"rank-math-pro-badge"},wp.element.createElement("a",{href:e,target:"_blank",rel:"noopener noreferrer"},(0,L.__)("PRO","rank-math")))},Rating:function(t){t.value;var e=t.onChange,n=t.className,o=void 0===n?"":n,r=Oe(t,ke),l=we((0,i.useState)({face:"",num:""}),2),c=l[0],s=l[1],u=function(){s({face:"",num:""})};return wp.element.createElement("div",xe({},r,{className:"rank-math-rating ".concat(o)}),wp.element.createElement("div",{className:"rank-math-rating__stars"},Array.from({length:5},(function(t,n){return wp.element.createElement(a.Button,{key:n,onMouseOut:u,onMouseOver:function(){return function(t){var e=t+1;s(t<2?{face:"angry",num:e}:t>=2&&t<4?{face:"normal",num:e}:{face:"happy",num:e})}(n)},onClick:function(){return e(n+1)},icon:wp.element.createElement(a.Icon,{icon:"star-filled"}),className:n<c.num&&"highlighted"})}))),wp.element.createElement("div",{className:"rank-math-rating__face"},wp.element.createElement("div",{className:"smiley ".concat(c.face)},wp.element.createElement("div",{className:"eyes"},wp.element.createElement("span",{className:"eye"}),wp.element.createElement("span",{className:"eye"})),wp.element.createElement("div",{className:"mouth"}))))},SocialShare:function(t){if(!t.isWhitelabel){var e=de("logo","Setup Wizard Tweet Button"),n=de("logo","Facebook"),o=(0,L.sprintf)((0,L.__)("I just installed @RankMathSEO #WordPress Plugin. It looks great! %s","rank-math"),e),r=(0,L.__)("I just installed Rank Math SEO WordPress Plugin. It looks promising!","rank-math"),a=(0,je.addQueryArgs)("https://www.facebook.com/sharer/sharer.php",{u:n,quote:r,caption:(0,L.__)("SEO by Rank Math","rank-math")}),i=(0,je.addQueryArgs)("https://twitter.com/intent/tweet",{text:o,hashtags:"SEO"}),l=function(t,e){t.preventDefault(),window.open(e,"sharewindow","resizable,width=600,height=300")};return wp.element.createElement("span",{className:"wizard-share"},wp.element.createElement("a",{href:"##",onClick:function(t){return l(t,i)},className:"share-twitter"},wp.element.createElement("span",{className:"dashicons dashicons-twitter"})," ",(0,L.__)("Tweet","rank-math")),wp.element.createElement("a",{href:"##",onClick:function(t){return l(t,a)},className:"share-facebook"},wp.element.createElement("span",{className:"dashicons dashicons-facebook-alt"})," ",(0,L.__)("Share","rank-math")))}},Tooltip:Ce,UploadFile:function(t){var e=t.value,n=t.onChange,o=t.name,r=t.description,a=wp.media({title:o,multiple:!1,library:{type:"image"},button:{text:(0,L.__)("Use this file","rank-math")}});return a.on("select",(function(){var t=a.state().get("selection").first().toJSON();n(t)})),wp.element.createElement(React.Fragment,null,wp.element.createElement(E,{variant:"secondary",onClick:function(){return a.open()}},(0,L.__)("Add or Upload File","rank-math")),wp.element.createElement("p",{className:"field-description",dangerouslySetInnerHTML:{__html:r}}),!(0,M.isEmpty)(e)&&wp.element.createElement("div",{className:"media-status"},wp.element.createElement("div",{className:"img-status media-item"},wp.element.createElement("img",{width:350,height:196,src:e,alt:""}),wp.element.createElement(E,{className:"remove-file-button",onClick:function(){return n({})}}))))},SelectControl:function(t){var e=t.value,n=t.options,o=t.onChange,i=t.className,l=t.disabled,c=void 0!==l&&l,s=t.variant,u=void 0===s?"metabox":s,m=t.disabledOptions,p=void 0===m?[]:m,b=Be(t,ze);i=r()(i,"rank-math-select-control",{"is-disabled":c,"is-metabox":"metabox"===u});var d=(0,M.map)((0,M.entries)(n),(function(t){var e=Fe(t,2),n=e[0];return{value:n,label:e[1],disabled:(0,M.includes)(p,n)}})),f=Le(Le({},b),{},{value:e,onChange:o,disabled:c,className:i,options:d,__nextUnconstrainedWidth:!0,__next36pxDefasultSize:!0,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0});return wp.element.createElement(a.SelectControl,f)},SelectVariable:tn,SelectWithSearch:pn,ToggleGroupControl:function(t){var e=t.value,n=t.options,o=t.onChange,r=t.children,i=t.className,l=void 0===i?"":i,c=t.width,s=void 0===c?"100%":c,u=t.disabled,m=void 0!==u&&u,p=kn(kn({},wn(t,hn)),{},{value:e,onChange:o,"aria-disabled":m,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,className:"rank-math-toggle-group-control ".concat(l)});return wp.element.createElement(a.Disabled,{isDisabled:m,style:{width:s}},wp.element.createElement(a.__experimentalToggleGroupControl,p,(0,M.map)((0,M.entries)(n),(function(t){var e=gn(t,2),n=e[0],o=e[1];return wp.element.createElement(a.__experimentalToggleGroupControlOption,{label:o,value:n,key:n})})),r))},StatusAnchorTag:function(t){var e=t.className,n=t.children,o=t.href,i=void 0===o?"":o,l=t.severity,c=void 0===l?"default":l,s=Cn(t,Sn);e=r()(e,"is-".concat(c),"rank-math-status-anchor-tag");var u=Pn(Pn({},s),{},{href:i,children:n,className:e,target:"_blank",variant:"secondary",rel:"noopener noreferrer"});return wp.element.createElement(a.Button,u)},StatusButton:function(t){var e=t.children,n=t.className,o=t.onClick,a=t.status,i=Rn(t,Zn);n=r()(n,"is-".concat(a),"rank-math-status-button");var l=Mn(Mn({},i),{},{onClick:o,children:e,className:n,icon:An.statusIcons[a],isDestructive:"disconnect"===a,size:"disconnect"===a?"xlarge":"large"});return wp.element.createElement(E,l)},StatusList:function(t){var e=t.status,n=void 0===e?"good":e,o=t.href,a=t.className,i=t.description,l=$n(t,Bn);return a=r()(a,"is-".concat(n),"rank-math-status-list"),wp.element.createElement("div",Hn({},l,{className:a}),wp.element.createElement("span",{className:"rank-math-status-list__icon"},An.status[n]),wp.element.createElement("div",{className:"rank-math-status-list__description"},i," ",wp.element.createElement("a",{href:o,target:"_blank",rel:"noopener noreferrer"},An.help)))},TabPanel:function(t){var e=t.className,n=void 0===e?"":e,o=Vn(t,Yn);return n="rank-math-tab-panel ".concat(n),wp.element.createElement(a.TabPanel,function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?qn(Object(n),!0).forEach((function(e){Qn(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):qn(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({className:n},o))},AnalyzerResult:function(t){var e=t.results;if(!(0,M.isEmpty)(e))return wp.element.createElement("div",{className:"rank-math-results-wrapper"},wp.element.createElement(to,e),wp.element.createElement("div",{id:"analysis-result"},wp.element.createElement(yo,e)))},ProgressBar:function(){var t=vo((0,i.useState)(0),2),e=t[0],n=t[1];return(0,i.useEffect)((function(){var t=setInterval((function(){n((function(e){return e>=100?(clearInterval(t),e):e+1}))}),30);return function(){return clearInterval(t)}}),[]),wp.element.createElement("div",{className:"progress-bar"},wp.element.createElement("div",{className:"progress",style:{width:e+"%"}}),wp.element.createElement("div",{className:"progress-text"},wp.element.createElement("span",null,e,"% "),(0,L.__)("Complete","rank-math")))},Breadcrumbs:function(t){var e=t.activePage;return wp.element.createElement("div",{className:"rank-math-breadcrumbs-wrap"},wp.element.createElement("div",{className:"rank-math-breadcrumbs"},wp.element.createElement("span",null,(0,L.__)("Dashboard","rank-math")),wp.element.createElement("span",{className:"divider"},"/"),wp.element.createElement("span",{className:"active"},e)))},Graphs:to,getCategoryLabel:fo,getStatusIcons:no,getStatusLabels:oo},e()((function(){var t=e()("body");if(t.hasClass("rank-math_page_rank-math-role-manager")||t.hasClass("rank-math_page_rank-math-seo-analysis")||t.hasClass("rank-math_page_rank-math-status")||t.hasClass("toplevel_page_rank-math")){setTimeout((function(){var t,e;t=document.querySelector(".rank-math-notice"),e=document.querySelector(".rank-math-wrap"),t&&e&&e.insertAdjacentElement("afterbegin",t)}),1)}}))}()}();