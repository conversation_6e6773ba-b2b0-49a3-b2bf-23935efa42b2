!function(){"use strict";var t={n:function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,{a:n}),n},d:function(e,n){for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}},e=jQuery,n=t.n(e),r=lodash;function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,u(r.key),r)}}function u(t){var e=function(t,e){if("object"!==i(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===i(e)?e:String(e)}var a=function(){function t(){var e,r,i;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e=this,i=function(t){return n()(this.fields).each((function(e,r){t+=n()(r).val()})),t},(r=u(r="getContent"))in e?Object.defineProperty(e,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[r]=i,this.init(),this.hooks(),this.events()}var e,i,a;return e=t,(i=[{key:"init",value:function(){this.fields=this.getFields(),this.getContent=this.getContent.bind(this)}},{key:"hooks",value:function(){wp.hooks.addFilter("rank_math_content","rank-math",this.getContent,11)}},{key:"events",value:function(){n()(this.fields).each((function(t,e){n()(e).on("keyup change",(0,r.debounce)((function(){rankMathEditor.refresh("content")}),500))}))}},{key:"getFields",value:function(){var t=[];return n()("#the-list > tr:visible").each((function(e,r){var i=n()("#"+r.id+"-key").val();-1!==n().inArray(i,rankMath.analyzeFields)&&t.push("#"+r.id+"-value")})),t}}])&&o(e.prototype,i),a&&o(e,a),Object.defineProperty(e,"prototype",{writable:!1}),t}();n()((function(){setTimeout((function(){new a}),500)}))}();