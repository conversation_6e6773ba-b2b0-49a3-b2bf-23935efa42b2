!function(){"use strict";var e={n:function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,{a:n}),n},d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}},t=jQuery,n=e.n(t),r=wp.i18n;function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function a(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(a=r.key,i=void 0,i=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(a,"string"),"symbol"===o(i)?i:String(i)),r)}var a,i}var i=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.deactivatePlugins(),this.initializeClipBoard(),this.modeSelector(),this.dashboardWidget()}var t,o,i;return t=e,(o=[{key:"deactivatePlugins",value:function(){n()(".dashboard-deactivate-plugin").on("click",(function(e){e.preventDefault();var t=n()(this);return n().ajax({url:window.ajaxurl,type:"POST",data:{action:"rank_math_deactivate_plugins",security:rankMath.security,plugin:"all"}}).always((function(e){"1"===e&&t.parents(".rank-math-notice").remove()})),!1}))}},{key:"initializeClipBoard",value:function(){"undefined"!=typeof ClipboardJS&&(n()(".get-debug-report").on("click",(function(){return n()("#debug-report").slideDown(),n()("#debug-report textarea").trigger("focus").trigger("select"),n()(this).parent().fadeOut(),!1})),new ClipboardJS("#copy-for-support"))}},{key:"modeSelector",value:function(){n()(".rank-math-mode-selector a").on("click",(function(e){e.preventDefault();var t=n()(this).data("mode");return n().ajax({url:rankMath.api.root+"rankmath/v1/updateMode",method:"POST",beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",rankMath.api.nonce)},data:{mode:t}}).done((function(e){e?window.location.reload():window.alert("Something went wrong! Please try again.")})),!1}))}},{key:"dashboardWidget",value:function(){var e=n()("#rank-math-dashboard-widget");e.length&&n().ajax({url:rankMath.api.root+"rankmath/v1/dashboardWidget",method:"GET",beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",rankMath.api.nonce)}}).done((function(t){t?e.removeClass("rank-math-loading").html(t):e.removeClass("rank-math-loading").html((0,r.__)("Something went wrong! Please try again.","rank-math"))}))}}])&&a(t.prototype,o),i&&a(t,i),Object.defineProperty(t,"prototype",{writable:!1}),e}();n()((function(){new i}))}();