!function(){"use strict";var t={n:function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,{a:n}),n},d:function(e,n){for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}},e=jQuery,n=t.n(e),r=lodash,i=wp.hooks;function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function a(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,u(r.key),r)}}function u(t){var e=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===o(e)?e:String(e)}new(function(){function t(){var e,n,r;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e=this,r=[],(n=u(n="fields"))in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,(0,i.addAction)("rank_math_loaded","rank-math",this.init.bind(this))}var e,o,l;return e=t,(o=[{key:"init",value:function(){this.getFields(),this.events()}},{key:"getFields",value:function(){var t=this;n()("#the-list > tr:visible").each((function(e,r){var i=n()("#"+r.id+"-key").val();-1!==n().inArray(i,rankMath.analyzeFields)&&t.fields.push("#"+r.id+"-value")}))}},{key:"events",value:function(){(0,i.addFilter)("rank_math_content","rank-math",this.getContent.bind(this)),n()(this.fields).each((function(t,e){n()(e).on("keyup change",(0,r.debounce)((function(){rankMathEditor.refresh("content")}),500))}))}},{key:"getContent",value:function(t){return n()(this.fields).each((function(e,r){t+=n()(r).val()})),t}}])&&a(e.prototype,o),l&&a(e,l),Object.defineProperty(e,"prototype",{writable:!1}),t}())}();