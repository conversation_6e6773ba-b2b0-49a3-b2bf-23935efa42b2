!function(){"use strict";var e={n:function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,{a:r}),r},d:function(t,r){for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}},t=jQuery,r=e.n(t),n=wp.hooks,o=wp.element,i=wp.components,a=wp.data,u=lodash,c=wp.i18n,s=wp.apiFetch,l=e.n(s),f=wp.url;function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function m(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(o=n.key,i=void 0,i=function(e,t){if("object"!==p(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==p(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===p(i)?i:String(i)),n)}var o,i}function y(e,t){return y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},y(e,t)}function d(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=b(e);if(t){var o=b(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===p(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return h(e)}(this,r)}}function h(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function b(e){return b=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},b(e)}var v=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&y(e,t)}(s,e);var t,r,n,o=d(s);function s(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),(e=o.apply(this,arguments)).onChange=e.onChange.bind(h(e)),e.state={loading:!0,availableTerms:[],selectedTerms:[]},e}return t=s,(r=[{key:"componentDidMount",value:function(){this.fetchTerms()}},{key:"componentWillUnmount",value:function(){(0,u.invoke)(this.fetchRequest,["abort"])}},{key:"componentDidUpdate",value:function(e,t){if(e.selectedTermIds.length<this.props.selectedTermIds.length){var r=(0,u.difference)(this.props.selectedTermIds,e.selectedTermIds)[0];if(!this.termIsAvailable(r))return void this.fetchTerms()}e.selectedTermIds!==this.props.selectedTermIds&&this.updateSelectedTerms(this.state.availableTerms,this.props.selectedTermIds),t.selectedTerms!==this.state.selectedTerms&&this.handleSelectedTermsChange()}},{key:"termIsAvailable",value:function(e){return!!this.state.availableTerms.find((function(t){return t.id===e}))}},{key:"updateSelectedTerms",value:function(e,t){this.setState({selectedTerms:this.filterSelectedTerms(e,t)})}},{key:"handleSelectedTermsChange",value:function(){var e=this.state.selectedTerms,t=this.props.primaryTermID;e.find((function(e){return e.id===t}))||this.onChange(e.length?e[0].id:"")}},{key:"fetchTerms",value:function(){var e=this,t=this.props.taxonomy;t&&(this.fetchRequest=l()({path:(0,f.addQueryArgs)("/wp/v2/".concat(t.rest_base),{per_page:-1,orderby:"count",order:"desc",_fields:"id,name"})}),this.fetchRequest.then((function(t){e.fetchRequest=null,e.setState({loading:!1,availableTerms:t,selectedTerms:e.filterSelectedTerms(t,e.props.selectedTermIds)})}),(function(t){"abort"!==t.statusText&&(e.fetchRequest=null,e.setState({loading:!1}))})))}},{key:"filterSelectedTerms",value:function(e,t){return e.filter((function(e){return t.includes(e.id)}))}},{key:"onChange",value:function(e){(0,a.dispatch)("rank-math").updatePrimaryTermID(e,this.props.taxonomy.slug)}},{key:"shouldComponentUpdate",value:function(e,t){return this.props.selectedTermIds!==e.selectedTermIds||this.props.primaryTermID!==e.primaryTermID||this.state.selectedTerms!==t.selectedTerms}},{key:"render",value:function(){return this.state.selectedTerms.length<2?null:this.state.loading?[wp.element.createElement(i.Spinner,{key:"spinner"}),wp.element.createElement("p",{key:"spinner-text"},"Loading")]:wp.element.createElement(i.SelectControl,{label:(0,c.__)("Select Primary Term","rank-math"),value:this.props.primaryTermID,options:this.state.selectedTerms.map((function(e){return{value:e.id,label:(0,u.unescape)(e.name)}})),onChange:this.onChange})}}])&&m(t.prototype,r),n&&m(t,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(o.Component);function T(e){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}function g(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(o=n.key,i=void 0,i=function(e,t){if("object"!==T(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==T(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===T(i)?i:String(i)),n)}var o,i}function w(e,t){return w=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},w(e,t)}function S(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=O(e);if(t){var o=O(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===T(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}function O(e){return O=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},O(e)}var j=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&w(e,t)}(u,e);var t,n,o,i=S(u);function u(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),i.apply(this,arguments)}return t=u,(n=[{key:"handleSelectedTermsChange",value:function(){var e=this.state.selectedTerms,t=parseInt(this.props.primaryTermID);e.find((function(e){return e.id===t}))||this.onChange(e.length?e[0].id:"")}},{key:"onChange",value:function(e){e=parseInt(e),rankMath.assessor.serpData.primaryTerm=e,r()("#rank_math_primary_"+this.props.taxonomy.slug).val(e),(0,a.dispatch)("core/editor").editPost({meta:{refreshMe:"refreshUI"}})}}])&&g(t.prototype,n),o&&g(t,o),Object.defineProperty(t,"prototype",{writable:!1}),u}(v),P=(0,a.withSelect)((function(e,t){var n=t.slug,o=e("core/editor").getEditedPostAttribute,i=o("meta"),a=e("core").getTaxonomy(n);return{taxonomy:a,meta:i,selectedTermIds:a?o(a.rest_base):[],primaryTermID:r()("#rank_math_primary_"+a.slug).val()}}))(j),k=function(e){var t=e.TermComponent;return function(e){return!1!==rankMath.assessor.primaryTaxonomy&&e.slug===rankMath.assessor.primaryTaxonomy.name}(e)?wp.element.createElement(o.Fragment,null,wp.element.createElement(t,e),wp.element.createElement(i.PanelRow,{className:"rank-math-primary-term-picker"},wp.element.createElement(P,e))):wp.element.createElement(t,e)};function _(e){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_(e)}function E(){return E=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},E.apply(this,arguments)}function I(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(o=n.key,i=void 0,i=function(e,t){if("object"!==_(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==_(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(o,"string"),"symbol"===_(i)?i:String(i)),n)}var o,i}function C(e,t,r){return t&&I(e.prototype,t),r&&I(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}var R=C((function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),(0,n.addFilter)("editor.PostTaxonomyType","rank-math",(function(e){return function(t){return wp.element.createElement(k,E({TermComponent:e},t))}}))}));r()(document).ready((function(){window.rankMathEditor=new R}))}();