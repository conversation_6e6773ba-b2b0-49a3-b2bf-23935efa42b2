!function(){var t={4184:function(t,e){var r;!function(){"use strict";var n={}.hasOwnProperty;function i(){for(var t=[],e=0;e<arguments.length;e++){var r=arguments[e];if(r){var a=typeof r;if("string"===a||"number"===a)t.push(r);else if(Array.isArray(r)){if(r.length){var o=i.apply(null,r);o&&t.push(o)}}else if("object"===a){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){t.push(r.toString());continue}for(var u in r)n.call(r,u)&&r[u]&&t.push(u)}}}return t.join(" ")}t.exports?(i.default=i,t.exports=i):void 0===(r=function(){return i}.apply(e,[]))||(t.exports=r)}()}},e={};function r(n){var i=e[n];if(void 0!==i)return i.exports;var a=e[n]={exports:{}};return t[n](a,a.exports,r),a.exports}r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,{a:e}),e},r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},function(){"use strict";var t={};r.r(t),r.d(t,{deleteSchema:function(){return le},lockModifiedDate:function(){return I},refreshResults:function(){return Gt},resetDirtyMetadata:function(){return E},resetRedirection:function(){return V},resetStore:function(){return A},saveSchema:function(){return se},saveTemplate:function(){return pe},schemaUpdated:function(){return re},setEditingSchemaId:function(){return ne},setEditorTab:function(){return ie},setTemplateTab:function(){return ae},setVersion:function(){return Bt},toggleFrontendScore:function(){return S},toggleIsDiviPageSettingsBarActive:function(){return Vt},toggleIsDiviRankMathModalActive:function(){return qt},toggleLoaded:function(){return Lt},toggleSchemaEditor:function(){return te},toggleSchemaTemplates:function(){return ee},toggleSnippetEditor:function(){return mt},updateAIScore:function(){return fe},updateAdvancedRobots:function(){return T},updateAnalysisScore:function(){return _},updateAppData:function(){return b},updateAppUi:function(){return v},updateBreadcrumbTitle:function(){return O},updateCanonicalUrl:function(){return P},updateDescription:function(){return L},updateEditSchema:function(){return ce},updateEditSchemas:function(){return oe},updateFacebookDescription:function(){return M},updateFacebookHasOverlay:function(){return U},updateFacebookImage:function(){return R},updateFacebookImageID:function(){return C},updateFacebookImageOverlay:function(){return N},updateFacebookTitle:function(){return j},updateFeaturedImage:function(){return H},updateHasRedirect:function(){return q},updateHighlightedParagraphs:function(){return Wt},updateKeywords:function(){return w},updatePermalink:function(){return x},updatePillarContent:function(){return k},updatePostID:function(){return F},updatePrimaryTermID:function(){return z},updateRedirection:function(){return G},updateRedirectionItem:function(){return B},updateRobots:function(){return D},updateSchemas:function(){return ue},updateSelectedKeyword:function(){return Ht},updateSerpDescription:function(){return dt},updateSerpSlug:function(){return ft},updateSerpTitle:function(){return pt},updateSnippetPreviewType:function(){return ht},updateSocialTab:function(){return zt},updateTitle:function(){return K},updateTwitterAppCountry:function(){return Kt},updateTwitterAppDescription:function(){return Et},updateTwitterAppGoogleplayID:function(){return Nt},updateTwitterAppGoogleplayName:function(){return Ft},updateTwitterAppGoogleplayUrl:function(){return xt},updateTwitterAppIpadID:function(){return Rt},updateTwitterAppIpadName:function(){return Ct},updateTwitterAppIpadUrl:function(){return Ut},updateTwitterAppIphoneID:function(){return At},updateTwitterAppIphoneName:function(){return jt},updateTwitterAppIphoneUrl:function(){return Mt},updateTwitterAuthor:function(){return wt},updateTwitterCardType:function(){return gt},updateTwitterDescription:function(){return vt},updateTwitterHasOverlay:function(){return _t},updateTwitterImage:function(){return St},updateTwitterImageID:function(){return kt},updateTwitterImageOverlay:function(){return Pt},updateTwitterPlayerSize:function(){return Dt},updateTwitterPlayerStreamCtype:function(){return It},updateTwitterPlayerStreamUrl:function(){return Ot},updateTwitterPlayerUrl:function(){return Tt},updateTwitterTitle:function(){return bt},updateTwitterUseFacebook:function(){return yt}});var e={};r.r(e),r.d(e,{appData:function(){return be},appUi:function(){return Pe}});var n={};r.r(n),r.d(n,{getAdvancedRobots:function(){return je},getAnalysisScore:function(){return Oe},getAppData:function(){return Te},getBreadcrumbTitle:function(){return Re},getCanonicalUrl:function(){return Me},getDescription:function(){return er},getDirtyMetadata:function(){return De},getEditSchemas:function(){return wr},getEditingSchema:function(){return kr},getEditorTab:function(){return _r},getFacebookAuthor:function(){return We},getFacebookDescription:function(){return qe},getFacebookHasOverlay:function(){return Qe},getFacebookImage:function(){return Je},getFacebookImageID:function(){return $e},getFacebookImageOverlay:function(){return Xe},getFacebookTitle:function(){return Ve},getFeaturedImage:function(){return rr},getFeaturedImageHtml:function(){return nr},getHighlightedParagraphs:function(){return Be},getKeywords:function(){return Ie},getPermalink:function(){return tr},getPillarContent:function(){return Ee},getPostID:function(){return Ye},getPreviewSchema:function(){return Sr},getPrimaryTermID:function(){return ir},getRedirectionID:function(){return ar},getRedirectionItem:function(){return cr},getRedirectionType:function(){return or},getRedirectionUrl:function(){return ur},getRichSnippets:function(){return Ce},getRobots:function(){return Ae},getSchemas:function(){return vr},getSelectedKeyword:function(){return xe},getSerpDescription:function(){return dr},getSerpSlug:function(){return fr},getSerpTitle:function(){return pr},getShowScoreFrontend:function(){return Ue},getSnippetPreviewType:function(){return hr},getSocialTab:function(){return Ke},getTemplateTab:function(){return Pr},getTitle:function(){return Ze},getTwitterAppCountry:function(){return Br},getTwitterAppDescription:function(){return Cr},getTwitterAppGoogleplayID:function(){return Hr},getTwitterAppGoogleplayName:function(){return zr},getTwitterAppGoogleplayUrl:function(){return Gr},getTwitterAppIpadID:function(){return xr},getTwitterAppIpadName:function(){return Kr},getTwitterAppIpadUrl:function(){return Lr},getTwitterAppIphoneID:function(){return Ur},getTwitterAppIphoneName:function(){return Nr},getTwitterAppIphoneUrl:function(){return Fr},getTwitterAuthor:function(){return Er},getTwitterCardType:function(){return Dr},getTwitterDescription:function(){return Ir},getTwitterHasOverlay:function(){return Mr},getTwitterImage:function(){return jr},getTwitterImageID:function(){return Ar},getTwitterImageOverlay:function(){return Rr},getTwitterPlayerSize:function(){return qr},getTwitterPlayerStream:function(){return Wr},getTwitterPlayerStreamCtype:function(){return $r},getTwitterPlayerUrl:function(){return Vr},getTwitterTitle:function(){return Or},getTwitterUseFacebook:function(){return Tr},hasRedirect:function(){return sr},hasSchemaUpdated:function(){return br},isDiviPageSettingsBarActive:function(){return ze},isDiviRankMathModalActive:function(){return Ge},isLoaded:function(){return Fe},isModifiedDateLocked:function(){return Ne},isPro:function(){return He},isRefreshing:function(){return Le},isSchemaEditorOpen:function(){return yr},isSchemaTemplatesOpen:function(){return gr},isSnippetEditorOpen:function(){return mr}});var i=jQuery,a=r.n(i),o=lodash,u=wp.i18n,c=wp.data,s=wp.element,l=wp.plugins,p=wp.hooks,f=wp.editPost,d=wp.editSite;function m(t){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}function h(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}function y(t){var e=function(t,e){if("object"!==m(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==m(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===m(e)?e:String(e)}var g=new(function(){function t(){var e,r,n;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e=this,n=null,(r=y(r="map"))in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n}var e,r,n;return e=t,(r=[{key:"swap",value:function(t,e){var r=this;if(!(t=t||""))return"";var n=new RegExp(/%(([a-z0-9_-]+)\(([^)]*)\)|[^\s]+)%/,"giu");return t.replace(" %page%","").replace("%sep% %sep%","%sep%").replace(n,(function(t){return r.replace(e,t)})).trim()}},{key:"replace",value:function(t,e){var r=e.toLowerCase().slice(1,-1);return["term_description","user_description"].includes(r)?"undefined"!=typeof tinymce&&void 0!==tinymce.editors.rank_math_description_editor?tinymce.editors.rank_math_description_editor.getContent():a()("#description").val():r.includes("customfield(")?(r=r.replace("customfield(","").replace(")",""))in rankMath.customFields?rankMath.customFields[r]:"":(t=t||this.getMap(),(r="seo_description"===(r="seo_title"===(r=r.includes("(")?r.split("(")[0]:r)?"title":r)?"excerpt":r)in t?t[r]:"")}},{key:"getMap",value:function(){var t=this;return null!==this.map||(this.map={},a().each(rankMath.variables,(function(e,r){e=e.toLowerCase().replace(/%+/g,"").split("(")[0],t.map[e]=r.example}))),this.map}},{key:"setVariable",value:function(t,e){null!==this.map?this.map[t]=e:void 0!==rankMath.variables[t]&&(rankMath.variables[t].example=e)}}])&&h(e.prototype,r),n&&h(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}());function b(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return e=(0,p.applyFilters)("rank_math_sanitize_data",e,t,r),null!==n&&(n=(0,p.applyFilters)("rank_math_sanitize_meta_value",n,t,r)),n=null===n?e:n,(0,p.doAction)("rank_math_data_changed",t,e,r),{type:"RANK_MATH_APP_DATA",key:t,value:e,metaKey:r,metaValue:n}}function v(t,e){return(0,p.doAction)("rank_math_update_app_ui",t,e),{type:"RANK_MATH_APP_UI",key:t,value:e}}function w(t){return g.setVariable("focuskw",t.split(",")[0]),rankMathEditor.refresh("keyword"),b("keywords",t,"rank_math_focus_keyword")}function k(t){return b("pillarContent",t,"rank_math_pillar_content",!0===t?"on":"off")}function S(t){return b("showScoreFrontend",t,"rank_math_dont_show_seo_score",!0===t?"off":"on")}function _(t){return b("score",t,"rank_math_seo_score")}function P(t){return b("canonicalUrl",t,"rank_math_canonical_url")}function T(t){return b("advancedRobots",t,"rank_math_advanced_robots")}function D(t){return b("robots",t,"rank_math_robots",Object.keys(t))}function O(t){return b("breadcrumbTitle",t,"rank_math_breadcrumb_title")}function I(t){return b("lockModifiedDate",t,"rank_math_lock_modified_date")}function E(){return b("dirtyMetadata",{})}function A(t){return{type:"RESET_STORE",value:t}}function j(t){return b("facebookTitle",t,"rank_math_facebook_title")}function M(t){return b("facebookDescription",t,"rank_math_facebook_description")}function R(t){return b("facebookImage",t,"rank_math_facebook_image")}function C(t){return b("facebookImageID",t,"rank_math_facebook_image_id")}function U(t){return b("facebookHasOverlay",t,"rank_math_facebook_enable_image_overlay",!0===t?"on":"off")}function N(t){return b("facebookImageOverlay",t,"rank_math_facebook_image_overlay")}function F(t){return rankMath.objectID=t,b("postID",t)}function x(t){return b("permalink",t,"permalink")}function K(t){return b("title",t,"rank_math_title")}function L(t){return b("description",t,"rank_math_description")}function H(t){return b("featuredImage",t)}function z(t,e){return b("primaryTerm",parseInt(t),"rank_math_primary_"+e)}function G(t,e){return b(t,e)}function B(t){return v("redirectionItem",t)}function V(){return v("redirectionItem",{})}function q(t){return v("hasRedirect",t)}var W=function(t){return t.replace(/<\/?[a-z][^>]*?>/gi,"\n")},$=function(t){return t.replace(/<style[^>]*>([\S\s]*?)<\/style>/gim,"")},J=function(t){return t.replace(/&nbsp;|&#160;/gi," ").replace(/\s{2,}/g," ").replace(/\s\./g,".").replace(/^\s+|\s+$/g,"")},Q=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return t.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,e)},X=function(t){return t.replace(/[‘’‛`]/g,"'").replace(/[“”〝〞〟‟„]/g,'"')},Y=function(t){return t.replace(/<!--[\s\S]*?-->/g,"")},Z=function(t){return t.replace(/&\S+?;/g,"")};function tt(t){return(0,o.isUndefined)(t)||!t?"":(0,o.flow)([$,Q,W,Y,Z,J,X])(t)}var et=wp.autop,rt="[^<>&/\\[\\]\0- =]+?",nt=new RegExp("\\["+rt+"( [^\\]]+?)?\\]","g"),it=new RegExp("\\[/"+rt+"\\]","g"),at=function(t){return t.replace(nt,"").replace(it,"")},ot=function(t,e){var r=function(t,e){for(var r,n=/<p(?:[^>]+)?>(.*?)<\/p>/gi,i=[];null!==(r=n.exec(t));)i.push(r);return(0,o.map)(i,(function(t){return e?tt(t[1]):t[1]}))}(t=(0,o.flow)([at,Y,et.autop])(t),e=e||!1);return 0<r.length?r:[e?tt(t):t]},ut=document.createElement("div");function ct(t){return t&&"string"==typeof t&&(t=t.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,"").replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gim,""),ut.innerHTML=t,t=ut.textContent,ut.textContent=""),t}var st=function(t,e){return t=(t=tt(t)).replace(/\r?\n|\r/g," "),e?(0,o.truncate)(t,{length:e,separator:" "}):t},lt=function(t){if((0,o.isEmpty)(t))return"";t=Q(t),t=$(t),t=(0,o.unescape)(t).replace(/\[caption[^\]]*\](.*)\[\/caption\]/g,"");var e=(0,o.filter)(ot(t,!0),(function(t){return""!==t.trim()}));if(!e.length)return"";var r=rankMathEditor.getPrimaryKeyword();if(""!==r){var n=(0,o.filter)(e,(function(t){return(0,o.includes)(t.toLowerCase(),r.toLowerCase())}));if(0<n.length)return st(n[0],160)}return st(e[0],160)};function pt(t){return t=g.swap(""!==t?t:rankMath.assessor.serpData.titleTemplate),rankMathEditor.refresh("title"),v("serpTitle",ct(t))}function ft(t){return t=""!==t?t:rankMathEditor.assessor.dataCollector.getSlug(),rankMathEditor.refresh("permalink"),v("serpSlug",t)}function dt(t){return t=g.swap(function(t){var e=rankMathEditor.assessor.dataCollector.getData(),r=e.excerpt,n=lt(e.content),i=(0,o.isUndefined)(r)||(0,o.isEmpty)(r)?n:(0,o.unescape)(r);if(g.setVariable("excerpt",i),g.setVariable("seo_description",i),""!==(t=ct((0,p.applyFilters)("rankMath/description",t))))return tt(t);if(!(0,o.isUndefined)(r)&&!(0,o.isEmpty)(r))return tt(r);var a=(0,o.unescape)(rankMath.assessor.serpData.descriptionTemplate);return(0,o.isUndefined)(a)||""===a?n:tt(a)}(t)),rankMathEditor.refresh("description"),v("serpDescription",t)}function mt(t){return v("isSnippetEditorOpen",t)}function ht(t){return v("snippetPreviewType",t)}function yt(t){return b("twitterUseFacebook",t,"rank_math_twitter_use_facebook",!0===t?"on":"off")}function gt(t){return b("twitterCardType",t,"rank_math_twitter_card_type")}function bt(t){return b("twitterTitle",t,"rank_math_twitter_title")}function vt(t){return b("twitterDescription",t,"rank_math_twitter_description")}function wt(t){return b("twitterAuthor",t,"rank_math_twitter_author")}function kt(t){return b("twitterImageID",t,"rank_math_twitter_image_id")}function St(t){return b("twitterImage",t,"rank_math_twitter_image")}function _t(t){return b("twitterHasOverlay",t,"rank_math_twitter_enable_image_overlay",!0===t?"on":"off")}function Pt(t){return b("twitterImageOverlay",t,"rank_math_twitter_image_overlay")}function Tt(t){return b("twitterPlayerUrl",t,"rank_math_twitter_player_url")}function Dt(t){return b("twitterPlayerSize",t,"rank_math_twitter_player_size")}function Ot(t){return b("twitterPlayerStream",t,"rank_math_twitter_player_stream")}function It(t){return b("twitterPlayerStreamCtype",t,"rank_math_twitter_player_stream_ctype")}function Et(t){return b("twitterAppDescription",t,"rank_math_twitter_app_description")}function At(t){return b("twitterAppIphoneID",t,"rank_math_twitter_app_iphone_id")}function jt(t){return b("twitterAppIphoneName",t,"rank_math_twitter_app_iphone_name")}function Mt(t){return b("twitterAppIphoneUrl",t,"rank_math_twitter_app_iphone_url")}function Rt(t){return b("twitterAppIpadID",t,"rank_math_twitter_app_ipad_id")}function Ct(t){return b("twitterAppIpadName",t,"rank_math_twitter_app_ipad_name")}function Ut(t){return b("twitterAppIpadUrl",t,"rank_math_twitter_app_ipad_url")}function Nt(t){return b("twitterAppGoogleplayID",t,"rank_math_twitter_app_googleplay_id")}function Ft(t){return b("twitterAppGoogleplayName",t,"rank_math_twitter_app_googleplay_name")}function xt(t){return b("twitterAppGoogleplayUrl",t,"rank_math_twitter_app_googleplay_url")}function Kt(t){return b("twitterAppCountry",t,"rank_math_twitter_app_country")}function Lt(t){return v("isLoaded",t)}function Ht(t){return v("selectedKeyword",t)}function zt(t){return v("socialTab",t)}function Gt(){return v("refreshResults",Date.now())}function Bt(){return v("isPro",!0)}function Vt(t){return v("isDiviPageSettingsBarActive",t)}function qt(t){return v("isDiviRankMathModalActive",t)}function Wt(t){return v("highlightedParagraphs",t)}var $t=wp.apiFetch,Jt=r.n($t);function Qt(t){return Qt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qt(t)}function Xt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Yt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Xt(Object(r),!0).forEach((function(e){Zt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Xt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Zt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Qt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Qt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Qt(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function te(t){return v("isSchemaEditorOpen",t)}function ee(t){return v("isSchemaTemplatesOpen",t)}function re(t){return v("schemaUpdated",t)}function ne(t){return v("editingSchemaId",t)}function ie(t){return v("editorTab",t)}function ae(t){return v("templateTab",t)}function oe(t){return v("editSchemas",t)}function ue(t){return b("schemas",t)}function ce(t,e){var r=Yt({},(0,c.select)("rank-math").getEditSchemas());return r[t]=e,v("editSchemas",r)}function se(t,e){var r=Yt({},(0,c.select)("rank-math").getSchemas());return r[t]=e,b("schemas",r)}function le(t){var e=Yt({},(0,c.select)("rank-math").getSchemas());return delete e[t],(0,p.doAction)("rank_math_schema_trash",t),b("schemas",e,"rank_math_delete_"+t,"")}function pe(t,e,r){return Jt()({method:"POST",path:"rankmath/v1/saveTemplate",data:{schema:t,postId:r}}).then((function(r){e({loading:!1,showNotice:!0,postId:r.id}),setTimeout((function(){e({showNotice:!1}),(0,o.get)(rankMath,"isTemplateScreen",!1)&&(document.title=(0,u.__)("Edit Schema","rank-math"),window.history.pushState(null,"",r.link.replace(/&amp;/g,"&")))}),2e3),rankMath.schemaTemplates.push({schema:t,title:t.metadata.title,type:t["@type"]})})),e({loading:!0}),{type:"DONT_WANT_TO_DO_SOMETHING"}}function fe(t){return b("contentAIScore",t,"rank_math_contentai_score",t)}function de(t){return de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},de(t)}function me(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function he(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?me(Object(r),!0).forEach((function(e){ye(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):me(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ye(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==de(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==de(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===de(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var ge=function(t){var e=t.assessor.serpData,r=t.assessor.hasRedirection;return{postID:null,title:e.title?e.title:e.titleTemplate,description:e.description,keywords:e.focusKeywords?e.focusKeywords:"",pillarContent:e.pillarContent,featuredImage:"",permalink:!1,primaryTerm:e.primaryTerm,robots:e.robots,advancedRobots:e.advancedRobots,canonicalUrl:e.canonicalUrl,breadcrumbTitle:e.breadcrumbTitle,showScoreFrontend:e.showScoreFrontend,lockModifiedDate:e.lockModifiedDate,redirectionID:r?(0,o.get)(t.assessor,"redirection.id",""):"",redirectionType:r?(0,o.get)(t.assessor,"redirection.header_code",""):"",redirectionUrl:r?(0,o.get)(t.assessor,"redirection.url_to",""):"",facebookTitle:e.facebookTitle,facebookImage:e.facebookImage,facebookImageID:e.facebookImageID,facebookAuthor:e.facebookAuthor,facebookDescription:e.facebookDescription,facebookHasOverlay:e.facebookHasOverlay,facebookImageOverlay:e.facebookImageOverlay,twitterTitle:e.twitterTitle,twitterImage:e.twitterImage,twitterAuthor:e.twitterAuthor,twitterImageID:e.twitterImageID,twitterCardType:e.twitterCardType,twitterUseFacebook:e.twitterUseFacebook,twitterDescription:e.twitterDescription,twitterHasOverlay:e.twitterHasOverlay,twitterImageOverlay:e.twitterImageOverlay,twitterPlayerUrl:e.twitterPlayerUrl,twitterPlayerSize:e.twitterPlayerSize,twitterPlayerStream:e.twitterPlayerStream,twitterPlayerStreamCtype:e.twitterPlayerStreamCtype,twitterAppDescription:e.twitterAppDescription,twitterAppIphoneName:e.twitterAppIphoneName,twitterAppIphoneID:e.twitterAppIphoneID,twitterAppIphoneUrl:e.twitterAppIphoneUrl,twitterAppIpadName:e.twitterAppIpadName,twitterAppIpadID:e.twitterAppIpadID,twitterAppIpadUrl:e.twitterAppIpadUrl,twitterAppGoogleplayName:e.twitterAppGoogleplayName,twitterAppGoogleplayID:e.twitterAppGoogleplayID,twitterAppGoogleplayUrl:e.twitterAppGoogleplayUrl,twitterAppCountry:e.twitterAppCountry,schemas:(0,o.get)(t,"schemas",{}),score:0,dirtyMetadata:{}}};function be(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ge(rankMath),e=arguments.length>1?arguments[1]:void 0,r=he({},t.dirtyMetadata);return!1!==e.metaKey&&(r[e.metaKey]=e.metaValue),"RESET_STORE"===e.type?he({},ge(e.value)):"RANK_MATH_APP_DATA"===e.type?"dirtyMetadata"===e.key?he(he({},t),{},{dirtyMetadata:e.value}):he(he({},t),{},ye(ye({},e.key,e.value),"dirtyMetadata",r)):t}function ve(t){return ve="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ve(t)}function we(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ke(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?we(Object(r),!0).forEach((function(e){Se(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):we(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Se(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==ve(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==ve(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===ve(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var _e={isLoaded:!1,isPro:!1,selectedKeyword:{tag:"",index:0,data:{value:""}},hasRedirect:rankMath.assessor.hasRedirection&&!(0,o.isEmpty)((0,o.get)(rankMath.assessor,"redirection.id",""))&&!(0,o.isEmpty)((0,o.get)(rankMath.assessor,"redirection.url_to","")),serpTitle:"",serpSlug:"",serpDescription:(0,o.get)(rankMath.assessor,"serpData.description",""),isSnippetEditorOpen:!1,snippetPreviewType:"",refreshResults:"",redirectionItem:{},socialTab:"facebook",highlightedParagraphs:[],editorTab:"",templateTab:"",editSchemas:{},editingSchemaId:"",isSchemaEditorOpen:!1,isSchemaTemplatesOpen:!1,schemaUpdated:!1,isDiviRankMathModalActive:!1,isDiviPageSettingsBarActive:!1};function Pe(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_e,e=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===e.type?ke(ke({},t),{},Se({},e.key,e.value)):t}function Te(t){return t.appData}function De(t){return t.appData.dirtyMetadata}function Oe(t){return t.appData.score}function Ie(t){return t.appData.keywords}function Ee(t){return t.appData.pillarContent}function Ae(t){return t.appData.robots}function je(t){return t.appData.advancedRobots}function Me(t){return t.appData.canonicalUrl}function Re(t){return t.appData.breadcrumbTitle}function Ce(t){return"todo"}function Ue(t){return t.appData.showScoreFrontend}function Ne(t){return t.appData.lockModifiedDate}function Fe(t){return t.appUi.isLoaded}function xe(t){return t.appUi.selectedKeyword}function Ke(t){return t.appUi.socialTab}function Le(t){return t.appUi.refreshResults}function He(t){return t.appUi.isPro}function ze(t){return t.appUi.isDiviPageSettingsBarActive}function Ge(t){return t.appUi.isDiviRankMathModalActive}function Be(t){return t.appUi.highlightedParagraphs}function Ve(t){return t.appData.facebookTitle}function qe(t){return t.appData.facebookDescription}function We(t){return t.appData.facebookAuthor}function $e(t){return t.appData.facebookImageID}function Je(t){return t.appData.facebookImage}function Qe(t){return t.appData.facebookHasOverlay}function Xe(t){return""!==t.appData.facebookImageOverlay?t.appData.facebookImageOverlay:"play"}function Ye(t){return t.appData.postID}function Ze(t){return t.appData.title}function tr(t){return t.appData.permalink}function er(t){return t.appData.description}function rr(t){return t.appData.featuredImage}function nr(t){var e=t.appData.featuredImage;return'<img src="'.concat(e.source_url,'" alt="').concat(e.alt_text,'" >')}function ir(t){return t.appData.primaryTerm}function ar(t){return String(t.appData.redirectionID)}function or(t){return t.appData.redirectionType}function ur(t){return t.appData.redirectionUrl}function cr(t){return t.appUi.redirectionItem}function sr(t){return t.appUi.hasRedirect}var lr=wp.url;function pr(t){return ct(t.appUi.serpTitle)}function fr(t){return(0,lr.safeDecodeURIComponent)(t.appUi.serpSlug)}function dr(t){return t.appUi.serpDescription}function mr(t){return t.appUi.isSnippetEditorOpen}function hr(t){return t.appUi.snippetPreviewType}function yr(t){return t.appUi.isSchemaEditorOpen}function gr(t){return t.appUi.isSchemaTemplatesOpen}function br(t){return t.appUi.schemaUpdated}function vr(t){return t.appData.schemas}function wr(t){return t.appUi.editSchemas}function kr(t){return{id:t.appUi.editingSchemaId,data:t.appUi.editSchemas[t.appUi.editingSchemaId]}}function Sr(t){return t.appData.schemas[t.appUi.editingSchemaId]}function _r(t){return t.appUi.editorTab}function Pr(t){return t.appUi.templateTab}function Tr(t){return t.appData.twitterUseFacebook}function Dr(t){return t.appData.twitterCardType}function Or(t){return t.appData.twitterTitle}function Ir(t){return t.appData.twitterDescription}function Er(t){return t.appData.twitterAuthor}function Ar(t){return t.appData.twitterImageID}function jr(t){return t.appData.twitterImage}function Mr(t){return t.appData.twitterHasOverlay}function Rr(t){return""!==t.appData.twitterImageOverlay?t.appData.twitterImageOverlay:"play"}function Cr(t){return t.appData.twitterAppDescription}function Ur(t){return t.appData.twitterAppIphoneID}function Nr(t){return t.appData.twitterAppIphoneName}function Fr(t){return t.appData.twitterAppIphoneUrl}function xr(t){return t.appData.twitterAppIpadID}function Kr(t){return t.appData.twitterAppIpadName}function Lr(t){return t.appData.twitterAppIpadUrl}function Hr(t){return t.appData.twitterAppGoogleplayID}function zr(t){return t.appData.twitterAppGoogleplayName}function Gr(t){return t.appData.twitterAppGoogleplayUrl}function Br(t){return t.appData.twitterAppCountry}function Vr(t){return t.appData.twitterPlayerUrl}function qr(t){return t.appData.twitterPlayerSize}function Wr(t){return t.appData.twitterPlayerStream}function $r(t){return t.appData.twitterPlayerStreamCtype}var Jr=(0,c.registerStore)("rank-math",{reducer:(0,c.combineReducers)(e),selectors:n,actions:t});function Qr(){return Jr}var Xr=r(4184),Yr=r.n(Xr);function Zr(t){return 100<t?"bad-fk dark":80<t?"good-fk":50<t?"ok-fk":"bad-fk"}var tn=(0,c.withSelect)((function(t){var e=t("rank-math");return{score:e.getAnalysisScore(),isRefreshing:e.isRefreshing()}}))((function(t){var e=t.score;return wp.element.createElement("div",{className:"seo-score "+Zr(e)},wp.element.createElement("div",{className:"score-text"},e," / 100"))}));function en(t){return en="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},en(t)}function rn(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==en(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==en(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===en(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var nn=(0,c.withSelect)((function(t){var e=t("rank-math");return{isLoaded:e.isLoaded(),score:e.getAnalysisScore()}}))((function(t){var e=t.isLoaded,r=t.score,n=Yr()("rank-math-toolbar-score",rn(rn({},Zr(r),!0),"loading",!e));return wp.element.createElement("div",{className:n},wp.element.createElement("i",{className:"rm-icon rm-icon-rank-math"}),wp.element.createElement(tn,null))})),an=wp.components;function on(t){return on="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},on(t)}function un(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==on(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==on(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===on(a)?a:String(a)),n)}var i,a}function cn(t,e){return cn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},cn(t,e)}function sn(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pn(t);if(e){var i=pn(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===on(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return ln(t)}(this,r)}}function ln(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function pn(t){return pn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},pn(t)}var fn=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&cn(t,e)}(a,t);var e,r,n,i=sn(a);function a(){var t;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a),(t=i.apply(this,arguments)).onChange=t.onChange.bind(ln(t)),t.state={loading:!0,availableTerms:[],selectedTerms:[]},t}return e=a,(r=[{key:"componentDidMount",value:function(){this.fetchTerms()}},{key:"componentWillUnmount",value:function(){(0,o.invoke)(this.fetchRequest,["abort"])}},{key:"componentDidUpdate",value:function(t,e){if(t.selectedTermIds.length<this.props.selectedTermIds.length){var r=(0,o.difference)(this.props.selectedTermIds,t.selectedTermIds)[0];if(!this.termIsAvailable(r))return void this.fetchTerms()}t.selectedTermIds!==this.props.selectedTermIds&&this.updateSelectedTerms(this.state.availableTerms,this.props.selectedTermIds),e.selectedTerms!==this.state.selectedTerms&&this.handleSelectedTermsChange()}},{key:"termIsAvailable",value:function(t){return!!this.state.availableTerms.find((function(e){return e.id===t}))}},{key:"updateSelectedTerms",value:function(t,e){this.setState({selectedTerms:this.filterSelectedTerms(t,e)})}},{key:"handleSelectedTermsChange",value:function(){var t=this.state.selectedTerms,e=this.props.primaryTermID;t.find((function(t){return t.id===e}))||this.onChange(t.length?t[0].id:"")}},{key:"fetchTerms",value:function(){var t=this,e=this.props.taxonomy;e&&(this.fetchRequest=Jt()({path:(0,lr.addQueryArgs)("/wp/v2/".concat(e.rest_base),{per_page:-1,orderby:"count",order:"desc",_fields:"id,name"})}),this.fetchRequest.then((function(e){t.fetchRequest=null,t.setState({loading:!1,availableTerms:e,selectedTerms:t.filterSelectedTerms(e,t.props.selectedTermIds)})}),(function(e){"abort"!==e.statusText&&(t.fetchRequest=null,t.setState({loading:!1}))})))}},{key:"filterSelectedTerms",value:function(t,e){return t.filter((function(t){return e.includes(t.id)}))}},{key:"onChange",value:function(t){(0,c.dispatch)("rank-math").updatePrimaryTermID(t,this.props.taxonomy.slug)}},{key:"shouldComponentUpdate",value:function(t,e){return this.props.selectedTermIds!==t.selectedTermIds||this.props.primaryTermID!==t.primaryTermID||this.state.selectedTerms!==e.selectedTerms}},{key:"render",value:function(){return this.state.selectedTerms.length<2?null:this.state.loading?[wp.element.createElement(an.Spinner,{key:"spinner"}),wp.element.createElement("p",{key:"spinner-text"},"Loading")]:wp.element.createElement(an.SelectControl,{label:(0,u.__)("Select Primary Term","rank-math"),value:this.props.primaryTermID,options:this.state.selectedTerms.map((function(t){return{value:t.id,label:(0,o.unescape)(t.name)}})),onChange:this.onChange})}}])&&un(e.prototype,r),n&&un(e,n),Object.defineProperty(e,"prototype",{writable:!1}),a}(s.Component);function dn(t){return dn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dn(t)}function mn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==dn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==dn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===dn(a)?a:String(a)),n)}var i,a}function hn(t,e){return hn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},hn(t,e)}function yn(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=gn(t);if(e){var i=gn(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===dn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function gn(t){return gn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},gn(t)}var bn=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&hn(t,e)}(a,t);var e,r,n,i=yn(a);function a(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a),i.apply(this,arguments)}return e=a,r&&mn(e.prototype,r),n&&mn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(fn),vn=(0,c.withSelect)((function(t,e){var r=e.slug,n=t("core").getTaxonomy(r),i=t("core/editor").getEditedPostAttribute;return{taxonomy:n,selectedTermIds:n?i(n.rest_base):[],primaryTermID:t("rank-math").getPrimaryTermID()}}))(bn),wn=function(t){var e=t.TermComponent;return function(t){return!1!==rankMath.assessor.primaryTaxonomy&&t.slug===rankMath.assessor.primaryTaxonomy.name}(t)?wp.element.createElement(s.Fragment,null,wp.element.createElement(e,t),wp.element.createElement(an.PanelRow,{className:"rank-math-primary-term-picker"},wp.element.createElement(vn,t))):wp.element.createElement(e,t)},kn=function(){return!((0,o.isNil)(window.wp)||(0,o.isNil)(wp.data)||(0,o.isNil)(wp.data.select("core/editor"))||!window.document.body.classList.contains("block-editor-page")||!(0,o.isFunction)(wp.data.select("core/editor").getEditedPostAttribute))};function Sn(t){return Sn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sn(t)}function _n(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==Sn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Sn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===Sn(a)?a:String(a)),n)}var i,a}function Pn(t,e,r){return e&&_n(t.prototype,e),r&&_n(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}var Tn=Pn((function t(){if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),"post"===rankMath.objectType&&rankMath.postSettings.linkSuggestions){a().fn.extend({insertLink:function(t,e){var r=this[0],n="";if(r.selectionStart||"0"===r.selectionStart){var i=r.selectionStart,a=r.selectionEnd,o=r.scrollTop;n='<a href="'+t+'">'+r.value.substring(i,a)+"</a>",i===a&&(n='<a href="'+t+'">'+e+"</a>"),r.value=r.value.substring(0,i)+n+r.value.substring(a,r.value.length),r.focus(),r.selectionStart=i+n.length,r.selectionEnd=i+n.length,r.scrollTop=o}else n='<a href="'+t+'">'+e+"</a>",r.value+=n,r.focus()}});if("function"==typeof ClipboardJS&&a()(".suggestion-copy").on("click",(function(t){t.preventDefault(),new ClipboardJS(".suggestion-copy");var e=a()(this).parent().next(".suggestion-title"),r=e.html();e.text("Link Copied"),setTimeout((function(){e.html(r)}),1500)})),"object"!==("undefined"==typeof tinymce?"undefined":Sn(tinymce)))return!0;var e=null,r=null;a()(".suggestion-insert").on("click",(function(t){t.preventDefault();var n,i,o,u,s=a()(this);if(s.hasClass("clicked"))return!0;if(null!==tinymce.activeEditor&&!0!==tinymce.activeEditor.isHidden()&&"content"===tinymce.activeEditor.id){e=tinymce.activeEditor,r=a()(e.getBody());var l=e.selection.getContent()||"";if(r.find("a[data-mce-selected]").length){var p=(n="",i="",o=e.selection.getStart(),(u=e.dom.getParent(o,"a[href]"))||(i=e.selection.getContent({format:"raw"}))&&-1!==i.indexOf("</a>")&&((n=i.match(/href="([^">]+)"/))&&n[1]&&(u=e.$('a[href="'+n[1]+'"]',o)[0]),u&&e.selection.select(u)),u);e.dom.setAttribs(p,{href:s.data("url")}),a()(p).text()!==l&&e.insertContent(l)}else l.length?e.insertContent('<a href="'+s.data("url")+'">'+l+"</a>"):e.insertContent('<a href="'+s.data("url")+'">'+s.data("text")+"</a>")}else if(kn()){var f=(0,c.select)("core/block-editor").getSelectedBlock().clientId,d=(0,c.select)("core/block-editor").getSelectionStart().offset,m=(0,c.select)("core/block-editor").getSelectionEnd().offset;if(document.getSelection){var h=document.getSelection();if(h.rangeCount){var y=h.getRangeAt(0),g=a()("#block-"+f).text().substring(d,m),b=document.createElement("a");b.href=s.data("url"),b.innerText=""!==g?g:s.data("text"),y.deleteContents(),y.insertNode(b);var v=h.focusNode.innerHTML;(0,c.dispatch)("core/block-editor").updateBlock(f,{attributes:{content:v}})}}}var w=s.closest(".suggestion-item").find(".suggestion-title"),k=w.html();w.text("Link Inserted"),s.addClass("clicked"),setTimeout((function(){var t,e,r,n;w.html(k),s.removeClass("clicked"),!0===rankMath.postSettings.useFocusKeyword&&(e=s,r=(t=w).data("fkcount")||0,n=t.data("fk"),(r+=1)===n.length&&(r=0),t.find(">a").text(n[r]),t.data("fkcount",r),e.data("text",n[r]))}),1500)})),a()("#rank_math_metabox_link_suggestions").find("h2").append(a()("#rank-math-link-suggestions-tooltip").html())}})),Dn=function(){return!(0,o.isNull)(document.getElementById("site-editor"))},On=function(){return Dn()&&(0,o.endsWith)(wp.data.select("core/edit-site").getEditedPostId(),"//home")},In=rankMathAnalyzer,En={};(0,o.isUndefined)(rankMath.assessor)||(0,o.forEach)(rankMath.assessor.diacritics,(function(t,e){return En[e]=new RegExp(t,"g")}));var An=function(t){if((0,o.isUndefined)(t))return t;for(var e in En)t=t.replace(En[e],e);return t},jn={"&amp;":"&","&quot;":'"',"&#39;":"'"},Mn=/&(?:amp|quot|#(0+)?39);/g,Rn=RegExp(Mn.source);var Cn=function(t){return t&&Rn.test(t)?t.replace(Mn,(function(t){return jn[t]||"'"})):t||""};function Un(t){return Un="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(t)}function Nn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==Un(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Un(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===Un(a)?a:String(a)),n)}var i,a}var Fn=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.analyzer=new In.Analyzer({i18n:u,analyses:rankMath.assessor.researchesTests}),this.dataCollector=e,this.registerRefresh(),this.updateKeywordResult=this.updateKeywordResult.bind(this),this.sanitizeData=this.sanitizeData.bind(this),(0,p.addAction)("rankMath_analysis_keywordUsage_updated","rank-math",this.updateKeywordResult),(0,p.addFilter)("rank_math_sanitize_meta_value","rank-math",this.sanitizeData),(0,p.addFilter)("rank_math_sanitize_data","rank-math",this.sanitizeData)}var e,r,n;return e=t,r=[{key:"updateKeywordResult",value:function(t,e){rankMathEditor.resultManager.update(t,{keywordNotUsed:e}),t===this.getSelectedKeyword().toLowerCase()&&(0,c.dispatch)("rank-math").refreshResults()}},{key:"sanitizeData",value:function(t,e){return"schemas"===e||(0,o.isObject)(t)||(0,o.isEmpty)(t)?t:(r=t,(0,o.isUndefined)(r)?"":(0,o.flow)([$,Q,W,Y])(r));var r}},{key:"getPaper",value:function(t,e){var r=Qr().getState(),n=this.dataCollector.getData(),i=new In.Paper("",{locale:rankMath.localeFull});i.setTitle(r.appUi.serpTitle),i.setPermalink(n.slug),i.setDescription(r.appUi.serpDescription),i.setUrl(n.permalink),i.setText(Cn((0,p.applyFilters)("rank_math_content",n.content))),i.setKeyword(t),i.setKeywords(e),i.setSchema(r.appData.schemas),(0,o.isUndefined)(n.featuredImage)||(i.setThumbnail(n.featuredImage.source_url),i.setThumbnailAltText(An(n.featuredImage.alt_text)));var a=(0,c.select)("rank-math-content-ai");if(!(0,o.isEmpty)(a)){var u=a.getData(),s=a.getScore();i.setContentAI(s||!(0,o.isEmpty)(u.keyword))}return i}},{key:"registerRefresh",value:function(){var t=this;this.refresh=(0,o.debounce)((function(e){var r=Qr().getState();if(!1!==r.appUi.isLoaded){var n=r.appData.keywords.split(","),i=[];(0,p.doAction)("rank_math_"+e+"_refresh"),n.map((function(e,r){var a=t.getPaper(An(e),n),o=0===r?rankMath.assessor.researchesTests:t.filterTests(t.getSecondaryKeywordTests());i.push(t.analyzer.analyzeSome(o,a).then((function(t){rankMathEditor.resultManager.update(a.getKeyword(),t,0===r),0===r&&(0,c.dispatch)("rank-math").updateAnalysisScore(rankMathEditor.resultManager.getScore(a.getKeyword()))}))),Promise.all(i).then((function(){(0,c.dispatch)("rank-math").refreshResults()}))}))}}),500)}},{key:"getSecondaryKeywordTests",value:function(){return["keywordInContent","lengthContent","keywordInSubheadings","keywordDensity","lengthPermalink","linksHasExternals","linksNotAllExternals","linksHasInternal","titleSentiment","titleHasPowerWords","titleHasNumber","contentHasTOC","contentHasShortParagraphs","contentHasAssets"]}},{key:"getPrimaryKeyword",value:function(){var t=Qr().getState().appData.keywords;return t?An(t.split(",")[0]):""}},{key:"getSelectedKeyword",value:function(){var t=Qr().getState(),e=""!==t.appUi.selectedKeyword.data.value?t.appUi.selectedKeyword.data.value:t.appData.keywords.split(",")[0];return An(e)}},{key:"getResearch",value:function(t){return this.analyzer.researcher.getResearch(t)}},{key:"filterTests",value:function(t){return(0,o.intersection)(t,rankMath.assessor.researchesTests)}}],r&&Nn(e.prototype,r),n&&Nn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),xn=Fn;function Kn(t){return Kn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kn(t)}function Ln(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==Kn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Kn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===Kn(a)?a:String(a)),n)}var i,a}var Hn=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),(rankMath.is_front_page||On())&&((0,p.addFilter)("rankMath_analysis_contentLength","rank-math",this.contentLength),(0,p.addFilter)("rankMath_analysis_contentLength_boundaries","rank-math",this.contentLengthBoundary))}var e,r,n;return e=t,(r=[{key:"contentLength",value:function(t){return{hasScore:t.hasScore,failed:(0,u.__)("Content is %1$d words long. Consider using at least 300 words.","rank-math"),tooltipText:(0,u.__)("Minimum recommended content length should be 300 words.","rank-math"),emptyContent:(0,u.sprintf)((0,u.__)("Content should be %1$s long.","rank-math"),'<a href="https://rankmath.com/kb/score-100-in-tests/?utm_source=Plugin&utm_campaign=WP#overall-content-length" target="_blank">'+(0,u.__)("300 words","rank-math")+"</a>")}}},{key:"contentLengthBoundary",value:function(){return{recommended:{boundary:299,score:8},belowRecommended:{boundary:200,score:5},low:{boundary:50,score:2}}}}])&&Ln(e.prototype,r),n&&Ln(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();function zn(t){return zn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zn(t)}function Gn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==zn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==zn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===zn(a)?a:String(a)),n)}var i,a}var Bn=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,r,n;return e=t,(r=[{key:"setup",value:function(t){Qr(),this.resultManager=new In.ResultManager,this.assessor=new xn(t),new Hn,(0,p.doAction)("rank_math_loaded")}},{key:"refresh",value:function(t){this.assessor.refresh(t)}},{key:"getPrimaryKeyword",value:function(){return this.assessor.getPrimaryKeyword()}},{key:"getSelectedKeyword",value:function(){return this.assessor.getSelectedKeyword()}},{key:"updatePermalink",value:function(t){throw"Implement the function"}},{key:"updatePermalinkSanitize",value:function(t){throw"Implement the function"}}])&&Gn(e.prototype,r),n&&Gn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();function Vn(t){return Vn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vn(t)}function qn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Wn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?qn(Object(r),!0).forEach((function(e){$n(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):qn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function $n(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Vn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Vn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Vn(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Jn=(0,wp.compose.compose)((0,c.withSelect)((function(t){return{lock:t("rank-math").isModifiedDateLocked()}})),(0,c.withDispatch)((function(t){return{onChange:function(e){t("rank-math").lockModifiedDate(e);var r=wp.data.select("core/editor");if(!(0,o.isUndefined)(r)&&!(0,o.isUndefined)(r.getEditedPostAttribute("meta"))){var n=Wn(Wn({},r.getEditedPostAttribute("meta")),{},{rank_math_lock_modified_date:e});t("core/editor").editPost({meta:n})}}}})))((function(t){var e=t.lock,r=t.onChange;return wp.element.createElement("div",null,wp.element.createElement(an.ToggleControl,{label:(0,u.__)("Lock Modified Date","rank-math"),checked:e,onChange:function(t){return r(t)}}))}));function Qn(t){return Qn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qn(t)}function Xn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Yn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Xn(Object(r),!0).forEach((function(e){ti(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Xn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Zn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ei(n.key),n)}}function ti(t,e,r){return(e=ei(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ei(t){var e=function(t,e){if("object"!==Qn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Qn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Qn(e)?e:String(e)}var ri=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),ti(this,"initialize",!1),ti(this,"isSavingRedirection",!1),ti(this,"isSavingSchemas",!1),ti(this,"oldSlug",!1),this._data={id:!1,slug:!1,permalink:!1,content:!1,title:!1,excerpt:!1,featuredImage:!1},this.refresh=this.refresh.bind(this),this.isSavingPost=this.isSavingPost.bind(this),this.getPostAttribute=this.getPostAttribute.bind(this),this.subscribeToGutenberg(),this.lockModifiedDate()}var e,r,n;return e=t,(r=[{key:"lockModifiedDate",value:function(){rankMath.showLockModifiedDate&&(0,l.registerPlugin)("rank-math-lock-last-modified",{render:function(){return wp.element.createElement(f.PluginPostStatusInfo,null,wp.element.createElement(Jn,null))}})}},{key:"collectGutenbergData",value:function(){return kn()?(this._coreEditorSelect||(this._coreEditorSelect=(0,c.select)("core/editor")),!1===this.oldSlug&&""!==this.getSlug()&&(this.oldSlug=this.getSlug()),{id:this.getPostID(),slug:this.getSlug(),permalink:this.getPermalink(),content:this.getPostAttribute("content"),title:this.getPostAttribute("title"),excerpt:this.getPostAttribute("excerpt"),featuredImage:this.getFeaturedImage()}):{}}},{key:"getPostID",value:function(){return this._coreEditorSelect.getCurrentPostId()}},{key:"getPermalink",value:function(){if(On())return rankMath.homeUrl;if("auto-draft"===this.getPostAttribute("status"))return"";var t=this.getPostAttribute("generated_slug");return"auto-draft"!==t&&"en"===rankMath.locale||(t=""),(0,lr.safeDecodeURIComponent)(this._coreEditorSelect.getPermalink())}},{key:"getSlug",value:function(){var t=this.getPostAttribute("generated_slug");return"auto-draft"!==t&&"en"===rankMath.locale||(t=""),(0,lr.safeDecodeURIComponent)(this.getPostAttribute("slug")||t)}},{key:"getFeaturedImage",value:function(){var t=this.getPostAttribute("featured_media");if(this.isValidMediaId(t)){var e=(0,c.select)("core").getMedia(t);if(!(0,o.isUndefined)(e))return e}}},{key:"isValidMediaId",value:function(t){return"number"==typeof t&&0<t}},{key:"getPostAttribute",value:function(t){return kn()?(this._coreEditorSelect||(this._coreEditorSelect=(0,c.select)("core/editor")),this._coreEditorSelect.getEditedPostAttribute(t)):""}},{key:"subscribeToGutenberg",value:function(){this.subscriber=(0,o.debounce)(this.refresh,500),(0,c.subscribe)(this.subscriber),(0,c.subscribe)(this.isSavingPost)}},{key:"refresh",value:function(){var t=Yn({},this._data);this._data=this.collectGutenbergData(),this.handleEditorChange(t)}},{key:"isSavingPost",value:function(){var t=(0,c.select)("core/editor");if(!t.isAutosavingPost()&&t.isSavingPost()){var e=(0,c.select)("rank-math").getDirtyMetadata();(0,o.isEmpty)(e)||(Jt()({method:"POST",path:"rankmath/v1/updateMeta",data:{objectID:rankMath.objectID,objectType:rankMath.objectType,meta:e,content:(0,p.applyFilters)("rank_math_content",this.getPostAttribute("content"))}}).then((function(t){(0,p.doAction)("rank_math_metadata_updated",t)})),(0,c.dispatch)("rank-math").resetDirtyMetadata()),"publish"===this.getPostAttribute("status")&&(this.saveRedirection(),this.autoCreateRedirectionNotice()),this.saveSchemas()}}},{key:"saveSchemas",value:function(){var t=this;if(!this.isSavingSchemas){var e=(0,c.select)("rank-math").getSchemas();if(!(0,o.isEmpty)(e)&&(0,c.select)("rank-math").hasSchemaUpdated()){this.isSavingSchemas=!0;var r=(0,c.select)("rank-math").getEditSchemas();Jt()({method:"POST",path:"rankmath/v1/updateSchemas",data:{objectID:this.getPostID(),objectType:rankMath.objectType,schemas:e}}).then((function(n){var i=Yn({},e),a=Yn({},r);(0,o.isEmpty)(n)||(0,o.map)(n,(function(t,e){i["schema-"+t]=Yn({},i[e]),a["schema-"+t]=Yn({},a[e]),delete i[e],delete a[e]})),(0,c.dispatch)("rank-math").updateSchemas(i),(0,c.dispatch)("rank-math").updateEditSchemas(a),setTimeout((function(){(0,c.dispatch)("rank-math").schemaUpdated(!1),(0,p.doAction)("rank_math_schema_changed"),t.isSavingSchemas=!1}),2e3)}))}}}},{key:"saveRedirection",value:function(){var t=this;if(!this.isSavingRedirection){var e=(0,c.select)("rank-math").getRedirectionItem();if(!(0,o.isEmpty)(e)){this.isSavingRedirection=!0,e.objectID=this.getPostID(),e.redirectionSources=this.getPermalink();var r=(0,c.dispatch)("rank-math"),n=(0,c.dispatch)("core/notices");r.resetRedirection(),Jt()({method:"POST",path:"rankmath/v1/updateRedirection",data:e}).then((function(e){"delete"===e.action?(n.createInfoNotice(e.message,{id:"redirectionNotice"}),r.updateRedirection("redirectionID",0)):"update"===e.action?n.createInfoNotice(e.message,{id:"redirectionNotice"}):"new"===e.action&&(r.updateRedirection("redirectionID",e.id),n.createSuccessNotice(e.message,{id:"redirectionNotice"})),setTimeout((function(){t.isSavingRedirection=!1,n.removeNotice("redirectionNotice")}),2e3)}))}}}},{key:"autoCreateRedirectionNotice",value:function(){if(rankMath.assessor.hasRedirection&&(0,o.get)(rankMath,["assessor","autoCreateRedirection"],!1)&&!(0,c.select)("core/editor").isEditedPostNew()&&!1!==this.oldSlug){var t=this.getSlug();if(this.oldSlug!==t){var e=(0,c.dispatch)("core/notices");this.oldSlug=t,e.createSuccessNotice((0,u.__)("Auto redirection created.","rank-math"),{id:"redirectionAutoCreationNotice"}),setTimeout((function(){e.removeNotice("redirectionAutoCreationNotice")}),2e3)}}}},{key:"handleEditorChange",value:function(t){var e=this,r={id:"handleIDChange",slug:"handleSlugChange",title:"handleTitleChange",excerpt:"handleExcerptChange",content:"handleContentChange",featuredImage:"handleFeaturedImageChange"};if(t.id)return this.initialize?void(0,o.forEach)(r,(function(r,n){e._data[n]!==t[n]&&e[r](e._data[n])})):(this.initialize=!0,(0,o.forEach)(r,(function(t,r){e[t](e._data[r])})),void rankMathEditor.refresh("init"));(0,c.dispatch)("rank-math").refreshResults()}},{key:"handleIDChange",value:function(t){On()&&(t=0),(0,c.dispatch)("rank-math").updatePostID(t),(0,c.dispatch)("rank-math").toggleLoaded(!0),(0,p.doAction)("rank_math_id_changed")}},{key:"handleSlugChange",value:function(){""!==this.getSlug()&&!1===this.oldSlug&&(this.oldSlug=this.getSlug()),rankMathEditor.refresh("permalink")}},{key:"handleTitleChange",value:function(t){g.setVariable("title",t),g.setVariable("term",t),g.setVariable("author",t),(0,c.dispatch)("rank-math").updateSerpTitle((0,c.select)("rank-math").getTitle()),rankMathEditor.refresh("title")}},{key:"handleExcerptChange",value:function(t){g.setVariable("excerpt",t),g.setVariable("excerpt_only",t),g.setVariable("wc_shortdesc",t),g.setVariable("seo_description",t),(0,c.dispatch)("rank-math").updateSerpDescription((0,c.select)("rank-math").getDescription())}},{key:"handleFeaturedImageChange",value:function(t){(0,c.dispatch)("rank-math").updateFeaturedImage(t),rankMathEditor.refresh("featuredImage")}},{key:"handleContentChange",value:function(){(0,c.dispatch)("rank-math").updateSerpDescription((0,c.select)("rank-math").getDescription()),rankMathEditor.refresh("content")}},{key:"getData",value:function(){return(0,p.applyFilters)("rank_math_dataCollector_data",this._data)}}])&&Zn(e.prototype,r),n&&Zn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),ni=wp.blocks,ii=[],ai=function(t){return(0,o.isEmpty)(ii)||(0,o.forEach)(ii,(function(e){(0,o.forEach)(e,(function(e){t+=e}))})),t},oi=function(){var t=(0,c.select)("core"),e=(0,c.select)("core/block-editor");(0,p.addFilter)("rank_math_content","rank-math",ai,11),(0,c.subscribe)((0,o.debounce)((function(){var r=e.getBlocks();if(!(0,o.isEmpty)(r)){var n=!1;(0,o.forEach)(r,(function(e){if((0,ni.isReusableBlock)(e)){var r=e.attributes.ref,i=function(t){return t&&t.content?(0,o.isFunction)(t.content)?t.content(t):t.content:""}(t.getEditedEntityRecord("postType","wp_block",r));if(!ii[r])return ii[r]={id:r,clientId:e.clientId,content:i},void(n=!0);ii[r].content!==i&&(ii[r].content=i,n=!0)}})),n&&rankMathEditor.refresh("content")}}),500))};function ui(t){return ui="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ui(t)}function ci(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,si(n.key),n)}}function si(t){var e=function(t,e){if("object"!==ui(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==ui(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===ui(e)?e:String(e)}var li=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),(0,p.addAction)("rank_math_id_changed","rank-math",this.resetStore),(0,p.addAction)("rank_math_data_changed","rank-math",this.activateButton)}var e,r,n;return e=t,(r=[{key:"resetStore",value:function(){var t=(0,c.select)("core/editor").getCurrentPost(),e=(0,o.isEmpty)(t.rankMath)?rankMath:t.rankMath;On()&&(e.assessor=rankMath.homepageData.assessor),(0,c.dispatch)("rank-math").resetStore(e),On()&&(0,c.dispatch)("rank-math").toggleLoaded(!0)}},{key:"activateButton",value:function(t,e,r){(0,o.startsWith)(r,"rank_math")&&(0,c.dispatch)("core/editor").editPost(function(t,e,r){return(e=si(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}({},r,e))}}])&&ci(e.prototype,r),n&&ci(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),pi=(0,c.withSelect)((function(t){var e=t("rank-math"),r=e.getTwitterUseFacebook()?e.getFacebookTitle():e.getTwitterTitle();return(0,o.isEmpty)(r)&&(r=e.getSerpTitle()),{title:r,permalink:t("core/editor").getPermalink()}}))((function(t){var e=t.title,r=t.permalink,n=encodeURI(r),i=encodeURI(e);return wp.element.createElement(s.Fragment,null,wp.element.createElement("p",null,(0,u.__)("Notify your readers by sharing!","rank-math")),wp.element.createElement("div",{className:"rank-math-social-share-buttons"},wp.element.createElement("div",{className:"rank-math-share-button rm-facebook"},wp.element.createElement("a",{href:"https://www.facebook.com/sharer/sharer.php?u="+n,target:"_blank",rel:"noopener noreferrer"},wp.element.createElement(an.Dashicon,{icon:"facebook-alt"}))),wp.element.createElement("div",{className:"rank-math-share-button rm-twitter"},wp.element.createElement("a",{href:"https://twitter.com/share?url="+n+"&text="+i,target:"_blank",rel:"noopener noreferrer"},wp.element.createElement(an.Dashicon,{icon:"twitter"}))),wp.element.createElement("div",{className:"rank-math-share-button rm-email"},wp.element.createElement("a",{href:"mailto:?subject="+i+"&body="+n,target:"_blank",rel:"noopener noreferrer"},wp.element.createElement(an.Dashicon,{icon:"email"})))))})),fi=(0,an.createSlotFill)("RankMathAfterEditor"),di=fi.Fill,mi=fi.Slot,hi=function(t){var e=t.children,r=t.className;return wp.element.createElement(di,null,wp.element.createElement(an.PanelRow,{className:r},e))};hi.Slot=mi;var yi=hi,gi=(0,an.createSlotFill)("RankMathAdvancedTab"),bi=gi.Fill,vi=gi.Slot,wi=function(t){var e=t.children,r=t.className;return wp.element.createElement(bi,null,wp.element.createElement(an.PanelRow,{className:r},e))};wi.Slot=vi;var ki=wi,Si=(0,an.createSlotFill)("RankMathAfterFocusKeyword"),_i=Si.Fill,Pi=Si.Slot,Ti=function(t){var e=t.children,r=t.className;return wp.element.createElement(_i,null,wp.element.createElement("div",{className:r},e))};Ti.Slot=Pi;var Di=Ti;function Oi(t){return Oi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Oi(t)}function Ii(){return Ii=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ii.apply(this,arguments)}function Ei(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=n.key,a=void 0,a=function(t,e){if("object"!==Oi(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Oi(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===Oi(a)?a:String(a)),n)}var i,a}function Ai(){return Ai="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=Ri(t)););return t}(t,e);if(n){var i=Object.getOwnPropertyDescriptor(n,e);return i.get?i.get.call(arguments.length<3?t:r):i.value}},Ai.apply(this,arguments)}function ji(t,e){return ji=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ji(t,e)}function Mi(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Ri(t);if(e){var i=Ri(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===Oi(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function Ri(t){return Ri=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ri(t)}var Ci=function(){var t=(0,c.useSelect)((function(t){return On()||!(0,o.isUndefined)(t("core/edit-site"))&&(0,o.endsWith)(t("core/edit-site").getEditedPostId(),"//page")}));return Dn()&&t?(On()&&(0,p.doAction)("rank_math_id_changed"),wp.element.createElement(s.Fragment,null,wp.element.createElement(d.PluginSidebarMoreMenuItem,{target:"seo-by-rank-math-sidebar",icon:wp.element.createElement(nn,null)},(0,u.__)("Rank Math","rank-math")),wp.element.createElement(d.PluginSidebar,{name:"seo-by-rank-math-sidebar",title:(0,u.__)("Rank Math","rank-math"),className:"rank-math-sidebar-panel"},(0,p.applyFilters)("rank_math_app",{})()))):wp.element.createElement(s.Fragment,null,wp.element.createElement(f.PluginSidebarMoreMenuItem,{target:"seo-by-rank-math-sidebar",icon:wp.element.createElement(nn,null)},(0,u.__)("Rank Math","rank-math")),wp.element.createElement(f.PluginSidebar,{name:"seo-by-rank-math-sidebar",title:(0,u.__)("Rank Math","rank-math"),className:"rank-math-sidebar-panel"},(0,p.applyFilters)("rank_math_app",{})()))},Ui=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ji(t,e)}(a,t);var e,r,n,i=Mi(a);function a(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a),i.apply(this,arguments)}return e=a,(r=[{key:"setup",value:function(t){Qr(),this.registerSlots=this.registerSlots.bind(this),(0,p.addAction)("rank_math_loaded","rank-math",this.registerSlots,0),Ai(Ri(a.prototype),"setup",this).call(this,t),this.registerPostPublish(),this.registerPrimaryTermSelector(),new Tn,new oi,Dn()&&new li,(0,p.addAction)("rank_math_data_changed","rank-math",this.toggleSettings)}},{key:"toggleSettings",value:function(t,e,r){r&&((0,c.select)("core/editor").isSavingPost()||(0,c.dispatch)("core/editor").editPost({meta:{refreshMe:"refreshUI"}}))}},{key:"registerSlots",value:function(){this.registerSidebar(),this.RankMathAfterEditor=yi,this.RankMathAfterFocusKeyword=Di,this.RankMathAdvancedTab=ki,this.slots={AfterEditor:yi,AfterFocusKeyword:Di,AdvancedTab:ki}}},{key:"registerSidebar",value:function(){(0,l.registerPlugin)("rank-math",{icon:wp.element.createElement(nn,null),render:Ci})}},{key:"registerPostPublish",value:function(){(0,l.registerPlugin)("rank-math-post-publish",{render:function(){return wp.element.createElement(f.PluginPostPublishPanel,{initialOpen:!0,title:(0,u.__)("Rank Math","rank-math"),className:"rank-math-post-publish",icon:wp.element.createElement(s.Fragment,null)},wp.element.createElement(pi,null))}})}},{key:"registerPrimaryTermSelector",value:function(){(0,p.addFilter)("editor.PostTaxonomyType","rank-math",(function(t){return function(e){return wp.element.createElement(wn,Ii({TermComponent:t},e))}}))}},{key:"updatePermalink",value:function(t){(0,c.dispatch)("core/editor").editPost({slug:t})}},{key:"updatePermalinkSanitize",value:function(t){t=this.assessor.getResearch("slugify")(t),(0,c.dispatch)("core/editor").editPost({slug:t})}}])&&Ei(e.prototype,r),n&&Ei(e,n),Object.defineProperty(e,"prototype",{writable:!1}),a}(Bn);a()(document).ready((function(){window.rankMathEditor=new Ui,window.rankMathGutenberg=window.rankMathEditor,window.rankMathEditor.setup(new ri)}))}()}();