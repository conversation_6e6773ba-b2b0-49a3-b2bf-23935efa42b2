!function(){var e={4184:function(e,t){var n;!function(){"use strict";var r={}.hasOwnProperty;function a(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var o=typeof n;if("string"===o||"number"===o)e.push(n);else if(Array.isArray(n)){if(n.length){var l=a.apply(null,n);l&&e.push(l)}}else if("object"===o){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var i in n)r.call(n,i)&&n[i]&&e.push(i)}}}return e.join(" ")}e.exports?(a.default=a,e.exports=a):void 0===(n=function(){return a}.apply(t,[]))||(e.exports=n)}()}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){"use strict";var e={};n.r(e),n.d(e,{resetStore:function(){return De},resetdirtySettings:function(){return xe},setStep:function(){return Ie},toggleLoaded:function(){return Me},updateModules:function(){return Ce},updateSettings:function(){return Re},updateView:function(){return Ue}});var t={};n.r(t),n.d(t,{appData:function(){return ze},appUi:function(){return Qe}});var r={};n.r(r),n.d(r,{getAppData:function(){return Xe},getCurrentStep:function(){return at},getModules:function(){return tt},getSettings:function(){return et},getView:function(){return rt},getdirtySettings:function(){return Ze},isLoaded:function(){return nt}});var a={};n.r(a),n.d(a,{updateViewData:function(){return ut}});var o={};n.r(o),n.d(o,{appUi:function(){return dt}});var l={};n.r(l),n.d(l,{getViewData:function(){return ht}});var i=lodash;var c,u=wp.element,s=React,m=ReactDOM;function p(){return p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},p.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(c||(c={}));const f="popstate";function d(e,t){if(!1===e||null==e)throw new Error(t)}function h(e,t){return{usr:e.state,key:e.key,idx:t}}function b(e,t,n,r){return void 0===n&&(n=null),p({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?w(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function y(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function w(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function v(e,t,n,r){void 0===r&&(r={});let{window:a=document.defaultView,v5Compat:o=!1}=r,l=a.history,i=c.Pop,u=null,s=m();function m(){return(l.state||{idx:null}).idx}function w(){i=c.Pop;let e=m(),t=null==e?null:e-s;s=e,u&&u({action:i,location:g.location,delta:t})}function v(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"==typeof e?e:y(e);return d(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==s&&(s=0,l.replaceState(p({},l.state,{idx:s}),""));let g={get action(){return i},get location(){return e(a,l)},listen(e){if(u)throw new Error("A history only accepts one active listener");return a.addEventListener(f,w),u=e,()=>{a.removeEventListener(f,w),u=null}},createHref(e){return t(a,e)},createURL:v,encodeLocation(e){let t=v(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){i=c.Push;let r=b(g.location,e,t);n&&n(r,e),s=m()+1;let p=h(r,s),f=g.createHref(r);try{l.pushState(p,"",f)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;a.location.assign(f)}o&&u&&u({action:i,location:g.location,delta:1})},replace:function(e,t){i=c.Replace;let r=b(g.location,e,t);n&&n(r,e),s=m();let a=h(r,s),p=g.createHref(r);l.replaceState(a,"",p),o&&u&&u({action:i,location:g.location,delta:0})},go(e){return l.go(e)}};return g}var g;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(g||(g={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function k(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function _(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function E(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function S(e,t,n,r){let a;void 0===r&&(r=!1),"string"==typeof e?a=w(e):(a=p({},e),d(!a.pathname||!a.pathname.includes("?"),_("?","pathname","search",a)),d(!a.pathname||!a.pathname.includes("#"),_("#","pathname","hash",a)),d(!a.search||!a.search.includes("#"),_("#","search","hash",a)));let o,l=""===e||""===a.pathname,i=l?"/":a.pathname;if(null==i)o=n;else if(r){let e=t[t.length-1].replace(/^\//,"").split("/");if(i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e.pop();a.pathname=t.join("/")}o="/"+e.join("/")}else{let e=t.length-1;if(i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let c=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"==typeof e?w(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:P(r),hash:j(a)}}(a,o),u=i&&"/"!==i&&i.endsWith("/"),s=(l||"."===i)&&n.endsWith("/");return c.pathname.endsWith("/")||!u&&!s||(c.pathname+="/"),c}const O=e=>e.join("/").replace(/\/\/+/g,"/"),P=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",j=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;const N=["post","put","patch","delete"],A=(new Set(N),["get",...N]);new Set(A),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function T(){return T=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},T.apply(this,arguments)}const R=s.createContext(null);const C=s.createContext(null);const x=s.createContext(null);const D=s.createContext({outlet:null,matches:[],isDataRoute:!1});function M(){return null!=s.useContext(x)}function U(){return M()||d(!1),s.useContext(x).location}function I(e){s.useContext(C).static||s.useLayoutEffect(e)}function V(){let{isDataRoute:e}=s.useContext(D);return e?function(){let{router:e}=B(L.UseNavigateStable),t=H(F.UseNavigateStable),n=s.useRef(!1);return I((()=>{n.current=!0})),s.useCallback((function(r,a){void 0===a&&(a={}),n.current&&("number"==typeof r?e.navigate(r):e.navigate(r,T({fromRouteId:t},a)))}),[e,t])}():function(){M()||d(!1);let e=s.useContext(R),{basename:t,navigator:n}=s.useContext(C),{matches:r}=s.useContext(D),{pathname:a}=U(),o=JSON.stringify(E(r).map((e=>e.pathnameBase))),l=s.useRef(!1);return I((()=>{l.current=!0})),s.useCallback((function(r,i){if(void 0===i&&(i={}),!l.current)return;if("number"==typeof r)return void n.go(r);let c=S(r,JSON.parse(o),a,"path"===i.relative);null==e&&"/"!==t&&(c.pathname="/"===c.pathname?t:O([t,c.pathname])),(i.replace?n.replace:n.push)(c,i.state,i)}),[t,n,o,a,e])}()}s.Component;var L=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(L||{}),F=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(F||{});function B(e){let t=s.useContext(R);return t||d(!1),t}function H(e){let t=function(e){let t=s.useContext(D);return t||d(!1),t}(),n=t.matches[t.matches.length-1];return n.route.id||d(!1),n.route.id}s.startTransition;function W(e){let{basename:t="/",children:n=null,location:r,navigationType:a=c.Pop,navigator:o,static:l=!1}=e;M()&&d(!1);let i=t.replace(/^\/*/,"/"),u=s.useMemo((()=>({basename:i,navigator:o,static:l})),[i,o,l]);"string"==typeof r&&(r=w(r));let{pathname:m="/",search:p="",hash:f="",state:h=null,key:b="default"}=r,y=s.useMemo((()=>{let e=k(m,i);return null==e?null:{location:{pathname:e,search:p,hash:f,state:h,key:b},navigationType:a}}),[i,m,p,f,h,b,a]);return null==y?null:s.createElement(C.Provider,{value:u},s.createElement(x.Provider,{children:n,value:y}))}new Promise((()=>{}));s.Component;function K(e){return void 0===e&&(e=""),new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce(((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map((e=>[n,e])):[[n,r]])}),[]))}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);new Map;const z=s.startTransition;m.flushSync;function Y(e){let{basename:t,children:n,future:r,window:a}=e,o=s.useRef();var l;null==o.current&&(o.current=(void 0===(l={window:a,v5Compat:!0})&&(l={}),v((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return b("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:y(t)}),null,l)));let i=o.current,[c,u]=s.useState({action:i.action,location:i.location}),{v7_startTransition:m}=r||{},p=s.useCallback((e=>{m&&z?z((()=>u(e))):u(e)}),[u,m]);return s.useLayoutEffect((()=>i.listen(p)),[i,p]),s.createElement(W,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:i})}"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;var G,$;function J(e){let t=s.useRef(K(e)),n=s.useRef(!1),r=U(),a=s.useMemo((()=>function(e,t){let n=K(e);return t&&t.forEach(((e,r)=>{n.has(r)||t.getAll(r).forEach((e=>{n.append(r,e)}))})),n}(r.search,n.current?null:t.current)),[r.search]),o=V(),l=s.useCallback(((e,t)=>{const r=K("function"==typeof e?e(a):e);n.current=!0,o("?"+r,t)}),[o,a]);return[a,l]}(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(G||(G={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}($||($={}));var q=wp.i18n;function Q(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=rankMath.links[e]||"";if(!n)return"#";if(!t)return n;var r={utm_source:"Plugin",utm_medium:encodeURIComponent(t),utm_campaign:"WP"};return n+"?"+Object.keys(r).map((function(e){return"".concat(e,"=").concat(r[e])})).join("&")}var X=rankMath.isPro,Z=[{heading:(0,q.__)("Next steps…","rank-math"),items:[{id:X?"how-to-setup":"upgrade-to-pro",icon:X?"settings":"star-filled",title:X?(0,q.__)("Setup Rank Math","rank-math"):(0,q.__)("Upgrade to PRO","rank-math"),description:X?(0,q.__)("How to Properly Setup Rank Math","rank-math"):(0,q.__)("Advanced Schema, Analytics and much more…","rank-math")},{id:"seo-import",icon:"import",title:(0,q.__)("Import Data","rank-math"),description:(0,q.__)("How to Import Data from Your Previous SEO Plugin","rank-math")},{id:"score-100",icon:"post",title:(0,q.__)("Improve SEO Score","rank-math"),description:(0,q.__)("How to Make Your Posts Pass All the Tests","rank-math")}]},{heading:(0,q.__)("Product Support","rank-math"),items:[{id:"kb-seo-suite",icon:"help",title:(0,q.__)("Online Documentation","rank-math"),description:(0,q.__)("Understand all the capabilities of Rank Math","rank-math")},{id:"support",icon:"support",title:(0,q.__)("Ticket Support","rank-math"),description:(0,q.__)("Direct help from our qualified support team","rank-math")},{id:"help-affiliate",icon:"sitemap",title:(0,q.__)("Affiliate Program","rank-math"),description:(0,q.__)("Earn flat 30% on every sale!","rank-math")}]}],ee={"how-to-setup":Q("how-to-setup","Help Tab Setup KB"),"upgrade-to-pro":Q("pro","Help Tab PRO Link"),"seo-import":Q("seo-import","Help Tab Import Data"),"score-100":Q("score-100","Help Tab Score KB"),"kb-seo-suite":Q("kb-seo-suite","Help Tab KB Link"),support:Q("support","Help Tab Ticket"),"help-affiliate":Q("help-affiliate","Help Tab Aff Link")},te=function(){return wp.element.createElement("div",{className:"two-col rank-math-box-help"},(0,i.map)(Z,(function(e,t){var n=e.heading,r=e.items;return wp.element.createElement("div",{key:t,className:"col rank-math-box"},wp.element.createElement("header",null,wp.element.createElement("h3",null,n)),wp.element.createElement("div",{className:"rank-math-box-content"},wp.element.createElement("ul",{className:"rank-math-list-icon"},(0,i.map)(r,(function(e){var t,n=e.id,r=e.icon,a=e.title,o=e.description;return wp.element.createElement("li",{key:n},wp.element.createElement("a",{target:"_blank",rel:"noreferrer",href:null!==(t=ee[n])&&void 0!==t?t:""},wp.element.createElement("i",{className:"rm-icon rm-icon-".concat(r)}),wp.element.createElement("div",null,wp.element.createElement("strong",null,a),wp.element.createElement("p",null,o))))})))))})))},ne=window.rankMathComponents,re=function(){var e=rankMath,t=e.activateUrl,n=e.isSiteUrlValid;return wp.element.createElement(React.Fragment,null,wp.element.createElement("p",null,(0,q.__)("The plugin is currently not connected with your Rank Math account. Click on the button below to login or register for FREE using your ","rank-math"),wp.element.createElement("strong",null,(0,q.__)("Google account, Facebook account","rank-math")),(0,q.__)(" or ","rank-math"),wp.element.createElement("strong",null,(0,q.__)("your email account.","rank-math"))),wp.element.createElement(ne.InvalidSiteUrlNotice,{isSiteUrlValid:n}),wp.element.createElement("div",{className:"center"},wp.element.createElement(ne.Button,{variant:"animate",href:t,className:"button-connect",disabled:!n},(0,q.__)("Connect Now","rank-math"))))},ae=function(){return wp.element.createElement(React.Fragment,null,wp.element.createElement("p",null,(0,q.__)("You have successfully activated Rank Math. If you find the plugin useful, ","rank-math"),wp.element.createElement("strong",null,(0,q.__)("feel free to recommend it to your friends or colleagues.","rank-math")),wp.element.createElement(ne.SocialShare,null)),wp.element.createElement("div",{className:"frm-submit"},wp.element.createElement(ne.StatusButton,{type:"submit",status:"disconnect",className:"button-xlarge",name:"button"},(0,q.__)("Disconnect Account","rank-math"))))},oe=function(){var e=rankMath,t=e.isSiteConnected,n=e.registerProductNonce,r=t?"connected":"disconnected",a=t?(0,q.__)("Connected","rank-math"):(0,q.__)("Not Connected","rank-math");return wp.element.createElement("div",{className:"rank-math-box ".concat(t?"status-green":"status-red")},wp.element.createElement("header",null,wp.element.createElement("h3",null,(0,q.__)("Account","rank-math")),wp.element.createElement(ne.StatusButton,{status:r},a)),wp.element.createElement("div",{className:"rank-math-box-content rank-math-ui"},wp.element.createElement("form",{method:"post"},wp.element.createElement(ne.TextControl,{type:"hidden",name:"registration-action",value:t?"deregister":"register"}),wp.element.createElement(ne.TextControl,{type:"hidden",name:"_wpnonce",value:n}),t?wp.element.createElement(ae,null):wp.element.createElement(re,null))))},le=function(){return rankMath.canUser.manageOptions?wp.element.createElement("div",{className:"rank-math-ui container help"},wp.element.createElement(oe,null),wp.element.createElement(te,null)):""},ie=wp.data,ce=wp.compose,ue=n(4184),se=n.n(ue),me=function(e){var t=e.betabadge,n=e.probadge;return t?wp.element.createElement("span",{className:"rank-math-pro-badge beta"},(0,q.__)("NEW!","rank-math")):n&&!rankMath.isPro?wp.element.createElement("span",{className:"rank-math-pro-badge"},(0,q.__)("PRO","rank-math")):null},pe=function(e){var t=e.upgradeable,n=e.probadge,r=rankMath.isPro;return t&&!r?wp.element.createElement(ne.Tooltip,{text:(0,q.__)("More powerful options are available in the PRO version.","rank-math")},wp.element.createElement("span",{className:"is-upgradeable"},wp.element.createElement("a",{href:"https://rankmath.com/pricing/?utm_source=Plugin&utm_medium=Content%20AI%20Module%20Upgradable%20Icon&utm_campaign=WP",target:"_blank",rel:"noreferrer"},wp.element.createElement("div",null,"«")))):t||n&&r?wp.element.createElement(ne.Tooltip,{text:(0,q.__)("PRO options are enabled.","rank-math")},wp.element.createElement("span",{className:"is-upgradeable"},wp.element.createElement("div",{className:"upgraded"},"«"))):null},fe=wp.apiFetch,de=n.n(fe),he=jQuery,be=n.n(he);function ye(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return we(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return we(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function we(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var ve=function(e,t){return wp.element.createElement(ne.ToggleControl,{checked:e.isActive,disabled:e.disabled,onChange:function(n){t(e.id),de()({method:"POST",path:"/rankmath/v1/saveModule",data:{module:e.id,state:n?"on":"off"}}).then((function(r){t(""),r?(wp.data.dispatch("rank-math-settings").updateModules(e.id,n),be().ajax({url:window.location.pathname+window.location.search,method:"GET"}).done((function(e){if(e){var t=be()(e).find("#toplevel_page_rank-math"),n=be()("#toplevel_page_rank-math > .wp-submenu");t.length&&t.find("> .wp-submenu > li").length!==n.children("li").length&&n.fadeOut(200,(function(){n.html(t.find("> .wp-submenu").hide().children()).fadeIn(400)}))}}))):window.alert((0,q.__)("Something went wrong! Please try again.","rank-math"))})).catch((function(e){t(""),window.alert(e.message)}))}})},ge=function(e){var t=e.module,n=ye((0,u.useState)(),2),r=n[0],a=n[1];return wp.element.createElement("div",{className:"status wp-clearfix"},function(e){return e.settings&&e.isActive&&!e.isDisabled?wp.element.createElement("a",{href:e.settings,className:"module-settings button button-secondary"},(0,q.__)("Settings","rank-math")):null}(t),wp.element.createElement("div",{className:"toggle-container"},function(e,t,n){return t===e.id?wp.element.createElement("span",{className:"input-loading"}):e.disabled&&e.disabled_text?wp.element.createElement(ne.Tooltip,{text:e.disabled_text,placement:"left"},ve(e,n)):ve(e,n)}(t,r,a)))},ke=function(e){var t=e.id,n=e.module,r=n.isHidden,a=n.isPro,o=n.icon,l=n.title,i=n.desc,c=se()("rank-math-box",{hidden:r,"is-pro":a});return wp.element.createElement("div",{key:t,className:c,"aria-hidden":"true",onClick:function(){a&&!rankMath.isPro&&window.open("//rankmath.com/pricing/?utm_source=Plugin&utm_medium=Unlock%20PRO%20Module%20Box&utm_campaign=WP")}},wp.element.createElement("i",{className:"rm-icon rm-icon-"+o}),"content-ai"===t&&"free"===rankMath.contentAiPlan&&wp.element.createElement("div",{className:"rank-math-free-badge"},(0,q.__)("Free","rank-math")),wp.element.createElement("header",null,wp.element.createElement("h3",null,l,wp.element.createElement(me,n),wp.element.createElement(pe,n)),wp.element.createElement("p",{dangerouslySetInnerHTML:{__html:i}})),wp.element.createElement(ge,{module:n}))},_e=(0,ce.compose)((0,ie.withSelect)((function(e){return{modules:e("rank-math-settings").getModules()}})))((function(e){var t=e.modules;return(0,i.map)(t,(function(e,t){if("internal"!==e.only)return wp.element.createElement(ke,{key:t,id:t,module:e})}))})),Ee=wp.components,Se=function(){return rankMath.isPro?null:wp.element.createElement("div",{className:"rank-math-box rank-math-unlock-pro-box"},wp.element.createElement("i",{className:"rm-icon rm-icon-software"}),wp.element.createElement("div",{className:"pro-link",onClick:function(){return function(){if(!(0,i.isNull)(document.getElementById("rank-math-onsite-checkout-wrapper")))return be()(".components-modal__screen-overlay").show(),!1;be()("body").append('<div id="rank-math-onsite-checkout-wrapper"></div>'),setTimeout((function(){(0,u.createRoot)(document.getElementById("rank-math-onsite-checkout-wrapper")).render(wp.element.createElement(Ee.Modal,{className:"rank-math-onsite-checkout-modal",onRequestClose:function(e){if(!(0,i.isUndefined)(e)&&(0,i.includes)(e.target.classList,"rank-math-onsite-checkout-modal"))return!1;be()(".components-modal__screen-overlay").hide(),be()("body").removeClass("modal-open")},shouldCloseOnClickOutside:!0},wp.element.createElement("iframe",{title:(0,q.__)("OnSite Checkout","rank-math"),width:"100%",height:"100%",src:"https://rankmath.com/site-checkout/"})))}),100)}()},"aria-hidden":"true"},wp.element.createElement("header",null,wp.element.createElement("h3",null,(0,q.__)("Take SEO to the Next Level!","rank-math")),wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,q.__)("Unlimited personal websites","rank-math")),wp.element.createElement("li",null,(0,q.__)("Free 15 Content AI Credits","rank-math")),wp.element.createElement("li",null,(0,q.__)("Track 500 Keywords","rank-math")),wp.element.createElement("li",null,(0,q.__)("Powerful Schema Generator","rank-math")),wp.element.createElement("li",null,(0,q.__)("24/7 Support","rank-math")))),wp.element.createElement("div",{className:"status wp-clearfix"},wp.element.createElement(ne.Button,{variant:"secondary",className:"button button-sedondary"},(0,q.__)("Buy","rank-math")))))},Oe=wp.hooks;function Pe(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return t=(0,Oe.applyFilters)("rank_math_sanitize_settings",t,e,n),null!==r&&(r=(0,Oe.applyFilters)("rank_math_sanitize_settings_value",r,e,n)),r=null===r?t:r,(0,Oe.doAction)("rank_math_settings_changed",e,t,n),{type:"RANK_MATH_SETTINGS_DATA",key:e,value:t,settingsKey:n,settingsValue:r}}function je(e,t){return(0,Oe.doAction)("rank_math_update_app_ui",e,t),{type:"RANK_MATH_APP_UI",key:e,value:t}}function Ne(e){return Ne="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ne(e)}function Ae(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Te(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Ne(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Ne(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ne(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Re(e){return Pe("settings",e,"settings")}function Ce(e,t){var n=wp.data.select("rank-math-settings").getModules();return n[e].isActive=t,(0,i.forEach)(n,(function(t,r){if((0,i.includes)(t.dep_modules,e)){var a=!1;(0,i.forEach)(t.dep_modules,(function(e){n[e].isActive||(a=!0)})),n[r].isDisabled=a,n[r].disabled=a}})),Pe("modules",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ae(Object(n),!0).forEach((function(t){Te(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ae(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},n))}function xe(){return Pe("dirtySettings",{})}function De(){return{type:"RESET_STORE"}}function Me(e){return je("isLoaded",e)}function Ue(e){return je("view",e)}function Ie(e){return je("currentStep",e)}function Ve(e){return Ve="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ve(e)}function Le(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Fe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Le(Object(n),!0).forEach((function(t){Be(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Le(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Be(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Ve(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Ve(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ve(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var He={header_code:"301",status:"active",sources:[{comparison:"exact"}]},We={roleCapabilities:(0,i.get)(rankMath,"roleCapabilities",{}),redirections:rankMath.redirections||He,modules:(0,i.get)(rankMath,"modulesList",{}),dirtySettings:{}},Ke=Fe(Fe({},We),{},{redirections:He});function ze(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:We,t=arguments.length>1?arguments[1]:void 0,n=Fe({},e.dirtySettings);return!1!==t.settingsKey&&(n=t.settingsValue),"RANK_MATH_SETTINGS_DATA"===t.type?"dirtySettings"===t.key?Fe(Fe({},e),{},{dirtySettings:t.value}):Fe(Fe({},e),{},Be(Be({},t.key,t.value),"dirtySettings",n)):"RESET_STORE"===t.type?Ke:e}function Ye(e){return Ye="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ye(e)}function Ge(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function $e(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ge(Object(n),!0).forEach((function(t){Je(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ge(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Je(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Ye(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Ye(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ye(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var qe={currentStep:"getting-started"};function Qe(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:qe,t=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===t.type?$e($e({},e),{},Je({},t.key,t.value)):e}function Xe(e){return e.appData}function Ze(e){return e.appData.dirtySettings}function et(e){return e.appData.settings}function tt(e){return e.appData.modules}function nt(e){return e.appUi.isLoaded}function rt(e){return e.appUi.view}function at(e){return e.appUi.currentStep}(0,ie.registerStore)("rank-math-settings",{reducer:(0,ie.combineReducers)(t),selectors:r,actions:e});var ot=function(){var e=rankMath,t=e.isPro;return e.canUser.manageOptions?wp.element.createElement("div",{className:"rank-math-ui module-listing"},wp.element.createElement("div",{className:"grid ".concat(t?"pro-active":"")},wp.element.createElement(Se,null),wp.element.createElement(_e,null))):wp.element.createElement("div",null,(0,q.__)("You can't access this page.","rank-math"))};function lt(e){return lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},lt(e)}function it(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ct(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==lt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==lt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===lt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ut(e,t){return n=e,r=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?it(Object(n),!0).forEach((function(t){ct(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):it(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},t),{type:"RANK_MATH_APP_UI",key:n,value:r};var n,r}function st(e){return st="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},st(e)}function mt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function pt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?mt(Object(n),!0).forEach((function(t){ft(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):mt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ft(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==st(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==st(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===st(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function dt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===t.type?pt(pt({},e),{},ft({},t.key,t.value)):e}function ht(e,t){return e.appUi[t]}(0,ie.register)((0,ie.createReduxStore)("rank-math-status",{reducer:(0,ie.combineReducers)(o),selectors:l,actions:a}));var bt=function(e){var t=e.title;return wp.element.createElement("div",{className:"rank-math-skeleton rank-math-system-status rank-math-ui container"},wp.element.createElement("div",{className:"rank-math-box"},wp.element.createElement("header",null,wp.element.createElement("h3",null,t)),wp.element.createElement("div",{className:"copy-button-wrapper"}),wp.element.createElement("div",{className:"rank-math-panel components-panel"},(0,i.map)(Array.from({length:8}),(function(e,t){return wp.element.createElement("div",{key:t,className:"components-panel__body"})})))))};function yt(e){return yt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},yt(e)}function wt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function vt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?wt(Object(n),!0).forEach((function(t){gt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):wt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function gt(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==yt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==yt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===yt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function kt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return _t(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _t(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _t(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Et=function(e){var t=kt((0,u.useState)((0,q.__)("Save Changes","rank-math")),2),n=t[0],r=t[1],a=kt((0,u.useState)(!1),2),o=a[0],l=a[1];return wp.element.createElement("footer",null,wp.element.createElement(ne.Button,{type:"submit",variant:"primary",size:"xlarge",disabled:o,onClick:function(){r((0,q.__)("Saving…","rank-math")),l(!0),de()({method:"POST",path:"/rankmath/v1/status/updateViewData",data:vt({},e)}).catch((function(e){console.error(e.message),r((0,q.__)("Failed! Try again","rank-math"))})).then((function(e){r(e?(0,q.__)("Saved","rank-math"):(0,q.__)("Failed! Try again","rank-math"))})).finally((function(){setTimeout((function(){l(!1),r((0,q.__)("Save Changes","rank-math"))}),1e3)}))}},n))},St=function(e){var t=e.title,n=e.description,r=e.warning,a=void 0===r?"":r;return wp.element.createElement(React.Fragment,null,wp.element.createElement("header",null,wp.element.createElement("h3",null,t)),wp.element.createElement("p",null,n),a&&wp.element.createElement("p",{className:"description warning"},wp.element.createElement("strong",null,wp.element.createElement("span",{className:"warning"},(0,q.__)("Warning: ","rank-math")),a)))};function Ot(){return Ot=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ot.apply(this,arguments)}var Pt=function(e){var t=e.data,n=e.updateViewData,r=t.autoUpdate,a=t.updateNotificationEmail,o=t.isPluginUpdateDisabled,l=t.rollbackVersion;return wp.element.createElement("div",{className:"rank-math-auto-update-form field-form rank-math-box"},wp.element.createElement(St,{title:(0,q.__)("Auto Update","rank-math"),description:o?(0,q.__)("You cannot turn on auto-updates to automatically update to stable versions of Rank Math as soon as they are released, because site wide plugins auto-update option is disabled on your site.","rank-math"):(0,q.__)("Turn on auto-updates to automatically update to stable versions of Rank Math as soon as they are released. The beta versions of Rank Math as soon as they are released. The beta versions will never install automatically.","rank-math")}),!o&&wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",{className:"field-row field-type-switch"},wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"enable_auto_update"},(0,q.__)("Auto Update Plugin","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(ne.ToggleControl,{id:"enable_auto_update",checked:r,onChange:function(e){t.autoUpdate=e,n(t)}}))))),wp.element.createElement("div",{id:"control_update_notification_email"},wp.element.createElement("p",null,(0,q.__)("When auto-updates are turned off, you can enable update notifications, to send an email to the site administrator when an update is available for Rank Math.","rank-math")),wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",{className:"field-row field-type-switch"},wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"enable_update_notification_email"},(0,q.__)("Update Notification Email","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(ne.ToggleControl,{id:"enable_update_notification_email",checked:a,onChange:function(e){t.updateNotificationEmail=e,n(t)}})))))),!o&&l&&wp.element.createElement(ne.Notice,{variant:"alt",status:"warning"},wp.element.createElement("p",null,(0,q.__)("Rank Math will not auto-update because you have rolled back to a previous version. Update to the latest version manually to make this option work again.","rank-math"))),wp.element.createElement(Et,Ot({panel:"auto_update"},t)))};function jt(){return jt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},jt.apply(this,arguments)}var Nt=function(e){var t=e.data,n=e.updateViewData,r=t.betaOptin,a=t.isPluginUpdateDisabled;return wp.element.createElement("div",{className:"rank-math-beta-optin-form field-form rank-math-box"},wp.element.createElement(St,{title:(0,q.__)("Beta Opt-in","rank-math"),description:a?(0,q.__)("You cannot turn on the Beta Tester feature because site wide plugins auto-update option is disabled on your site.","rank-math"):(0,q.__)("You can take part in shaping Rank Math by test-driving the newest features and letting us know what you think. Turn on the Beta Tester feature to get notified about new beta releases. The beta version will not install automatically and you always have the option to ignore it.","rank-math"),warning:a?"":(0,q.__)("It is not recommended to use the beta version on live production sites.","rank-math")}),!a&&wp.element.createElement(React.Fragment,null,wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",{className:"field-row field-type-switch"},wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"beta_tester"},(0,q.__)("Beta Tester","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(ne.ToggleControl,{id:"beta_tester",checked:r,onChange:function(e){t.betaOptin=e,n(t)}}))))),wp.element.createElement(Et,jt({panel:"beta_optin"},t))))};function At(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Tt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Tt(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Rt=function(e){var t=e.data,n=t.latestVersion,r=t.isRollbackVersion,a=t.isPluginUpdateDisabled,o=t.availableVersions,l=t.updateCoreUrl,c=t.rollbackNonce;if((0,i.isEmpty)(o))return"";var s=rankMath.version,m=At((0,u.useState)(o[1]),2),p=m[0],f=m[1],d=At((0,u.useState)(!1),2),h=d[0],b=d[1],y=(0,i.reduce)(o,(function(e,t){return e[t]=t,e}),{});return wp.element.createElement("form",{className:"rank-math-rollback-form field-form rank-math-box",method:"post",action:""},wp.element.createElement(St,{title:(0,q.__)("Rollback to Previous Version","rank-math"),description:(0,q.__)("If you are facing issues after an update, you can reinstall a previous version with this tool.","rank-math"),warning:(0,q.__)("Previous versions may not be secure or stable. Proceed with caution and always create a backup.","rank-math")}),wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",null,wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"your-verions"},(0,q.__)("Your Version","rank-math"))),wp.element.createElement("td",null,wp.element.createElement("strong",null,s),r&&wp.element.createElement(React.Fragment,null,wp.element.createElement("br",null),wp.element.createElement("span",{className:"rollback-version-label"},(0,q.__)("Rolled Back Version: ","rank-math")),(0,q.__)("Auto updates will not work, please update the plugin manually.","rank-math")),s===n?wp.element.createElement("p",{className:"description"},(0,q.__)("You are using the latest version of the plugin.","rank-math")):wp.element.createElement("p",{className:"description"},(0,q.__)("This is the version you are using on this site.","rank-math")))),s!==n&&wp.element.createElement("tr",null,wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"latest-stable"},(0,q.__)("Latest Stable Version","rank-math"))),wp.element.createElement("td",null,wp.element.createElement("strong",null,n),a&&s<n&&wp.element.createElement("a",{href:l,className:"update-link"},(0,q.__)("Update Now","rank-math")),wp.element.createElement("p",{className:"description"},(0,q.__)("This is the latest version of the plugin.","rank-math")))),wp.element.createElement("tr",null,wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"rollback_version"},(0,q.__)("Rollback Version","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(ne.SelectControl,{variant:"default",id:"rm_rollback_version",name:"rm_rollback_version",value:p,options:y,disabledOptions:[s],onChange:function(e){return f(e)}}),wp.element.createElement("p",{className:"description"},(0,q.__)("Roll back to this version.","rank-math")))))),wp.element.createElement("footer",null,wp.element.createElement(ne.TextControl,{type:"hidden",name:"_wpnonce",value:c}),wp.element.createElement(ne.Button,{type:"submit",variant:"primary",size:"xlarge",id:"rm-rollback-button",onClick:function(){return b(!0)}},(0,q.__)("Install Version ","rank-math"),p),h&&wp.element.createElement("div",{className:"alignright rollback-loading-indicator"},wp.element.createElement("span",{className:"loading-indicator-text"},(0,q.__)("Reinstalling, please wait…","rank-math")),wp.element.createElement("span",{className:"spinner is-active"}))))},Ct=function(e){return wp.element.createElement(React.Fragment,null,wp.element.createElement(Rt,e),wp.element.createElement(Nt,e),wp.element.createElement(Pt,e))},xt=(0,ce.compose)((0,ie.withSelect)((function(e){return{data:e("rank-math-status").getViewData("version_control")}})),(0,ie.withDispatch)((function(e){var t="version_control";return{getViewData:function(){de()({method:"POST",path:"/rankmath/v1/status/getViewData",data:{activeTab:t}}).catch((function(e){alert(e.message)})).then((function(n){e("rank-math-status").updateViewData(t,n)}))},updateViewData:function(n){e("rank-math-status").updateViewData(t,n)}}})))((function(e){var t=e.data,n=e.getViewData,r=e.updateViewData;return(0,u.useEffect)((function(){(0,i.isEmpty)(t)&&n()}),[]),(0,i.isUndefined)(t)?wp.element.createElement(bt,{title:"Version Control"}):wp.element.createElement("div",{className:"rank-math-ui container version-control"},wp.element.createElement(Ct,{data:t,updateViewData:r}))})),Dt=rankMath,Mt=Dt.isAdvancedMode,Ut=Dt.isPluginActiveForNetwork,It=Dt.isNetworkAdmin,Vt=Dt.canUser,Lt=Vt.manageOptions&&It?[{name:"help",title:(0,q.__)("Dashboard","rank-math"),view:le},{name:"version_control",title:(0,q.__)("Version Control","rank-math"),view:xt}]:(0,i.filter)([Vt.manageOptions&&{name:"modules",title:(0,q.__)("Modules","rank-math"),view:ot},Vt.manageOptions&&!Ut&&{name:"help",title:(0,q.__)("Help","rank-math"),view:le},Vt.manageOptions&&{name:"setup-wizard",className:"is-external",title:wp.element.createElement("a",{href:rankMath.adminurl+"?page=rank-math-wizard",onClick:function(e){return e.stopPropagation()}},(0,q.__)("Setup Wizard","rank-math"))},Mt&&Vt.installPlugins&&{name:"import-export",className:"is-external",title:wp.element.createElement("a",{href:rankMath.adminurl+"?page=rank-math-status&view=import_export",onClick:function(e){return e.stopPropagation()}},(0,q.__)("Import & Export","rank-math"))}],Boolean),Ft={modules:(0,q.__)("Modules","rank-math"),help:(0,q.__)("Help","rank-math"),version_control:(0,q.__)("Version Control","rank-math")};function Bt(e){return function(e){if(Array.isArray(e))return Kt(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Wt(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ht(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw a}}return i}}(e,t)||Wt(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Wt(e,t){if(e){if("string"==typeof e)return Kt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Kt(e,t):void 0}}function Kt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var zt,Yt=function(){var e=Ht(J({view:Lt[0].name}),2),t=e[0],n=e[1],r=t.get("view");return wp.element.createElement(React.Fragment,null,wp.element.createElement(ne.Breadcrumbs,{activePage:Ft[r]}),wp.element.createElement(ne.TabPanel,{tabs:Lt,key:r,initialTabName:r,onSelect:function(e){e===r||(0,i.includes)(["setup-wizard","import-export"],e)||n((function(t){return(0,i.fromPairs)([].concat(Bt(t),[["view",e]]))}))}},(function(e){var t=e.view;return t?wp.element.createElement(t,null):null})))},Gt=function(){return wp.element.createElement(Y,null,wp.element.createElement(Yt,null))};zt=function(){var e=document.getElementById("rank-math-dashboard-page");(0,i.isNull)(e)||(0,u.createRoot)(e).render(wp.element.createElement(Gt,null))},"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",zt):zt())}()}();