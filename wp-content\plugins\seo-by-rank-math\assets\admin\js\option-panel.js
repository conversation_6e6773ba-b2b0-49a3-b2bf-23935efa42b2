!function(){"use strict";var t,a={n:function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return a.d(e,{a:e}),e},d:function(t,e){for(var n in e)a.o(e,n)&&!a.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},o:function(t,a){return Object.prototype.hasOwnProperty.call(t,a)}},e=jQuery,n=a.n(e),i=lodash,r=wp.apiFetch,o=a.n(r),s=wp.i18n;function c(t,a,e){return n().ajax({url:rankMath.ajaxurl,type:e||"POST",dataType:"json",data:n().extend(!0,{action:"rank_math_"+t,security:rankMath.security},a)})}function d(t,a,e,i){a=a||"error",i=i||!1;var r=n()('<div class="notice notice-'+a+' is-dismissible"><p>'+t+"</p></div>").hide();e.next(".notice").remove(),e.after(r),r.slideDown(),n()(document).trigger("wp-updates-notice-added"),n()("html,body").animate({scrollTop:r.offset().top-50},"slow"),i&&setTimeout((function(){r.fadeOut((function(){r.remove()}))}),i)}(t=n())((function(){window.rankMathOptions={init:function(){this.preview(),this.misc(),this.scCache(),rankMathAdmin.variableInserter(),this.searchEngine.init(),this.addressFormat(),this.siteMap(),this.robotsEvents(),this.proRedirect(),this.contentAI(),this.localSEO(),this.htmlSitemap()},searchEngine:{init:function(){t("#setting-panel-analytics").addClass("exclude"),this.form=t(".rank-math-search-options"),this.wrap=this.form.closest(".rank-math-wrap-settings"),this.input=this.form.find("input"),this.tabs=this.wrap.find(".rank-math-tabs"),this.panels=this.wrap.find(".rank-math-tab:not(.exclude)"),this.ids=["general","titles","sitemap"],this.indexes={},this.buildIndex(),this.events()},events:function(){var a=this,e=a.tabs.find(">.rank-math-tabs-navigation"),n=t('<div class="rank-math-search-dropdown"></div>');a.tabs.find(">.rank-math-tabs-content").prepend('<div class="rank-math-setting-search-empty hidden">No results found.</div>'),a.form.append(n),n.hide().empty();var r=(0,i.debounce)((function(t){a.wrap.addClass("searching"),a.searchIndexes(t,n)}),300);a.form.on("click",".clear-search",(function(t){t.preventDefault(),a.input.val(""),a.clearSearch(e)})),this.ids.forEach((function(t){n.append(a.indexes[t])})),a.input.on("input",(function(){if(""===a.input.val())return a.clearSearch(e,n),!1;r(a.input.val().toLowerCase())})),n.on("click",".cmb-row",(function(){var a=t(this),e=window.location.origin+window.location.pathname+"?page=rank-math-options-"+a.data("settings-id")+"#"+a.closest(".dropdown-tab").data("id");e===window.location.href?window.location.reload():window.location=e}));var o=t(".rank-math-search-options, .rank-math-search-options *, .rank-math-search-dropdown, .rank-math-search-dropdown *");t("body").on("click",(function(a){t(a.target).is(o)||n.hide()}))},searchIndexes:function(a,e){if(!(1>a.trim().length)){e.find(".dropdown-tab").each((function(){var a=t(this);"setting-panel-analytics"===a.attr("data-id")&&a.css("display","none")}));var n=e.find(".cmb-row"),i=0;n.hide().each((function(){var e=t(this);e.text().trim().toLowerCase().includes(a)&&(e.show(),++i)})),e.show(),e.toggleClass("empty",0===i)}},clearSearch:function(a,e){e=e||!1,this.wrap.removeClass("searching search-no-results"),t(">a.active",a).trigger("click"),e?e.hide():(t(".cmb-row").show(),t(".rank-math-cmb-dependency",".cmb-form, .rank-math-metabox-wrap").each((function(){rankMathAdmin.loopDependencies(t(this))})))},buildIndex:function(){var a=window.localStorage.getItem("rank-math-option-search-index"),e=void 0===a||a!==rankMath.version;this.ids.forEach((function(a){this.getIndex(a,e),this.indexes[a]=t(window.localStorage.getItem("rank-math-option-"+a+"-index"))}),this),e&&window.localStorage.setItem("rank-math-option-search-index",rankMath.version)},getIndex:function(a,e){e&&t("<div/>").load(rankMath.adminurl+"?page=rank-math-options-"+a,(function(e,n){if("error"!==n){var i=t(e).find(".rank-math-tabs-content");i.find(".rank-math-tab").each((function(){var a=t(this);a.removeClass().addClass("dropdown-tab"),a.attr("data-id",a.attr("id")),a.removeAttr("id"),a.find(".rank-math-notice").remove(),a.find(".rank-math-desc").remove(),a.find("input, select, textarea").remove()})),i.find(".rank-math-tab").removeClass().addClass("dropdown-tab").removeAttr("id"),i.find(".cmb-row").each((function(){var e=t(this);e.attr("data-settings-id",a),(e.hasClass("cmb-type-title")||e.hasClass("cmb-type-notice")||e.hasClass("rank-math-notice")||e.hasClass("rank-math-desc")||e.hasClass("rank-math-exclude-from-search"))&&e.remove(),e.find(".cmb-td").children(":not(.cmb2-metabox-description)").remove(),e.find("label,.cmb2-metabox-description").unwrap(),e.removeAttr("data-fieldtype")})),i=i.html().replace(/(\r\n\t|\n|\r\t)/gm,""),window.localStorage.setItem("rank-math-option-"+a+"-index",i)}}))}},scCache:function(){t(".console-cache-delete").on("click",(function(a){a.preventDefault();var e=t(this),n=e.data("days"),i=-1===n?rankMath.confirmClearImportedData:rankMath.confirmClear90DaysCache;window.confirm(i+" "+rankMath.confirmAction)&&(e.prop("disabled",!0),c("analytics_delete_cache",{days:n},"GET").always((function(){e.prop("disabled",!1)})).done((function(a){a&&a.success&&(d(rankMath.feedbackCacheDeleted,"success",t("h1",".rank-math-wrap-settings")),t(".rank-math-console-db-info").remove(),e.closest(".cmb-td").append(a.message))})))}));var a=t("#console_caching_control");t(".console-cache-update-manually").on("click",(function(e){e.preventDefault();var n=t(this),i=a.val();n.prop("disabled",!0),c("analytic_start_fetching",{days:i},"GET").done((function(a){a&&a.success?(d(a.message,"success",t("h1.page-title")),n.text("Fetching in Progress"),t(".cancel-fetch").prop("disabled",!1)):d("Unable to update cache due to: "+a.error,"error",t("h1.page-title"))}))})),t(".cancel-fetch").on("click",(function(a){a.preventDefault(),t(this).prop("disabled",!0),c("analytic_cancel_fetching",{},"GET").done((function(t){t&&t.success&&window.location.reload()}))}))},addressFormat:function(){var a=t("input[type=text], textarea",".rank-math-address-format");if(a.length){a.attr("autocomplete","off"),a.wrap('<div class="rank-math-variables-wrap"/>');var e=t("body"),n=a.parent(".rank-math-variables-wrap");n.append('<a href="#" class="rank-math-variables-button button button-secondary button-address"><span class="dashicons dashicons-arrow-down-alt2"></span></a>');var i=t("<ul/>"),r=t('<div class="rank-math-variables-dropdown"></div>');t.each({"{address} {locality}, {region} {postalcode}":"(New York, NY 12345)","{address} {postalcode}, {locality} {region}":"(New York 12345, NY)","{address} {locality} {postalcode}":"(New York NY 12345)","{postalcode} {region} {locality} {address}":"(12345 NY New York)","{address} {locality}":"(New York NY)"},(function(t,a){i.append('<li data-var="'+a+'"><strong>'+t+"</strong></li>")})),r.append(i),t("rank-math-variables-wrap:eq(0)").append(r);var o=t(".rank-math-variables-button, .rank-math-variables-button *, .rank-math-variables-dropdown, .rank-math-variables-dropdown *");e.on("click",(function(a){t(a.target).is(o)||r.hide()}));var s=r.find("input"),c=r.find("li");t(n).on("click",".rank-math-variables-button",(function(a){a.preventDefault(),t(this).after(r),c.show(),r.show(),s.val("").focus()})),r.on("click","li",(function(a){a.preventDefault();var e=t(this);e.closest(".rank-math-variables-wrap").find("textarea").val(e.find("strong").text())}))}},misc:function(){void 0!==n().fn.select2&&t("[data-s2-pages]").select2({allowClear:!0,placeholder:(0,s.__)("Select Page","rank-math"),ajax:{url:rankMath.ajaxurl+"?action=rank_math_search_pages",data:function(t){return{term:t.term,security:rankMath.security}},dataType:"json",delay:250},width:"100%",minimumInputLength:2}).on("select2:select",(function(a){var e=a.params.data;t(this).closest(".cmb-td").find(".rank-math-selected-page").prop("href",e.url).text(e.url)})),t("#htaccess_accept_changes").on("change",(function(){t("#htaccess_content").prop("readonly",!this.checked)})),t(".reset-options").on("click",(function(){return!!window.confirm("Are you sure? You want to reset settings.")&&(t(window).off("beforeunload"),!0)}));var a=t(".rank-math-tabs");setTimeout((function(){window.localStorage.removeItem(a.attr("id"))}),1e3),t(".save-options").on("click",(function(){var e=t("> .rank-math-tabs-navigation > a.active",a);return window.localStorage.setItem(a.attr("id"),e.attr("href")),t(window).off("beforeunload"),!0}));var e=!1;t(".cmb-form").on("change","input:not(.notrack), textarea:not(.notrack), select:not(.notrack)",(function(){e=!0})),t(window).on("beforeunload",(function(){if(e)return"Are you sure? You didn't finish the form!"})),t(".custom-sep").on("keyup",(function(){var a=t(this);a.closest("li").find("input.cmb2-option").val(a.text()).trigger("change")})),t(".cmb-form").on("keydown","input, textarea",(function(a){"Enter"===a.key&&((a.ctrlKey||a.metaKey)&&t("#submit-cmb").trigger("click"),"TEXTAREA"!==this.tagName&&a.preventDefault())}))},preview:function(){t("[data-preview]").on("change",(function(){var a=t(this),e=null,n="";if(a.is(":radio")&&(e=a.closest(".cmb-td")),null!==e)if(e.hasClass("done"))a.is(":checked")&&(n=e.find("h5")).text(n.data("title").format(a.val()));else if(e.addClass("done"),"title"===a.data("preview")){var i="";i+='<div class="rank-math-preview-title" data-title="Preview"><div>',i+='<h5 data-title="'+rankMath.postTitle+" {0} "+rankMath.blogName+'"></h5>',i+="<span>"+rankMath.postUri+"</span>",i+="</div></div>",e.append(i),(n=e.find("h5")).text(n.data("title").format(a.val()))}})).trigger("change")},siteMap:function(){var a=t(".sitemap-nginx-notice");a.length&&(a.on("click","a span",(function(t){return t.preventDefault(),a.toggleClass("active"),!1})),a.on("click","a.sitemap-close-notice",(function(t){return t.preventDefault(),c("remove_nginx_notice",{},"GET").done((function(){a.remove()})),!1})))},robotsEvents:function(){t(".rank-math-robots-data").each((function(){var a=t(this).find("ul li:first-child input"),e=t(this).find("ul li:nth-child(2) input");a.on("change",(function(){a.is(":checked")&&e.prop("checked",!1).trigger("change")})),e.on("change",(function(){e.is(":checked")&&a.prop("checked",!1)}))}))},proRedirect:function(){t(".cmb-redirector-element").on("click",(function(a){var e=t(a.target);if(e.is("a")||e.closest("a").length)return!0;var n=t(this),i=n.data("url");if(!i)return!0;a.preventDefault(),n.css("cursor","pointer"),window.open(i)}))},contentAI:function(){var a=t(".buy-more-credits .update-credit");a.length&&a.on("click",(function(t){return t.preventDefault(),a.addClass("loading"),o()({method:"POST",path:"/rankmath/v1/ca/getCredits"}).catch((function(t){alert(t.message)})).then((function(t){if(t.error)return alert(t.error),void a.removeClass("loading").next("strong").text(t.credits);a.removeClass("loading").next("strong").text(t)})),!1}))},localSEO:function(){var t=n()("#website_name"),a=n()("#knowledgegraph_name");t.val()===a.val()&&t.on("keyup",(function(){a.val(t.val())}))},htmlSitemap:function(){var t=n()("#html_sitemap"),a=n()(".rank-math-html-sitemap");t.on("change",(function(){t.is(":checked")?a.removeClass("hidden"):a.addClass("hidden")}))}},window.rankMathOptions.init()}))}();