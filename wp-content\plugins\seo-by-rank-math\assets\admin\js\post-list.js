!function(){"use strict";var e={n:function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,{a:n}),n},d:function(t,n){for(var a in n)e.o(n,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:n[a]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}},t=jQuery,n=e.n(t),a=lodash,r=wp.i18n,l=wp.element,o=wp.components,i=React;function c(e){return e.startsWith("{{/")?{type:"componentClose",value:e.replace(/\W/g,"")}:e.endsWith("/}}")?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.startsWith("{{")?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}function s(e,t){let n,a,r=[];for(let l=0;l<e.length;l++){const o=e[l];if("string"!==o.type){if(void 0===t[o.value])throw new Error(`Invalid interpolation, missing component node: \`${o.value}\``);if("object"!=typeof t[o.value])throw new Error(`Invalid interpolation, component node must be a ReactElement or null: \`${o.value}\``);if("componentClose"===o.type)throw new Error(`Missing opening component token: \`${o.value}\``);if("componentOpen"===o.type){n=t[o.value],a=l;break}r.push(t[o.value])}else r.push(o.value)}if(n){const l=function(e,t){const n=t[e];let a=0;for(let r=e+1;r<t.length;r++){const e=t[r];if(e.value===n.value){if("componentOpen"===e.type){a++;continue}if("componentClose"===e.type){if(0===a)return r;a--}}}throw new Error("Missing closing component token `"+n.value+"`")}(a,e),o=s(e.slice(a+1,l),t),c=(0,i.cloneElement)(n,{},o);if(r.push(c),l<e.length-1){const n=s(e.slice(l+1),t);r=r.concat(n)}}return r=r.filter(Boolean),0===r.length?null:1===r.length?r[0]:(0,i.createElement)(i.Fragment,null,...r)}function m(e){const{mixedString:t,components:n,throwErrors:a}=e;if(!n)return t;if("object"!=typeof n){if(a)throw new Error(`Interpolation Error: unable to process \`${t}\` because components is not an object`);return t}const r=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(c)}(t);try{return s(r,n)}catch(e){if(a)throw new Error(`Interpolation Error: unable to process \`${t}\` because of error \`${e.message}\``);return t}}var u=function(e){var t=e.tags,n=e.components,r=e.children;return n=n||{},!1===(0,a.isUndefined)(t)&&(t=t.split(",")).forEach((function(e){var t=e;n[e]=wp.element.createElement(t,null)})),m({mixedString:r,components:n})},p=function(e,t){arguments.length>2&&void 0!==arguments[2]&&arguments[2]&&(t+="+Free+Plan");var n=new URLSearchParams({utm_source:"Plugin",utm_medium:t,utm_campaign:"WP"});return(0,a.includes)(e,"?")||(e+="?"),e+n.toString()},d=function(e){var t=e.width,n=void 0===t?40:t,l=e.showProNotice,i=void 0!==l&&l,c=e.isBulkEdit,s=void 0!==c&&c,m=e.isResearch,d=void 0!==m&&m,h=e.creditsRequired,k=void 0===h?0:h,f=e.isKeywordIntent,v=void 0!==f&&f;if(i)return function(e){return wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks top-20 less-padding "+e},wp.element.createElement("h3",null,(0,r.__)("🔒 This is a PRO-Only Feature","rank-math")),wp.element.createElement("p",null,(0,r.__)("We are sorry but this feature is only available to Rank Math PRO/Business/Agency Users. Unlock this feature and many more by getting a Rank Math plan.","rank-math")),wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,r.__)("Bulk Edit SEO Tags","rank-math")),wp.element.createElement("li",null,(0,r.__)("Advanced Google Analytics 4 Integration","rank-math")),wp.element.createElement("li",null,(0,r.__)("Keyword Rank Tracker","rank-math")),wp.element.createElement("li",null,(0,r.__)("Free Content AI Trial","rank-math")),wp.element.createElement("li",null,(0,r.__)("SEO Performance Email Reports","rank-math"))),wp.element.createElement(o.Button,{href:rankMath.links.pro,target:"_blank",className:"button button-primary is-green"},(0,r.__)("Learn More","rank-math"))))}(n);if(v)return function(e){return wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks top-20 less-padding "+e},wp.element.createElement("h3",null,(0,r.__)("⛔️ Update Required","rank-math")),wp.element.createElement("p",null,(0,r.__)("Your current plugin version does not support this feature. Please update Rank Math PRO to version 3.0.83 or later to unlock full functionality.","rank-math")),wp.element.createElement(o.Button,{href:rankMath.links.pro,target:"_blank",className:"button button-primary is-green"},(0,r.__)("Update Now","rank-math"))))}(n);var _=(0,a.isUndefined)(wp.data.select("rank-math-content-ai"))?rankMath.contentAI:wp.data.select("rank-math-content-ai").getData(),w=_.isUserRegistered,g=_.plan,y="free"===g,E=_.credits>k,b=_.isMigrating;if(E&&d&&!y&&_.credits<500&&(E=!1),w&&g&&E&&!b&&!y)return null;var C,A="width-"+n;return!w||!g||E&&y?wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks top-20 less-padding "+A},wp.element.createElement("h3",null,(0,r.__)("🚀 Supercharge Your Content With AI","rank-math")),wp.element.createElement("p",null,!w&&!s&&(0,r.__)("Start using Content AI by connecting your RankMath.com Account","rank-math"),w&&!g&&!s&&!y&&(0,r.__)("To access this Content AI feature, you need to have an active subscription plan.","rank-math"),w&&!s&&y&&(0,r.__)("To access this Content AI feature, you have to purchase a Content AI Subscription.","rank-math"),s&&(0,r.__)("You are one step away from unlocking this premium feature along with many more.","rank-math")),function(e,t,n){return t?wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,r.__)("1-Click Article Generation","rank-math")),wp.element.createElement("li",null,(0,r.__)("Bulk Update Your SEO Meta using AI","rank-math")),wp.element.createElement("li",null,(0,r.__)("Get Access to 40+ AI SEO Tools","rank-math")),wp.element.createElement("li",null,(0,r.__)("125+ Expert-Written Prompts","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click Competitor Content Research","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click WooCommerce Product Descriptions","rank-math"))):n?wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,r.__)("1-Click Article Generation","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click Competitor Research","rank-math")),wp.element.createElement("li",null,(0,r.__)("On-Page SEO Suggestions","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click Bulk SEO Meta","rank-math")),wp.element.createElement("li",null,(0,r.__)("125+ Pre-Built Prompts","rank-math")),wp.element.createElement("li",null,(0,r.__)("Multiple RankBot Sessions","rank-math"))):40===e?wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,r.__)("1-Click Article Generation","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click SEO Content","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click SEO Meta","rank-math")),wp.element.createElement("li",null,(0,r.__)("40+ Specialized AI Tools","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click Competitor Research","rank-math")),wp.element.createElement("li",null,(0,r.__)("125+ Pre-Built Prompts","rank-math"))):wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,r.__)("Gain access to 40+ advanced AI tools, empowering your content strategy.","rank-math")),wp.element.createElement("li",null,(0,r.__)("Experience the revolutionary AI-powered Content Editor for unparalleled efficiency.","rank-math")),wp.element.createElement("li",null,(0,r.__)("Engage with RankBot, your personal AI Chat Assistant, for real-time assistance.","rank-math")))}(n,s,y),!w&&wp.element.createElement(o.Button,{href:rankMath.contentAI.connectSiteUrl,className:"button button-primary is-green"},(0,r.__)("Connect Now","rank-math")),w&&(!g||y)&&wp.element.createElement(o.Button,{href:p(rankMath.links["content-ai"]+"?play-video=ioPeVIntJWw&","Buy+Plan+Button",y),className:"button button-primary is-green",target:"_blank"},(0,r.__)("Learn More","rank-math")))):b?wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{style:{textAlign:"center"},className:"rank-math-cta-box less-padding top-20 "+A},wp.element.createElement("h3",null,(0,r.__)("Server Maintenance Underway","rank-math")),wp.element.createElement("p",null,(0,r.__)("We are working on improving your Content AI experience. Please wait for 5 minutes and then refresh to start using the optimized Content AI. If you see this for more than 5 minutes, please ","rank-math"),wp.element.createElement("a",{href:rankMath.links.support,target:"_blank",rel:"noreferrer"},(0,r.__)("reach out to the support team.","rank-math")),(0,r.__)(" We are sorry for the inconvenience.","rank-math")))):wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box less-padding top-20 "+A},wp.element.createElement("h3",null,(0,r.__)("⛔️ Content AI Credit Alert!","rank-math")),wp.element.createElement("p",null,(C=_.resetDate)?wp.element.createElement(u,{components:{strong:wp.element.createElement("strong",null)}},(0,r.sprintf)((0,r.__)("Your monthly Content AI credits have been fully utilized. You can wait till %s for your credits to refresh or upgrade to continue enjoying seamless content creation","rank-math"),"{{strong}}"+C+"{{/strong}}")):(0,r.__)("Your monthly Content AI credits have been fully utilized. To continue enjoying seamless content creation, simply click the button below to upgrade your plan and access more credits.","rank-math")),wp.element.createElement(o.Button,{href:p(rankMath.links["content-ai"]+"?play-video=ioPeVIntJWw&","Buy+Credits+Button",y),className:"button button-primary is-green",target:"_blank"},(0,r.__)("Learn More","rank-math")),wp.element.createElement(o.Button,{variant:"link",href:p(rankMath.links["content-ai-restore-credits"],"Buy+Credits+Button",y),className:"button button-secondary",target:"_blank"},(0,r.__)("Missing Credits?","rank-math"))))},h=function(e){var t=e.showProNotice,r=void 0!==t&&t,i=e.isBulkEdit,c=void 0!==i&&i,s=e.creditsRequired,m=void 0===s?0:s,u=e.isKeywordIntent,p=void 0!==u&&u;(0,a.isNull)(document.getElementById("rank-math-content-ai-modal-wrapper"))&&n()("#wpwrap").append('<div id="rank-math-content-ai-modal-wrapper"></div>'),setTimeout((function(){(0,l.render)(wp.element.createElement(o.Modal,{className:"rank-math-contentai-modal rank-math-modal rank-math-error-modal",shouldCloseOnClickOutside:!1,onRequestClose:function(e){(0,a.isUndefined)(e)||(n()(".components-modal__screen-overlay").remove(),document.getElementById("rank-math-content-ai-modal-wrapper").remove(),document.body.classList.remove("modal-open"))}},wp.element.createElement(d,{width:100,showProNotice:r,isBulkEdit:c,creditsRequired:m,isKeywordIntent:p})),document.getElementById("rank-math-content-ai-modal-wrapper"))}),100)};function k(e){return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},k(e)}function f(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return v(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return v(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,r=function(){};return{s:r,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,o=!0,i=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){i=!0,l=e},f:function(){try{o||null==n.return||n.return()}finally{if(i)throw l}}}}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}function _(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,(r=a.key,l=void 0,l=function(e,t){if("object"!==k(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==k(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===k(l)?l:String(l)),a)}var r,l}var w=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.addButtons(),this.bulkEvents(),this.editingEvents(),this.saveEvents()}var t,l,o;return t=e,(l=[{key:"addButtons",value:function(){n()(".tablenav .bulkactions select").find('option[value="rank_math_ai_options"]').attr("disabled","disabled");var e=n()("#rank_math_seo_details, #rank_math_title, #rank_math_description, #rank_math_image_alt, #rank_math_image_title");e.each((function(){var e=n()(this);e.append(' <a href=#" class="dashicons dashicons-edit" title="'+(0,r.__)("Bulk Edit This Field","rank-math")+'"></a>'),e.wrapInner("<span/>"),e.append(' <span><a href="#" class="button button-primary button-small rank-math-column-save-all">'+(0,r.__)("Save All Edits","rank-math")+'</a> <a href="#" class="button-link button-link-delete rank-math-column-cancel-all">'+(0,r.__)("Cancel","rank-math")+"</a></span>")})),e.on("click",".dashicons-edit, .rank-math-column-cancel-all",(function(t){t.preventDefault();var a=n()(this).closest("th");n()(this).hasClass("rank-math-column-cancel-all")?(e.removeClass("bulk-editing"),n()(".rank-math-column-cancel","td.bulk-editing.dirty").trigger("click"),n()("td.bulk-editing").removeClass("bulk-editing")):(a.toggleClass("bulk-editing"),n()("td.column-"+a.attr("id")).toggleClass("bulk-editing"))}))}},{key:"bulkEvents",value:function(){var e=n()(".tablenav .bulkactions select");e.find('option[value="rank_math_ai_options"]').attr("disabled","disabled"),e.find('option[value="rank_math_options"]').attr("disabled","disabled");var t=["rank_math_content_ai_fetch_seo_title","rank_math_content_ai_fetch_seo_description","rank_math_content_ai_fetch_seo_title_description"];e.on("change",(function(){var n=e.val(),r=rankMath.contentAI;if("rank_math_content_ai_fetch_image_alt"===n&&(r.credits<50||!r.isUserRegistered))return e.val("-1").change(),void h({isBulkEdit:!0,creditsRequired:50});!(0,a.includes)(t,n)||r.isUserRegistered&&r.credits&&r.plan&&"free"!==r.plan||(e.val("-1").change(),h({isBulkEdit:!0})),"rank_math_bulk_detect_keyword_intent"!==n||r.isUserRegistered||(e.val("-1").change(),h({isBulkEdit:!0})),(0,a.startsWith)(n,"rank_math_bulk")&&!rankMath.isProActive&&(e.val("-1").change(),h({showProNotice:!0,isBulkEdit:!0}))}))}},{key:"editingEvents",value:function(){n()(".rank-math-column-value").on("input",(function(){var e=n()(this),t=e.closest("td");e.val()!==e.prev().text()?t.addClass("dirty"):t.removeClass("dirty")})).on("keypress",(function(e){if(13===e.keyCode)return n()(this).parent().find(".rank-math-column-save").trigger("click"),!1})),n()(".rank-math-column-cancel").on("click",(function(e){e.preventDefault();var t=n()(this).closest("td");t.removeClass("dirty");var a=t.find(".rank-math-column-value").prev(".rank-math-column-display");a.find("span").length&&(a=a.find("span"));var r=a.html(),l=r.match(/<img\s+[^>]*?src=("|')([^"']+)">/gm);if(l){var o,i=f(l);try{for(i.s();!(o=i.n()).done;){var c=o.value,s=c.match(/alt=("|')([^"']+)/gm)[0];s=s.replace('alt="',""),r=r.replaceAll(c,s)}}catch(e){i.e(e)}finally{i.f()}}t.find(".rank-math-column-value").val(r)}))}},{key:"saveEvents",value:function(){var e=this;n()(".rank-math-column-save-all").on("click",(function(t){t.preventDefault();var a=n()(this),r={},l=[];if(n()(".dirty.bulk-editing").each((function(){var e=n()(this),t=parseInt(e.closest("tr").attr("id").replace("post-","")),a=e.find(".rank-math-column-value");l.push(e),r[t]=r[t]||{},r[t][a.data("field")]=a.val()})),n().isEmptyObject(r))return n()(a.next()).trigger("click"),!0;e.save(r).done((function(t){t.success&&(l.forEach((function(t){e.setColumn(t)})),n()(a.next()).trigger("click"))}))})),n()(".rank-math-column-save").on("click",(function(t){t.preventDefault();var a=n()(this).closest(".dirty"),r=parseInt(a.closest("tr").attr("id").replace("post-","")),l=a.find(".rank-math-column-value"),o={};o[r]={},o[r][l.data("field")]=l.val(),e.save(o).done((function(t){t.success&&e.setColumn(a)}))}))}},{key:"setColumn",value:function(e){e.removeClass("dirty bulk-editing");var t=e.find(".rank-math-column-value").prev(".rank-math-column-display");t.find("span").length&&(t=t.find("span"));var n=e.find(".rank-math-column-value").val();t.text(n)}},{key:"save",value:function(e){return n().ajax({url:rankMath.endpoint+"/updateMetaBulk",method:"POST",beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",rankMath.restNonce)},data:{rows:e}})}}])&&_(t.prototype,l),o&&_(t,o),Object.defineProperty(t,"prototype",{writable:!1}),e}();n()((function(){new w}))}();