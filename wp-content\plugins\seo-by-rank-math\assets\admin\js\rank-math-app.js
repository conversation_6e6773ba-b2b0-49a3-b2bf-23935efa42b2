!function(){var e={8634:function(e){e.exports=function(){"use strict";function e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function t(t){for(var a=1;a<arguments.length;a++){var r=null!=arguments[a]?arguments[a]:{};a%2?e(Object(r),!0).forEach((function(e){n(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function n(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var a="​";const r=(e,t,n,a)=>(e=""+e,t=""+t,a&&(e=e.trim(),t=t.trim()),n?e==t:e.toLowerCase()==t.toLowerCase()),i=(e,t)=>e&&Array.isArray(e)&&e.map((e=>o(e,t)));function o(e,t){var n,a={};for(n in e)t.indexOf(n)<0&&(a[n]=e[n]);return a}function s(e){var t=document.createElement("div");return e.replace(/\&#?[0-9a-z]+;/gi,(function(e){return t.innerHTML=e,t.innerText}))}function l(e){return(new DOMParser).parseFromString(e.trim(),"text/html").body.firstElementChild}function c(e,t){for(t=t||"previous";e=e[t+"Sibling"];)if(3==e.nodeType)return e}function u(e){return"string"==typeof e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/`|'/g,"&#039;"):e}function p(e){var t=Object.prototype.toString.call(e).split(" ")[1].slice(0,-1);return e===Object(e)&&"Array"!=t&&"Function"!=t&&"RegExp"!=t&&"HTMLUnknownElement"!=t}function d(e,t,n){function a(e,t){for(var n in t)if(t.hasOwnProperty(n)){if(p(t[n])){p(e[n])?a(e[n],t[n]):e[n]=Object.assign({},t[n]);continue}if(Array.isArray(t[n])){e[n]=Object.assign([],t[n]);continue}e[n]=t[n]}}return e instanceof Object||(e={}),a(e,t),n&&a(e,n),e}function m(){const e=[],t={};for(let n of arguments)for(let a of n)p(a)?t[a.value]||(e.push(a),t[a.value]=1):e.includes(a)||e.push(a);return e}function h(e){return String.prototype.normalize?"string"==typeof e?e.normalize("NFD").replace(/[\u0300-\u036f]/g,""):void 0:e}var g=()=>/(?=.*chrome)(?=.*android)/i.test(navigator.userAgent);function f(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(e=>(e^crypto.getRandomValues(new Uint8Array(1))[0]&15>>e/4).toString(16)))}function w(e){return e&&e.classList&&e.classList.contains(this.settings.classNames.tag)}function v(e,t){var n=window.getSelection();return t=t||n.getRangeAt(0),"string"==typeof e&&(e=document.createTextNode(e)),t&&(t.deleteContents(),t.insertNode(e)),e}function y(e,t,n){return e?(t&&(e.__tagifyTagData=n?t:d({},e.__tagifyTagData||{},t)),e.__tagifyTagData):(console.warn("tag element doesn't exist",e,t),t)}function b(e){if(e&&e.parentNode){var t=e,n=window.getSelection(),a=n.getRangeAt(0);n.rangeCount&&(a.setStartAfter(t),a.collapse(!0),n.removeAllRanges(),n.addRange(a))}}function k(e,t){e.forEach((e=>{if(y(e.previousSibling)||!e.previousSibling){var n=document.createTextNode(a);e.before(n),t&&b(n)}}))}var E={delimiters:",",pattern:null,tagTextProp:"value",maxTags:1/0,callbacks:{},addTagOnBlur:!0,onChangeAfterBlur:!0,duplicates:!1,whitelist:[],blacklist:[],enforceWhitelist:!1,userInput:!0,keepInvalidTags:!1,createInvalidTags:!0,mixTagsAllowedAfter:/,|\.|\:|\s/,mixTagsInterpolator:["[[","]]"],backspace:!0,skipInvalid:!1,pasteAsTags:!0,editTags:{clicks:2,keepInvalid:!0},transformTag:()=>{},trim:!0,a11y:{focusableTags:!1},mixMode:{insertAfterTag:" "},autoComplete:{enabled:!0,rightKey:!1},classNames:{namespace:"tagify",mixMode:"tagify--mix",selectMode:"tagify--select",input:"tagify__input",focus:"tagify--focus",tagNoAnimation:"tagify--noAnim",tagInvalid:"tagify--invalid",tagNotAllowed:"tagify--notAllowed",scopeLoading:"tagify--loading",hasMaxTags:"tagify--hasMaxTags",hasNoTags:"tagify--noTags",empty:"tagify--empty",inputInvalid:"tagify__input--invalid",dropdown:"tagify__dropdown",dropdownWrapper:"tagify__dropdown__wrapper",dropdownHeader:"tagify__dropdown__header",dropdownFooter:"tagify__dropdown__footer",dropdownItem:"tagify__dropdown__item",dropdownItemActive:"tagify__dropdown__item--active",dropdownItemHidden:"tagify__dropdown__item--hidden",dropdownInital:"tagify__dropdown--initial",tag:"tagify__tag",tagText:"tagify__tag-text",tagX:"tagify__tag__removeBtn",tagLoading:"tagify__tag--loading",tagEditing:"tagify__tag--editable",tagFlash:"tagify__tag--flash",tagHide:"tagify__tag--hide"},dropdown:{classname:"",enabled:2,maxItems:10,searchKeys:["value","searchBy"],fuzzySearch:!0,caseSensitive:!1,accentedSearch:!0,includeSelectedTags:!1,highlightFirst:!1,closeOnSelect:!0,clearOnSelect:!0,position:"all",appendTarget:null},hooks:{beforeRemoveTag:()=>Promise.resolve(),beforePaste:()=>Promise.resolve(),suggestionClick:()=>Promise.resolve()}};function _(){this.dropdown={};for(let e in this._dropdown)this.dropdown[e]="function"==typeof this._dropdown[e]?this._dropdown[e].bind(this):this._dropdown[e];this.dropdown.refs()}var T={refs(){this.DOM.dropdown=this.parseTemplate("dropdown",[this.settings]),this.DOM.dropdown.content=this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-wrapper']")},getHeaderRef(){return this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-header']")},getFooterRef(){return this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-footer']")},getAllSuggestionsRefs(){return[...this.DOM.dropdown.content.querySelectorAll(this.settings.classNames.dropdownItemSelector)]},show(e){var t,n,a,i=this.settings,o="mix"==i.mode&&!i.enforceWhitelist,s=!i.whitelist||!i.whitelist.length,l="manual"==i.dropdown.position;if(e=void 0===e?this.state.inputText:e,!(s&&!o&&!i.templates.dropdownItemNoMatch||!1===i.dropdown.enable||this.state.isLoading||this.settings.readonly)){if(clearTimeout(this.dropdownHide__bindEventsTimeout),this.suggestedListItems=this.dropdown.filterListItems(e),e&&!this.suggestedListItems.length&&(this.trigger("dropdown:noMatch",e),i.templates.dropdownItemNoMatch&&(a=i.templates.dropdownItemNoMatch.call(this,{value:e}))),!a){if(this.suggestedListItems.length)e&&o&&!this.state.editing.scope&&!r(this.suggestedListItems[0].value,e)&&this.suggestedListItems.unshift({value:e});else{if(!e||!o||this.state.editing.scope)return this.input.autocomplete.suggest.call(this),void this.dropdown.hide();this.suggestedListItems=[{value:e}]}n=""+(p(t=this.suggestedListItems[0])?t.value:t),i.autoComplete&&n&&0==n.indexOf(e)&&this.input.autocomplete.suggest.call(this,t)}this.dropdown.fill(a),i.dropdown.highlightFirst&&this.dropdown.highlightOption(this.DOM.dropdown.content.querySelector(i.classNames.dropdownItemSelector)),this.state.dropdown.visible||setTimeout(this.dropdown.events.binding.bind(this)),this.state.dropdown.visible=e||!0,this.state.dropdown.query=e,this.setStateSelection(),l||setTimeout((()=>{this.dropdown.position(),this.dropdown.render()})),setTimeout((()=>{this.trigger("dropdown:show",this.DOM.dropdown)}))}},hide(e){var t=this.DOM,n=t.scope,a=t.dropdown,r="manual"==this.settings.dropdown.position&&!e;if(a&&document.body.contains(a)&&!r)return window.removeEventListener("resize",this.dropdown.position),this.dropdown.events.binding.call(this,!1),n.setAttribute("aria-expanded",!1),a.parentNode.removeChild(a),setTimeout((()=>{this.state.dropdown.visible=!1}),100),this.state.dropdown.query=this.state.ddItemData=this.state.ddItemElm=this.state.selection=null,this.state.tag&&this.state.tag.value.length&&(this.state.flaggedTags[this.state.tag.baseOffset]=this.state.tag),this.trigger("dropdown:hide",a),this},toggle(e){this.dropdown[this.state.dropdown.visible&&!e?"hide":"show"]()},render(){var e,t,n=((t=this.DOM.dropdown.cloneNode(!0)).style.cssText="position:fixed; top:-9999px; opacity:0",document.body.appendChild(t),e=t.clientHeight,t.parentNode.removeChild(t),e),a=this.settings;return"number"==typeof a.dropdown.enabled&&a.dropdown.enabled>=0?(this.DOM.scope.setAttribute("aria-expanded",!0),document.body.contains(this.DOM.dropdown)||(this.DOM.dropdown.classList.add(a.classNames.dropdownInital),this.dropdown.position(n),a.dropdown.appendTarget.appendChild(this.DOM.dropdown),setTimeout((()=>this.DOM.dropdown.classList.remove(a.classNames.dropdownInital)))),this):this},fill(e){e="string"==typeof e?e:this.dropdown.createListHTML(e||this.suggestedListItems);var t,n=this.settings.templates.dropdownContent.call(this,e);this.DOM.dropdown.content.innerHTML=(t=n)?t.replace(/\>[\r\n ]+\</g,"><").split(/>\s+</).join("><").trim():""},fillHeaderFooter(){var e=this.dropdown.filterListItems(this.state.dropdown.query),t=this.parseTemplate("dropdownHeader",[e]),n=this.parseTemplate("dropdownFooter",[e]),a=this.dropdown.getHeaderRef(),r=this.dropdown.getFooterRef();t&&a?.parentNode.replaceChild(t,a),n&&r?.parentNode.replaceChild(n,r)},refilter(e){e=e||this.state.dropdown.query||"",this.suggestedListItems=this.dropdown.filterListItems(e),this.dropdown.fill(),this.suggestedListItems.length||this.dropdown.hide(),this.trigger("dropdown:updated",this.DOM.dropdown)},position(e){var t=this.settings.dropdown;if("manual"!=t.position){var n,a,r,i,o,s,l=this.DOM.dropdown,c=t.placeAbove,u=t.appendTarget===document.body,p=u?window.pageYOffset:t.appendTarget.scrollTop,d=document.fullscreenElement||document.webkitFullscreenElement||document.documentElement,m=d.clientHeight,h=Math.max(d.clientWidth||0,window.innerWidth||0)>480?t.position:"all",g=this.DOM["input"==h?"input":"scope"];if(e=e||l.clientHeight,this.state.dropdown.visible){if("text"==h?(r=(n=function(){const e=document.getSelection();if(e.rangeCount){const t=e.getRangeAt(0),n=t.startContainer,a=t.startOffset;let r,i;if(a>0)return i=document.createRange(),i.setStart(n,a-1),i.setEnd(n,a),r=i.getBoundingClientRect(),{left:r.right,top:r.top,bottom:r.bottom};if(n.getBoundingClientRect)return n.getBoundingClientRect()}return{left:-9999,top:-9999}}()).bottom,a=n.top,i=n.left,o="auto"):(s=function(e){for(var t=0,n=0;e&&e!=d;)t+=e.offsetLeft||0,n+=e.offsetTop||0,e=e.parentNode;return{left:t,top:n}}(t.appendTarget),a=(n=g.getBoundingClientRect()).top-s.top,r=n.bottom-1-s.top,i=n.left-s.left,o=n.width+"px"),!u){let e=function(){for(var e=0,n=t.appendTarget.parentNode;n;)e+=n.scrollTop||0,n=n.parentNode;return e}();a+=e,r+=e}a=Math.floor(a),r=Math.ceil(r),c=void 0===c?m-n.bottom<e:c,l.style.cssText="left:"+(i+window.pageXOffset)+"px; width:"+o+";"+(c?"top: "+(a+p)+"px":"top: "+(r+p)+"px"),l.setAttribute("placement",c?"top":"bottom"),l.setAttribute("position",h)}}},events:{binding(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];var t=this.dropdown.events.callbacks,n=this.listeners.dropdown=this.listeners.dropdown||{position:this.dropdown.position.bind(this,null),onKeyDown:t.onKeyDown.bind(this),onMouseOver:t.onMouseOver.bind(this),onMouseLeave:t.onMouseLeave.bind(this),onClick:t.onClick.bind(this),onScroll:t.onScroll.bind(this)},a=e?"addEventListener":"removeEventListener";"manual"!=this.settings.dropdown.position&&(document[a]("scroll",n.position,!0),window[a]("resize",n.position),window[a]("keydown",n.onKeyDown)),this.DOM.dropdown[a]("mouseover",n.onMouseOver),this.DOM.dropdown[a]("mouseleave",n.onMouseLeave),this.DOM.dropdown[a]("mousedown",n.onClick),this.DOM.dropdown.content[a]("scroll",n.onScroll)},callbacks:{onKeyDown(e){if(this.state.hasFocus&&!this.state.composing){var t=this.DOM.dropdown.querySelector(this.settings.classNames.dropdownItemActiveSelector),n=this.dropdown.getSuggestionDataByNode(t);switch(e.key){case"ArrowDown":case"ArrowUp":case"Down":case"Up":e.preventDefault();var a=this.dropdown.getAllSuggestionsRefs(),r="ArrowUp"==e.key||"Up"==e.key;t&&(t=this.dropdown.getNextOrPrevOption(t,!r)),t&&t.matches(this.settings.classNames.dropdownItemSelector)||(t=a[r?a.length-1:0]),this.dropdown.highlightOption(t,!0);break;case"Escape":case"Esc":this.dropdown.hide();break;case"ArrowRight":if(this.state.actions.ArrowLeft)return;case"Tab":if("mix"!=this.settings.mode&&t&&!this.settings.autoComplete.rightKey&&!this.state.editing){e.preventDefault();var i=this.dropdown.getMappedValue(n);return this.input.autocomplete.set.call(this,i),!1}return!0;case"Enter":e.preventDefault(),this.settings.hooks.suggestionClick(e,{tagify:this,tagData:n,suggestionElm:t}).then((()=>{if(t)return this.dropdown.selectOption(t),t=this.dropdown.getNextOrPrevOption(t,!r),void this.dropdown.highlightOption(t);this.dropdown.hide(),"mix"!=this.settings.mode&&this.addTags(this.state.inputText.trim(),!0)})).catch((e=>e));break;case"Backspace":{if("mix"==this.settings.mode||this.state.editing.scope)return;const e=this.input.raw.call(this);""!=e&&8203!=e.charCodeAt(0)||(!0===this.settings.backspace?this.removeTags():"edit"==this.settings.backspace&&setTimeout(this.editTag.bind(this),0))}}}},onMouseOver(e){var t=e.target.closest(this.settings.classNames.dropdownItemSelector);t&&this.dropdown.highlightOption(t)},onMouseLeave(e){this.dropdown.highlightOption()},onClick(e){if(0==e.button&&e.target!=this.DOM.dropdown&&e.target!=this.DOM.dropdown.content){var t=e.target.closest(this.settings.classNames.dropdownItemSelector),n=this.dropdown.getSuggestionDataByNode(t);this.state.actions.selectOption=!0,setTimeout((()=>this.state.actions.selectOption=!1),50),this.settings.hooks.suggestionClick(e,{tagify:this,tagData:n,suggestionElm:t}).then((()=>{t?this.dropdown.selectOption(t,e):this.dropdown.hide()})).catch((e=>console.warn(e)))}},onScroll(e){var t=e.target,n=t.scrollTop/(t.scrollHeight-t.parentNode.clientHeight)*100;this.trigger("dropdown:scroll",{percentage:Math.round(n)})}}},getSuggestionDataByNode(e){var t=e&&e.getAttribute("value");return this.suggestedListItems.find((e=>e.value==t))||null},getNextOrPrevOption(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];var n=this.dropdown.getAllSuggestionsRefs(),a=n.findIndex((t=>t===e));return t?n[a+1]:n[a-1]},highlightOption(e,t){var n,a=this.settings.classNames.dropdownItemActive;if(this.state.ddItemElm&&(this.state.ddItemElm.classList.remove(a),this.state.ddItemElm.removeAttribute("aria-selected")),!e)return this.state.ddItemData=null,this.state.ddItemElm=null,void this.input.autocomplete.suggest.call(this);n=this.dropdown.getSuggestionDataByNode(e),this.state.ddItemData=n,this.state.ddItemElm=e,e.classList.add(a),e.setAttribute("aria-selected",!0),t&&(e.parentNode.scrollTop=e.clientHeight+e.offsetTop-e.parentNode.clientHeight),this.settings.autoComplete&&(this.input.autocomplete.suggest.call(this,n),this.dropdown.position())},selectOption(e,t){var n=this.settings.dropdown,a=n.clearOnSelect,r=n.closeOnSelect;if(!e)return this.addTags(this.state.inputText,!0),void(r&&this.dropdown.hide());t=t||{};var i=e.getAttribute("value"),o="noMatch"==i,s=this.suggestedListItems.find((e=>(e.value??e)==i));this.trigger("dropdown:select",{data:s,elm:e,event:t}),i&&(s||o)?(this.state.editing?this.onEditTagDone(null,d({__isValid:!0},this.normalizeTags([s])[0])):this["mix"==this.settings.mode?"addMixTags":"addTags"]([s||this.input.raw.call(this)],a),this.DOM.input.parentNode&&(setTimeout((()=>{this.DOM.input.focus(),this.toggleFocusClass(!0)})),r&&setTimeout(this.dropdown.hide.bind(this)),e.addEventListener("transitionend",(()=>{this.dropdown.fillHeaderFooter(),setTimeout((()=>e.remove()),100)}),{once:!0}),e.classList.add(this.settings.classNames.dropdownItemHidden))):r&&setTimeout(this.dropdown.hide.bind(this))},selectAll(e){this.suggestedListItems.length=0,this.dropdown.hide(),this.dropdown.filterListItems("");var t=this.dropdown.filterListItems("");return e||(t=this.state.dropdown.suggestions),this.addTags(t,!0),this},filterListItems(e,t){var n,a,r,i,o,s=this.settings,l=s.dropdown,c=(t=t||{},[]),u=[],d=s.whitelist,m=l.maxItems>=0?l.maxItems:1/0,g=l.searchKeys,f=0;if(!(e="select"==s.mode&&this.value.length&&this.value[0][s.tagTextProp]==e?"":e)||!g.length)return c=l.includeSelectedTags?d:d.filter((e=>!this.isTagDuplicate(p(e)?e.value:e))),this.state.dropdown.suggestions=c,c.slice(0,m);function w(e,t){return t.toLowerCase().split(" ").every((t=>e.includes(t.toLowerCase())))}for(o=l.caseSensitive?""+e:(""+e).toLowerCase();f<d.length;f++){let e,s;n=d[f]instanceof Object?d[f]:{value:d[f]};let m=Object.keys(n).some((e=>g.includes(e)))?g:["value"];l.fuzzySearch&&!t.exact?(r=m.reduce(((e,t)=>e+" "+(n[t]||"")),"").toLowerCase().trim(),l.accentedSearch&&(r=h(r),o=h(o)),e=0==r.indexOf(o),s=r===o,a=w(r,o)):(e=!0,a=m.some((e=>{var a=""+(n[e]||"");return l.accentedSearch&&(a=h(a),o=h(o)),l.caseSensitive||(a=a.toLowerCase()),s=a===o,t.exact?a===o:0==a.indexOf(o)}))),i=!l.includeSelectedTags&&this.isTagDuplicate(p(n)?n.value:n),a&&!i&&(s&&e?u.push(n):"startsWith"==l.sortby&&e?c.unshift(n):c.push(n))}return this.state.dropdown.suggestions=u.concat(c),"function"==typeof l.sortby?l.sortby(u.concat(c),o):u.concat(c).slice(0,m)},getMappedValue(e){var t=this.settings.dropdown.mapValueTo;return t?"function"==typeof t?t(e):e[t]||e.value:e.value},createListHTML(e){return d([],e).map(((e,n)=>{"string"!=typeof e&&"number"!=typeof e||(e={value:e});var a=this.dropdown.getMappedValue(e);return a="string"==typeof a?u(a):a,this.settings.templates.dropdownItem.apply(this,[t(t({},e),{},{mappedValue:a}),this])})).join("")}};const x="@yaireo/tagify/";var O,S={empty:"empty",exceed:"number of tags exceeded",pattern:"pattern mismatch",duplicate:"already exists",notAllowed:"not allowed"},C={wrapper:(e,t)=>`<tags class="${t.classNames.namespace} ${t.mode?`${t.classNames[t.mode+"Mode"]}`:""} ${e.className}"\n                    ${t.readonly?"readonly":""}\n                    ${t.disabled?"disabled":""}\n                    ${t.required?"required":""}\n                    ${"select"===t.mode?"spellcheck='false'":""}\n                    tabIndex="-1">\n            <span ${!t.readonly&&t.userInput?"contenteditable":""} tabIndex="0" data-placeholder="${t.placeholder||"&#8203;"}" aria-placeholder="${t.placeholder||""}"\n                class="${t.classNames.input}"\n                role="textbox"\n                aria-autocomplete="both"\n                aria-multiline="${"mix"==t.mode}"></span>\n                &#8203;\n        </tags>`,tag(e,t){let n=t.settings;return`<tag title="${e.title||e.value}"\n                    contenteditable='false'\n                    spellcheck='false'\n                    tabIndex="${n.a11y.focusableTags?0:-1}"\n                    class="${n.classNames.tag} ${e.class||""}"\n                    ${this.getAttributes(e)}>\n            <x title='' class="${n.classNames.tagX}" role='button' aria-label='remove tag'></x>\n            <div>\n                <span class="${n.classNames.tagText}">${e[n.tagTextProp]||e.value}</span>\n            </div>\n        </tag>`},dropdown(e){var t=e.dropdown,n="manual"==t.position,a=`${e.classNames.dropdown}`;return`<div class="${n?"":a} ${t.classname}" role="listbox" aria-labelledby="dropdown">\n                    <div data-selector='tagify-suggestions-wrapper' class="${e.classNames.dropdownWrapper}"></div>\n                </div>`},dropdownContent(e){var t=this.settings,n=this.state.dropdown.suggestions;return`\n            ${t.templates.dropdownHeader.call(this,n)}\n            ${e}\n            ${t.templates.dropdownFooter.call(this,n)}\n        `},dropdownItem(e){return`<div ${this.getAttributes(e)}\n                    class='${this.settings.classNames.dropdownItem} ${e.class?e.class:""}'\n                    tabindex="0"\n                    role="option">${e.mappedValue||e.value}</div>`},dropdownHeader(e){return`<header data-selector='tagify-suggestions-header' class="${this.settings.classNames.dropdownHeader}"></header>`},dropdownFooter(e){var t=e.length-this.settings.dropdown.maxItems;return t>0?`<footer data-selector='tagify-suggestions-footer' class="${this.settings.classNames.dropdownFooter}">\n                ${t} more items. Refine your search.\n            </footer>`:""},dropdownItemNoMatch:null},N={customBinding(){this.customEventsList.forEach((e=>{this.on(e,this.settings.callbacks[e])}))},binding(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];var t,n=this.events.callbacks,a=e?"addEventListener":"removeEventListener";if(!this.state.mainEvents||!e){for(var r in this.state.mainEvents=e,e&&!this.listeners.main&&(this.events.bindGlobal.call(this),this.settings.isJQueryPlugin&&jQuery(this.DOM.originalInput).on("tagify.removeAllTags",this.removeAllTags.bind(this))),t=this.listeners.main=this.listeners.main||{focus:["input",n.onFocusBlur.bind(this)],keydown:["input",n.onKeydown.bind(this)],click:["scope",n.onClickScope.bind(this)],dblclick:["scope",n.onDoubleClickScope.bind(this)],paste:["input",n.onPaste.bind(this)],drop:["input",n.onDrop.bind(this)],compositionstart:["input",n.onCompositionStart.bind(this)],compositionend:["input",n.onCompositionEnd.bind(this)]})this.DOM[t[r][0]][a](r,t[r][1]);clearInterval(this.listeners.main.originalInputValueObserverInterval),this.listeners.main.originalInputValueObserverInterval=setInterval(n.observeOriginalInputValue.bind(this),500);var i=this.listeners.main.inputMutationObserver||new MutationObserver(n.onInputDOMChange.bind(this));i.disconnect(),"mix"==this.settings.mode&&i.observe(this.DOM.input,{childList:!0})}},bindGlobal(e){var t,n=this.events.callbacks,a=e?"removeEventListener":"addEventListener";if(this.listeners&&(e||!this.listeners.global))for(t of(this.listeners.global=this.listeners.global||[{type:this.isIE?"keydown":"input",target:this.DOM.input,cb:n[this.isIE?"onInputIE":"onInput"].bind(this)},{type:"keydown",target:window,cb:n.onWindowKeyDown.bind(this)},{type:"blur",target:this.DOM.input,cb:n.onFocusBlur.bind(this)},{type:"click",target:document,cb:n.onClickAnywhere.bind(this)}],this.listeners.global))t.target[a](t.type,t.cb)},unbindGlobal(){this.events.bindGlobal.call(this,!0)},callbacks:{onFocusBlur(e){var t=this.settings,n=e.target?this.trim(e.target.textContent):"",a=this.value?.[0]?.[t.tagTextProp],r=e.type,i=t.dropdown.enabled>=0,o={relatedTarget:e.relatedTarget},s=this.state.actions.selectOption&&(i||!t.dropdown.closeOnSelect),l=this.state.actions.addNew&&i,c=e.relatedTarget&&w.call(this,e.relatedTarget)&&this.DOM.scope.contains(e.relatedTarget);if("blur"==r){if(e.relatedTarget===this.DOM.scope)return this.dropdown.hide(),void this.DOM.input.focus();this.postUpdate(),t.onChangeAfterBlur&&this.triggerChangeEvent()}if(!s&&!l)if(this.state.hasFocus="focus"==r&&+new Date,this.toggleFocusClass(this.state.hasFocus),"mix"!=t.mode){if("focus"==r)return this.trigger("focus",o),void(0!==t.dropdown.enabled&&t.userInput||this.dropdown.show(this.value.length?"":void 0));"blur"==r&&(this.trigger("blur",o),this.loading(!1),"select"==t.mode&&(c&&(this.removeTags(),n=""),a===n&&(n="")),n&&!this.state.actions.selectOption&&t.addTagOnBlur&&this.addTags(n,!0)),this.DOM.input.removeAttribute("style"),this.dropdown.hide()}else"focus"==r?this.trigger("focus",o):"blur"==e.type&&(this.trigger("blur",o),this.loading(!1),this.dropdown.hide(),this.state.dropdown.visible=void 0,this.setStateSelection())},onCompositionStart(e){this.state.composing=!0},onCompositionEnd(e){this.state.composing=!1},onWindowKeyDown(e){var t,n=document.activeElement,a=w.call(this,n)&&this.DOM.scope.contains(document.activeElement),r=a&&n.hasAttribute("readonly");if(a&&!r)switch(t=n.nextElementSibling,e.key){case"Backspace":this.settings.readonly||(this.removeTags(n),(t||this.DOM.input).focus());break;case"Enter":setTimeout(this.editTag.bind(this),0,n)}},onKeydown(e){var t=this.settings;if(!this.state.composing&&t.userInput){"select"==t.mode&&t.enforceWhitelist&&this.value.length&&"Tab"!=e.key&&e.preventDefault();var n=this.trim(e.target.textContent);if(this.trigger("keydown",{event:e}),"mix"==t.mode){switch(e.key){case"Left":case"ArrowLeft":this.state.actions.ArrowLeft=!0;break;case"Delete":case"Backspace":if(this.state.editing)return;var a=document.getSelection(),r="Delete"==e.key&&a.anchorOffset==(a.anchorNode.length||0),i=a.anchorNode.previousSibling,o=1==a.anchorNode.nodeType||!a.anchorOffset&&i&&1==i.nodeType&&a.anchorNode.previousSibling;s(this.DOM.input.innerHTML);var l,u,p,d=this.getTagElms(),m=1===a.anchorNode.length&&a.anchorNode.nodeValue==String.fromCharCode(8203);if("edit"==t.backspace&&o)return l=1==a.anchorNode.nodeType?null:a.anchorNode.previousElementSibling,setTimeout(this.editTag.bind(this),0,l),void e.preventDefault();if(g()&&o instanceof Element)return p=c(o),o.hasAttribute("readonly")||o.remove(),this.DOM.input.focus(),void setTimeout((()=>{b(p),this.DOM.input.click()}));if("BR"==a.anchorNode.nodeName)return;if((r||o)&&1==a.anchorNode.nodeType?u=0==a.anchorOffset?r?d[0]:null:d[Math.min(d.length,a.anchorOffset)-1]:r?u=a.anchorNode.nextElementSibling:o instanceof Element&&(u=o),3==a.anchorNode.nodeType&&!a.anchorNode.nodeValue&&a.anchorNode.previousElementSibling&&e.preventDefault(),(o||r)&&!t.backspace)return void e.preventDefault();if("Range"!=a.type&&!a.anchorOffset&&a.anchorNode==this.DOM.input&&"Delete"!=e.key)return void e.preventDefault();if("Range"!=a.type&&u&&u.hasAttribute("readonly"))return void b(c(u));"Delete"==e.key&&m&&y(a.anchorNode.nextSibling)&&this.removeTags(a.anchorNode.nextSibling),clearTimeout(O),O=setTimeout((()=>{var e=document.getSelection();s(this.DOM.input.innerHTML),!r&&e.anchorNode.previousSibling,this.value=[].map.call(d,((e,t)=>{var n=y(e);if(e.parentNode||n.readonly)return n;this.trigger("remove",{tag:e,index:t,data:n})})).filter((e=>e))}),20)}return!0}switch(e.key){case"Backspace":"select"==t.mode&&t.enforceWhitelist&&this.value.length?this.removeTags():this.state.dropdown.visible&&"manual"!=t.dropdown.position||""!=e.target.textContent&&8203!=n.charCodeAt(0)||(!0===t.backspace?this.removeTags():"edit"==t.backspace&&setTimeout(this.editTag.bind(this),0));break;case"Esc":case"Escape":if(this.state.dropdown.visible)return;e.target.blur();break;case"Down":case"ArrowDown":this.state.dropdown.visible||this.dropdown.show();break;case"ArrowRight":{let e=this.state.inputSuggestion||this.state.ddItemData;if(e&&t.autoComplete.rightKey)return void this.addTags([e],!0);break}case"Tab":{let a="select"==t.mode;if(!n||a)return!0;e.preventDefault()}case"Enter":if(this.state.dropdown.visible&&"manual"!=t.dropdown.position)return;e.preventDefault(),setTimeout((()=>{this.state.dropdown.visible||this.state.actions.selectOption||this.addTags(n,!0)}))}}},onInput(e){this.postUpdate();var t=this.settings;if("mix"==t.mode)return this.events.callbacks.onMixTagsInput.call(this,e);var n=this.input.normalize.call(this),a=n.length>=t.dropdown.enabled,r={value:n,inputElm:this.DOM.input},i=this.validateTag({value:n});"select"==t.mode&&this.toggleScopeValidation(i),r.isValid=i,this.state.inputText!=n&&(this.input.set.call(this,n,!1),-1!=n.search(t.delimiters)?this.addTags(n)&&this.input.set.call(this):t.dropdown.enabled>=0&&this.dropdown[a?"show":"hide"](n),this.trigger("input",r))},onMixTagsInput(e){var t,n,a,r,i,o,s,l,c=this.settings,u=this.value.length,p=this.getTagElms(),m=document.createDocumentFragment(),h=window.getSelection().getRangeAt(0),f=[].map.call(p,(e=>y(e).value));if("deleteContentBackward"==e.inputType&&g()&&this.events.callbacks.onKeydown.call(this,{target:e.target,key:"Backspace"}),k(this.getTagElms()),this.value.slice().forEach((e=>{e.readonly&&!f.includes(e.value)&&m.appendChild(this.createTagElem(e))})),m.childNodes.length&&(h.insertNode(m),this.setRangeAtStartEnd(!1,m.lastChild)),p.length!=u)return this.value=[].map.call(this.getTagElms(),(e=>y(e))),void this.update({withoutChangeEvent:!0});if(this.hasMaxTags())return!0;if(window.getSelection&&(o=window.getSelection()).rangeCount>0&&3==o.anchorNode.nodeType){if((h=o.getRangeAt(0).cloneRange()).collapse(!0),h.setStart(o.focusNode,0),a=(t=h.toString().slice(0,h.endOffset)).split(c.pattern).length-1,(n=t.match(c.pattern))&&(r=t.slice(t.lastIndexOf(n[n.length-1]))),r){if(this.state.actions.ArrowLeft=!1,this.state.tag={prefix:r.match(c.pattern)[0],value:r.replace(c.pattern,"")},this.state.tag.baseOffset=o.baseOffset-this.state.tag.value.length,l=this.state.tag.value.match(c.delimiters))return this.state.tag.value=this.state.tag.value.replace(c.delimiters,""),this.state.tag.delimiters=l[0],this.addTags(this.state.tag.value,c.dropdown.clearOnSelect),void this.dropdown.hide();i=this.state.tag.value.length>=c.dropdown.enabled;try{s=(s=this.state.flaggedTags[this.state.tag.baseOffset]).prefix==this.state.tag.prefix&&s.value[0]==this.state.tag.value[0],this.state.flaggedTags[this.state.tag.baseOffset]&&!this.state.tag.value&&delete this.state.flaggedTags[this.state.tag.baseOffset]}catch(e){}(s||a<this.state.mixMode.matchedPatternCount)&&(i=!1)}else this.state.flaggedTags={};this.state.mixMode.matchedPatternCount=a}setTimeout((()=>{this.update({withoutChangeEvent:!0}),this.trigger("input",d({},this.state.tag,{textContent:this.DOM.input.textContent})),this.state.tag&&this.dropdown[i?"show":"hide"](this.state.tag.value)}),10)},onInputIE(e){var t=this;setTimeout((function(){t.events.callbacks.onInput.call(t,e)}))},observeOriginalInputValue(){this.DOM.originalInput.parentNode||this.destroy(),this.DOM.originalInput.value!=this.DOM.originalInput.tagifyValue&&this.loadOriginalValues()},onClickAnywhere(e){e.target==this.DOM.scope||this.DOM.scope.contains(e.target)||(this.toggleFocusClass(!1),this.state.hasFocus=!1)},onClickScope(e){var t=this.settings,n=e.target.closest("."+t.classNames.tag),a=+new Date-this.state.hasFocus;if(e.target!=this.DOM.scope){if(!e.target.classList.contains(t.classNames.tagX))return n?(this.trigger("click",{tag:n,index:this.getNodeIndex(n),data:y(n),event:e}),void(1!==t.editTags&&1!==t.editTags.clicks||this.events.callbacks.onDoubleClickScope.call(this,e))):void(e.target==this.DOM.input&&("mix"==t.mode&&this.fixFirefoxLastTagNoCaret(),a>500)?this.state.dropdown.visible?this.dropdown.hide():0===t.dropdown.enabled&&"mix"!=t.mode&&this.dropdown.show(this.value.length?"":void 0):"select"!=t.mode||0!==t.dropdown.enabled||this.state.dropdown.visible||this.dropdown.show());this.removeTags(e.target.parentNode)}else this.DOM.input.focus()},onPaste(e){e.preventDefault();var t,n,a=this.settings;if("select"==a.mode&&a.enforceWhitelist||!a.userInput)return!1;a.readonly||(t=e.clipboardData||window.clipboardData,n=t.getData("Text"),a.hooks.beforePaste(e,{tagify:this,pastedText:n,clipboardData:t}).then((t=>{void 0===t&&(t=n),t&&(this.injectAtCaret(t,window.getSelection().getRangeAt(0)),"mix"==this.settings.mode?this.events.callbacks.onMixTagsInput.call(this,e):this.settings.pasteAsTags?this.addTags(this.state.inputText+t,!0):this.state.inputText=t)})).catch((e=>e)))},onDrop(e){e.preventDefault()},onEditTagInput(e,t){var n=e.closest("."+this.settings.classNames.tag),a=this.getNodeIndex(n),r=y(n),i=this.input.normalize.call(this,e),o={[this.settings.tagTextProp]:i,__tagId:r.__tagId},s=this.validateTag(o);this.editTagChangeDetected(d(r,o))||!0!==e.originalIsValid||(s=!0),n.classList.toggle(this.settings.classNames.tagInvalid,!0!==s),r.__isValid=s,n.title=!0===s?r.title||r.value:s,i.length>=this.settings.dropdown.enabled&&(this.state.editing&&(this.state.editing.value=i),this.dropdown.show(i)),this.trigger("edit:input",{tag:n,index:a,data:d({},this.value[a],{newValue:i}),event:t})},onEditTagPaste(e,t){var n=(t.clipboardData||window.clipboardData).getData("Text");t.preventDefault();var a=v(n);this.setRangeAtStartEnd(!1,a)},onEditTagFocus(e){this.state.editing={scope:e,input:e.querySelector("[contenteditable]")}},onEditTagBlur(e){if(this.state.hasFocus||this.toggleFocusClass(),this.DOM.scope.contains(e)){var t,n,a=this.settings,r=e.closest("."+a.classNames.tag),i=y(r),o=this.input.normalize.call(this,e),s={[a.tagTextProp]:o,__tagId:i.__tagId},l=i.__originalData,c=this.editTagChangeDetected(d(i,s)),u=this.validateTag(s);if(o)if(c){if(t=this.hasMaxTags(),n=d({},l,{[a.tagTextProp]:this.trim(o),__isValid:u}),a.transformTag.call(this,n,l),!0!==(u=(!t||!0===l.__isValid)&&this.validateTag(n))){if(this.trigger("invalid",{data:n,tag:r,message:u}),a.editTags.keepInvalid)return;a.keepInvalidTags?n.__isValid=u:n=l}else a.keepInvalidTags&&(delete n.title,delete n["aria-invalid"],delete n.class);this.onEditTagDone(r,n)}else this.onEditTagDone(r,l);else this.onEditTagDone(r)}},onEditTagkeydown(e,t){if(!this.state.composing)switch(this.trigger("edit:keydown",{event:e}),e.key){case"Esc":case"Escape":t.parentNode.replaceChild(t.__tagifyTagData.__originalHTML,t),this.state.editing=!1;case"Enter":case"Tab":e.preventDefault(),e.target.blur()}},onDoubleClickScope(e){var t,n,a=e.target.closest("."+this.settings.classNames.tag),r=y(a),i=this.settings;a&&i.userInput&&!1!==r.editable&&(t=a.classList.contains(this.settings.classNames.tagEditing),n=a.hasAttribute("readonly"),"select"==i.mode||i.readonly||t||n||!this.settings.editTags||this.editTag(a),this.toggleFocusClass(!0),this.trigger("dblclick",{tag:a,index:this.getNodeIndex(a),data:y(a)}))},onInputDOMChange(e){e.forEach((e=>{e.addedNodes.forEach((e=>{if("<div><br></div>"==e.outerHTML)e.replaceWith(document.createElement("br"));else if(1==e.nodeType&&e.querySelector(this.settings.classNames.tagSelector)){let t=document.createTextNode("");3==e.childNodes[0].nodeType&&"BR"!=e.previousSibling.nodeName&&(t=document.createTextNode("\n")),e.replaceWith(t,...[...e.childNodes].slice(0,-1)),b(t)}else if(w.call(this,e))if(3!=e.previousSibling?.nodeType||e.previousSibling.textContent||e.previousSibling.remove(),e.previousSibling&&"BR"==e.previousSibling.nodeName){e.previousSibling.replaceWith("\n"+a);let t=e.nextSibling,n="";for(;t;)n+=t.textContent,t=t.nextSibling;n.trim()&&b(e.previousSibling)}else e.previousSibling&&!y(e.previousSibling)||e.before(a)})),e.removedNodes.forEach((e=>{e&&"BR"==e.nodeName&&w.call(this,t)&&(this.removeTags(t),this.fixFirefoxLastTagNoCaret())}))}));var t=this.DOM.input.lastChild;t&&""==t.nodeValue&&t.remove(),t&&"BR"==t.nodeName||this.DOM.input.appendChild(document.createElement("br"))}}};function I(e,t){if(!e){console.warn("Tagify:","input element not found",e);const t=new Proxy(this,{get:()=>()=>t});return t}if(e.__tagify)return console.warn("Tagify: ","input element is already Tagified - Same instance is returned.",e),e.__tagify;var n;d(this,function(e){var t=document.createTextNode("");function n(e,n,a){a&&n.split(/\s+/g).forEach((n=>t[e+"EventListener"].call(t,n,a)))}return{off(e,t){return n("remove",e,t),this},on(e,t){return t&&"function"==typeof t&&n("add",e,t),this},trigger(n,a,r){var i;if(r=r||{cloneData:!0},n)if(e.settings.isJQueryPlugin)"remove"==n&&(n="removeTag"),jQuery(e.DOM.originalInput).triggerHandler(n,[a]);else{try{var o="object"==typeof a?a:{value:a};if((o=r.cloneData?d({},o):o).tagify=this,a.event&&(o.event=this.cloneEvent(a.event)),a instanceof Object)for(var s in a)a[s]instanceof HTMLElement&&(o[s]=a[s]);i=new CustomEvent(n,{detail:o})}catch(e){console.warn(e)}t.dispatchEvent(i)}}}}(this)),this.isFirefox=/firefox|fxios/i.test(navigator.userAgent)&&!/seamonkey/i.test(navigator.userAgent),this.isIE=window.document.documentMode,t=t||{},this.getPersistedData=(n=t.id,e=>{let t,a="/"+e;if(1==localStorage.getItem(x+n+"/v",1))try{t=JSON.parse(localStorage[x+n+a])}catch(e){}return t}),this.setPersistedData=(e=>e?(localStorage.setItem(x+e+"/v",1),(t,n)=>{let a="/"+n,r=JSON.stringify(t);t&&n&&(localStorage.setItem(x+e+a,r),dispatchEvent(new Event("storage")))}):()=>{})(t.id),this.clearPersistedData=(e=>t=>{const n=x+"/"+e+"/";if(t)localStorage.removeItem(n+t);else for(let e in localStorage)e.includes(n)&&localStorage.removeItem(e)})(t.id),this.applySettings(e,t),this.state={inputText:"",editing:!1,composing:!1,actions:{},mixMode:{},dropdown:{},flaggedTags:{}},this.value=[],this.listeners={},this.DOM={},this.build(e),_.call(this),this.getCSSVars(),this.loadOriginalValues(),this.events.customBinding.call(this),this.events.binding.call(this),e.autofocus&&this.DOM.input.focus(),e.__tagify=this}return I.prototype={_dropdown:T,getSetTagData:y,helpers:{sameStr:r,removeCollectionProp:i,omit:o,isObject:p,parseHTML:l,escapeHTML:u,extend:d,concatWithoutDups:m,getUID:f,isNodeTag:w},customEventsList:["change","add","remove","invalid","input","click","keydown","focus","blur","edit:input","edit:beforeUpdate","edit:updated","edit:start","edit:keydown","dropdown:show","dropdown:hide","dropdown:select","dropdown:updated","dropdown:noMatch","dropdown:scroll"],dataProps:["__isValid","__removed","__originalData","__originalHTML","__tagId"],trim(e){return this.settings.trim&&e&&"string"==typeof e?e.trim():e},parseHTML:l,templates:C,parseTemplate(e,t){return l((e=this.settings.templates[e]||e).apply(this,t))},set whitelist(e){const t=e&&Array.isArray(e);this.settings.whitelist=t?e:[],this.setPersistedData(t?e:[],"whitelist")},get whitelist(){return this.settings.whitelist},generateClassSelectors(e){for(let t in e){let n=t;Object.defineProperty(e,n+"Selector",{get(){return"."+this[n].split(" ")[0]}})}},applySettings(e,n){E.templates=this.templates;var a=d({},E,"mix"==n.mode?{dropdown:{position:"text"}}:{}),r=this.settings=d({},a,n);if(r.disabled=e.hasAttribute("disabled"),r.readonly=r.readonly||e.hasAttribute("readonly"),r.placeholder=u(e.getAttribute("placeholder")||r.placeholder||""),r.required=e.hasAttribute("required"),this.generateClassSelectors(r.classNames),void 0===r.dropdown.includeSelectedTags&&(r.dropdown.includeSelectedTags=r.duplicates),this.isIE&&(r.autoComplete=!1),["whitelist","blacklist"].forEach((t=>{var n=e.getAttribute("data-"+t);n&&(n=n.split(r.delimiters))instanceof Array&&(r[t]=n)})),"autoComplete"in n&&!p(n.autoComplete)&&(r.autoComplete=E.autoComplete,r.autoComplete.enabled=n.autoComplete),"mix"==r.mode&&(r.pattern=r.pattern||/@/,r.autoComplete.rightKey=!0,r.delimiters=n.delimiters||null,r.tagTextProp&&!r.dropdown.searchKeys.includes(r.tagTextProp)&&r.dropdown.searchKeys.push(r.tagTextProp)),e.pattern)try{r.pattern=new RegExp(e.pattern)}catch(e){}if(r.delimiters){r._delimiters=r.delimiters;try{r.delimiters=new RegExp(this.settings.delimiters,"g")}catch(e){}}r.disabled&&(r.userInput=!1),this.TEXTS=t(t({},S),r.texts||{}),("select"!=r.mode||n.dropdown?.enabled)&&r.userInput||(r.dropdown.enabled=0),r.dropdown.appendTarget=n.dropdown?.appendTarget||document.body;let i=this.getPersistedData("whitelist");Array.isArray(i)&&(this.whitelist=Array.isArray(r.whitelist)?m(r.whitelist,i):i)},getAttributes(e){var t,n=this.getCustomAttributes(e),a="";for(t in n)a+=" "+t+(void 0!==e[t]?`="${n[t]}"`:"");return a},getCustomAttributes(e){if(!p(e))return"";var t,n={};for(t in e)"__"!=t.slice(0,2)&&"class"!=t&&e.hasOwnProperty(t)&&void 0!==e[t]&&(n[t]=u(e[t]));return n},setStateSelection(){var e=window.getSelection(),t={anchorOffset:e.anchorOffset,anchorNode:e.anchorNode,range:e.getRangeAt&&e.rangeCount&&e.getRangeAt(0)};return this.state.selection=t,t},getCSSVars(){var e,t=getComputedStyle(this.DOM.scope,null);this.CSSVars={tagHideTransition:(e=>{let t=e.value;return"s"==e.unit?1e3*t:t})(function(e){if(!e)return{};var t=(e=e.trim().split(" ")[0]).split(/\d+/g).filter((e=>e)).pop().trim();return{value:+e.split(t).filter((e=>e))[0].trim(),unit:t}}((e="tag-hide-transition",t.getPropertyValue("--"+e))))}},build(e){var t=this.DOM;this.settings.mixMode.integrated?(t.originalInput=null,t.scope=e,t.input=e):(t.originalInput=e,t.originalInput_tabIndex=e.tabIndex,t.scope=this.parseTemplate("wrapper",[e,this.settings]),t.input=t.scope.querySelector(this.settings.classNames.inputSelector),e.parentNode.insertBefore(t.scope,e),e.tabIndex=-1)},destroy(){this.events.unbindGlobal.call(this),this.DOM.scope.parentNode.removeChild(this.DOM.scope),this.DOM.originalInput.tabIndex=this.DOM.originalInput_tabIndex,delete this.DOM.originalInput.__tagify,this.dropdown.hide(!0),clearTimeout(this.dropdownHide__bindEventsTimeout),clearInterval(this.listeners.main.originalInputValueObserverInterval)},loadOriginalValues(e){var t,n=this.settings;if(this.state.blockChangeEvent=!0,void 0===e){const t=this.getPersistedData("value");e=t&&!this.DOM.originalInput.value?t:n.mixMode.integrated?this.DOM.input.textContent:this.DOM.originalInput.value}if(this.removeAllTags(),e)if("mix"==n.mode)this.parseMixTags(e),(t=this.DOM.input.lastChild)&&"BR"==t.tagName||this.DOM.input.insertAdjacentHTML("beforeend","<br>");else{try{JSON.parse(e)instanceof Array&&(e=JSON.parse(e))}catch(e){}this.addTags(e,!0).forEach((e=>e&&e.classList.add(n.classNames.tagNoAnimation)))}else this.postUpdate();this.state.lastOriginalValueReported=n.mixMode.integrated?"":this.DOM.originalInput.value},cloneEvent(e){var t={};for(var n in e)"path"!=n&&(t[n]=e[n]);return t},loading(e){return this.state.isLoading=e,this.DOM.scope.classList[e?"add":"remove"](this.settings.classNames.scopeLoading),this},tagLoading(e,t){return e&&e.classList[t?"add":"remove"](this.settings.classNames.tagLoading),this},toggleClass(e,t){"string"==typeof e&&this.DOM.scope.classList.toggle(e,t)},toggleScopeValidation(e){var t=!0===e||void 0===e;!this.settings.required&&e&&e===this.TEXTS.empty&&(t=!0),this.toggleClass(this.settings.classNames.tagInvalid,!t),this.DOM.scope.title=t?"":e},toggleFocusClass(e){this.toggleClass(this.settings.classNames.focus,!!e)},triggerChangeEvent:function(){if(!this.settings.mixMode.integrated){var e=this.DOM.originalInput,t=this.state.lastOriginalValueReported!==e.value,n=new CustomEvent("change",{bubbles:!0});t&&(this.state.lastOriginalValueReported=e.value,n.simulated=!0,e._valueTracker&&e._valueTracker.setValue(Math.random()),e.dispatchEvent(n),this.trigger("change",this.state.lastOriginalValueReported),e.value=this.state.lastOriginalValueReported)}},events:N,fixFirefoxLastTagNoCaret(){},setRangeAtStartEnd(e,t){if(t){e="number"==typeof e?e:!!e,t=t.lastChild||t;var n=document.getSelection();if(n.focusNode instanceof Element&&!this.DOM.input.contains(n.focusNode))return!0;try{n.rangeCount>=1&&["Start","End"].forEach((a=>n.getRangeAt(0)["set"+a](t,e||t.length)))}catch(e){}}},insertAfterTag(e,t){if(t=t||this.settings.mixMode.insertAfterTag,e&&e.parentNode&&t)return t="string"==typeof t?document.createTextNode(t):t,e.parentNode.insertBefore(t,e.nextSibling),t},editTagChangeDetected(e){var t=e.__originalData;for(var n in t)if(!this.dataProps.includes(n)&&e[n]!=t[n])return!0;return!1},getTagTextNode(e){return e.querySelector(this.settings.classNames.tagTextSelector)},setTagTextNode(e,t){this.getTagTextNode(e).innerHTML=u(t)},editTag(e,t){e=e||this.getLastTag(),t=t||{},this.dropdown.hide();var n=this.settings,a=this.getTagTextNode(e),r=this.getNodeIndex(e),i=y(e),o=this.events.callbacks,s=this,l=!0;if(a){if(!(i instanceof Object&&"editable"in i)||i.editable)return i=y(e,{__originalData:d({},i),__originalHTML:e.cloneNode(!0)}),y(i.__originalHTML,i.__originalData),a.setAttribute("contenteditable",!0),e.classList.add(n.classNames.tagEditing),a.addEventListener("focus",o.onEditTagFocus.bind(this,e)),a.addEventListener("blur",(function(){setTimeout((()=>o.onEditTagBlur.call(s,s.getTagTextNode(e))))})),a.addEventListener("input",o.onEditTagInput.bind(this,a)),a.addEventListener("paste",o.onEditTagPaste.bind(this,a)),a.addEventListener("keydown",(t=>o.onEditTagkeydown.call(this,t,e))),a.addEventListener("compositionstart",o.onCompositionStart.bind(this)),a.addEventListener("compositionend",o.onCompositionEnd.bind(this)),t.skipValidation||(l=this.editTagToggleValidity(e)),a.originalIsValid=l,this.trigger("edit:start",{tag:e,index:r,data:i,isValid:l}),a.focus(),this.setRangeAtStartEnd(!1,a),this}else console.warn("Cannot find element in Tag template: .",n.classNames.tagTextSelector)},editTagToggleValidity(e,t){var n;if(t=t||y(e))return(n=!("__isValid"in t)||!0===t.__isValid)||this.removeTagsFromValue(e),this.update(),e.classList.toggle(this.settings.classNames.tagNotAllowed,!n),t.__isValid=n,t.__isValid;console.warn("tag has no data: ",e,t)},onEditTagDone(e,t){t=t||{};var n={tag:e=e||this.state.editing.scope,index:this.getNodeIndex(e),previousData:y(e),data:t};this.trigger("edit:beforeUpdate",n,{cloneData:!1}),this.state.editing=!1,delete t.__originalData,delete t.__originalHTML,e&&t[this.settings.tagTextProp]?(e=this.replaceTag(e,t),this.editTagToggleValidity(e,t),this.settings.a11y.focusableTags?e.focus():b(e)):e&&this.removeTags(e),this.trigger("edit:updated",n),this.dropdown.hide(),this.settings.keepInvalidTags&&this.reCheckInvalidTags()},replaceTag(e,t){t&&t.value||(t=e.__tagifyTagData),t.__isValid&&1!=t.__isValid&&d(t,this.getInvalidTagAttrs(t,t.__isValid));var n=this.createTagElem(t);return e.parentNode.replaceChild(n,e),this.updateValueByDOMTags(),n},updateValueByDOMTags(){this.value.length=0,[].forEach.call(this.getTagElms(),(e=>{e.classList.contains(this.settings.classNames.tagNotAllowed.split(" ")[0])||this.value.push(y(e))})),this.update()},injectAtCaret(e,t){return!(t=t||this.state.selection?.range)&&e?(this.appendMixTags(e),this):(v(e,t),this.setRangeAtStartEnd(!1,e),this.updateValueByDOMTags(),this.update(),this)},input:{set(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];var n=this.settings.dropdown.closeOnSelect;this.state.inputText=e,t&&(this.DOM.input.innerHTML=u(""+e)),!e&&n&&this.dropdown.hide.bind(this),this.input.autocomplete.suggest.call(this),this.input.validate.call(this)},raw(){return this.DOM.input.textContent},validate(){var e=!this.state.inputText||!0===this.validateTag({value:this.state.inputText});return this.DOM.input.classList.toggle(this.settings.classNames.inputInvalid,!e),e},normalize(e){var t=e||this.DOM.input,n=[];t.childNodes.forEach((e=>3==e.nodeType&&n.push(e.nodeValue))),n=n.join("\n");try{n=n.replace(/(?:\r\n|\r|\n)/g,this.settings.delimiters.source.charAt(0))}catch(e){}return n=n.replace(/\s/g," "),this.trim(n)},autocomplete:{suggest(e){if(this.settings.autoComplete.enabled){"string"==typeof(e=e||{value:""})&&(e={value:e});var t=this.dropdown.getMappedValue(e);if("number"!=typeof t){var n=t.substr(0,this.state.inputText.length).toLowerCase(),a=t.substring(this.state.inputText.length);t&&this.state.inputText&&n==this.state.inputText.toLowerCase()?(this.DOM.input.setAttribute("data-suggest",a),this.state.inputSuggestion=e):(this.DOM.input.removeAttribute("data-suggest"),delete this.state.inputSuggestion)}}},set(e){var t=this.DOM.input.getAttribute("data-suggest"),n=e||(t?this.state.inputText+t:null);return!!n&&("mix"==this.settings.mode?this.replaceTextWithNode(document.createTextNode(this.state.tag.prefix+n)):(this.input.set.call(this,n),this.setRangeAtStartEnd(!1,this.DOM.input)),this.input.autocomplete.suggest.call(this),this.dropdown.hide(),!0)}}},getTagIdx(e){return this.value.findIndex((t=>t.__tagId==(e||{}).__tagId))},getNodeIndex(e){var t=0;if(e)for(;e=e.previousElementSibling;)t++;return t},getTagElms(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a="."+[...this.settings.classNames.tag.split(" "),...t].join(".");return[].slice.call(this.DOM.scope.querySelectorAll(a))},getLastTag(){var e=this.DOM.scope.querySelectorAll(`${this.settings.classNames.tagSelector}:not(.${this.settings.classNames.tagHide}):not([readonly])`);return e[e.length-1]},isTagDuplicate(e,t,n){var a=0;if("select"==this.settings.mode)return!1;for(let i of this.value)r(this.trim(""+e),i.value,t)&&n!=i.__tagId&&a++;return a},getTagIndexByValue(e){var t=[],n=this.settings.dropdown.caseSensitive;return this.getTagElms().forEach(((a,i)=>{a.__tagifyTagData&&r(this.trim(a.__tagifyTagData.value),e,n)&&t.push(i)})),t},getTagElmByValue(e){var t=this.getTagIndexByValue(e)[0];return this.getTagElms()[t]},flashTag(e){e&&(e.classList.add(this.settings.classNames.tagFlash),setTimeout((()=>{e.classList.remove(this.settings.classNames.tagFlash)}),100))},isTagBlacklisted(e){return e=this.trim(e.toLowerCase()),this.settings.blacklist.filter((t=>(""+t).toLowerCase()==e)).length},isTagWhitelisted(e){return!!this.getWhitelistItem(e)},getWhitelistItem(e,t,n){t=t||"value";var a,i=this.settings;return(n=n||i.whitelist).some((n=>{var o="string"==typeof n?n:n[t]||n.value;if(r(o,e,i.dropdown.caseSensitive,i.trim))return a="string"==typeof n?{value:n}:n,!0})),a||"value"!=t||"value"==i.tagTextProp||(a=this.getWhitelistItem(e,i.tagTextProp,n)),a},validateTag(e){var t=this.settings,n="value"in e?"value":t.tagTextProp,a=this.trim(e[n]+"");return(e[n]+"").trim()?"mix"!=t.mode&&t.pattern&&t.pattern instanceof RegExp&&!t.pattern.test(a)?this.TEXTS.pattern:!t.duplicates&&this.isTagDuplicate(a,t.dropdown.caseSensitive,e.__tagId)?this.TEXTS.duplicate:this.isTagBlacklisted(a)||t.enforceWhitelist&&!this.isTagWhitelisted(a)?this.TEXTS.notAllowed:!t.validate||t.validate(e):this.TEXTS.empty},getInvalidTagAttrs(e,t){return{"aria-invalid":!0,class:`${e.class||""} ${this.settings.classNames.tagNotAllowed}`.trim(),title:t}},hasMaxTags(){return this.value.length>=this.settings.maxTags&&this.TEXTS.exceed},setReadonly(e,t){var n=this.settings;document.activeElement.blur(),n[t||"readonly"]=e,this.DOM.scope[(e?"set":"remove")+"Attribute"](t||"readonly",!0),this.settings.userInput=!0,this.setContentEditable(!e)},setContentEditable(e){this.settings.userInput&&(this.DOM.input.contentEditable=e,this.DOM.input.tabIndex=e?0:-1)},setDisabled(e){this.setReadonly(e,"disabled")},normalizeTags(e){var t=this.settings,n=t.whitelist,a=t.delimiters,r=t.mode,i=t.tagTextProp,o=[],s=!!n&&n[0]instanceof Object,l=Array.isArray(e),c=l&&e[0].value,u=e=>(e+"").split(a).filter((e=>e)).map((e=>({[i]:this.trim(e),value:this.trim(e)})));if("number"==typeof e&&(e=e.toString()),"string"==typeof e){if(!e.trim())return[];e=u(e)}else l&&(e=[].concat(...e.map((e=>null!=e.value?e:u(e)))));return s&&!c&&(e.forEach((e=>{var t=o.map((e=>e.value)),n=this.dropdown.filterListItems.call(this,e[i],{exact:!0});this.settings.duplicates||(n=n.filter((e=>!t.includes(e.value))));var a=n.length>1?this.getWhitelistItem(e[i],i,n):n[0];a&&a instanceof Object?o.push(a):"mix"!=r&&(null==e.value&&(e.value=e[i]),o.push(e))})),o.length&&(e=o)),e},parseMixTags(e){var t=this.settings,n=t.mixTagsInterpolator,a=t.duplicates,r=t.transformTag,i=t.enforceWhitelist,o=t.maxTags,s=t.tagTextProp,l=[];e=e.split(n[0]).map(((e,t)=>{var c,u,p,d=e.split(n[1]),m=d[0],h=l.length==o;try{if(m==+m)throw Error;u=JSON.parse(m)}catch(e){u=this.normalizeTags(m)[0]||{value:m}}if(r.call(this,u),h||!(d.length>1)||i&&!this.isTagWhitelisted(u.value)||!a&&this.isTagDuplicate(u.value)){if(e)return t?n[0]+e:e}else u[c=u[s]?s:"value"]=this.trim(u[c]),p=this.createTagElem(u),l.push(u),p.classList.add(this.settings.classNames.tagNoAnimation),d[0]=p.outerHTML,this.value.push(u);return d.join("")})).join(""),this.DOM.input.innerHTML=e,this.DOM.input.appendChild(document.createTextNode("")),this.DOM.input.normalize();var c=this.getTagElms();return c.forEach(((e,t)=>y(e,l[t]))),this.update({withoutChangeEvent:!0}),k(c,this.state.hasFocus),e},replaceTextWithNode(e,t){if(this.state.tag||t){t=t||this.state.tag.prefix+this.state.tag.value;var n,a,r=this.state.selection||window.getSelection(),i=r.anchorNode,o=this.state.tag.delimiters?this.state.tag.delimiters.length:0;return i.splitText(r.anchorOffset-o),-1==(n=i.nodeValue.lastIndexOf(t))||(a=i.splitText(n),e&&i.parentNode.replaceChild(e,a)),!0}},selectTag(e,t){var n=this.settings;if(!n.enforceWhitelist||this.isTagWhitelisted(t.value)){this.input.set.call(this,t[n.tagTextProp]||t.value,!0),this.state.actions.selectOption&&setTimeout((()=>this.setRangeAtStartEnd(!1,this.DOM.input)));var a=this.getLastTag();return a?this.replaceTag(a,t):this.appendTag(e),this.value[0]=t,this.update(),this.trigger("add",{tag:e,data:t}),[e]}},addEmptyTag(e){var t=d({value:""},e||{}),n=this.createTagElem(t);y(n,t),this.appendTag(n),this.editTag(n,{skipValidation:!0})},addTags(e,t,n){var a=[],r=this.settings,i=[],o=document.createDocumentFragment();if(n=n||r.skipInvalid,!e||0==e.length)return a;switch(e=this.normalizeTags(e),r.mode){case"mix":return this.addMixTags(e);case"select":t=!1,this.removeAllTags()}return this.DOM.input.removeAttribute("style"),e.forEach((e=>{var t,s={},l=Object.assign({},e,{value:e.value+""});if(e=Object.assign({},l),r.transformTag.call(this,e),e.__isValid=this.hasMaxTags()||this.validateTag(e),!0!==e.__isValid){if(n)return;if(d(s,this.getInvalidTagAttrs(e,e.__isValid),{__preInvalidData:l}),e.__isValid==this.TEXTS.duplicate&&this.flashTag(this.getTagElmByValue(e.value)),!r.createInvalidTags)return void i.push(e.value)}if("readonly"in e&&(e.readonly?s["aria-readonly"]=!0:delete e.readonly),t=this.createTagElem(e,s),a.push(t),"select"==r.mode)return this.selectTag(t,e);o.appendChild(t),e.__isValid&&!0===e.__isValid?(this.value.push(e),this.trigger("add",{tag:t,index:this.value.length-1,data:e})):(this.trigger("invalid",{data:e,index:this.value.length,tag:t,message:e.__isValid}),r.keepInvalidTags||setTimeout((()=>this.removeTags(t,!0)),1e3)),this.dropdown.position()})),this.appendTag(o),this.update(),e.length&&t&&(this.input.set.call(this,r.createInvalidTags?"":i.join(r._delimiters)),this.setRangeAtStartEnd(!1,this.DOM.input)),r.dropdown.enabled&&this.dropdown.refilter(),a},addMixTags(e){if((e=this.normalizeTags(e))[0].prefix||this.state.tag)return this.prefixedTextToTag(e[0]);var t=document.createDocumentFragment();return e.forEach((e=>{var n=this.createTagElem(e);t.appendChild(n)})),this.appendMixTags(t),t},appendMixTags(e){var t=!!this.state.selection;t?this.injectAtCaret(e):(this.DOM.input.focus(),(t=this.setStateSelection()).range.setStart(this.DOM.input,t.range.endOffset),t.range.setEnd(this.DOM.input,t.range.endOffset),this.DOM.input.appendChild(e),this.updateValueByDOMTags(),this.update())},prefixedTextToTag(e){var t,n=this.settings,a=this.state.tag.delimiters;if(n.transformTag.call(this,e),e.prefix=e.prefix||this.state.tag?this.state.tag.prefix:(n.pattern.source||n.pattern)[0],t=this.createTagElem(e),this.replaceTextWithNode(t)||this.DOM.input.appendChild(t),setTimeout((()=>t.classList.add(this.settings.classNames.tagNoAnimation)),300),this.value.push(e),this.update(),!a){var r=this.insertAfterTag(t)||t;setTimeout(b,0,r)}return this.state.tag=null,this.trigger("add",d({},{tag:t},{data:e})),t},appendTag(e){var t=this.DOM,n=t.input;t.scope.insertBefore(e,n)},createTagElem(e,n){e.__tagId=f();var a,r=d({},e,t({value:u(e.value+"")},n));return function(e){for(var t,n=document.createNodeIterator(e,NodeFilter.SHOW_TEXT,null,!1);t=n.nextNode();)t.textContent.trim()||t.parentNode.removeChild(t)}(a=this.parseTemplate("tag",[r,this])),y(a,e),a},reCheckInvalidTags(){var e=this.settings;this.getTagElms(e.classNames.tagNotAllowed).forEach(((t,n)=>{var a=y(t),r=this.hasMaxTags(),i=this.validateTag(a),o=!0===i&&!r;if("select"==e.mode&&this.toggleScopeValidation(i),o)return a=a.__preInvalidData?a.__preInvalidData:{value:a.value},this.replaceTag(t,a);t.title=r||i}))},removeTags(e,t,n){var a,r=this.settings;if(e=e&&e instanceof HTMLElement?[e]:e instanceof Array?e:e?[e]:[this.getLastTag()],a=e.reduce(((e,t)=>{t&&"string"==typeof t&&(t=this.getTagElmByValue(t));var n=y(t);return t&&n&&!n.readonly&&e.push({node:t,idx:this.getTagIdx(n),data:y(t,{__removed:!0})}),e}),[]),n="number"==typeof n?n:this.CSSVars.tagHideTransition,"select"==r.mode&&(n=0,this.input.set.call(this)),1==a.length&&"select"!=r.mode&&a[0].node.classList.contains(r.classNames.tagNotAllowed)&&(t=!0),a.length)return r.hooks.beforeRemoveTag(a,{tagify:this}).then((()=>{function e(e){e.node.parentNode&&(e.node.parentNode.removeChild(e.node),t?r.keepInvalidTags&&this.trigger("remove",{tag:e.node,index:e.idx}):(this.trigger("remove",{tag:e.node,index:e.idx,data:e.data}),this.dropdown.refilter(),this.dropdown.position(),this.DOM.input.normalize(),r.keepInvalidTags&&this.reCheckInvalidTags()))}n&&n>10&&1==a.length?function(t){t.node.style.width=parseFloat(window.getComputedStyle(t.node).width)+"px",document.body.clientTop,t.node.classList.add(r.classNames.tagHide),setTimeout(e.bind(this),n,t)}.call(this,a[0]):a.forEach(e.bind(this)),t||(this.removeTagsFromValue(a.map((e=>e.node))),this.update(),"select"==r.mode&&this.setContentEditable(!0))})).catch((e=>{}))},removeTagsFromDOM(){[].slice.call(this.getTagElms()).forEach((e=>e.parentNode.removeChild(e)))},removeTagsFromValue(e){(e=Array.isArray(e)?e:[e]).forEach((e=>{var t=y(e),n=this.getTagIdx(t);n>-1&&this.value.splice(n,1)}))},removeAllTags(e){e=e||{},this.value=[],"mix"==this.settings.mode?this.DOM.input.innerHTML="":this.removeTagsFromDOM(),this.dropdown.refilter(),this.dropdown.position(),this.state.dropdown.visible&&setTimeout((()=>{this.DOM.input.focus()})),"select"==this.settings.mode&&(this.input.set.call(this),this.setContentEditable(!0)),this.update(e)},postUpdate(){this.state.blockChangeEvent=!1;var e=this.settings,t=e.classNames,n="mix"==e.mode?e.mixMode.integrated?this.DOM.input.textContent:this.DOM.originalInput.value.trim():this.value.length+this.input.raw.call(this).length;this.toggleClass(t.hasMaxTags,this.value.length>=e.maxTags),this.toggleClass(t.hasNoTags,!this.value.length),this.toggleClass(t.empty,!n),"select"==e.mode&&this.toggleScopeValidation(this.value?.[0]?.__isValid)},setOriginalInputValue(e){var t=this.DOM.originalInput;this.settings.mixMode.integrated||(t.value=e,t.tagifyValue=t.value,this.setPersistedData(e,"value"))},update(e){clearTimeout(this.debouncedUpdateTimeout),this.debouncedUpdateTimeout=setTimeout(function(){var t=this.getInputValue();this.setOriginalInputValue(t),this.settings.onChangeAfterBlur&&(e||{}).withoutChangeEvent||this.state.blockChangeEvent||this.triggerChangeEvent(),this.postUpdate()}.bind(this),100)},getInputValue(){var e=this.getCleanValue();return"mix"==this.settings.mode?this.getMixedTagsAsString(e):e.length?this.settings.originalInputValueFormat?this.settings.originalInputValueFormat(e):JSON.stringify(e):""},getCleanValue(e){return i(e||this.value,this.dataProps)},getMixedTagsAsString(){var e="",t=this,n=this.settings,a=n.originalInputValueFormat||JSON.stringify,r=n.mixTagsInterpolator;return function n(i){i.childNodes.forEach((i=>{if(1==i.nodeType){const s=y(i);if("BR"==i.tagName&&(e+="\r\n"),s&&w.call(t,i)){if(s.__removed)return;e+=r[0]+a(o(s,t.dataProps))+r[1]}else i.getAttribute("style")||["B","I","U"].includes(i.tagName)?e+=i.textContent:"DIV"!=i.tagName&&"P"!=i.tagName||(e+="\r\n",n(i))}else e+=i.textContent}))}(this.DOM.input),e}},I.prototype.removeTag=I.prototype.removeTags,I}()},4184:function(e,t){var n;!function(){"use strict";var a={}.hasOwnProperty;function r(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n)){if(n.length){var o=r.apply(null,n);o&&e.push(o)}}else if("object"===i){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var s in n)a.call(n,s)&&n[s]&&e.push(s)}}}return e.join(" ")}e.exports?(r.default=r,e.exports=r):void 0===(n=function(){return r}.apply(t,[]))||(e.exports=n)}()},2705:function(e,t,n){var a=n(5639).Symbol;e.exports=a},4239:function(e,t,n){var a=n(2705),r=n(9607),i=n(2333),o=a?a.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":o&&o in Object(e)?r(e):i(e)}},7561:function(e,t,n){var a=n(7990),r=/^\s+/;e.exports=function(e){return e?e.slice(0,a(e)+1).replace(r,""):e}},1957:function(e,t,n){var a="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;e.exports=a},9607:function(e,t,n){var a=n(2705),r=Object.prototype,i=r.hasOwnProperty,o=r.toString,s=a?a.toStringTag:void 0;e.exports=function(e){var t=i.call(e,s),n=e[s];try{e[s]=void 0;var a=!0}catch(e){}var r=o.call(e);return a&&(t?e[s]=n:delete e[s]),r}},2333:function(e){var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},5639:function(e,t,n){var a=n(1957),r="object"==typeof self&&self&&self.Object===Object&&self,i=a||r||Function("return this")();e.exports=i},7990:function(e){var t=/\s/;e.exports=function(e){for(var n=e.length;n--&&t.test(e.charAt(n)););return n}},3279:function(e,t,n){var a=n(3218),r=n(7771),i=n(4841),o=Math.max,s=Math.min;e.exports=function(e,t,n){var l,c,u,p,d,m,h=0,g=!1,f=!1,w=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function v(t){var n=l,a=c;return l=c=void 0,h=t,p=e.apply(a,n)}function y(e){var n=e-m;return void 0===m||n>=t||n<0||f&&e-h>=u}function b(){var e=r();if(y(e))return k(e);d=setTimeout(b,function(e){var n=t-(e-m);return f?s(n,u-(e-h)):n}(e))}function k(e){return d=void 0,w&&l?v(e):(l=c=void 0,p)}function E(){var e=r(),n=y(e);if(l=arguments,c=this,m=e,n){if(void 0===d)return function(e){return h=e,d=setTimeout(b,t),g?v(e):p}(m);if(f)return clearTimeout(d),d=setTimeout(b,t),v(m)}return void 0===d&&(d=setTimeout(b,t)),p}return t=i(t)||0,a(n)&&(g=!!n.leading,u=(f="maxWait"in n)?o(i(n.maxWait)||0,t):u,w="trailing"in n?!!n.trailing:w),E.cancel=function(){void 0!==d&&clearTimeout(d),h=0,l=m=c=d=void 0},E.flush=function(){return void 0===d?p:k(r())},E}},3218:function(e){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},7005:function(e){e.exports=function(e){return null!=e&&"object"==typeof e}},3448:function(e,t,n){var a=n(4239),r=n(7005);e.exports=function(e){return"symbol"==typeof e||r(e)&&"[object Symbol]"==a(e)}},7771:function(e,t,n){var a=n(5639);e.exports=function(){return a.Date.now()}},4841:function(e,t,n){var a=n(7561),r=n(3218),i=n(3448),o=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,l=/^0o[0-7]+$/i,c=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return NaN;if(r(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=r(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=a(e);var n=s.test(e);return n||l.test(e)?c(e.slice(2),n?2:8):o.test(e)?NaN:+e}}},t={};function n(a){var r=t[a];if(void 0!==r)return r.exports;var i=t[a]={exports:{}};return e[a].call(i.exports,i,i.exports,n),i.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){"use strict";var e={};n.r(e),n.d(e,{__experimentalGetAllAnnotationsForBlock:function(){return lt},__experimentalGetAnnotations:function(){return ut},__experimentalGetAnnotationsForBlock:function(){return st},__experimentalGetAnnotationsForRichText:function(){return ct}});var t={};n.r(t),n.d(t,{__experimentalAddAnnotation:function(){return vt},__experimentalRemoveAnnotation:function(){return yt},__experimentalRemoveAnnotationsBySource:function(){return kt},__experimentalUpdateAnnotationRange:function(){return bt}});var a=wp.hooks,r=wp.i18n,i=wp.data,o=wp.element,s=n(4184),l=n.n(s),c=lodash,u=wp.compose,p=wp.components;function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}var m=["tabId","onClick","children","selected"];function h(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,b(a.key),a)}}function g(e,t){return g=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},g(e,t)}function f(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=v(e);if(t){var r=v(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return function(e,t){if(t&&("object"===d(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return w(e)}(this,n)}}function w(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function v(e){return v=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},v(e)}function y(e,t,n){return(t=b(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function b(e){var t=function(e,t){if("object"!==d(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==d(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===d(t)?t:String(t)}function k(){return k=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},k.apply(this,arguments)}function E(e,t){if(null==e)return{};var n,a,r=function(e,t){if(null==e)return{};var n,a,r={},i=Object.keys(e);for(a=0;a<i.length;a++)n=i[a],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}var _=function(e){var t=e.tabId,n=e.onClick,a=e.children,r=e.selected,i=E(e,m);return wp.element.createElement(p.Button,k({role:"tab",tabIndex:r?null:-1,"aria-selected":r,id:t,onClick:n},i),a)},T=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&g(e,t)}(i,e);var t,n,a,r=f(i);function i(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),y(w(e=r.apply(this,arguments)),"handleClick",(function(t){var n=e.props.onSelect,a=void 0===n?c.noop:n;e.setState({selected:t}),a(t)})),y(w(e),"onNavigate",(function(e,t){t.click()}));var t=e.props,n=t.tabs,a=t.initialTabName;return e.state={selected:a||(n.length>0?n[0].name:null)},e}return t=i,(n=[{key:"render",value:function(){var e=this,t=this.state.selected,n=this.props,a=n.activeClass,r=void 0===a?"is-active":a,i=n.className,o=n.instanceId,s=n.orientation,u=void 0===s?"horizontal":s,d=n.tabs,m=(0,c.find)(d,{name:t}),h=o+"-"+m.name,g=d.slice(4);return wp.element.createElement("div",{className:i},wp.element.createElement(p.NavigableMenu,{role:"tablist",orientation:u,onNavigate:this.onNavigate,className:"components-tab-panel__tabs "+t},d.slice(0,4).map((function(n){return wp.element.createElement(_,{className:l()("components-tab-panel__tabs-item",n.className,y({},r,n.name===t)),tabId:o+"-"+n.name,"aria-controls":o+"-"+n.name+"-view",selected:n.name===t,key:n.name,onClick:(0,c.partial)(e.handleClick,n.name)},n.title)})),g.map((function(n){return wp.element.createElement(_,{className:l()("components-tab-panel__tabs-item",n.className,y({},r,n.name===t)),tabId:o+"-"+n.name,"aria-controls":o+"-"+n.name+"-view",selected:n.name===t,key:n.name,onClick:(0,c.partial)(e.handleClick,n.name)},n.title)}))),m&&wp.element.createElement("div",{"aria-labelledby":h,role:"tabpanel",id:h+"-view",className:"components-tab-panel__tab-content"},this.props.children(m)))}}])&&h(t.prototype,n),a&&h(t,a),Object.defineProperty(t,"prototype",{writable:!1}),i}(o.Component),x=(0,u.withInstanceId)(T),O=document.createElement("div");function S(e){return e&&"string"==typeof e&&(e=e.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,"").replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gim,""),O.innerHTML=e,e=O.textContent,O.textContent=""),e}function C(e,t,n){var a=0;return!1!==(0,c.get)(n,"pixelWidth",!1)&&(a=Math.min(100,Math.floor(t/n.pixelWidth*100))),Math.max(Math.min(100,Math.floor(e/n.max*100)),a)+"%"}function N(e,t){return e<=t.min||e>t.max}function I(e,t){return e<=t.minWidth||e>t.pixelWidth}var D=function(e){var t=function(e,t){var n=S(e).length,a=0,r=!1;if(!1!==(0,c.get)(t,"pixelWidth",!1)){r=!0;var i=document.createTextNode(e),o=document.createElement("span");o.appendChild(i),o.id="rank-math-width-tester",o.className=t.widthCheckerClass;var s=document.body.appendChild(o);a=document.getElementById("rank-math-width-tester").offsetWidth,s.outerHTML=""}return{left:C(n,a,t),isInvalid:N(n,t),isInvalidWidth:!!r&&I(a,t),count:n+" / "+t.max,pixelWidth:r?a+"px / "+t.pixelWidth+"px":""}}(e.source,e),n=t.pixelWidth?" (".concat(t.pixelWidth,")"):"";return wp.element.createElement("span",{className:l()("length-indicator-wrapper",{invalid:t.isInvalid||t.isInvalidWidth})},wp.element.createElement("span",{className:"length-count"},t.count,n),wp.element.createElement("span",{className:"length-indicator"},wp.element.createElement("span",{style:{left:t.left}})))},M=wp.htmlEntities,A=React;function P(e){return e.startsWith("{{/")?{type:"componentClose",value:e.replace(/\W/g,"")}:e.endsWith("/}}")?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.startsWith("{{")?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}function R(e,t){let n,a,r=[];for(let i=0;i<e.length;i++){const o=e[i];if("string"!==o.type){if(void 0===t[o.value])throw new Error(`Invalid interpolation, missing component node: \`${o.value}\``);if("object"!=typeof t[o.value])throw new Error(`Invalid interpolation, component node must be a ReactElement or null: \`${o.value}\``);if("componentClose"===o.type)throw new Error(`Missing opening component token: \`${o.value}\``);if("componentOpen"===o.type){n=t[o.value],a=i;break}r.push(t[o.value])}else r.push(o.value)}if(n){const i=function(e,t){const n=t[e];let a=0;for(let r=e+1;r<t.length;r++){const e=t[r];if(e.value===n.value){if("componentOpen"===e.type){a++;continue}if("componentClose"===e.type){if(0===a)return r;a--}}}throw new Error("Missing closing component token `"+n.value+"`")}(a,e),o=R(e.slice(a+1,i),t),s=(0,A.cloneElement)(n,{},o);if(r.push(s),i<e.length-1){const n=R(e.slice(i+1),t);r=r.concat(n)}}return r=r.filter(Boolean),0===r.length?null:1===r.length?r[0]:(0,A.createElement)(A.Fragment,null,...r)}function j(e){const{mixedString:t,components:n,throwErrors:a}=e;if(!n)return t;if("object"!=typeof n){if(a)throw new Error(`Interpolation Error: unable to process \`${t}\` because components is not an object`);return t}const r=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(P)}(t);try{return R(r,n)}catch(e){if(a)throw new Error(`Interpolation Error: unable to process \`${t}\` because of error \`${e.message}\``);return t}}var F=function(e){var t=e.tags,n=e.components,a=e.children;return n=n||{},!1===(0,c.isUndefined)(t)&&(t=t.split(",")).forEach((function(e){var t=e;n[e]=wp.element.createElement(t,null)})),j({mixedString:a,components:n})};function L(e){return L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},L(e)}function B(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,z(a.key),a)}}function U(e,t){return U=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},U(e,t)}function V(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=W(e);if(t){var r=W(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return function(e,t){if(t&&("object"===L(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return H(e)}(this,n)}}function H(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function W(e){return W=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},W(e)}function z(e){var t=function(e,t){if("object"!==L(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==L(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===L(t)?t:String(t)}var G=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&U(e,t)}(s,e);var t,n,a,i=V(s);function s(e){var t,n,a,r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),t=i.call(this,e),n=H(t),r={},(a=z(a="state"))in n?Object.defineProperty(n,a,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[a]=r,t.state.variables=t.getFiltered(),t.state.filtered=t.state.variables,t.state.display="none",t.state.isOpen=!1,t.handleOutsideClick=t.handleOutsideClick.bind(H(t)),t}return t=s,(n=[{key:"getFiltered",value:function(){var e=this.props.exclude,t=Object.values(rankMath.variables);return(0,c.isUndefined)(e)?t:t.filter((function(t){return!e.includes(t.variable)}))}},{key:"render",value:function(){var e=this;return wp.element.createElement(o.Fragment,null,wp.element.createElement("div",{className:"rank-math-variables-dropdown",style:{display:this.state.display},ref:function(t){e.node=t}},wp.element.createElement(p.TextControl,{autoComplete:"off",placeholder:(0,M.decodeEntities)((0,r.__)("Search &hellip;","rank-math")),onChange:function(t){e.handleSearch(t)}}),wp.element.createElement("ul",null,this.state.filtered.map((function(t,n){return wp.element.createElement("li",{key:n,"data-var":t.variable,role:"presentation",onClick:function(){e.props.onClick(t)}},wp.element.createElement("strong",null,t.name),wp.element.createElement("span",null,wp.element.createElement(F,{tags:"strong"},t.description.replace("<strong>","{{strong}}").replace("</strong>","{{/strong}}"))))}),this))),wp.element.createElement(p.Button,{icon:"arrow-down-alt2",onClick:function(){e.toggle()}}))}},{key:"toggle",value:function(){this.state.isOpen?document.removeEventListener("click",this.handleOutsideClick,!0):document.addEventListener("click",this.handleOutsideClick,!0),this.setState({display:this.state.isOpen?"none":"block",isOpen:!this.state.isOpen})}},{key:"handleOutsideClick",value:function(e){this.node.contains(e.target)||this.toggle()}},{key:"handleSearch",value:function(e){var t=e.toLowerCase();2>t.length?this.setState({filtered:this.state.variables}):this.setState({filtered:this.state.variables.filter((function(e){return-1!==Object.values(e).join(" ").toLowerCase().indexOf(t)}))})}}])&&B(t.prototype,n),a&&B(t,a),Object.defineProperty(t,"prototype",{writable:!1}),s}(o.Component),K=(0,u.compose)((0,i.withSelect)((function(e){var t=e("rank-math");return{title:t.getTitle(),serpTitle:t.getSerpTitle()}})),(0,i.withDispatch)((function(e){return{updateTitle:function(t){e("rank-math").updateSerpTitle(t),e("rank-math").updateTitle(t)}}})))((function(e){var t=e.title,n=e.serpTitle,a=e.updateTitle;return wp.element.createElement("div",{className:"field-group"},wp.element.createElement("label",{htmlFor:"rank-math-editor-title"},(0,r.__)("Title","rank-math")),wp.element.createElement(D,{source:n,min:15,max:60,pixelWidth:580,widthCheckerClass:"title"}),wp.element.createElement("div",{className:"variable-group"},wp.element.createElement(p.TextControl,{id:"rank-math-editor-title",value:t,placeholder:rankMath.assessor.serpData.titleTemplate,help:(0,r.__)("This is what will appear in the first line when this post shows up in the search results.","rank-math"),onChange:a}),wp.element.createElement(G,{exclude:["seo_title","seo_description"],onClick:function(e){return a(t+" %"+e.variable+"%")}})))})),$=wp.url,q=function(e){return e.replace(/<\/?[a-z][^>]*?>/gi,"\n")},Y=function(e){return e.replace(/<style[^>]*>([\S\s]*?)<\/style>/gim,"")},J=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,t)},X=function(e){return e.replace(/<!--[\s\S]*?-->/g,"")};function Z(e){return e.replace(/&nbsp;|&#160;/gi," ").replace(/\s{2,}/g," ").replace(/\s\./g,".").replace(/(\r\n|\n|\r)/gm,"")}function Q(e){return(0,c.isUndefined)(e)?"":(0,c.flow)([Y,J,q,X,Z])(e)}var ee={};(0,c.isUndefined)(rankMath.assessor)||(0,c.forEach)(rankMath.assessor.diacritics,(function(e,t){return ee[t]=new RegExp(e,"g")}));var te=function(e){if((0,c.isUndefined)(e))return e;for(var t in ee)e=e.replace(ee[t],t);return e};var ne=function(){return!(0,c.isNull)(document.getElementById("site-editor"))&&(0,c.endsWith)(wp.data.select("core/edit-site").getEditedPostId(),"//home")},ae=(0,u.compose)((0,i.withSelect)((function(e){var t=rankMathEditor.assessor.dataCollector,n=e("rank-math").getSerpSlug();return{permalink:n||t.getSlug(),serpPermalink:t.getPermalink()}})),(0,i.withDispatch)((function(){return{updatePermalink:function(e){rankMathEditor.updatePermalink(Q(e),!0)},updatePermalinkSanitize:function(e){rankMathEditor.updatePermalinkSanitize(function(e){return e=e.replace(/,/g,""),Q(e=te(e))}(e))}}})))((function(e){var t=e.permalink,n=e.serpPermalink,a=e.updatePermalink,i=e.updatePermalinkSanitize;return wp.element.createElement("div",{className:"field-group"},wp.element.createElement("label",{htmlFor:"rank-math-editor-permalink"},(0,r.__)("Permalink","rank-math")),wp.element.createElement(D,{source:(0,$.safeDecodeURIComponent)(n),min:5,max:75}),wp.element.createElement(p.TextControl,{id:"rank-math-editor-permalink",value:rankMath.is_front_page||ne()?"/":(0,$.safeDecodeURIComponent)(t),onChange:a,help:rankMath.is_front_page||ne()?(0,r.__)("Editing Homepage permalink is not possible.","rank-math"):(0,r.__)("This is the unique URL of this page, displayed below the post title in the search results.","rank-math"),disabled:rankMath.is_front_page||ne()?"disabled":"",onBlur:function(e){i(e.target.value)}}))})),re=(0,u.compose)((0,i.withSelect)((function(e){var t=e("rank-math");return{description:t.getDescription(),serpDescription:t.getSerpDescription()}})),(0,i.withDispatch)((function(e){return{updateDescription:function(t){e("rank-math").updateSerpDescription(t),e("rank-math").updateDescription(t)}}})))((function(e){var t=e.description,n=e.serpDescription,a=e.updateDescription;return wp.element.createElement("div",{className:"field-group"},wp.element.createElement("label",{htmlFor:"rank-math-editor-description"},(0,r.__)("Description","rank-math")),wp.element.createElement(D,{source:n,min:80,max:160,pixelWidth:920,widthCheckerClass:"description"}),wp.element.createElement("div",{className:"variable-group rank-math-description-variables"},wp.element.createElement(p.TextareaControl,{id:"rank-math-editor-description",value:S(t),placeholder:n||(0,c.unescape)(rankMath.assessor.serpData.descriptionTemplate),help:(0,r.__)("This is what will appear as the description when this post shows up in the search results.","rank-math"),onChange:a}),wp.element.createElement(G,{exclude:["seo_title","seo_description"],onClick:function(e){return a(t+" %"+e.variable+"%")}})))})),ie=(0,u.compose)((0,i.withSelect)((function(e){return{type:e("rank-math").getSnippetPreviewType()}})),(0,i.withDispatch)((function(e,t){return{updatePreviewType:function(n){e("rank-math").updateSnippetPreviewType(n===t.type?"":n)}}})))((function(e){var t=e.type,n=e.updatePreviewType,r=l()("button button-secondary button-small",{active:"desktop"===t}),i=l()("button button-secondary button-small",{active:"mobile"===t});return wp.element.createElement("div",{className:"rank-math-button-devices alignright"},(0,a.applyFilters)("rank_math_before_serp_devices",""),wp.element.createElement("div",{onClick:function(){return n("desktop")},className:r},wp.element.createElement("i",{className:"rm-icon rm-icon-desktop"})),wp.element.createElement("div",{onClick:function(){return n("mobile")},className:i},wp.element.createElement("i",{className:"rm-icon rm-icon-mobile"})))}));function oe(e){return 100<e?"bad-fk dark":80<e?"good-fk":50<e?"ok-fk":"bad-fk"}var se=(0,i.withSelect)((function(e){var t=e("rank-math");return{score:t.getAnalysisScore(),isRefreshing:t.isRefreshing()}}))((function(e){var t=e.score;return wp.element.createElement("div",{className:"seo-score "+oe(t)},wp.element.createElement("div",{className:"score-text"},t," / 100"))}));function le(e,t,n,a){return(0,c.isUndefined)(t)?t:(t=(0,c.truncate)(t,{length:n,separator:a||" "}),""===e?t:t.replace(new RegExp(function(e){return e.replace(/[\\^$*+?.()|[\]{}]/g,"\\$&")}(e),"gi"),(function(e){return'<mark className="highlight">'+e+"</mark>"})))}function ce(e){return e.replace(/(^\w{1})|(\s+\w{1})/g,(function(e){return e.toUpperCase()}))}var ue=function(e,t){for(var n=[],a=1;a<=e;a++)n.push(wp.element.createElement(p.Dashicon,{key:Math.random(),size:"13",icon:"star-"+t}));return n},pe=(0,i.withSelect)((function(e){var t=e("rank-math").getSchemas();return{schema:(0,c.find)(t,(function(e){return!(0,c.isEmpty)(e.metadata.isPrimary)}))}}))((function(e){var t=e.schema;if((0,c.isEmpty)(t))return null;var n=(0,c.get)(t,"review.reviewRating",{}),a=n.ratingValue;if((0,c.isEmpty)(a))return null;(0,c.get)(n,"worstRating",1);var i=(0,c.get)(n,"bestRating",5);return wp.element.createElement("div",{className:"rank-math-rating-preview"},wp.element.createElement("div",{className:"serp-ratings"},ue(i,"filled"),function(e,t,n){return e*=100/n,wp.element.createElement("div",{className:"serp-result",style:{width:e+"%"}},ue(n,"filled"))}(a,0,i)),wp.element.createElement("span",{className:"serp-rating-label"},(0,r.__)("Rating: ","rank-math")),wp.element.createElement("span",{className:"serp-rating-value"},a))}));function de(e){return de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},de(e)}function me(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==de(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==de(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===de(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var he=(0,i.withSelect)((function(e){var t=e("rank-math"),n=t.getRobots();return{title:t.getSerpTitle(),permalink:rankMathEditor.assessor.dataCollector.getPermalink(),description:t.getSerpDescription(),previewType:t.getSnippetPreviewType(),isNoIndex:"noindex"in n,keyword:t.getSelectedKeyword().data.value}}))((function(e){var t=e.title,n=e.permalink,a=e.description,i=e.previewType,o=void 0===i?"desktop":i,s=e.isNoIndex,c=e.keyword,u=e.onClick,p=e.showScore,d=void 0===p||p,m=e.showDevices,h=void 0!==m&&m,g=l()("serp-preview",me(me({"expanded-preview":""!==o},"".concat(o,"-preview"),""!==o&&h),"noindex-preview",s)),f=rankMath.capitalizeTitle?ce(t):t,w=rankMathEditor.assessor.getResearch("slugify")(c);return wp.element.createElement("div",{className:g},wp.element.createElement("div",{className:"serp-preview-title","data-title":(0,r.__)("Preview","rank-math"),"data-desktop":(0,r.__)("Desktop Preview","rank-math"),"data-mobile":(0,r.__)("Mobile Preview","rank-math")},d&&wp.element.createElement(se,null)," ",h&&wp.element.createElement(ie,null)),wp.element.createElement("div",{className:"serp-preview-wrapper"},wp.element.createElement("div",{className:"serp-preview-bg"},wp.element.createElement("div",{className:"serp-preview-input"},wp.element.createElement("input",{type:"text",value:c||(0,r.__)("Rank Math","rank-math"),disabled:!0}),wp.element.createElement("span",{className:"serp-icon-search"},wp.element.createElement("svg",{focusable:"false",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},wp.element.createElement("path",{d:"M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}))),wp.element.createElement("span",{className:"serp-icon-mic"})),wp.element.createElement("div",{className:"serp-preview-menus"},wp.element.createElement("ul",null,wp.element.createElement("li",{className:"current"},wp.element.createElement("img",{src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgyNHYyNEgweiIvPjxwYXRoIGZpbGw9IiMzNEE4NTMiIGQ9Ik0xMCAydjJhNiA2IDAgMCAxIDYgNmgyYTggOCAwIDAgMC04LTh6Ii8+PHBhdGggZmlsbD0iI0VBNDMzNSIgZD0iTTEwIDRWMmE4IDggMCAwIDAtOCA4aDJjMC0zLjMgMi43LTYgNi02eiIvPjxwYXRoIGZpbGw9IiNGQkJDMDQiIGQ9Ik00IDEwSDJhOCA4IDAgMCAwIDggOHYtMmMtMy4zIDAtNi0yLjY5LTYtNnoiLz48cGF0aCBmaWxsPSIjNDI4NUY0IiBkPSJNMjIgMjAuNTlsLTUuNjktNS42OUE3Ljk2IDcuOTYgMCAwIDAgMTggMTBoLTJhNiA2IDAgMCAxLTYgNnYyYzEuODUgMCAzLjUyLS42NCA0Ljg4LTEuNjhsNS42OSA1LjY5TDIyIDIwLjU5eiIvPjwvc3ZnPgo=",alt:"","data-atf":"1"}),(0,r.__)("All","rank-math")),wp.element.createElement("li",null,wp.element.createElement("svg",{focusable:"false",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},wp.element.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),wp.element.createElement("path",{d:"M14 13l4 5H6l4-4 1.79 1.78L14 13zm-6.01-2.99A2 2 0 0 0 8 6a2 2 0 0 0-.01 4.01zM22 5v14a3 3 0 0 1-3 2.99H5c-1.64 0-3-1.36-3-3V5c0-1.64 1.36-3 3-3h14c1.65 0 3 1.36 3 3zm-2.01 0a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h7v-.01h7a1 1 0 0 0 1-1V5z"}))," ",(0,r.__)("Images","rank-math")),wp.element.createElement("li",null,wp.element.createElement("svg",{focusable:"false",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},wp.element.createElement("path",{clipRule:"evenodd",d:"M0 0h24v24H0z",fill:"none"}),wp.element.createElement("path",{clipRule:"evenodd",d:"M10 16.5l6-4.5-6-4.5v9zM5 20h14a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1zm14.5 2H5a3 3 0 0 1-3-3V4.4A2.4 2.4 0 0 1 4.4 2h15.2A2.4 2.4 0 0 1 22 4.4v15.1a2.5 2.5 0 0 1-2.5 2.5z",fillRule:"evenodd"}))," ",(0,r.__)("Videos","rank-math")),wp.element.createElement("li",null,wp.element.createElement("svg",{focusable:"false",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},wp.element.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),wp.element.createElement("path",{d:"M12 11h6v2h-6v-2zm-6 6h12v-2H6v2zm0-4h4V7H6v6zm16-7.22v12.44c0 1.54-1.34 2.78-3 2.78H5c-1.64 0-3-1.25-3-2.78V5.78C2 4.26 3.36 3 5 3h14c1.64 0 3 1.25 3 2.78zM19.99 12V5.78c0-.42-.46-.78-1-.78H5c-.54 0-1 .36-1 .78v12.44c0 .42.46.78 1 .78h14c.54 0 1-.36 1-.78V12zM12 9h6V7h-6v2z"}))," ",(0,r.__)("News","rank-math")),wp.element.createElement("li",null,wp.element.createElement("svg",{focusable:"false",viewBox:"0 0 16 16"},wp.element.createElement("path",{d:"M7.503 0c3.09 0 5.502 2.487 5.502 5.427 0 2.337-1.13 3.694-2.26 5.05-.454.528-.906 1.13-1.358 1.734-.452.603-.754 1.508-.98 1.96-.226.452-.377.829-.904.829-.528 0-.678-.377-.905-.83-.226-.451-.527-1.356-.98-1.959-.452-.603-.904-1.206-1.356-1.734C3.132 9.121 2 7.764 2 5.427 2 2.487 4.412 0 7.503 0zm0 1.364c-2.283 0-4.14 1.822-4.14 4.063 0 1.843.86 2.873 1.946 4.177.468.547.942 1.178 1.4 1.79.34.452.596.99.794 1.444.198-.455.453-.992.793-1.445.459-.61.931-1.242 1.413-1.803 1.074-1.29 1.933-2.32 1.933-4.163 0-2.24-1.858-4.063-4.139-4.063zm0 2.734a1.33 1.33 0 11-.001 2.658 1.33 1.33 0 010-2.658"}))," ",(0,r.__)("Maps","rank-math")),wp.element.createElement("li",null,wp.element.createElement("svg",{focusable:"false",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},wp.element.createElement("path",{d:"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"}))," ",(0,r.__)("More","rank-math"))),wp.element.createElement("ul",{className:"menus-right"},wp.element.createElement("li",null,(0,r.__)("Settings","rank-math")),wp.element.createElement("li",null,(0,r.__)("Tools","rank-math")))),wp.element.createElement("div",{className:"serp-preview-result-stats"},(0,r.__)("About 43,700,000 results (0.32 seconds) ","rank-math"))),wp.element.createElement("div",{className:"serp-preview-body",role:"button",tabIndex:0,onClick:function(){u&&u()}},wp.element.createElement("div",{className:"group"},wp.element.createElement("img",{src:rankMath.siteFavIcon,width:"16",height:"16",className:"serp-preview-favicon",alt:""}),wp.element.createElement("div",{className:"serp-url",dangerouslySetInnerHTML:{__html:le(w,Q((0,$.safeDecodeURIComponent)(n)),75,/-? +/)}})),wp.element.createElement("div",{className:"group"},wp.element.createElement("h5",{className:"serp-title",dangerouslySetInnerHTML:{__html:le(c,Q(f),60)}})),wp.element.createElement(pe,null),wp.element.createElement("div",{className:"group"},wp.element.createElement("div",{className:"serp-description",dangerouslySetInnerHTML:{__html:le(c,Q(a),160)}}))),wp.element.createElement("div",{className:"serp-preview-noindex"},wp.element.createElement("h3",null,(0,r.__)("Noindex robots meta is enabled","rank-math")),wp.element.createElement("p",null,(0,r.__)("This page will not appear in search results. You can disable noindex in the Advanced tab.","rank-math")))))})),ge=(0,i.withSelect)((function(e){return{isNoIndex:"noindex"in e("rank-math").getRobots()}}))((function(){return wp.element.createElement("div",{className:"rank-math-editor-general"},wp.element.createElement(he,{showScore:!1,showDevices:!0}),wp.element.createElement(K,null),wp.element.createElement(ae,null),wp.element.createElement(re,null))}));function fe(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=rankMath.links[e]||"";if(!n)return"#";if(!t)return n;var a={utm_source:"Plugin",utm_medium:encodeURIComponent(t),utm_campaign:"WP"};return n+"?"+Object.keys(a).map((function(e){return"".concat(e,"=").concat(a[e])})).join("&")}var we=jQuery,ve=n.n(we);function ye(e){return ye="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ye(e)}function be(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,ke(a.key),a)}}function ke(e){var t=function(e,t){if("object"!==ye(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==ye(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ye(t)?t:String(t)}var Ee=new(function(){function e(){var t,n,a;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,a=null,(n=ke(n="map"))in t?Object.defineProperty(t,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[n]=a}var t,n,a;return t=e,(n=[{key:"swap",value:function(e,t){var n=this;if(!(e=e||""))return"";var a=new RegExp(/%(([a-z0-9_-]+)\(([^)]*)\)|[^\s]+)%/,"giu");return e.replace(" %page%","").replace("%sep% %sep%","%sep%").replace(a,(function(e){return n.replace(t,e)})).trim()}},{key:"replace",value:function(e,t){var n=t.toLowerCase().slice(1,-1);return["term_description","user_description"].includes(n)?"undefined"!=typeof tinymce&&void 0!==tinymce.editors.rank_math_description_editor?tinymce.editors.rank_math_description_editor.getContent():ve()("#description").val():n.includes("customfield(")?(n=n.replace("customfield(","").replace(")",""))in rankMath.customFields?rankMath.customFields[n]:"":(e=e||this.getMap(),(n="seo_description"===(n="seo_title"===(n=n.includes("(")?n.split("(")[0]:n)?"title":n)?"excerpt":n)in e?e[n]:"")}},{key:"getMap",value:function(){var e=this;return null!==this.map||(this.map={},ve().each(rankMath.variables,(function(t,n){t=t.toLowerCase().replace(/%+/g,"").split("(")[0],e.map[t]=n.example}))),this.map}},{key:"setVariable",value:function(e,t){null!==this.map?this.map[e]=t:void 0!==rankMath.variables[e]&&(rankMath.variables[e].example=t)}}])&&be(t.prototype,n),a&&be(t,a),Object.defineProperty(t,"prototype",{writable:!1}),e}());function _e(e){return _e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_e(e)}var Te=function(e){var t=l()("rank-math-social-preview","rank-math-social-preview-"+e.network,e.cardType),n=e.title?Ee.swap(e.title):e.serpTitle,a=rankMath.capitalizeTitle?ce(n):n;return wp.element.createElement("div",{className:t},wp.element.createElement("div",{className:"rank-math-social-preview-item"},wp.element.createElement("div",{className:"rank-math-social-preview-meta"},wp.element.createElement("div",{className:"social-profile-image"}),e.children),wp.element.createElement("div",{className:"rank-math-social-preview-item-wrapper"},wp.element.createElement("div",{className:"rank-math-social-preview-image"},wp.element.createElement("img",{className:"rank-math-social-image-thumbnail",src:e.image,alt:""}),e.hasOverlay&&e.imageOverlay&&"object"===_e(rankMath.overlayImages[e.imageOverlay])&&wp.element.createElement("img",{src:rankMath.overlayImages[e.imageOverlay].url,className:"rank-math-social-preview-image-overlay overlay-position-"+rankMath.overlayImages[e.imageOverlay].position,alt:""})),wp.element.createElement("div",{className:"rank-math-social-preview-caption"},"facebook"===e.network&&wp.element.createElement("h4",{className:"rank-math-social-preview-publisher"},e.siteurl),wp.element.createElement("h3",{className:"rank-math-social-preview-title"},a),wp.element.createElement("p",{className:"rank-math-social-preview-description"},e.description?Ee.swap(e.description):e.serpDescription),"twitter"===e.network&&wp.element.createElement(o.Fragment,null,wp.element.createElement("h4",{className:"rank-math-social-preview-publisher"},wp.element.createElement(p.Dashicon,{icon:"admin-links"}),e.siteurl))))))},xe=wp.mediaUtils,Oe=function(e){return wp.element.createElement("div",{className:"components-base-control field-group"},wp.element.createElement(xe.MediaUpload,{allowedTypes:["image"],multiple:!1,value:e.imageID,render:function(t){var n=t.open;return wp.element.createElement(o.Fragment,null,wp.element.createElement(p.Button,{onClick:n,className:"button",isPrimary:!0},e.imageID>0?(0,r.__)("Replace Image","rank-math"):(0,r.__)("Add Image","rank-math")),e.imageID>0&&wp.element.createElement(p.Button,{className:"button",isDestructive:!0,isLink:!0,onClick:e.removeImage},(0,r.__)("Remove Image","rank-math")))},onSelect:e.updateImage}),wp.element.createElement("p",{className:"components-base-control__help"},(0,r.__)("Upload at least 600x315px image. Recommended size is 1200x630px.","rank-math")),wp.element.createElement("div",{className:"notice notice-warning inline hidden"},wp.element.createElement("p",null,(0,r.__)("Image is smaller than the minimum size, please select a different image.","rank-math"))))};function Se(){var e=rankMath.overlayImages,t=[];return Object.keys(e).forEach((function(n){t.push({label:e[n].name,value:n})})),t}function Ce(){return Ce=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ce.apply(this,arguments)}var Ne=(0,u.compose)((0,i.withSelect)((function(e){var t=e("rank-math"),n=function(){if(t.getFacebookImage())return t.getFacebookImage();var e=t.getFeaturedImage();return(0,c.isUndefined)(e)||""===e?rankMath.defautOgImage:t.getFeaturedImage().source_url}();return{title:t.getFacebookTitle(),description:t.getFacebookDescription(),serpTitle:t.getSerpTitle(),serpDescription:t.getSerpDescription(),author:t.getFacebookAuthor(),image:n,imageID:t.getFacebookImageID(),hasOverlay:t.getFacebookHasOverlay(),imageOverlay:t.getFacebookImageOverlay()}})),(0,i.withDispatch)((function(e){return{removeImage:function(){e("rank-math").updateFacebookImage(""),e("rank-math").updateFacebookImageID(0),e("rank-math").updateFacebookHasOverlay(!1)},updateImage:function(t){e("rank-math").updateFacebookImage(t.url),e("rank-math").updateFacebookImageID(t.id)},updateTitle:function(t){e("rank-math").updateFacebookTitle(t)},updateDescription:function(t){e("rank-math").updateFacebookDescription(t)},updateImageOverlay:function(t){e("rank-math").updateFacebookImageOverlay(t)},toggleOverlay:function(t){e("rank-math").updateFacebookHasOverlay(t)}}})))((function(e){return wp.element.createElement(o.Fragment,null,wp.element.createElement(Te,Ce({},e,{network:"facebook",siteurl:rankMath.parentDomain}),wp.element.createElement("div",{className:"social-name"},rankMath.assessor.serpData.authorName),wp.element.createElement("div",{className:"social-time"},wp.element.createElement("span",null,(0,r.__)("2 hrs","rank-math")),wp.element.createElement("span",null,wp.element.createElement(p.Dashicon,{icon:"admin-site",size:"12"})))),wp.element.createElement("div",{className:"notice notice-alt notice-info components-base-control"},wp.element.createElement("p",null,(0,r.__)("Customize the title, description and images of your post used while sharing on Facebook and Twitter.","rank-math")," ",wp.element.createElement("a",{href:fe("meta-box-social-tab","Gutenberg Social Tab"),target:"_blank",rel:"noreferrer noopener"},(0,r.__)("Read more","rank-math")))),wp.element.createElement(Oe,e),(0,a.applyFilters)("rank_math_before_serp_devices","","Opengraph"),wp.element.createElement("div",{className:"field-group"},wp.element.createElement("label",{htmlFor:"rank-math-facebook-title"},(0,r.__)("Title","rank-math")),wp.element.createElement("div",{className:"variable-group"},wp.element.createElement(p.TextControl,{id:"rank-math-facebook-title",value:e.title,placeholder:e.serpTitle,onChange:e.updateTitle}),wp.element.createElement(G,{onClick:function(t){return e.updateTitle(e.title+" %"+t.variable+"%")}}))),wp.element.createElement("div",{className:"field-group"},wp.element.createElement("label",{htmlFor:"rank-math-facebook-description"},(0,r.__)("Description","rank-math")),wp.element.createElement("div",{className:"variable-group"},wp.element.createElement(p.TextareaControl,{id:"rank-math-facebook-description",value:e.description,placeholder:e.serpDescription,onChange:e.updateDescription}),wp.element.createElement(G,{onClick:function(t){return e.updateDescription(e.description+" %"+t.variable+"%")}}))),!ne()&&wp.element.createElement("div",{className:"field-group"},wp.element.createElement(p.ToggleControl,{label:(0,r.__)("Add icon overlay to thumbnail","rank-math"),checked:e.hasOverlay,onChange:e.toggleOverlay}),wp.element.createElement("div",{className:e.hasOverlay?"components-base-control":"hidden"},wp.element.createElement(p.SelectControl,{value:e.imageOverlay,label:(0,r.__)("Icon overlay","rank-math"),options:Se(),onChange:e.updateImageOverlay}),!rankMath.isPro&&wp.element.createElement("div",{className:"notice notice-alt notice-warning"},wp.element.createElement("p",null,wp.element.createElement(F,{components:{link:wp.element.createElement("a",{href:fe("pro","Gutenberg Social Tab"),target:"_blank",rel:"noopener noreferrer"})}},(0,r.__)("You can add custom thumbnail overlays with {{link}}Rank Math Pro{{/link}}.","rank-math")))))))})),Ie=(0,u.compose)((0,i.withSelect)((function(e){var t=e("rank-math");return{country:t.getTwitterAppCountry(),description:t.getTwitterAppDescription(),iphoneName:t.getTwitterAppIphoneName(),iphoneID:t.getTwitterAppIphoneID(),iphoneUrl:t.getTwitterAppIphoneUrl(),ipadName:t.getTwitterAppIpadName(),ipadID:t.getTwitterAppIpadID(),ipadUrl:t.getTwitterAppIpadUrl(),googleplayName:t.getTwitterAppGoogleplayName(),googleplayID:t.getTwitterAppGoogleplayID(),googleplayUrl:t.getTwitterAppGoogleplayUrl()}})),(0,i.withDispatch)((function(e){return{updateAppIphoneName:function(t){e("rank-math").updateTwitterAppIphoneName(t)},updateAppIphoneID:function(t){e("rank-math").updateTwitterAppIphoneID(t)},updateAppIphoneUrl:function(t){e("rank-math").updateTwitterAppIphoneUrl(t)},updateAppIpadName:function(t){e("rank-math").updateTwitterAppIpadName(t)},updateAppIpadID:function(t){e("rank-math").updateTwitterAppIpadID(t)},updateAppIpadUrl:function(t){e("rank-math").updateTwitterAppIpadUrl(t)},updateAppGoogleplayName:function(t){e("rank-math").updateTwitterAppGoogleplayName(t)},updateAppGoogleplayID:function(t){e("rank-math").updateTwitterAppGoogleplayID(t)},updateAppGoogleplayUrl:function(t){e("rank-math").updateTwitterAppGoogleplayUrl(t)},updateAppDescription:function(t){e("rank-math").updateTwitterAppDescription(t)},updateAppCountry:function(t){e("rank-math").updateTwitterAppCountry(t)}}})))((function(e){return wp.element.createElement(o.Fragment,null,wp.element.createElement(p.TextControl,{value:e.description,label:(0,r.__)("App Description","rank-math"),help:(0,r.__)("You can use this as a more concise description than what you may have on the app store. This field has a maximum of 200 characters. (optional)","rank-math"),onChange:e.updateAppDescription}),function(e){return wp.element.createElement(o.Fragment,null,wp.element.createElement(p.TextControl,{value:e.iphoneName,label:(0,r.__)("iPhone App Name","rank-math"),help:(0,r.__)("The name of your app to show.","rank-math"),onChange:e.updateAppIphoneName}),wp.element.createElement(p.TextControl,{value:e.iphoneID,label:(0,r.__)("iPhone App ID","rank-math"),help:(0,r.__)("The numeric representation of your app ID in the App Store.","rank-math"),onChange:e.updateAppIphoneID}),wp.element.createElement(p.TextControl,{value:e.iphoneUrl,label:(0,r.__)("iPhone App URL","rank-math"),help:(0,r.__)("Your app's custom URL scheme (must include ://).","rank-math"),onChange:e.updateAppIphoneUrl}))}(e),function(e){return wp.element.createElement(o.Fragment,null,wp.element.createElement(p.TextControl,{value:e.ipadName,label:(0,r.__)("iPad App Name","rank-math"),help:(0,r.__)("The name of your app to show.","rank-math"),onChange:e.updateAppIpadName}),wp.element.createElement(p.TextControl,{value:e.ipadID,label:(0,r.__)("iPad App ID","rank-math"),help:(0,r.__)("The numeric representation of your app ID in the App Store.","rank-math"),onChange:e.updateAppIpadID}),wp.element.createElement(p.TextControl,{value:e.ipadUrl,label:(0,r.__)("iPad App URL","rank-math"),help:(0,r.__)("Your app's custom URL scheme (must include ://).","rank-math"),onChange:e.updateAppIpadUrl}))}(e),function(e){return wp.element.createElement(o.Fragment,null,wp.element.createElement(p.TextControl,{value:e.googleplayName,label:(0,r.__)("Google Play App Name","rank-math"),help:(0,r.__)("The name of your app to show.","rank-math"),onChange:e.updateAppGoogleplayName}),wp.element.createElement(p.TextControl,{value:e.googleplayID,label:(0,r.__)("Google Play App ID","rank-math"),help:(0,r.__)("Your app ID in the Google Play (.i.e. com.android.app)","rank-math"),onChange:e.updateAppGoogleplayID}),wp.element.createElement(p.TextControl,{value:e.googleplayUrl,label:(0,r.__)("Google Play App URL","rank-math"),help:(0,r.__)("Your app's custom URL scheme (must include ://).","rank-math"),onChange:e.updateAppGoogleplayUrl}))}(e),wp.element.createElement(p.TextControl,{value:e.country,label:(0,r.__)("App Country","rank-math"),help:(0,r.__)("If your application is not available in the US App Store, you must set this value to the two-letter country code for the App Store that contains your application.","rank-math"),onChange:e.updateAppCountry}))})),De=(0,u.compose)((0,i.withSelect)((function(e){var t=e("rank-math");return{url:t.getTwitterPlayerUrl(),size:t.getTwitterPlayerSize(),stream:t.getTwitterPlayerStream(),ctype:t.getTwitterPlayerStreamCtype()}})),(0,i.withDispatch)((function(e){return{updatePlayerUrl:function(t){e("rank-math").updateTwitterPlayerUrl(t)},updatePlayerSize:function(t){e("rank-math").updateTwitterPlayerSize(t)},updatePlayerStreamUrl:function(t){e("rank-math").updateTwitterPlayerStreamUrl(t)},updatePlayerStreamCtype:function(t){e("rank-math").updateTwitterPlayerStreamCtype(t)}}})))((function(e){return wp.element.createElement(o.Fragment,null,wp.element.createElement(p.TextControl,{value:e.url,label:(0,r.__)("Player URL","rank-math"),help:(0,r.__)("HTTPS URL to iFrame player. This must be a HTTPS URL which does not generate active mixed content warnings in a web browser. The audio or video player must not require plugins such as Adobe Flash.","rank-math"),onChange:e.updatePlayerUrl}),wp.element.createElement(p.TextControl,{value:e.size,label:(0,r.__)("Player Size","rank-math"),help:(0,r.__)("iFrame width and height, specified in pixels in the following format: 600x400.","rank-math"),onChange:e.updatePlayerSize}),wp.element.createElement(p.TextControl,{value:e.stream,label:(0,r.__)("Stream URL","rank-math"),help:(0,r.__)("Optional URL to raw stream that will be rendered in Twitter’s mobile applications directly. If provided, the stream must be delivered in the MPEG-4 container format (the .mp4 extension). The container can store a mix of audio and video with the following codecs: Video: H.264, Baseline Profile (BP), Level 3.0, up to 640 x 480 at 30 fps. Audio: AAC, Low Complexity Profile (LC).","rank-math"),onChange:e.updatePlayerStreamUrl}),wp.element.createElement(p.TextControl,{value:e.ctype,label:(0,r.__)("Stream Content Type","rank-math"),help:(0,r.__)("The MIME type/subtype combination that describes the content contained in twitter:player:stream. Takes the form specified in RFC 6381. Currently supported content_type values are those defined in RFC 4337 (MIME Type Registration for MP4).","rank-math"),onChange:e.updatePlayerStreamCtype}))}));function Me(){return Me=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Me.apply(this,arguments)}var Ae=(0,u.compose)((0,i.withSelect)((function(e){var t=e("rank-math"),n=!!ne()||t.getTwitterUseFacebook(),a=function(){if(n&&t.getFacebookImage())return t.getFacebookImage();if(t.getTwitterImage())return t.getTwitterImage();var e=t.getFeaturedImage();return(0,c.isUndefined)(e)||""===e?rankMath.defautOgImage:t.getFeaturedImage().source_url}();return{useFacebook:n,cardType:t.getTwitterCardType(),title:n?t.getFacebookTitle():t.getTwitterTitle(),description:n?t.getFacebookDescription():t.getTwitterDescription(),serpTitle:t.getSerpTitle(),serpDescription:t.getSerpDescription(),author:t.getTwitterAuthor(),image:a,imageID:n?t.getFacebookImageID():t.getTwitterImageID(),hasOverlay:n?t.getFacebookHasOverlay():t.getTwitterHasOverlay(),imageOverlay:n?t.getFacebookImageOverlay():t.getTwitterImageOverlay()}})),(0,i.withDispatch)((function(e){return{removeImage:function(){e("rank-math").updateTwitterImage(""),e("rank-math").updateTwitterImageID(0),e("rank-math").updateTwitterHasOverlay(!1)},updateImage:function(t){e("rank-math").updateTwitterImage(t.url),e("rank-math").updateTwitterImageID(t.id)},updateTitle:function(t){e("rank-math").updateTwitterTitle(t)},updateDescription:function(t){e("rank-math").updateTwitterDescription(t)},updateImageOverlay:function(t){e("rank-math").updateTwitterImageOverlay(t)},toggleUseFacebook:function(t){e("rank-math").updateTwitterUseFacebook(t)},updateCardType:function(t){e("rank-math").updateTwitterCardType(t)},toggleOverlay:function(t){e("rank-math").updateTwitterHasOverlay(t)}}})))((function(e){return wp.element.createElement(o.Fragment,null,wp.element.createElement(Te,Me({},e,{network:"twitter",siteurl:rankMath.parentDomain,classes:e.cardType,description:(0,c.truncate)(e.description?e.description:e.serpDescription,{length:240,separator:" "})}),wp.element.createElement("div",{className:"social-name"},rankMath.assessor.serpData.authorName,wp.element.createElement("span",{className:"social-username"},"@",e.author),wp.element.createElement("span",{className:"social-time"},(0,r.__)("2h","rank-math"))),wp.element.createElement("div",{className:"social-text"},(0,r.__)("The card for your website will look little something like this!","rank-math"))),wp.element.createElement("div",{className:"field-group"},wp.element.createElement(p.ToggleControl,{label:(0,r.__)("Use Data from Facebook Tab","rank-math"),checked:e.useFacebook,onChange:e.toggleUseFacebook})),wp.element.createElement("div",{className:"field-group"},wp.element.createElement(p.SelectControl,{value:e.cardType,label:(0,r.__)("Card Type","rank-math"),options:[{value:"summary_large_image",label:(0,r.__)("Summary Card with Large Image","rank-math")},{value:"summary_card",label:(0,r.__)("Summary Card","rank-math")},{value:"app",label:(0,r.__)("App Card","rank-math")},{value:"player",label:(0,r.__)("Player Card","rank-math")}],onChange:e.updateCardType})),"player"===e.cardType&&wp.element.createElement("div",{className:"notice notice-alt notice-info"},wp.element.createElement("p",null,(0,r.__)("Video clips and audio streams have a special place on the Twitter platform thanks to the Player Card. Player Cards must be submitted for approval before they can be used. More information: ","rank-math"),wp.element.createElement("a",{href:"https://dev.twitter.com/cards/types/player",target:"blank"},"https://dev.twitter.com/cards/types/player"))),"app"===e.cardType&&wp.element.createElement("div",{className:"notice notice-alt notice-info"},wp.element.createElement("p",null,(0,r.__)("The App Card is a great way to represent mobile applications on Twitter and to drive installs. More information: ","rank-math"),wp.element.createElement("a",{href:"https://dev.twitter.com/cards/types/app",target:"blank"}," https://dev.twitter.com/cards/types/app"))),!e.useFacebook&&"app"!==e.cardType&&wp.element.createElement(Oe,e),!e.useFacebook&&(0,a.applyFilters)("rank_math_before_serp_devices","","Opengraph"),!e.useFacebook&&"app"!==e.cardType&&wp.element.createElement("div",{className:"field-group"},wp.element.createElement("label",{htmlFor:"rank-math-twitter-title"},(0,r.__)("Title","rank-math")),wp.element.createElement("div",{className:"variable-group"},wp.element.createElement(p.TextControl,{id:"rank-math-twitter-title",value:e.title,placeholder:e.serpTitle,onChange:e.updateTitle}),wp.element.createElement(G,{onClick:function(t){return e.updateTitle(e.title+" %"+t.variable+"%")}}))),!e.useFacebook&&"app"!==e.cardType&&wp.element.createElement("div",{className:"field-group"},wp.element.createElement("label",{htmlFor:"rank-math-twitter-description"},(0,r.__)("Description","rank-math")),wp.element.createElement("div",{className:"variable-group"},wp.element.createElement(p.TextareaControl,{id:"rank-math-twitter-description",value:e.description,placeholder:e.serpDescription,onChange:e.updateDescription}),wp.element.createElement(G,{onClick:function(t){return e.updateDescription(e.description+" %"+t.variable+"%")}}))),!e.useFacebook&&"app"!==e.cardType&&!ne()&&wp.element.createElement("div",{className:"field-group"},wp.element.createElement(p.ToggleControl,{label:(0,r.__)("Add icon overlay to thumbnail","rank-math"),checked:e.hasOverlay,onChange:e.toggleOverlay}),wp.element.createElement("div",{className:e.hasOverlay?"components-base-control":"hidden"},wp.element.createElement(p.SelectControl,{value:e.imageOverlay,label:(0,r.__)("Icon overlay","rank-math"),options:Se(),onChange:e.updateImageOverlay}),!rankMath.isPro&&wp.element.createElement("div",{className:"notice notice-alt notice-warning"},wp.element.createElement("p",null,wp.element.createElement(F,{components:{link:wp.element.createElement("a",{href:fe("pro","Gutenberg Social Tab"),target:"_blank",rel:"noopener noreferrer"})}},(0,r.__)("You can add custom thumbnail overlays with {{link}}Rank Math Pro{{/link}}.","rank-math")))))),"player"===e.cardType&&wp.element.createElement(De,null),"app"===e.cardType&&wp.element.createElement(Ie,null))})),Pe=function(){return wp.element.createElement(o.Fragment,null,wp.element.createElement(p.TabPanel,{className:"rank-math-editor-social",activeClass:"is-active",tabs:[{name:"facebook",title:wp.element.createElement(o.Fragment,null,wp.element.createElement(p.Dashicon,{icon:"facebook-alt"}),(0,r.__)("Facebook","rank-math")),view:Ne,className:"button-facebook"},{name:"twitter",title:wp.element.createElement(o.Fragment,null,wp.element.createElement(p.Dashicon,{icon:"twitter"}),(0,r.__)("Twitter","rank-math")),view:Ae,className:"button-twitter"}],onSelect:function(e){wp.data.dispatch("rank-math").updateSocialTab(e)}},(function(e){return(0,o.createElement)(e.view)})))};function Re(e){return Re="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Re(e)}function je(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,(r=a.key,i=void 0,i=function(e,t){if("object"!==Re(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==Re(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Re(i)?i:String(i)),a)}var r,i}function Fe(e,t){return Fe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Fe(e,t)}function Le(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=Ue(e);if(t){var r=Ue(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return function(e,t){if(t&&("object"===Re(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Be(e)}(this,n)}}function Be(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ue(e){return Ue=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ue(e)}var Ve=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Fe(e,t)}(s,e);var t,n,a,i=Le(s);function s(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),(e=i.call(this))._handleRef=e._handleRef.bind(Be(e)),e}return t=s,(n=[{key:"componentDidMount",value:function(){var e=ve()(this.component),t=e.find(".stars a"),n=e.find(".smiley");t.on("mouseenter",(function(){var e=ve()(this).index();t.removeClass("highlighted"),t.slice(0,e+1).addClass("highlighted"),e<2?n.removeClass("normal happy").addClass("angry"):e>3?n.removeClass("normal angry").addClass("happy"):n.removeClass("happy angry").addClass("normal")}))}},{key:"shouldComponentUpdate",value:function(){return!1}},{key:"_handleRef",value:function(e){this.component=e}},{key:"render",value:function(){for(var e=this,t=[],n=1;n<=5;n++)t.push(wp.element.createElement("a",{key:n,href:"https://wordpress.org/support/plugin/seo-by-rank-math/reviews/?filter=5#new-post",target:"_blank",rel:"noopener noreferrer"},wp.element.createElement("span",{className:"dashicons dashicons-star-filled"})));return wp.element.createElement("div",{className:"ask-review",ref:this._handleRef},wp.element.createElement("h3",null,(0,r.__)("Rate Rank Math SEO","rank-math")),wp.element.createElement("p",null,wp.element.createElement(F,{tags:"em"},(0,r.__)("Hey, we noticed you are using Rank Math SEO plugin for more than a week now –{{em}}that's awesome!{{/em}} Could you please do us a BIG favor and give it a 5-star rating on WordPress to help us spread the word and boost our motivation?","rank-math"))),wp.element.createElement("div",{className:"stars-wrapper"},wp.element.createElement("div",{className:"face"},wp.element.createElement("div",{className:"smiley happy"},wp.element.createElement("div",{className:"eyes"},wp.element.createElement("div",{className:"eye"}),wp.element.createElement("div",{className:"eye"})),wp.element.createElement("div",{className:"mouth"}))),wp.element.createElement("div",{className:"stars"},t)),wp.element.createElement(p.CheckboxControl,{label:wp.element.createElement(o.Fragment,null,wp.element.createElement("span",null,(0,r.__)("I already did. Please don't show this message again.","rank-math"))),onChange:function(){return e.alreadyReviewed()}}))}},{key:"alreadyReviewed",value:function(){ve().ajax({url:rankMath.ajaxurl,data:{action:"rank_math_already_reviewed",security:rankMath.security}}),rankMath.pluginReviewed=!0,ve()(this.component).animate({opacity:.01},1500,(function(){var e=ve()(".rank-math-editor > .components-tab-panel__tabs > button");e.first().click(),e.last().remove()}))}}])&&je(t.prototype,n),a&&je(t,a),Object.defineProperty(t,"prototype",{writable:!1}),s}(o.Component),He=(0,p.withFilters)("rankMath.analytics.reportButton")((function(){return null})),We=(0,u.compose)((0,i.withSelect)((function(e){return{isOpen:e("rank-math").isSnippetEditorOpen()}})),(0,i.withDispatch)((function(e,t){return{toggleEditor:function(){e("rank-math").toggleSnippetEditor(!t.isOpen)}}})))((function(e){var t=e.isOpen,n=e.initialTab,a=void 0===n?"":n,i=e.toggleEditor,s=[{name:"general",title:wp.element.createElement(o.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-settings"}),wp.element.createElement("span",null,(0,r.__)("General","rank-math"))),view:ge}];return rankMath.canUser.social&&s.push({name:"social",title:wp.element.createElement(o.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-social"}),wp.element.createElement("span",null,(0,r.__)("Social","rank-math"))),view:Pe}),rankMath.showReviewTab&&!1===rankMath.pluginReviewed&&s.push({name:"review",className:"review-tab",title:wp.element.createElement(o.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-heart-filled"})),view:Ve}),wp.element.createElement(o.Fragment,null,wp.element.createElement(p.Button,{isPrimary:!0,className:"rank-math-edit-snippet",onClick:i},(0,r.__)("Edit Snippet","rank-math")),""===a&&wp.element.createElement(He,null),t&&wp.element.createElement(p.Modal,{title:(0,r.__)("Preview Snippet Editor","rank-math"),closeButtonLabel:(0,r.__)("Close","rank-math"),shouldCloseOnClickOutside:!1,onRequestClose:function(e){void 0!==e&&(document.body.classList.remove("modal-open"),i())},className:"rank-math-modal",overlayClassName:"rank-math-modal-overlay"},wp.element.createElement(p.TabPanel,{className:"rank-math-tabs rank-math-editor",activeClass:"is-active",initialTabName:a,tabs:s},(function(e){return(0,o.createElement)(e.view)}))))})),ze=function(){return wp.element.createElement(p.PanelBody,{initialOpen:!0},wp.element.createElement(p.BaseControl,{className:"rank-math-social"},wp.element.createElement("span",{className:"components-base-control__label"},(0,r.__)("Social Media Preview","rank-math")),wp.element.createElement("p",{className:"cmb2-metabox-description"},(0,r.__)("Here you can view and edit the thumbnail, title and description that will be displayed when your site is shared on social media.","rank-math")),wp.element.createElement("p",{className:"cmb2-metabox-description"},(0,r.__)("Click on the button below to view and edit the preview.","rank-math")),wp.element.createElement(We,{buttonLabel:(0,r.__)("Preview & Edit Social Media","rank-math"),initialTab:"social"})))},Ge=wp.wordcount,Ke=wp.richText;const $e="core/annotations",qe="core/annotation",Ye="annotation-text-";const Je={name:qe,title:(0,r.__)("Annotation"),tagName:"mark",className:"annotation-text",attributes:{className:"class",id:"id"},edit(){return null},__experimentalGetPropsForEditableTreePreparation(e,{richTextIdentifier:t,blockClientId:n}){return{annotations:e($e).__experimentalGetAnnotationsForRichText(n,t)}},__experimentalCreatePrepareEditableTree({annotations:e}){return(t,n)=>{if(0===e.length)return t;let a={formats:t,text:n};return a=function(e,t=[]){return t.forEach((t=>{let{start:n,end:a}=t;n>e.text.length&&(n=e.text.length),a>e.text.length&&(a=e.text.length);const r=Ye+t.source,i=Ye+t.id;e=(0,Ke.applyFormat)(e,{type:qe,attributes:{className:r,id:i}},n,a)})),e}(a,e),a.formats}},__experimentalGetPropsForEditableTreeChangeHandler(e){return{removeAnnotation:e($e).__experimentalRemoveAnnotation,updateAnnotationRange:e($e).__experimentalUpdateAnnotationRange}},__experimentalCreateOnChangeEditableValue(e){return t=>{const n=function(e){const t={};return e.forEach(((e,n)=>{(e=(e=e||[]).filter((e=>e.type===qe))).forEach((e=>{let{id:a}=e.attributes;a=a.replace(Ye,""),t.hasOwnProperty(a)||(t[a]={start:n}),t[a].end=n+1}))})),t}(t),{removeAnnotation:a,updateAnnotationRange:r,annotations:i}=e;!function(e,t,{removeAnnotation:n,updateAnnotationRange:a}){e.forEach((e=>{const r=t[e.id];if(!r)return void n(e.id);const{start:i,end:o}=e;i===r.start&&o===r.end||a(e.id,r.start,r.end)}))}(i,n,{removeAnnotation:a,updateAnnotationRange:r})}}},{name:Xe,...Ze}=Je;(0,Ke.registerFormatType)(Xe,Ze);function Qe(e,t){const n=e.filter(t);return e.length===n.length?e:n}(0,a.addFilter)("editor.BlockListBlock","core/annotations",(e=>(0,i.withSelect)(((e,{clientId:t,className:n})=>({className:e($e).__experimentalGetAnnotationsForBlock(t).map((e=>"is-annotated-by-"+e.source)).concat(n).filter(Boolean).join(" ")})))(e)));const et=(e,t)=>Object.entries(e).reduce(((e,[n,a])=>({...e,[n]:t(a)})),{});var tt=function(e={},t){var n;switch(t.type){case"ANNOTATION_ADD":const a=t.blockClientId,r={id:t.id,blockClientId:a,richTextIdentifier:t.richTextIdentifier,source:t.source,selector:t.selector,range:t.range};if("range"===r.selector&&!function(e){return"number"==typeof e.start&&"number"==typeof e.end&&e.start<=e.end}(r.range))return e;const i=null!==(n=e?.[a])&&void 0!==n?n:[];return{...e,[a]:[...i,r]};case"ANNOTATION_REMOVE":return et(e,(e=>Qe(e,(e=>e.id!==t.annotationId))));case"ANNOTATION_UPDATE_RANGE":return et(e,(e=>{let n=!1;const a=e.map((e=>e.id===t.annotationId?(n=!0,{...e,range:{start:t.start,end:t.end}}):e));return n?a:e}));case"ANNOTATION_REMOVE_SOURCE":return et(e,(e=>Qe(e,(e=>e.source!==t.source))))}return e},nt={};function at(e){return[e]}function rt(e,t,n){var a;if(e.length!==t.length)return!1;for(a=n;a<e.length;a++)if(e[a]!==t[a])return!1;return!0}function it(e,t){var n,a=t||at;function r(){n=new WeakMap}function i(){var t,r,i,o,s,l=arguments.length;for(o=new Array(l),i=0;i<l;i++)o[i]=arguments[i];for(t=function(e){var t,a,r,i,o,s=n,l=!0;for(t=0;t<e.length;t++){if(!(o=a=e[t])||"object"!=typeof o){l=!1;break}s.has(a)?s=s.get(a):(r=new WeakMap,s.set(a,r),s=r)}return s.has(nt)||((i=function(){var e={clear:function(){e.head=null}};return e}()).isUniqueByDependants=l,s.set(nt,i)),s.get(nt)}(s=a.apply(null,o)),t.isUniqueByDependants||(t.lastDependants&&!rt(s,t.lastDependants,0)&&t.clear(),t.lastDependants=s),r=t.head;r;){if(rt(r.args,o,1))return r!==t.head&&(r.prev.next=r.next,r.next&&(r.next.prev=r.prev),r.next=t.head,r.prev=null,t.head.prev=r,t.head=r),r.val;r=r.next}return r={val:e.apply(null,o)},o[0]=null,r.args=o,t.head&&(t.head.prev=r,r.next=t.head),t.head=r,r.val}return i.getDependants=a,i.clear=r,r(),i}const ot=[],st=it(((e,t)=>{var n;return(null!==(n=e?.[t])&&void 0!==n?n:[]).filter((e=>"block"===e.selector))}),((e,t)=>{var n;return[null!==(n=e?.[t])&&void 0!==n?n:ot]}));function lt(e,t){var n;return null!==(n=e?.[t])&&void 0!==n?n:ot}const ct=it(((e,t,n)=>{var a;return(null!==(a=e?.[t])&&void 0!==a?a:[]).filter((e=>"range"===e.selector&&n===e.richTextIdentifier)).map((e=>{const{range:t,...n}=e;return{...t,...n}}))}),((e,t)=>{var n;return[null!==(n=e?.[t])&&void 0!==n?n:ot]}));function ut(e){return Object.values(e).flat()}var pt={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let dt;const mt=new Uint8Array(16);function ht(){if(!dt&&(dt="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!dt))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return dt(mt)}const gt=[];for(let e=0;e<256;++e)gt.push((e+256).toString(16).slice(1));function ft(e,t=0){return gt[e[t+0]]+gt[e[t+1]]+gt[e[t+2]]+gt[e[t+3]]+"-"+gt[e[t+4]]+gt[e[t+5]]+"-"+gt[e[t+6]]+gt[e[t+7]]+"-"+gt[e[t+8]]+gt[e[t+9]]+"-"+gt[e[t+10]]+gt[e[t+11]]+gt[e[t+12]]+gt[e[t+13]]+gt[e[t+14]]+gt[e[t+15]]}var wt=function(e,t,n){if(pt.randomUUID&&!t&&!e)return pt.randomUUID();const a=(e=e||{}).random||(e.rng||ht)();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){n=n||0;for(let e=0;e<16;++e)t[n+e]=a[e];return t}return ft(a)};function vt({blockClientId:e,richTextIdentifier:t=null,range:n=null,selector:a="range",source:r="default",id:i=wt()}){const o={type:"ANNOTATION_ADD",id:i,blockClientId:e,richTextIdentifier:t,source:r,selector:a};return"range"===a&&(o.range=n),o}function yt(e){return{type:"ANNOTATION_REMOVE",annotationId:e}}function bt(e,t,n){return{type:"ANNOTATION_UPDATE_RANGE",annotationId:e,start:t,end:n}}function kt(e){return{type:"ANNOTATION_REMOVE_SOURCE",source:e}}const Et=(0,i.createReduxStore)($e,{reducer:tt,selectors:e,actions:t});function _t(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return Tt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Tt(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,r=function(){};return{s:r,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){s=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw i}}}}function Tt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}(0,i.register)(Et);var xt="core/annotations",Ot="rank-math-annotations",St=function e(t,n){var a,r=_t(t);try{for(r.s();!(a=r.n()).done;){var i=a.value;if("div"===i.localName&&i.children&&e(i.children,n),"p"===i.localName)if((0,Ge.count)(i.innerText,"words")<120)n.annotator.remove(Ot),i.getElementsByClassName("rank-math-content-ai-tooltip").length&&i.getElementsByClassName("rank-math-content-ai-tooltip")[0].remove();else{var o=n.selection.win.getSelection();o.selectAllChildren(i),n.annotator.annotate(Ot,{uid:"rank-math-annotation"}),o.empty()}}}catch(e){r.e(e)}finally{r.f()}},Ct=function(e){var t=window.tinymce.get(window.wpActiveEditor);if(!t)return!1;if(!e)return t.focus(),t.annotator.remove(Ot),void(0,a.doAction)("rank_math_annotations_removed");var n=t.getBody().children||[];St(n,t)},Nt=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;if("classic"!==rankMath.currentEditor){if(!e)return(0,i.dispatch)(xt).__experimentalRemoveAnnotationsBySource(Ot),void n([]);var a=(0,i.select)("core/block-editor").getBlocks();(0,c.forEach)(a,(function(e){if("core/paragraph"===e.name){var a=e.clientId;(0,Ge.count)(e.attributes.content,"words")<120?(0,c.includes)(t,a)&&((0,i.dispatch)(xt).__experimentalRemoveAnnotation(a),t=(0,c.remove)(t,a),n(t)):(t.push(a),n(t),(0,i.dispatch)(xt).__experimentalAddAnnotation({id:a,blockClientId:a,source:Ot,richTextIdentifier:"content",range:{start:0,end:e.attributes.content.length}}))}}))}else Ct(e)},It=(0,p.withFilters)("rankMath.checklists.FixWithAI")((function(){return null}));function Dt(e){return Dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Dt(e)}function Mt(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,(r=a.key,i=void 0,i=function(e,t){if("object"!==Dt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==Dt(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Dt(i)?i:String(i)),a)}var r,i}function At(e,t){return At=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},At(e,t)}function Pt(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=Rt(e);if(t){var r=Rt(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return function(e,t){if(t&&("object"===Dt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,n)}}function Rt(e){return Rt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Rt(e)}var jt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&At(e,t)}(s,e);var t,n,a,i=Pt(s);function s(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),(e=i.call(this)).state={highlightText:!0},e}return t=s,(n=[{key:"componentDidMount",value:function(){if("classic"!==rankMath.currentEditor||(0,c.isUndefined)(window.tinymce))return!1;var e=window.tinymce.get(window.wpActiveEditor);if(!e)return!1;e.annotator.register("rank-math-annotations",{persistent:!1,decorate:function(){return{classes:["rank-math-annotations"]}}}),e.dom.addStyle('\n\t\t.rank-math-annotations.mce-annotation {\n\t\t\tbackground-color: mark !important;\n\t\t\tposition: relative;\n\t\t}\n\t\t.rank-math-annotations.mce-annotation .rank-math-content-ai-tooltip {\n\t\t\tdisplay: none;\n\t\t\tbackground-color: #2271b1;\n\t\t\tborder-color: #2271b1;\n\t\t\tcolor: #fff;\n\t\t\tcursor: pointer;\n\t\t\tposition: absolute;\n\t\t\ttop: -30px;\n\t\t\tleft: 0px;\n\t\t}\n\t\t.rank-math-annotations.mce-annotation[data-mce-selected="inline-boundary"] .rank-math-content-ai-tooltip {\n\t\t\tdisplay: inline-block;\n\t\t}\n\t\t')}},{key:"shouldComponentUpdate",value:function(e,t){return e.isLoaded!==this.props.isLoaded||e.selectedKeyword.index!==this.props.selectedKeyword.index||e.isRefreshing!==this.props.isRefreshing||t.highlightText!==this.state.highlightText}},{key:"render",value:function(){var e=this,t=rankMathEditor.getSelectedKeyword();return this.results=rankMathEditor.resultManager.getResult(t),this.isPrimary=0===this.props.selectedKeyword.index,(0,c.isUndefined)(this.results)?null:Object.keys(this.getGroups()).map((function(t){return e.renderGroup(t)}))}},{key:"renderGroup",value:function(e){this.errors=0;var t=this.renderGroupItems(e);return wp.element.createElement(p.PanelBody,{key:"panel-"+e,title:wp.element.createElement(o.Fragment,null,this.getGroupTitle(e),0===this.errors?wp.element.createElement("span",{className:"rank-math-group-score test-ok"},(0,r.__)("All Good","rank-math")):wp.element.createElement("span",{className:"rank-math-group-score test-fail"},this.errors," ",(0,r.__)("Errors","rank-math"))),initialOpen:"basic"===e,className:"rank-math-checklist"},wp.element.createElement("ul",null,t))}},{key:"renderGroupItems",value:function(e){var t=this,n=this.results.results,a=this.getGroupItems(e);return Object.keys(a).map((function(e){if((0,c.isUndefined)(n[e])||!t.isPrimary&&a[e])return!1;var r=n[e],i=l()("seo-check-"+e,{"test-ok":r.hasScore(),"test-fail":!r.hasScore()});r.hasScore()&&["contentHasAssets","lengthContent","keywordDensity"].includes(e)&&(i+=" "+function(e,t){var n=(0,c.round)(e/t*100);if(!(100<=n))return 49<n?"test-check-good":30<n?"test-check-ok":"test-check-bad"}(r.getScore(),r.getMaxScore())),!1===r.hasScore()&&(t.errors+=1);var o=t.getLink(e);return"contentHasShortParagraphs"===e&&(0,c.includes)(["classic","gutenberg"],rankMath.currentEditor)&&!t.state.highlightText&&Nt(!0,t.props.highlightedParagraphs,t.props.updateHighlightedParagraphs),wp.element.createElement("li",{key:e,className:i},wp.element.createElement("span",null,wp.element.createElement("span",{dangerouslySetInnerHTML:{__html:r.getText()}}),!r.hasScore()&&"contentHasShortParagraphs"===e&&!(0,c.isEmpty)(r.text)&&(0,c.includes)(["classic","gutenberg"],rankMath.currentEditor)&&wp.element.createElement(p.Button,{className:"rank-math-highlight-button",onClick:function(){Nt(t.state.highlightText,t.props.highlightedParagraphs,t.props.updateHighlightedParagraphs),t.setState({highlightText:!t.state.highlightText})}},t.state.highlightText&&wp.element.createElement("i",{className:"dashicons dashicons-visibility"}),!t.state.highlightText&&wp.element.createElement("i",{className:"dashicons dashicons-hidden"})),wp.element.createElement(It,{id:e,result:r})),o&&wp.element.createElement("a",{href:fe("score-100","Content Analysis Single Test KB")+o,rel:"noreferrer",target:"_blank",className:"dashicons-before dashicons-editor-help rank-math-help-icon"}))}))}},{key:"getGroupItems",value:function(e){return("post"===rankMath.objectType?{basic:{keywordInTitle:!0,keywordInMetaDescription:!0,keywordInPermalink:!0,keywordIn10Percent:!0,keywordInContent:!1,lengthContent:!1,hasProductSchema:!0},advanced:{keywordInSubheadings:!1,keywordInImageAlt:!0,keywordDensity:!1,lengthPermalink:!1,linksHasExternals:!1,linksNotAllExternals:!1,linksHasInternal:!1,keywordNotUsed:!0,hasContentAI:!0,isReviewEnabled:!0},"title-readability":{titleStartWithKeyword:!0,titleSentiment:!1,titleHasPowerWords:!1,titleHasNumber:!1},"content-readability":{contentHasTOC:!1,contentHasShortParagraphs:!1,contentHasAssets:!1}}:{basic:{keywordInTitle:!0,keywordInMetaDescription:!0,keywordInPermalink:!0},advanced:{titleStartWithKeyword:!0,keywordNotUsed:!0}})[e]}},{key:"getGroupTitle",value:function(e){return this.getGroups()[e]}},{key:"getGroups",value:function(){return"post"===rankMath.objectType?{basic:(0,r.__)("Basic SEO","rank-math"),advanced:(0,r.__)("Additional","rank-math"),"title-readability":(0,r.__)("Title Readability","rank-math"),"content-readability":(0,r.__)("Content Readability","rank-math")}:{basic:(0,r.__)("Basic SEO","rank-math"),advanced:(0,r.__)("Additional","rank-math")}}},{key:"getLink",value:function(e){var t={keywordInTitle:"#focus-keyword-in-the-seo-title-primary-focus-keyword-only",keywordInMetaDescription:"#focus-keyword-in-the-meta-description-primary-focus-keyword-only",keywordInPermalink:"#focus-keyword-in-the-url-primary-focus-keyword-only",keywordIn10Percent:"#focus-keyword-at-the-beginning-of-the-content",keywordInContent:"#focus-keyword-in-the-content-runs-of-all-focus-keywords",lengthContent:"#overall-content-length",keywordInSubheadings:"#focus-keyword-in-subheading-primary-and-secondary-focus-keywords",keywordInImageAlt:"#focus-keyword-in-image-alt-attributes-primary-focus-keyword-only",keywordDensity:"#keyword-density-primary-and-secondary-focus-keywords",lengthPermalink:"#url-length",linksHasExternals:"#linking-to-external-sources",linksNotAllExternals:"#linking-to-external-content-with-a-followed-link",linksHasInternal:"#linking-to-internal-resources",keywordNotUsed:"#focus-keyword-uniqueness-primary-focus-keyword-only",titleStartWithKeyword:"#focus-keyword-at-the-beginning-of-the-seo-title-only-for-primary-keyword",titleSentiment:"#sentiment-in-a-title",titleHasPowerWords:"#use-of-power-word-in-title",titleHasNumber:"#number-in-title",contentHasTOC:"#table-of-contents",contentHasShortParagraphs:"#use-of-short-paragraphs",contentHasAssets:"#use-of-media-in-your-posts",hasContentAI:"#used-content-ai",hasProductSchema:"#has-product-schema",isReviewEnabled:"#is-review-enabled"};return(0,c.has)(t,e)?t[e]:""}}])&&Mt(t.prototype,n),a&&Mt(t,a),Object.defineProperty(t,"prototype",{writable:!1}),s}(o.Component),Ft=(0,u.compose)((0,i.withSelect)((function(e){var t=e("rank-math");return{isLoaded:t.isLoaded(),isRefreshing:t.isRefreshing(),selectedKeyword:t.getSelectedKeyword(),highlightedParagraphs:t.getHighlightedParagraphs()}})),(0,i.withDispatch)((function(e){return{updateHighlightedParagraphs:function(t){e("rank-math").updateHighlightedParagraphs(t)}}})))(jt),Lt=(0,p.createSlotFill)("RankMathAfterFocusKeyword"),Bt=Lt.Fill,Ut=Lt.Slot,Vt=function(e){var t=e.children,n=e.className;return wp.element.createElement(Bt,null,wp.element.createElement("div",{className:n},t))};Vt.Slot=Ut;var Ht=Vt,Wt=(0,i.withSelect)((function(e){return{isPro:e("rank-math").isPro()}}))((function(e){return e.isPro?rankMath.isUserRegistered?wp.element.createElement(p.Notice,{status:"warning",isDismissible:!1},wp.element.createElement(F,{components:{link:wp.element.createElement("a",{href:fe("score-100","General Tab Score Notice"),target:"_blank",rel:"noopener noreferrer"})}},(0,r.__)("Read here to {{link}}Score 100/100{{/link}} ","rank-math"))):wp.element.createElement(p.Notice,{status:"warning",isDismissible:!1},wp.element.createElement(F,{components:{link:wp.element.createElement("a",{href:rankMath.adminurl+"?page=rank-math&view=help",target:"_blank",rel:"noopener noreferrer"})}},(0,r.__)("Activate your account by {{link}}connecting to Rank Math!{{/link}} ","rank-math"))):wp.element.createElement(p.Notice,{status:"warning",isDismissible:!1},wp.element.createElement(F,{components:{link:wp.element.createElement("a",{href:fe("pro","Gutenberg General Tab Notice"),target:"_blank",rel:"noopener noreferrer"}),strong:wp.element.createElement("strong",null)}},(0,r.__)("Want more? {{link}}{{strong}}Upgrade today to the PRO{{/strong}}{{/link}} version.","rank-math")))})),zt=(0,u.withInstanceId)((function(e){var t=e.className,n=e.instanceId,a=e.children,r="rank-math-tooltip-"+n,i=l()("rank-math-tooltip",t);return wp.element.createElement("span",{className:i},wp.element.createElement("input",{id:r,type:"checkbox"}),wp.element.createElement("label",{htmlFor:r,className:"dashicons-before dashicons-editor-help"}),wp.element.createElement("div",{className:"rank-math-tooltip-content"},a))})),Gt=n(8634),Kt=n.n(Gt);function $t(e){return $t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$t(e)}function qt(){return qt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},qt.apply(this,arguments)}function Yt(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,(r=a.key,i=void 0,i=function(e,t){if("object"!==$t(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==$t(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===$t(i)?i:String(i)),a)}var r,i}function Jt(e,t){return Jt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Jt(e,t)}function Xt(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=Qt(e);if(t){var r=Qt(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return function(e,t){if(t&&("object"===$t(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Zt(e)}(this,n)}}function Zt(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Qt(e){return Qt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Qt(e)}var en=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Jt(e,t)}(o,e);var t,n,r,i=Xt(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e))._handleRef=t._handleRef.bind(Zt(t)),t}return t=o,(n=[{key:"componentDidMount",value:function(){var e=this;this.tagify=new(Kt())(this.component,this.props.settings||{}),this.props.settings.callbacks.setup&&setTimeout((function(){e.props.settings.callbacks.setup.call(e.tagify),e.tagify.DOM.input.setAttribute("contenteditable",!0),e.tagify.DOM.input.addEventListener("blur",e.props.settings.callbacks.blur)}),100),this.props.settings.callbacks.dragEnd&&this.tagify.DOM.scope.addEventListener("dragend",this.props.settings.callbacks.dragEnd),(0,a.doAction)("rank_math_tagify_init",this)}},{key:"shouldComponentUpdate",value:function(e){return this.tagify.settings.whitelist=e.settings.whitelist,e.showDropdown&&this.tagify.dropdown.show.call(this.tagify,e.showDropdown),!1===e.showDropdown&&this.tagify.dropdown.hide.call(this.tagify,!0),!1}},{key:"_handleRef",value:function(e){this.component=e}},{key:"render",value:function(){var e={ref:this._handleRef,id:this.props.id,name:this.props.name,className:this.props.className,placeholder:this.props.placeholder};return"textarea"===this.props.mode?wp.element.createElement("textarea",qt({},e,{defaultValue:this.props.initialValue})):wp.element.createElement("input",qt({},e,{defaultValue:this.props.initialValue}))}},{key:"toArray",value:function(){return this.tagify.value.map((function(e){return e.value}))}},{key:"toString",value:function(){return this.toArray().join(",")}},{key:"queryTags",value:function(){return this.tagify.DOM.scope.querySelectorAll("tag")}}])&&Yt(t.prototype,n),r&&Yt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(o.Component),tn=function(e){return e=ve()("<textarea />").html(e).text(),((new DOMParser).parseFromString(e,"text/html").body.textContent||"").replace(/["<>]/g,"")||""},nn=function(e,t){arguments.length>2&&void 0!==arguments[2]&&arguments[2]&&(t+="+Free+Plan");var n=new URLSearchParams({utm_source:"Plugin",utm_medium:t,utm_campaign:"WP"});return(0,c.includes)(e,"?")||(e+="?"),e+n.toString()},an=function(e){var t=e.width,n=void 0===t?40:t,a=e.showProNotice,i=void 0!==a&&a,o=e.isBulkEdit,s=void 0!==o&&o,l=e.isResearch,u=void 0!==l&&l,d=e.creditsRequired,m=void 0===d?0:d,h=e.isKeywordIntent,g=void 0!==h&&h;if(i)return function(e){return wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks top-20 less-padding "+e},wp.element.createElement("h3",null,(0,r.__)("🔒 This is a PRO-Only Feature","rank-math")),wp.element.createElement("p",null,(0,r.__)("We are sorry but this feature is only available to Rank Math PRO/Business/Agency Users. Unlock this feature and many more by getting a Rank Math plan.","rank-math")),wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,r.__)("Bulk Edit SEO Tags","rank-math")),wp.element.createElement("li",null,(0,r.__)("Advanced Google Analytics 4 Integration","rank-math")),wp.element.createElement("li",null,(0,r.__)("Keyword Rank Tracker","rank-math")),wp.element.createElement("li",null,(0,r.__)("Free Content AI Trial","rank-math")),wp.element.createElement("li",null,(0,r.__)("SEO Performance Email Reports","rank-math"))),wp.element.createElement(p.Button,{href:rankMath.links.pro,target:"_blank",className:"button button-primary is-green"},(0,r.__)("Learn More","rank-math"))))}(n);if(g)return function(e){return wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks top-20 less-padding "+e},wp.element.createElement("h3",null,(0,r.__)("⛔️ Update Required","rank-math")),wp.element.createElement("p",null,(0,r.__)("Your current plugin version does not support this feature. Please update Rank Math PRO to version 3.0.83 or later to unlock full functionality.","rank-math")),wp.element.createElement(p.Button,{href:rankMath.links.pro,target:"_blank",className:"button button-primary is-green"},(0,r.__)("Update Now","rank-math"))))}(n);var f=(0,c.isUndefined)(wp.data.select("rank-math-content-ai"))?rankMath.contentAI:wp.data.select("rank-math-content-ai").getData(),w=f.isUserRegistered,v=f.plan,y="free"===v,b=f.credits>m,k=f.isMigrating;if(b&&u&&!y&&f.credits<500&&(b=!1),w&&v&&b&&!k&&!y)return null;var E,_="width-"+n;return!w||!v||b&&y?wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks top-20 less-padding "+_},wp.element.createElement("h3",null,(0,r.__)("🚀 Supercharge Your Content With AI","rank-math")),wp.element.createElement("p",null,!w&&!s&&(0,r.__)("Start using Content AI by connecting your RankMath.com Account","rank-math"),w&&!v&&!s&&!y&&(0,r.__)("To access this Content AI feature, you need to have an active subscription plan.","rank-math"),w&&!s&&y&&(0,r.__)("To access this Content AI feature, you have to purchase a Content AI Subscription.","rank-math"),s&&(0,r.__)("You are one step away from unlocking this premium feature along with many more.","rank-math")),function(e,t,n){return t?wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,r.__)("1-Click Article Generation","rank-math")),wp.element.createElement("li",null,(0,r.__)("Bulk Update Your SEO Meta using AI","rank-math")),wp.element.createElement("li",null,(0,r.__)("Get Access to 40+ AI SEO Tools","rank-math")),wp.element.createElement("li",null,(0,r.__)("125+ Expert-Written Prompts","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click Competitor Content Research","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click WooCommerce Product Descriptions","rank-math"))):n?wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,r.__)("1-Click Article Generation","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click Competitor Research","rank-math")),wp.element.createElement("li",null,(0,r.__)("On-Page SEO Suggestions","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click Bulk SEO Meta","rank-math")),wp.element.createElement("li",null,(0,r.__)("125+ Pre-Built Prompts","rank-math")),wp.element.createElement("li",null,(0,r.__)("Multiple RankBot Sessions","rank-math"))):40===e?wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,r.__)("1-Click Article Generation","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click SEO Content","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click SEO Meta","rank-math")),wp.element.createElement("li",null,(0,r.__)("40+ Specialized AI Tools","rank-math")),wp.element.createElement("li",null,(0,r.__)("1-Click Competitor Research","rank-math")),wp.element.createElement("li",null,(0,r.__)("125+ Pre-Built Prompts","rank-math"))):wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,r.__)("Gain access to 40+ advanced AI tools, empowering your content strategy.","rank-math")),wp.element.createElement("li",null,(0,r.__)("Experience the revolutionary AI-powered Content Editor for unparalleled efficiency.","rank-math")),wp.element.createElement("li",null,(0,r.__)("Engage with RankBot, your personal AI Chat Assistant, for real-time assistance.","rank-math")))}(n,s,y),!w&&wp.element.createElement(p.Button,{href:rankMath.contentAI.connectSiteUrl,className:"button button-primary is-green"},(0,r.__)("Connect Now","rank-math")),w&&(!v||y)&&wp.element.createElement(p.Button,{href:nn(rankMath.links["content-ai"]+"?play-video=ioPeVIntJWw&","Buy+Plan+Button",y),className:"button button-primary is-green",target:"_blank"},(0,r.__)("Learn More","rank-math")))):k?wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{style:{textAlign:"center"},className:"rank-math-cta-box less-padding top-20 "+_},wp.element.createElement("h3",null,(0,r.__)("Server Maintenance Underway","rank-math")),wp.element.createElement("p",null,(0,r.__)("We are working on improving your Content AI experience. Please wait for 5 minutes and then refresh to start using the optimized Content AI. If you see this for more than 5 minutes, please ","rank-math"),wp.element.createElement("a",{href:rankMath.links.support,target:"_blank",rel:"noreferrer"},(0,r.__)("reach out to the support team.","rank-math")),(0,r.__)(" We are sorry for the inconvenience.","rank-math")))):wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box less-padding top-20 "+_},wp.element.createElement("h3",null,(0,r.__)("⛔️ Content AI Credit Alert!","rank-math")),wp.element.createElement("p",null,(E=f.resetDate)?wp.element.createElement(F,{components:{strong:wp.element.createElement("strong",null)}},(0,r.sprintf)((0,r.__)("Your monthly Content AI credits have been fully utilized. You can wait till %s for your credits to refresh or upgrade to continue enjoying seamless content creation","rank-math"),"{{strong}}"+E+"{{/strong}}")):(0,r.__)("Your monthly Content AI credits have been fully utilized. To continue enjoying seamless content creation, simply click the button below to upgrade your plan and access more credits.","rank-math")),wp.element.createElement(p.Button,{href:nn(rankMath.links["content-ai"]+"?play-video=ioPeVIntJWw&","Buy+Credits+Button",y),className:"button button-primary is-green",target:"_blank"},(0,r.__)("Learn More","rank-math")),wp.element.createElement(p.Button,{variant:"link",href:nn(rankMath.links["content-ai-restore-credits"],"Buy+Credits+Button",y),className:"button button-secondary",target:"_blank"},(0,r.__)("Missing Credits?","rank-math"))))},rn=function(e){var t=e.onClick;return wp.element.createElement(p.Modal,{title:(0,r.__)("Upgrade to re-order Focus Keywords","rank-math"),closeButtonLabel:(0,r.__)("Close","rank-math"),shouldCloseOnClickOutside:!0,onRequestClose:function(){return t()},className:"rank-math-modal rank-math-focus-keywords-cta-modal",overlayClassName:"rank-math-modal-overlay"},wp.element.createElement("div",{className:"components-panel__body rank-math-focus-keywords-cta-wrapper"},wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks"},wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,r.__)("Improve the SEO workflow","rank-math")),wp.element.createElement("li",null,(0,r.__)("Set different Primary Focus Keyword","rank-math")),wp.element.createElement("li",null,(0,r.__)("and many other premium SEO features","rank-math"))),wp.element.createElement("a",{className:"button button-primary is-green",href:rankMath.trendsUpgradeLink,rel:"noreferrer noopener",target:"_blank"},rankMath.trendsUpgradeLabel)))))};function on(e){return on="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},on(e)}function sn(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,mn(a.key),a)}}function ln(e,t){return ln=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ln(e,t)}function cn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=pn(e);if(t){var r=pn(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return function(e,t){if(t&&("object"===on(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return un(e)}(this,n)}}function un(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function pn(e){return pn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},pn(e)}function dn(e,t,n){return(t=mn(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function mn(e){var t=function(e,t){if("object"!==on(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==on(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===on(t)?t:String(t)}var hn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ln(e,t)}(l,e);var t,n,i,s=cn(l);function l(e){var t,n=e.keywords;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),dn(un(t=s.call(this)),"state",{}),dn(un(t),"request",null),dn(un(t),"hasAdded",!1),dn(un(t),"clickCount",0),dn(un(t),"singleClickTimer",null),t.state={showCTA:!1},t.tagifyField=(0,o.createRef)(),t.showKeywordIntent=rankMath.showKeywordIntent,t.keywords=n,t.hideDropdown=t.hideDropdown.bind(un(t)),t.callbacks={add:t.onAdd.bind(un(t)),remove:(0,c.debounce)(t.onRemove.bind(un(t)),300),"edit:updated":t.onEditUpdated.bind(un(t)),click:t.onClick.bind(un(t)),dragEnd:t.onDragEnd.bind(un(t)),setup:t.onSetup.bind(un(t)),blur:t.hideDropdown},rankMath.autoSuggestKeywords&&(t.callbacks.input=(0,c.debounce)(t.onInput.bind(un(t)),300)),window.rankMathEditor.focusKeywordField=un(t),t}return t=l,n=[{key:"render",value:function(){var e=this,t=(0,a.applyFilters)("rank_math_focus_keyword_settings",{addTagOnBlur:!0,maxTags:"post"===rankMath.objectType?rankMath.maxTags:1,whitelist:this.state.whitelist||[],focusableTags:!0,transformTag:function(e){e.value=tn(e.value.replaceAll(",",""))},templates:{tag:function(t){var n=t.title||t.value,r="tagify__tag ".concat(e.maybeAddIntentClass(n)," ");if(!t.class){var i=rankMathEditor.resultManager.getScore(te(n));r+=e.getScoreClass(i)}return n=tn(n),'\n\t\t\t\t\t\t\t<tag\n\t\t\t\t\t\t\t\tdraggable="true"\n\t\t\t\t\t\t\t\ttabIndex="0"\n\t\t\t\t\t\t\t\tcontenteditable="false"\n\t\t\t\t\t\t\t\tspellcheck="false"\n\t\t\t\t\t\t\t\tclass="'.concat(r,'"\n\t\t\t\t\t\t\t\ttitle="').concat(n,'"\n\t\t\t\t\t\t\t\tdata-value="').concat((0,c.toLower)(te(n)),'"\n\t\t\t\t\t\t\t\t').concat(function(e){if("[object Object]"!==Object.prototype.toString.call(e))return"";var t,n,a="",r=Object.keys(e);for(n=r.length;n--;)"class"!==(t=r[n])&&e.hasOwnProperty(t)&&e[t]&&(a+=""+t+(e[t]?'="'.concat(tn(e[t]),'"'):""));return a}(t),"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>\n\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t<span class=\"tagify__tag-text\">").concat(n,"</span>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t").concat((0,a.applyFilters)("rank_math_after_focus_keyword",e.addKeywordIntent(te(n)),te(n)),"\n\t\t\t\t\t\t\t</tag>\n\t\t\t\t\t\t")}},callbacks:this.callbacks},this);return this.setKeywordsClasses(),wp.element.createElement(React.Fragment,null,wp.element.createElement(en,{ref:this.tagifyField,mode:"input",settings:t,showDropdown:this.state.showDropdown,initialValue:this.keywords,placeholder:(0,r.__)("Example: Rank Math SEO","rank-math")}),this.state.showCTA&&wp.element.createElement(rn,{onClick:function(){return e.setState({showCTA:!1})}}))}},{key:"shouldComponentUpdate",value:function(e,t){return this.state.showCTA!==t.showCTA||this.state.showDropdown!==t.showDropdown||e.isRefreshing!==this.props.isRefreshing||e.keywords!==this.props.keywords}},{key:"getScoreClass",value:function(e){return 80<e?"good-fk":50<e?"ok-fk":"bad-fk"}},{key:"setKeywordsClasses",value:function(){var e=this;if(null!==this.tagifyField.current){var t=this.tagifyField.current,n=t.tagify.value;if(n.length>0){var a=t.queryTags();n.map((function(t,n){if(!(0,c.isUndefined)(a[n])){var r=rankMathEditor.resultManager.getScore(te(t.value));a[n].classList.remove("ok-fk","good-fk","bad-fk"),a[n].classList.add(e.getScoreClass(r))}}))}}}},{key:"maybeAddIntentClass",value:function(e){if(!this.showKeywordIntent)return"";var t="has-keyword-intent";return e.length>10&&(t+=" float-keyword-intent"),t}},{key:"addKeywordIntent",value:function(e){return!this.showKeywordIntent||rankMathEditor.getPrimaryKeyword()&&e!==rankMathEditor.getPrimaryKeyword()?"":(ve()(document).on("click","#rank-math-get-keyword-intent",(function(e){var t,n,a,r,i,s,l,u,d,m=rankMath,h=m.isPro,g=m.searchIntents;if(!h||(0,c.isUndefined)(g))return e.preventDefault(),t={showProNotice:!h,isKeywordIntent:(0,c.isUndefined)(g)},n=t.showProNotice,a=void 0!==n&&n,r=t.isBulkEdit,i=void 0!==r&&r,s=t.creditsRequired,l=void 0===s?0:s,u=t.isKeywordIntent,d=void 0!==u&&u,(0,c.isNull)(document.getElementById("rank-math-content-ai-modal-wrapper"))&&ve()("#wpwrap").append('<div id="rank-math-content-ai-modal-wrapper"></div>'),setTimeout((function(){(0,o.render)(wp.element.createElement(p.Modal,{className:"rank-math-contentai-modal rank-math-modal rank-math-error-modal",shouldCloseOnClickOutside:!1,onRequestClose:function(e){(0,c.isUndefined)(e)||(ve()(".components-modal__screen-overlay").remove(),document.getElementById("rank-math-content-ai-modal-wrapper").remove(),document.body.classList.remove("modal-open"))}},wp.element.createElement(an,{width:100,showProNotice:a,isBulkEdit:i,creditsRequired:l,isKeywordIntent:d})),document.getElementById("rank-math-content-ai-modal-wrapper"))}),100),!1})),'\n\t\t\t<span class="rank-math-keyword-intent-container">\n\t\t\t\t<button id="rank-math-get-keyword-intent" class="rank-math-keyword-intent-wrapper is-show-intent">\n\t\t\t\t\t<span class=\'icon rm-icon-bulb-1\'></span>\n\t\t\t\t\t<span class="intent">'.concat((0,r.__)("Show Intent","rank-math"),"</span>\n\t\t\t\t</button>\n\t\t\t</span>\n\t\t"))}},{key:"onSetup",value:function(){this.selectFirstKeyword(),this.setKeywordsClasses()}},{key:"onInput",value:function(e){var t=this;(e=(0,c.has)(e.detail,"value")?e.detail.value:e.detail.data.value).length<2||(this.hideDropdown(),this.hasAdded?this.hasAdded=!1:this.request=ve().ajax({url:rankMath.keywordsApi.url,data:{keyword:e,locale:rankMath.locale},success:function(n){if(t.hasAdded)t.hasAdded=!1;else{var a=ve().map(n,(function(e){return e}));t.setState({whitelist:a,showDropdown:e})}}}))}},{key:"onAdd",value:function(e){this.hasAdded=!0,0===e.detail.index&&this.props.updateSelectedKeyword(e.detail,this.tagifyField.current),this.updateKeywords()}},{key:"onRemove",value:function(e){this.hideDropdown(),0===e.detail.index&&this.onSetup(),rankMathEditor.resultManager.deleteResult(e.detail.data.value),this.selectFirstKeyword(),this.updateKeywords()}},{key:"onClick",value:function(e){var t=this;this.clickCount++,1===this.clickCount?this.singleClickTimer=setTimeout((function(){t.clickCount=0,t.props.updateSelectedKeyword(e.detail,t.tagifyField.current)}),400):2===this.clickCount&&(clearTimeout(this.singleClickTimer),this.clickCount=0)}},{key:"onEditUpdated",value:function(e){this.hasAdded=!0,0===e.detail.index&&this.props.updateSelectedKeyword(e.detail,this.tagifyField.current),this.updateKeywords()}},{key:"selectFirstKeyword",value:function(){var e=this.tagifyField.current,t=e.tagify.value,n={tag:"",index:0,data:{value:""}};t.length>0&&(n={tag:e.queryTags()[0],index:0,data:{value:t[0].value}}),this.props.updateSelectedKeyword(n,e)}},{key:"updateKeywords",value:function(){var e=this.tagifyField.current,t=tn(e.toString());this.props.updateKeywords(t)}},{key:"onDragEnd",value:function(){1!==this.tagifyField.current.queryTags().length&&this.setState({showCTA:!0})}},{key:"hideDropdown",value:function(){null!==this.request&&(this.request.abort(),this.request=null),this.setState({whitelist:[],showDropdown:!1})}}],n&&sn(t.prototype,n),i&&sn(t,i),Object.defineProperty(t,"prototype",{writable:!1}),l}(o.Component),gn=(0,u.compose)((0,i.withSelect)((function(e){var t=e("rank-math");return{keywords:t.getKeywords(),isRefreshing:t.isRefreshing()}})),(0,i.withDispatch)((function(e){return{updateKeywords:function(t){e("rank-math").updateKeywords(t)},updateSelectedKeyword:function(t,n){n.queryTags().forEach((function(e){e.classList.remove("selected")})),(0,c.isUndefined)(t.tag)||""===t.tag||t.tag.classList.add("selected"),e("rank-math").updateSelectedKeyword(t)}}})))(hn);function fn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return wn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return wn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var vn=(0,p.withFilters)("rankMath.focusKeywords.Trends")((function(){var e=fn((0,o.useState)(!1),2),t=e[0],n=e[1];return wp.element.createElement(o.Fragment,null,wp.element.createElement(p.Button,{title:(0,r.__)("Trends","rank-math"),rel:"noreferrer noopener",id:"rank-math-compare-keywords-trigger",className:"button button-icon rank-math-compare-keywords-trigger",onClick:function(){n(!0)}},wp.element.createElement("span",{className:"button-icon",dangerouslySetInnerHTML:{__html:rankMath.trendsIcon}})),t&&wp.element.createElement(p.Modal,{title:(0,r.__)("Google Trends","rank-math"),closeButtonLabel:(0,r.__)("Close","rank-math"),shouldCloseOnClickOutside:!0,onRequestClose:function(){n(!1)},className:"rank-math-modal rank-math-trends-cta-modal",overlayClassName:"rank-math-modal-overlay"},wp.element.createElement("div",{className:"components-panel__body rank-math-trends-cta-wrapper"},wp.element.createElement("img",{src:rankMath.trendsPreviewImage,alt:"",className:"trends-cta blurred"}),wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks width-50"},wp.element.createElement("h3",null,(0,r.__)("Track Keyword Trends","rank-math")),wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,r.__)("Data fetched directly from Google","rank-math")),wp.element.createElement("li",null,(0,r.__)("Analyze search trends and compare keywords","rank-math")),wp.element.createElement("li",null,(0,r.__)("See data from a particular Country or timeframe","rank-math"))),wp.element.createElement("a",{className:"button button-primary is-green",href:rankMath.trendsUpgradeLink,rel:"noreferrer noopener",target:"_blank"},rankMath.trendsUpgradeLabel))))))})),yn=function(){return!((0,c.isNil)(window.wp)||(0,c.isNil)(wp.data)||(0,c.isNil)(wp.data.select("core/editor"))||!window.document.body.classList.contains("block-editor-page")||!(0,c.isFunction)(wp.data.select("core/editor").getEditedPostAttribute))},bn=(0,p.withFilters)("rankMath.analytics.contentAI")((function(){return!1})),kn=(0,u.compose)((0,i.withSelect)((function(e){var t=e("rank-math");return{isLoaded:t.isLoaded(),isPillarContent:t.getPillarContent()}})),(0,i.withDispatch)((function(e){return{togglePillarContent:function(t){e("rank-math").updatePillarContent(t)}}})))((function(e){var t=e.isLoaded,n=e.isPillarContent,a=e.togglePillarContent;return t?wp.element.createElement(p.PanelBody,{initialOpen:!0,className:"rank-math-focus-keyword"},wp.element.createElement("h2",{className:"components-panel__body-title"},(0,r.__)("Focus Keyword","rank-math"),wp.element.createElement(zt,null,wp.element.createElement(F,{components:{link:wp.element.createElement("a",{href:fe("score-100","General Focus Keyword"),target:"_blank",rel:"noopener noreferrer"})}},(0,r.__)("Insert keywords you want to rank for. Try to {{link}}attain 100/100 points{{/link}} for better chances of ranking.","rank-math")))),wp.element.createElement(vn,null),rankMath.currentEditor&&("classic"!==rankMath.currentEditor||yn())&&wp.element.createElement(bn,null),wp.element.createElement(Ht.Slot,null,(function(e){return e.length>0?e:[]})),wp.element.createElement("div",null,wp.element.createElement(gn,null)),wp.element.createElement(Wt,null),"post"===rankMath.objectType&&wp.element.createElement(p.CheckboxControl,{className:"pillar-content",label:wp.element.createElement(o.Fragment,null,wp.element.createElement("strong",null,(0,r.__)("This post is Pillar Content","rank-math")),wp.element.createElement("a",{href:fe("pillar-content-internal-linking","Pillar Content"),rel:"noreferrer",target:"_blank",className:"dashicons-before dashicons-editor-help rank-math-help-icon"})),checked:n,onChange:a})):null})),En=(0,p.createSlotFill)("RankMathAfterEditor"),_n=En.Fill,Tn=En.Slot,xn=function(e){var t=e.children,n=e.className;return wp.element.createElement(_n,null,wp.element.createElement(p.PanelRow,{className:n},t))};xn.Slot=Tn;var On=xn,Sn=(0,i.withDispatch)((function(e){return{toggleEditor:function(){e("rank-math").toggleSnippetEditor(!0)}}}))((function(e){var t=e.toggleEditor;return wp.element.createElement(o.Fragment,null,wp.element.createElement(p.PanelBody,{initialOpen:!0},wp.element.createElement(he,{onClick:t}),wp.element.createElement(We,null),wp.element.createElement(On.Slot,null,(function(e){return e.length>0?e:[]}))),wp.element.createElement(kn,null),rankMath.canUser.analysis&&wp.element.createElement(Ft,null))})),Cn=function(e,t,n){return!1===n?delete e[t]:e[t]=!0,e},Nn=(0,u.compose)((0,i.withSelect)((function(e){var t=e("rank-math").getRobots();return{robots:t,isRobotIndex:"index"in t,isRobotNoIndex:"noindex"in t,isRobotNoFollow:"nofollow"in t,isRobotNoArchive:"noarchive"in t,isRobotNoImageIndex:"noimageindex"in t,isRobotNoSnippet:"nosnippet"in t}})),(0,i.withDispatch)((function(e,t){var n=t.robots;return{updateRobots:function(t,a){n=Cn(n,t,a),"index"===t&&(n=Cn(n,"noindex",!a)),"noindex"===t&&(n=Cn(n,"index",!a)),e("rank-math").updateRobots(n)}}})))((function(e){return wp.element.createElement(p.BaseControl,{className:"rank-math-robots",id:"rank-math-robots",label:(0,r.__)("Robots Meta","rank-math")},wp.element.createElement("div",{className:"rank-math-robots-list"},wp.element.createElement(p.CheckboxControl,{className:"robot-choice-index",label:wp.element.createElement(o.Fragment,null,(0,r.__)("Index","rank-math"),wp.element.createElement(zt,null,(0,r.__)("Instructs search engines to index and show these pages in the search results","rank-math"))),checked:e.isRobotIndex,onChange:function(t){return e.updateRobots("index",t)}}),wp.element.createElement(p.CheckboxControl,{className:"robot-choice-noindex",label:wp.element.createElement(o.Fragment,null,(0,r.__)("No Index","rank-math"),wp.element.createElement(zt,null,(0,r.__)("Prevents pages from being indexed and displayed in search engine result pages","rank-math"))),checked:e.isRobotNoIndex,onChange:function(t){return e.updateRobots("noindex",t)}}),wp.element.createElement(p.CheckboxControl,{className:"robot-choice-nofollow",label:wp.element.createElement(o.Fragment,null,(0,r.__)("Nofollow","rank-math"),wp.element.createElement(zt,null,(0,r.__)("Prevents search engines from following links on the pages","rank-math"))),checked:e.isRobotNoFollow,onChange:function(t){return e.updateRobots("nofollow",t)}}),wp.element.createElement(p.CheckboxControl,{className:"robot-choice-noarchive",label:wp.element.createElement(o.Fragment,null,(0,r.__)("No Archive","rank-math"),wp.element.createElement(zt,null,(0,r.__)("Prevents search engines from showing Cached links for pages","rank-math"))),checked:e.isRobotNoArchive,onChange:function(t){return e.updateRobots("noarchive",t)}}),wp.element.createElement(p.CheckboxControl,{className:"robot-choice-noimageindex",label:wp.element.createElement(o.Fragment,null,(0,r.__)("No Image Index","rank-math"),wp.element.createElement(zt,null,(0,r.__)("This option prevents images on a page from being indexed by Google and other search engines","rank-math"))),checked:e.isRobotNoImageIndex,onChange:function(t){return e.updateRobots("noimageindex",t)}}),wp.element.createElement(p.CheckboxControl,{className:"robot-choice-nosnippet",label:wp.element.createElement(o.Fragment,null,(0,r.__)("No Snippet","rank-math"),wp.element.createElement(zt,null,(0,r.__)("Prevents a snippet from being shown in the search results","rank-math"))),checked:e.isRobotNoSnippet,onChange:function(t){return e.updateRobots("nosnippet",t)}})))}));function In(e){return In="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},In(e)}function Dn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function Mn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Dn(Object(n),!0).forEach((function(t){An(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Dn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function An(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==In(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==In(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===In(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Pn=(0,u.compose)((0,i.withSelect)((function(e){var t=e("rank-math");return{redirectionID:t.getRedirectionID(),redirectionUrl:t.getRedirectionUrl(),redirectionType:t.getRedirectionType(),hasRedirect:t.hasRedirect()}})),(0,i.withDispatch)((function(e,t){return{toggle:function(){e("rank-math").updateHasRedirect(!t.hasRedirect),e("rank-math").updateRedirectionItem(Mn(Mn({},t),{},{hasRedirect:!t.hasRedirect}))},updateRedirection:function(n,a){e("rank-math").updateRedirection(n,a),e("rank-math").updateRedirectionItem(Mn(Mn({},t),{},An({},n,a)))}}})))((function(e){return wp.element.createElement(o.Fragment,null,wp.element.createElement(p.ToggleControl,{className:e.hasRedirect?"is-open":"is-close",label:(0,r.__)("Redirect","rank-math"),checked:e.hasRedirect,onChange:function(){return e.toggle()}}),e.hasRedirect&&wp.element.createElement(p.SelectControl,{label:(0,r.__)("Redirection Type","rank-math"),className:e.hasRedirect?"":"hidden",value:e.redirectionType,onChange:function(t){return e.updateRedirection("redirectionType",t)},options:[{value:"301",label:(0,r.__)("301 Permanent Move","rank-math")},{value:"302",label:(0,r.__)("302 Temporary Move","rank-math")},{value:"307",label:(0,r.__)("307 Temporary Redirect","rank-math")},{value:"410",label:(0,r.__)("410 Content Deleted","rank-math")},{value:"451",label:(0,r.__)("451 Content Unavailable for Legal Reasons","rank-math")}]}),!1===["410","451"].includes(e.redirectionType)&&wp.element.createElement(p.TextControl,{type:"url",autoComplete:"off",label:(0,r.__)("Destination URL","rank-math"),value:e.redirectionUrl,placeholder:"https://rankmath.com/",className:e.hasRedirect?"":"hidden",onChange:function(t){return e.updateRedirection("redirectionUrl",t)}}),wp.element.createElement(p.TextControl,{type:"hidden",value:e.redirectionID,className:"hidden"}))})),Rn=(0,u.compose)((0,i.withSelect)((function(e){var t=e("rank-math"),n=rankMathEditor.assessor.dataCollector;return{placeholder:t.getCanonicalUrl()?t.getCanonicalUrl():n.getPermalink()?n.getPermalink():"https://rankmath.com/",canonicalUrl:t.getCanonicalUrl()}})),(0,i.withDispatch)((function(e){return{onUrlChange:function(t){e("rank-math").updateCanonicalUrl(t)}}})))((function(e){var t=e.canonicalUrl,n=e.placeholder,a=e.onUrlChange;return wp.element.createElement(p.BaseControl,{className:"rank-math-canonical"},wp.element.createElement("span",{className:"components-base-control__label"},(0,r.__)("Canonical URL","rank-math"),wp.element.createElement(zt,null,(0,r.__)("The canonical URL informs search crawlers which page is the main page if you have double content","rank-math"))),wp.element.createElement(p.TextControl,{type:"url",autoComplete:"off",value:t,placeholder:n,onChange:function(e){return a(e)}}))})),jn=(0,u.compose)((0,i.withSelect)((function(e){return{title:e("rank-math").getBreadcrumbTitle()}})),(0,i.withDispatch)((function(e){return{onTitleChange:function(t){e("rank-math").updateBreadcrumbTitle(t)}}})))((function(e){var t=e.title,n=e.onTitleChange;return wp.element.createElement(p.BaseControl,{className:"rank-math-breadcrumb-title"},wp.element.createElement("span",{className:"components-base-control__label"},(0,r.__)("Breadcrumb Title","rank-math"),wp.element.createElement(zt,null,(0,r.__)("Breadcrumb Title to use for this post","rank-math"))),wp.element.createElement(p.TextControl,{value:t,onChange:function(e){return n(e)}}))}));function Fn(e){return Fn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Fn(e)}function Ln(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function Bn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ln(Object(n),!0).forEach((function(t){Un(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ln(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Un(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Fn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==Fn(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Fn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Vn={"max-snippet":-1,"max-video-preview":-1,"max-image-preview":"large"},Hn=(0,u.compose)((0,i.withSelect)((function(e){var t=e("rank-math").getAdvancedRobots(),n=Bn({},t);(0,c.defaults)(n,Vn);var a=null,r=null,i=null;return"max-snippet"in t&&(a=n["max-snippet"]),"max-image-preview"in t&&(r=n["max-image-preview"]),"max-video-preview"in t&&(i=n["max-video-preview"]),{meta:t,isSnippet:a,isImage:r,isVideo:i,maxSnippet:a?n["max-snippet"]:-1,maxImage:n["max-image-preview"],maxVideo:i?n["max-video-preview"]:-1}})),(0,i.withDispatch)((function(e,t){var n=Bn({},t.meta);return{updateRobots:function(t,a){(0,c.isBoolean)(a)?!1===a?delete n[t]:n[t]=Vn[t]:n[t]=a,e("rank-math").updateAdvancedRobots(n)}}})))((function(e){return wp.element.createElement(p.BaseControl,{className:"rank-math-robots",id:"rank-math-robots",label:(0,r.__)("Advanced Robots Meta","rank-math")},wp.element.createElement("div",{className:"rank-math-robots-list advanced-robots"},wp.element.createElement(p.CheckboxControl,{label:wp.element.createElement(o.Fragment,null,(0,r.__)("Max Snippet","rank-math"),wp.element.createElement(zt,null,(0,r.__)("Specify a maximum text-length, in characters, of a snippet for your page","rank-math"))),checked:e.isSnippet,onChange:function(t){return e.updateRobots("max-snippet",t)}}),wp.element.createElement(p.TextControl,{type:"number",value:e.maxSnippet,onChange:function(t){return e.updateRobots("max-snippet",t)}}),wp.element.createElement(p.CheckboxControl,{label:wp.element.createElement(o.Fragment,null,(0,r.__)("Max Video Preview","rank-math"),wp.element.createElement(zt,null,(0,r.__)("Specify a maximum duration in seconds of an animated video preview","rank-math"))),checked:e.isVideo,onChange:function(t){return e.updateRobots("max-video-preview",t)}}),wp.element.createElement(p.TextControl,{type:"number",value:e.maxVideo,onChange:function(t){return e.updateRobots("max-video-preview",t)}}),wp.element.createElement(p.CheckboxControl,{label:wp.element.createElement(o.Fragment,null,(0,r.__)("Max Image Preview","rank-math"),wp.element.createElement(zt,null,(0,r.__)("Specify a maximum size of image preview to be shown for images on this page","rank-math"))),checked:e.isImage,onChange:function(t){return e.updateRobots("max-image-preview",t)}}),wp.element.createElement(p.SelectControl,{value:e.maxImage,onChange:function(t){return e.updateRobots("max-image-preview",t)},options:[{value:"large",label:(0,r.__)("Large","rank-math")},{value:"standard",label:(0,r.__)("Standard","rank-math")},{value:"none",label:(0,r.__)("None","rank-math")}]})))})),Wn=(0,u.compose)((0,i.withSelect)((function(e){return{showScore:e("rank-math").getShowScoreFrontend()}})),(0,i.withDispatch)((function(e){return{toggleScore:function(t){e("rank-math").toggleFrontendScore(t)}}})))((function(e){var t=e.showScore,n=e.toggleScore;return wp.element.createElement(p.BaseControl,{className:"rank-math-frontend-score"},wp.element.createElement(p.ToggleControl,{label:(0,r.__)("Show SEO Score on Front-end","rank-math"),checked:t,onChange:n}))})),zn=(0,p.createSlotFill)("RankMathAdvancedTab"),Gn=zn.Fill,Kn=zn.Slot,$n=function(e){var t=e.children,n=e.className;return wp.element.createElement(Gn,null,wp.element.createElement(p.PanelRow,{className:n},t))};$n.Slot=Kn;var qn=$n,Yn=(0,p.withFilters)("rankMath.advanced.newsSitemap")((function(){return null})),Jn=function(){return wp.element.createElement(o.Fragment,null,wp.element.createElement(p.PanelBody,{initialOpen:!0},wp.element.createElement(Nn,null)),wp.element.createElement(p.PanelBody,{initialOpen:!0},wp.element.createElement(Hn,null)),wp.element.createElement(Yn,null),wp.element.createElement(p.PanelBody,{initialOpen:!0},wp.element.createElement(Rn,null)),rankMath.assessor.hasBreadcrumb&&wp.element.createElement(p.PanelBody,{initialOpen:!0},wp.element.createElement(jn,null)),rankMath.assessor.hasRedirection&&wp.element.createElement(p.PanelBody,{initialOpen:!0,className:"rank-math-redirect"},wp.element.createElement(Pn,null)),rankMath.frontEndScore&&wp.element.createElement(p.PanelBody,{initialOpen:!0},wp.element.createElement(Wn,null)),wp.element.createElement(qn.Slot,null,(function(e){return e.length>0?e:[]})))},Xn=n(3279),Zn=n.n(Xn),Qn={init:function(){this.cacheProps(),this.initVars(),this.bindEvents()},cacheProps:function(){this.root=document.documentElement,this.$wpAdminbar=ve()("#wpadminbar")},initVars:function(){this.$wpAdminbar.length&&this.setWpAdminbarHeight.call(this)},bindEvents:function(){window.addEventListener("resize",Zn()(this.onWindowResize.bind(this)))},onWindowResize:function(){this.$wpAdminbar.length&&this.setWpAdminbarHeight.call(this)},setWpAdminbarHeight:function(){this.root.style.setProperty("--rankmath-wp-adminbar-height",this.$wpAdminbar.outerHeight()+"px")}};ve()((function(){Qn.init()}));var ea=function(e){"social"===e&&(0,i.dispatch)("rank-math").toggleSnippetEditor(!0)},ta=function(){return wp.element.createElement(x,{className:"rank-math-tabs",activeClass:"is-active",tabs:(e=[],rankMath.canUser.general&&e.push({name:"general",title:wp.element.createElement(o.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-settings",title:(0,r.__)("General","rank-math")}),wp.element.createElement("span",null,(0,r.__)("General","rank-math"))),view:Sn,className:"rank-math-general-tab"}),rankMath.canUser.advanced&&e.push({name:"advanced",title:wp.element.createElement(o.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-toolbox",title:(0,r.__)("Advanced","rank-math")}),wp.element.createElement("span",null,(0,r.__)("Advanced","rank-math"))),view:Jn,className:"rank-math-advanced-tab"}),rankMath.canUser.social&&e.push({name:"social",title:wp.element.createElement(o.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-social",title:(0,r.__)("Social","rank-math")}),wp.element.createElement("span",null,(0,r.__)("Social","rank-math"))),view:ze,className:"rank-math-social-tab"}),(0,a.applyFilters)("rank_math_sidebar_tabs",e)),onSelect:ea},(function(e){return wp.element.createElement("div",{className:"rank-math-tab-content-"+e.name},(0,o.createElement)(e.view))}));var e};(0,a.addAction)("rank_math_loaded","rank-math",(function(){(0,a.addFilter)("rank_math_app","rank-math",(function(){return ta}))}))}()}();