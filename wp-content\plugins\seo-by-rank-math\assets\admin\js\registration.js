!function(){var e={4184:function(e,t){var n;!function(){"use strict";var r={}.hasOwnProperty;function a(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var o=typeof n;if("string"===o||"number"===o)e.push(n);else if(Array.isArray(n)){if(n.length){var i=a.apply(null,n);i&&e.push(i)}}else if("object"===o){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var l in n)r.call(n,l)&&n[l]&&e.push(l)}}}return e.join(" ")}e.exports?(a.default=a,e.exports=a):void 0===(n=function(){return a}.apply(t,[]))||(e.exports=n)}()}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";var e=wp.element;function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=rankMath.links[e]||"";if(!n)return"#";if(!t)return n;var r={utm_source:"Plugin",utm_medium:encodeURIComponent(t),utm_campaign:"WP"};return n+"?"+Object.keys(r).map((function(e){return"".concat(e,"=").concat(r[e])})).join("&")}var r=function(){return wp.element.createElement("div",{className:"header"},wp.element.createElement("div",{className:"logo text-center"},wp.element.createElement("a",{href:t("seo-suite","SW Logo"),target:"_blank",rel:"noreferrer"},wp.element.createElement("img",{src:rankMath.logo,alt:"Rank Math SEO",width:"245"}))))},a=n(4184),o=n.n(a),i=wp.i18n,l=window.rankMathComponents,c=function(e){var t=e.link,n=e.heading,r=e.description,a=e.linkText,o=void 0===a?(0,i.__)("Learn more.","rank-math"):a,l=e.className,c=void 0===l?"":l;return wp.element.createElement("header",null,wp.element.createElement("h1",{dangerouslySetInnerHTML:{__html:n}}),wp.element.createElement("p",{className:c},r,t&&wp.element.createElement("a",{href:t,target:"_blank",rel:"noreferrer"},o)))};function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return m(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return m(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var s,p=function(){var n=rankMath,r=n.adminUrl,a=n.registerNonce,m=n.isSiteUrlValid,s=u((0,e.useState)("rank_math_save_registration"),2),p=s[0],d=s[1],f=o()("button button-primary button-connect",{"button-animated":m,disabled:!m});return wp.element.createElement("div",{className:"wrapper"},wp.element.createElement("form",{method:"post",action:r},wp.element.createElement(l.TextControl,{type:"hidden",name:"action",value:p}),wp.element.createElement(l.TextControl,{type:"hidden",name:"step",value:"register"}),wp.element.createElement(l.TextControl,{type:"hidden",name:"security",value:a}),wp.element.createElement("div",{className:"main-content wizard-content--register"},wp.element.createElement(c,{heading:(0,i.__)("Connect FREE Account","rank-math"),description:(0,i.__)("By connecting your free account, you get keyword suggestions directly from Google when entering the focus keywords. Not only that, get access to our revolutionary Content AI, SEO Analyzer inside WordPress that scans your website for SEO errors and suggest improvements.","rank-math"),link:t("free-account-benefits","SW Connect Free Account"),linkText:(0,i.__)("Read more by following this link.","rank-math"),className:"rank-math-gray-box"}),wp.element.createElement(l.InvalidSiteUrlNotice,{isSiteUrlValid:m}),wp.element.createElement("div",{className:"text-center wp-core-ui rank-math-ui",style:{marginTop:"30px"}},wp.element.createElement("button",{type:"submit",className:f,name:"rank_math_activate"},(0,i.__)("Connect Your Account","rank-math")))),wp.element.createElement("footer",{className:"form-footer wp-core-ui rank-math-ui"},wp.element.createElement(l.Button,{variant:"secondary",className:"button-skip",type:"submit",onClick:function(){d("rank_math_skip_wizard")}},(0,i.__)("Skip Step","rank-math")))))};s=function(){(0,e.createRoot)(document.getElementById("rank-math-wizard-wrapper")).render(wp.element.createElement(React.Fragment,null,wp.element.createElement(r,null),wp.element.createElement(p,null)))},"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",s):s())}()}();