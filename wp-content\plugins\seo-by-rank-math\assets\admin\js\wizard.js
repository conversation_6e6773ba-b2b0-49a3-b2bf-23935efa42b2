!function(){var e={4184:function(e,t){var n;!function(){"use strict";var r={}.hasOwnProperty;function a(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var o=typeof n;if("string"===o||"number"===o)e.push(n);else if(Array.isArray(n)){if(n.length){var i=a.apply(null,n);i&&e.push(i)}}else if("object"===o){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var l in n)r.call(n,l)&&n[l]&&e.push(l)}}}return e.join(" ")}e.exports?(a.default=a,e.exports=a):void 0===(n=function(){return a}.apply(t,[]))||(e.exports=n)}()},5317:function(e,t,n){"use strict";var r=n(8081),a=n.n(r),o=n(3645),i=n.n(o)()(a());i.push([e.id,':root{--rankmath-wp-adminbar-height: 0}p.field-description{padding-top:.5em;margin:0;color:#666;letter-spacing:.01em}.form-table{width:100%;margin-top:.5em;border-collapse:collapse;clear:both}.form-table,.form-table td,.form-table td p,.form-table th{font-size:14px}.field-metabox{margin:0;clear:both}.field-metabox>.field-row:first-of-type>.field-td,.field-metabox>.field-row:first-of-type>.field-th,.field-metabox.field-list>.field-row:first-of-type>.field-td,.field-metabox.field-list>.field-row:first-of-type>.field-th{border:0}.field-metabox .note{margin-right:5px;padding:2px 6px;border-radius:3px;color:#794800;background:rgba(255,190,95,.5)}.field-th{float:left;width:200px;padding:20px 10px 20px 0;color:#222;font-weight:600;vertical-align:top}.field-th label{display:block;padding:5px 0}.field-th+.field-td{float:left}.field-td{max-width:100%;padding:15px 10px;line-height:1.3;vertical-align:middle}[id^=field-metabox-rank] .field-row{margin:0;padding:25px 0}[id^=field-metabox-rank] .field-row:not(.field-type-title):first-of-type{padding-top:0}[id^=field-metabox-rank] .field-row:not(.field-type-title):last-of-type{padding-bottom:0}[id^=field-metabox-rank] .field-row.rank-math-advanced-option~.rank-math-advanced-option,[id^=field-metabox-rank] .field-row:not(.rank-math-advanced-option):not(.tab-header):not(.field-type-notice)~.field-row:not(.rank-math-advanced-option):not(.rank-math-notice){border-top:1px solid #dadfe4;border-bottom:0}[id^=field-metabox-rank] .field-row .field-td,[id^=field-metabox-rank] .field-row .field-th{padding:0;box-sizing:border-box}[id^=field-metabox-rank] .field-row .field-th{width:25%;max-width:200px;color:#242628}[id^=field-metabox-rank] .field-row .field-th label{padding:0 15px 0 0}[id^=field-metabox-rank] .field-row .field-td{float:left;width:75%}[id^=field-metabox-rank] .field-description{padding-top:10px;color:#7f868d;font-size:14px;font-style:normal}[id^=field-metabox-rank] .media-status .img-status img{box-shadow:0 0 0 1px #e9e9e9;outline:none}.field-list>.field-row{vertical-align:top}.field-wrap{margin:0}.field-wrap .field-row{position:relative;margin:0}.field-wrap .field-row::after{content:"";display:block;width:100%;clear:both}.field-wrap .field-row:first-of-type>.field-td .rank-math-button.toggle-all-capabilities{top:32px}.field-wrap+footer.form-footer{display:flex;justify-content:space-between;flex-wrap:wrap;padding:1.875rem;margin:30px -1.875rem -1.875rem;border:0;border-top:1px solid #c3c4c7;border-radius:0 0 6px 6px;box-sizing:border-box;width:auto;background:#f8f9fa;text-align:center;overflow:hidden}.field-wrap+footer.form-footer .rank-math-button{align-items:center;justify-content:center;padding:0}.field-wrap ul{margin:0}.field-wrap li{margin:1px 0 5px 0;font-size:14px;line-height:16px}.field-disabled{opacity:.4;pointer-events:none}@media(min-width: 641px){.field-td .rank-math-button.toggle-all-capabilities{position:absolute;top:56px;left:0;font-weight:600}}@media screen and (max-width: 782px){.form-table label{font-size:14px}}@media screen and (max-width: 640px){.field-td .rank-math-button.toggle-all-capabilities{margin:1em 0 1.7em}[id^=field-metabox-rank] .field-row .field-td,[id^=field-metabox-rank] .field-row .field-th label{padding:0 0 15px 2px}[id^=field-metabox-rank] .field-row .field-td,[id^=field-metabox-rank] .field-row .field-th{width:100%;padding:0}.field-wrap .field-row{padding:15px 0}}@media screen and (max-width: 450px){.field-th{display:block;float:none;width:100%;padding-bottom:1em;font-size:1.2em;text-align:left}.field-th label{display:block;margin-top:0;margin-bottom:.5em}.field-td,.field-th+.field-td{display:block;float:none;width:100%}}.field-disabled{opacity:.4;pointer-events:none}',""]),t.Z=i},4653:function(e,t,n){"use strict";var r=n(8081),a=n.n(r),o=n(3645),i=n.n(o)()(a());i.push([e.id,".rank-math-tab-header{margin:-1.875rem -1.875rem 30px;padding:1.875rem 1.875rem 0;text-align:center;border-bottom:1px solid #b5bfc9;border-radius:6px 6px 0 0;background-color:#f8f9fa}.rank-math-tab-header h2{margin:0;font-size:30px;font-weight:500}.rank-math-tab-header p{font-size:1rem;max-width:715px;margin:0 auto 2rem}",""]),t.Z=i},3645:function(e){"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n="",r=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),r&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),r&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n})).join("")},t.i=function(e,n,r,a,o){"string"==typeof e&&(e=[[null,e,void 0]]);var i={};if(r)for(var l=0;l<this.length;l++){var c=this[l][0];null!=c&&(i[c]=!0)}for(var s=0;s<e.length;s++){var u=[].concat(e[s]);r&&i[u[0]]||(void 0!==o&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=o),n&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=n):u[2]=n),a&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=a):u[4]="".concat(a)),t.push(u))}},t}},8081:function(e){"use strict";e.exports=function(e){return e[1]}},3379:function(e){"use strict";var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var o={},i=[],l=0;l<e.length;l++){var c=e[l],s=r.base?c[0]+r.base:c[0],u=o[s]||0,m="".concat(s," ").concat(u);o[s]=u+1;var p=n(m),f={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==p)t[p].references++,t[p].updater(f);else{var d=a(f,r);r.byIndex=l,t.splice(l,0,{identifier:m,updater:d,references:1})}i.push(m)}return i}function a(e,t){var n=t.domAPI(t);n.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,a){var o=r(e=e||[],a=a||{});return function(e){e=e||[];for(var i=0;i<o.length;i++){var l=n(o[i]);t[l].references--}for(var c=r(e,a),s=0;s<o.length;s++){var u=n(o[s]);0===t[u].references&&(t[u].updater(),t.splice(u,1))}o=c}}},569:function(e){"use strict";var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},9216:function(e){"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},3565:function(e,t,n){"use strict";e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},7795:function(e){"use strict";e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var a=void 0!==n.layer;a&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,a&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var o=n.sourceMap;o&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),t.styleTagTransform(r,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},4589:function(e){"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={id:r,exports:{}};return e[r](o,o.exports,n),o.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nc=void 0,function(){"use strict";var e={};n.r(e),n.d(e,{resetStore:function(){return rn},resetdirtySettings:function(){return nn},setStep:function(){return ln},toggleLoaded:function(){return an},updateModules:function(){return tn},updateSettings:function(){return en},updateView:function(){return on}});var t={};n.r(t),n.d(t,{appData:function(){return hn},appUi:function(){return _n}});var r={};n.r(r),n.d(r,{getAppData:function(){return kn},getCurrentStep:function(){return xn},getModules:function(){return On},getSettings:function(){return En},getView:function(){return Pn},getdirtySettings:function(){return Sn},isLoaded:function(){return jn}});var a={};n.r(a),n.d(a,{setStep:function(){return Yr},updateStepData:function(){return Vr}});var o={};n.r(o),n.d(o,{appUi:function(){return Xr}});var i={};n.r(i),n.d(i,{getCurrentStep:function(){return ea},getStepData:function(){return Qr}});var l,c=React,s=ReactDOM;function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(l||(l={}));const m="popstate";function p(e,t){if(!1===e||null==e)throw new Error(t)}function f(e,t){return{usr:e.state,key:e.key,idx:t}}function d(e,t,n,r){return void 0===n&&(n=null),u({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?y(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function h(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function y(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function b(e,t,n,r){void 0===r&&(r={});let{window:a=document.defaultView,v5Compat:o=!1}=r,i=a.history,c=l.Pop,s=null,y=b();function b(){return(i.state||{idx:null}).idx}function g(){c=l.Pop;let e=b(),t=null==e?null:e-y;y=e,s&&s({action:c,location:w.location,delta:t})}function v(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"==typeof e?e:h(e);return p(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==y&&(y=0,i.replaceState(u({},i.state,{idx:y}),""));let w={get action(){return c},get location(){return e(a,i)},listen(e){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(m,g),s=e,()=>{a.removeEventListener(m,g),s=null}},createHref(e){return t(a,e)},createURL:v,encodeLocation(e){let t=v(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){c=l.Push;let r=d(w.location,e,t);n&&n(r,e),y=b()+1;let u=f(r,y),m=w.createHref(r);try{i.pushState(u,"",m)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;a.location.assign(m)}o&&s&&s({action:c,location:w.location,delta:1})},replace:function(e,t){c=l.Replace;let r=d(w.location,e,t);n&&n(r,e),y=b();let a=f(r,y),u=w.createHref(r);i.replaceState(a,"",u),o&&s&&s({action:c,location:w.location,delta:0})},go(e){return i.go(e)}};return w}var g;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(g||(g={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function v(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function w(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function _(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function k(e,t,n,r){let a;void 0===r&&(r=!1),"string"==typeof e?a=y(e):(a=u({},e),p(!a.pathname||!a.pathname.includes("?"),w("?","pathname","search",a)),p(!a.pathname||!a.pathname.includes("#"),w("#","pathname","hash",a)),p(!a.search||!a.search.includes("#"),w("#","search","hash",a)));let o,i=""===e||""===a.pathname,l=i?"/":a.pathname;if(null==l)o=n;else if(r){let e=t[t.length-1].replace(/^\//,"").split("/");if(l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e.pop();a.pathname=t.join("/")}o="/"+e.join("/")}else{let e=t.length-1;if(l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let c=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"==typeof e?y(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:E(r),hash:O(a)}}(a,o),s=l&&"/"!==l&&l.endsWith("/"),m=(i||"."===l)&&n.endsWith("/");return c.pathname.endsWith("/")||!s&&!m||(c.pathname+="/"),c}const S=e=>e.join("/").replace(/\/\/+/g,"/"),E=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",O=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;const j=["post","put","patch","delete"],P=(new Set(j),["get",...j]);new Set(P),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function x(){return x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},x.apply(this,arguments)}const A=c.createContext(null);const C=c.createContext(null);const N=c.createContext(null);const D=c.createContext({outlet:null,matches:[],isDataRoute:!1});function T(){return null!=c.useContext(N)}function R(){return T()||p(!1),c.useContext(N).location}function I(e){c.useContext(C).static||c.useLayoutEffect(e)}function M(){let{isDataRoute:e}=c.useContext(D);return e?function(){let{router:e}=B(U.UseNavigateStable),t=L(F.UseNavigateStable),n=c.useRef(!1);I((()=>{n.current=!0}));let r=c.useCallback((function(r,a){void 0===a&&(a={}),n.current&&("number"==typeof r?e.navigate(r):e.navigate(r,x({fromRouteId:t},a)))}),[e,t]);return r}():function(){T()||p(!1);let e=c.useContext(A),{basename:t,navigator:n}=c.useContext(C),{matches:r}=c.useContext(D),{pathname:a}=R(),o=JSON.stringify(_(r).map((e=>e.pathnameBase))),i=c.useRef(!1);I((()=>{i.current=!0}));let l=c.useCallback((function(r,l){if(void 0===l&&(l={}),!i.current)return;if("number"==typeof r)return void n.go(r);let c=k(r,JSON.parse(o),a,"path"===l.relative);null==e&&"/"!==t&&(c.pathname="/"===c.pathname?t:S([t,c.pathname])),(l.replace?n.replace:n.push)(c,l.state,l)}),[t,n,o,a,e]);return l}()}c.Component;var U=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(U||{}),F=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(F||{});function B(e){let t=c.useContext(A);return t||p(!1),t}function L(e){let t=function(e){let t=c.useContext(D);return t||p(!1),t}(),n=t.matches[t.matches.length-1];return n.route.id||p(!1),n.route.id}c.startTransition;function W(e){let{basename:t="/",children:n=null,location:r,navigationType:a=l.Pop,navigator:o,static:i=!1}=e;T()&&p(!1);let s=t.replace(/^\/*/,"/"),u=c.useMemo((()=>({basename:s,navigator:o,static:i})),[s,o,i]);"string"==typeof r&&(r=y(r));let{pathname:m="/",search:f="",hash:d="",state:h=null,key:b="default"}=r,g=c.useMemo((()=>{let e=v(m,s);return null==e?null:{location:{pathname:e,search:f,hash:d,state:h,key:b},navigationType:a}}),[s,m,f,d,h,b,a]);return null==g?null:c.createElement(C.Provider,{value:u},c.createElement(N.Provider,{children:n,value:g}))}new Promise((()=>{}));c.Component;function z(e){return void 0===e&&(e=""),new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce(((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map((e=>[n,e])):[[n,r]])}),[]))}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);new Map;const H=c.startTransition;s.flushSync;function G(e){let{basename:t,children:n,future:r,window:a}=e,o=c.useRef();null==o.current&&(o.current=function(e){return void 0===e&&(e={}),b((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return d("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:h(t)}),null,e)}({window:a,v5Compat:!0}));let i=o.current,[l,s]=c.useState({action:i.action,location:i.location}),{v7_startTransition:u}=r||{},m=c.useCallback((e=>{u&&H?H((()=>s(e))):s(e)}),[s,u]);return c.useLayoutEffect((()=>i.listen(m)),[i,m]),c.createElement(W,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:i})}"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;var V,Y;function $(e){let t=c.useRef(z(e)),n=c.useRef(!1),r=R(),a=c.useMemo((()=>function(e,t){let n=z(e);return t&&t.forEach(((e,r)=>{n.has(r)||t.getAll(r).forEach((e=>{n.append(r,e)}))})),n}(r.search,n.current?null:t.current)),[r.search]),o=M(),i=c.useCallback(((e,t)=>{const r=z("function"==typeof e?e(a):e);n.current=!0,o("?"+r,t)}),[o,a]);return[a,i]}(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(V||(V={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(Y||(Y={}));var K=wp.element;function q(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=rankMath.links[e]||"";if(!n)return"#";if(!t)return n;var r={utm_source:"Plugin",utm_medium:encodeURIComponent(t),utm_campaign:"WP"};return n+"?"+Object.keys(r).map((function(e){return"".concat(e,"=").concat(r[e])})).join("&")}var Z=function(){return wp.element.createElement("div",{className:"header"},wp.element.createElement("div",{className:"logo text-center"},wp.element.createElement("a",{href:q("seo-suite","SW Logo"),target:"_blank",rel:"noreferrer"},wp.element.createElement("img",{src:rankMath.logo,alt:"Rank Math SEO",width:"245"}))))},J=lodash,X=wp.compose,Q=wp.data,ee=wp.apiFetch,te=n.n(ee),ne=wp.components,re=wp.i18n,ae=window.rankMathComponents,oe=function(e){var t=e.saveData,n=e.skipStep,r=e.currentStep;return wp.element.createElement("footer",{className:"form-footer wp-core-ui rank-math-ui"},"schema-markup"!==r&&wp.element.createElement(ae.Button,{variant:"secondary",className:"button-skip",onClick:n},(0,re.__)("Skip Step","rank-math")),wp.element.createElement(ae.Button,{variant:"primary",onClick:function(){t()}},(0,re.__)("Save and Continue","rank-math")))},ie=wp.hooks;function le(){return le=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},le.apply(this,arguments)}var ce=function(e){var t=e.value,n=e.onChange,r=(0,ie.applyFilters)("rank_math_wizard_modes",[{mode:"easy",title:(0,re.__)("Easy","rank-math"),desc:(0,re.__)("For websites where you only want to change the basics and let Rank Math do most of the heavy lifting. Most settings are set to default as per industry best practices. One just has to set it and forget it.","rank-math")},{mode:"advanced",title:(0,re.__)("Advanced","rank-math"),desc:(0,re.__)("For the advanced users who want to control every SEO aspect of the website. You are offered options to change everything and have full control over the website’s SEO.","rank-math")},{mode:"custom",title:(0,re.__)("Custom Mode","rank-math"),desc:(0,re.__)("Select this if you have a custom Rank Math settings file you want to use.","rank-math"),isFree:!0}]),a=function(e){e.preventDefault(),window.open("//rankmath.com/pricing/?utm_source=Plugin&utm_medium=Setup%20Wizard%20Custom%20Mode&utm_campaign=WP")};return wp.element.createElement(React.Fragment,null,wp.element.createElement("ul",null,(0,J.map)(r,(function(e){var r=e.mode,o=e.title,i=e.desc,l=e.children,c=e.isFree,s=r===t,u="custom"===r&&c&&{onClick:a,"aria-hidden":!0};return wp.element.createElement("li",le({key:r},u),wp.element.createElement("div",{className:"metabox rank-math-radio-control components-radio-control"},wp.element.createElement("input",{type:"radio",name:"setup_mode",id:r,value:r,checked:s,onChange:function(){return n(r)},className:"components-radio-control__input"})),wp.element.createElement("label",{htmlFor:r,className:s?"is-checked":""},wp.element.createElement("div",{className:"rank-math-mode-title"},o),l?(0,K.createElement)(l,{checked:s,desc:i}):wp.element.createElement("p",null,i)))}))),wp.element.createElement("p",null,wp.element.createElement("strong",{className:"note"},(0,re.__)("Note","rank-math")),(0,re.__)("You can easily switch between modes at any point.","rank-math")))},se=function(e){var t=e.allGood,n=e.isWhitelabel;return t?wp.element.createElement("p",{className:"description checklist-ok"},n?(0,re.__)("Your server is correctly configured to use Rank Math.","rank-math"):(0,re.__)("Your server is correctly configured to use this plugin.","rank-math")):wp.element.createElement("p",{className:"description checklist-not-ok"},n?(0,re.__)("Please resolve the issues above to be able to use all features of Rank Math plugin. If you are not sure how to do it, please contact your hosting provider.","rank-math"):(0,re.__)("Please resolve the issues above to be able to use all SEO features. If you are not sure how to do it, please contact your hosting provider.","rank-math"))},ue=jQuery,me=n.n(ue);function pe(e,t,n){return me().ajax({url:rankMath.ajaxurl,type:n||"POST",dataType:"json",data:me().extend(!0,{action:"rank_math_"+e,security:rankMath.security},t)})}function fe(e){return fe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fe(e)}function de(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==fe(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==fe(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===fe(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function he(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ye(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ye(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ye(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var be=function(e){return(0,J.reduce)((0,J.entries)(e),(function(e,t){var n=he(t,2),r=n[0],a=n[1],o=(0,J.includes)(["all-in-one-schemaorg-rich-snippets/index.php","wordpress-seo/wp-seo.php","wordpress-seo-premium/wp-seo-premium.php","all-in-one-seo-pack/all_in_one_seo_pack.php"],r)?"<span class='import-info'>".concat((0,re.__)("You can import settings in the next step.","rank-math"),"</span>"):"",i='<span class="dashicons dashicons-warning"></span> '.concat(a," ").concat(o);return Object.assign(e,de({},i,wp.element.createElement(ae.Button,{size:"small",variant:"secondary",className:"wizard-deactivate-plugin",onClick:function(e){return function(e,t){var n=me()(e.target);pe("deactivate_plugins",{plugin:t}).always((function(e){1===e&&(n.parents("tr").find(".dashicons-warning").removeClass("dashicons-warning").addClass("dashicons-yes"),n.text((0,re.__)("Deactivated","rank-math")).attr("disabled","disabled"))}))}(e,r)}},(0,re.__)("Deactivate Plugin","rank-math")))),e}),{})},ge=function(e){var t=e.isWhitelabel,n=e.conflictingPlugins;return(0,J.isEmpty)(n)?wp.element.createElement("p",{className:"conflict-text noconflict"},(0,re.__)("No known conflicting plugins found.","rank-math")):wp.element.createElement(React.Fragment,null,wp.element.createElement("p",{className:"conflict-text"},t?(0,re.__)("The following active plugins on your site may cause conflict issues when used alongside Rank Math: ","rank-math"):(0,re.__)("The following active plugins on your site may cause conflict issues when used alongside this plugin: ","rank-math")),wp.element.createElement(ae.Table,{className:"wizard-conflicts",fields:be(n)}))};function ve(e){return ve="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ve(e)}function we(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _e(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ve(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ve(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ve(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ke=function(e){return wp.element.createElement("span",{className:"dashicons dashicons-".concat(e?"yes":"no")})},Se=function(e){var t=e.phpVersion,n=e.phpVersionOk,r=e.phpVersionRecommended,a=e.extensions,o=e.isWhitelabel,i=a.dom,l=a.simpleXml,c=a.image,s=a.mbString,u=a.openSsl,m=a.base64Func,p=[{feature:(0,re.__)("You are using the recommended WordPress version.","rank-math"),passed:!0,showStatus:!1},{feature:(0,re.__)("PHP DOM Extension","rank-math"),passed:i},{feature:(0,re.__)("PHP SimpleXML Extension","rank-math"),passed:l},{feature:(0,re.__)("PHP GD or Imagick Extension","rank-math"),passed:c},{feature:(0,re.__)("PHP MBstring Extension","rank-math"),passed:s},{feature:(0,re.__)("PHP OpenSSL Extension","rank-math"),passed:u,showStatus:s},{feature:(0,re.__)("Base64 encode & decode functions","rank-math"),passed:m,isBase64:!0}],f=(0,J.fromPairs)((0,J.map)(p,(function(e){var t,n=function(e){var t=e.feature,n=e.passed,r=e.showStatus,a=void 0===r||r,o=e.isBase64,i=void 0!==o&&o?(0,re.__)("available","rank-math"):(0,re.__)("installed","rank-math"),l=n?i:(0,re.__)("missing","rank-math"),c=a?l:"";return"".concat(t," ").concat(c)}(e);return[n,ke(null!==(t=e.status)&&void 0!==t?t:e.passed)]}))),d=function(e){var t=e.phpVersion,n=e.phpVersionOk,r=e.phpVersionRecommend,a=e.isWhitelabel;if(!n)return(0,re.sprintf)((0,re.__)("Your PHP Version: %s | Recommended version: 7.4 | Minimal required: 7.2","rank-math"),t);var o=(0,re.sprintf)((0,re.__)("Your PHP Version: %s","rank-math"),t),i="".concat((0,re.__)("Rank Math is compatible with your PHP version but we recommend updating to PHP 7.4 for increased speed and security. ","rank-math")," <a href='").concat(q("requirements","Setup wizard compatibility step"),"'>").concat((0,re.__)("More information","rank-math"),"</a>"),l=(0,re.__)("This plugin is compatible with your PHP version but we recommend updating to PHP 7.4 for increased speed and security.","rank-math"),c=r?"".concat((0,re.__)(" | Recommended: PHP 7.4 or later","rank-math")," <p class='description'>").concat(a?i:l,"</p>"):"";return"".concat(o," ").concat(c)}({phpVersion:t,phpVersionOk:n,phpVersionRecommended:r,isWhitelabel:o});return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?we(Object(n),!0).forEach((function(t){_e(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):we(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}(_e({},d,ke(t)),f)};function Ee(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Oe(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Oe(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Oe(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var je=function(e){var t=e.allGood,n=e.phpVersionRecommended,r=Ee((0,K.useState)(!t),2),a=r[0],o=r[1];return wp.element.createElement(React.Fragment,null,t&&wp.element.createElement(React.Fragment,null,wp.element.createElement("br",null),wp.element.createElement("h2",{className:"text-center compatibility-check"},wp.element.createElement(ne.Icon,{icon:n?"warning":"yes"}),(0,re.__)("Your website is compatible to run Rank Math SEO","rank-math"),wp.element.createElement(ae.Button,{size:"small",variant:"link",className:"rank-math-collapsible-trigger",onClick:function(){return o((function(e){return!e}))}},wp.element.createElement(ne.Icon,{icon:a?"arrow-up-alt2":"arrow-down-alt2"},wp.element.createElement("span",null,a?(0,re.__)("Less","rank-math"):(0,re.__)("More","rank-math")))))),a&&wp.element.createElement("div",{id:"rank-math-compatibility-collapsible"},wp.element.createElement(ae.Table,{fields:Se(e),th:!0}),wp.element.createElement(se,e),wp.element.createElement(ge,e)))};function Pe(){return Pe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Pe.apply(this,arguments)}function xe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Ae(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ae(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ae(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ce=function(e){var t=e.data,n=e.saveData,r=(e.skipStep,xe((0,K.useState)(t.setup_mode),2)),a=r[0],o=r[1],i=t.phpVersionOk,l=t.extensions,c=(0,J.some)(l,(function(e){return!1===e})),s=i&&!c;return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"field-metabox rank-math-setup-mode ".concat(rankMath.isPro?"":"is-free")},wp.element.createElement(ce,{value:a,onChange:o})),wp.element.createElement(je,Pe({},t,{allGood:s})),wp.element.createElement("footer",{className:"form-footer rank-math-custom wp-core-ui rank-math-ui text-center"},s&&wp.element.createElement(ae.Button,{variant:"animate",onClick:function(){t.setup_mode=a,n(t)}},(0,re.__)("Start Wizard","rank-math"),wp.element.createElement(ne.Icon,{icon:"arrow-right-alt2"}))))};function Ne(e){return Ne="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ne(e)}function De(e){return function(e){if(Array.isArray(e))return Ue(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Me(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Te(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Re(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Ne(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Ne(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ne(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ie(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||Me(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Me(e,t){if(e){if("string"==typeof e)return Ue(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ue(e,t):void 0}}function Ue(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Fe=function(e){var t=e.options,n=e.setSelectedPlugins,r=e.selectedPlugins,a=Ie((0,K.useState)(!1),2),o=a[0],i=a[1],l=t.name,c=t.plugin,s=t.metaOptions,u=t.metaDescription,m="recalculate",p=r[c],f=["yoast","seopress","aioseo","all-in-one-seo-pack-pro","yoast-premium","aio-rich-snippet","wp-schema-pro"],d=function(e){var t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Te(Object(n),!0).forEach((function(t){Re(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Te(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},r);(0,J.includes)(f,c)&&function(e){(0,J.forEach)((0,J.keys)(e),(function(t){(0,J.includes)(f,t)&&delete e[t]}))}(t),e.length>0?t[c]=e:delete t[c],n(t)};return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"plugin-title ".concat(o?"is-open":"")},wp.element.createElement(ae.CheckboxControl,{variant:"metabox",checked:Boolean(p),onChange:function(e){var t=(0,J.map)(s,(function(e){return e.id})),n=e?[].concat(De(t),[m]):[];return d(n)}}),wp.element.createElement(ne.Button,{onClick:function(){return i((function(e){return!e}))}},wp.element.createElement("h3",null,l),wp.element.createElement(ne.Icon,{icon:o?"arrow-up-alt2":"arrow-down-alt2"}))),o&&wp.element.createElement("div",{className:"inside"},wp.element.createElement(ae.CheckboxList,{toggleAll:!0,variant:"metabox",value:p,onChange:function(e){var t=p,n=[].concat(De(e),De((0,J.includes)(t,m)?[m]:[]));return d(n)},options:s}),wp.element.createElement("p",{className:"description",dangerouslySetInnerHTML:{__html:u}}),wp.element.createElement(ae.CheckboxControl,{variant:"metabox",label:(0,re.__)("Recalculate SEO Scores","rank-math"),checked:(0,J.includes)(p,m),onChange:function(e){var t=p,n=[].concat(De((0,J.filter)(t,(function(e){return e!==m}))),De(e?[m]:[]));return d(n)}})))},Be=rankMathAnalyzer;function Le(e){return Le="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Le(e)}function We(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ze(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Le(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Le(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Le(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function He(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Ge(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ge(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ge(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ve=function(e){var t=e.selectedPlugins,n=e.setImportComplete,r=He((0,K.useState)(""),2),a=r[0],o=r[1],i=He((0,K.useState)(1),2),l=i[0],c=i[1],s=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?We(Object(n),!0).forEach((function(t){ze(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):We(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},t),u=(0,J.entries)(t),m=(0,J.map)(u,(function(e){return e[0]})),p=(0,J.flatMap)((0,J.map)(u,(function(e){return e[1]}))).length,f=[],d=function(e){var t=new Date,n="["+(10>t.getHours()?"0":"")+t.getHours()+":"+(10>t.getMinutes()?"0":"")+t.getMinutes()+":"+(10>t.getSeconds()?"0":"")+t.getSeconds()+"] "+e+"\n";o((function(e){return e+n}))},h=function(e,t,n,r,a){var o={};if("complete"!==e)return new Promise((function(t){(0,J.forEach)((0,J.entries)(e),(function(e){var t=He(e,2),n=t[0],r=t[1];if(!f.includes(n)){f.push(n);var a=new Be.ResultManager,i=wp.i18n,l=new Be.Paper;l.setTitle(r.title),l.setDescription(r.description),l.setText(r.content),l.setKeyword(r.keyword),l.setKeywords(r.keywords),l.setPermalink(r.url),l.setUrl(r.url),r.thumbnail&&l.setThumbnail(r.thumbnail),l.setContentAI(r.hasContentAi);var c=function(e){var t=rankMath.assessor.researchesTests;return t=(0,J.difference)(t,["keywordNotUsed"]),e.isProduct?t=(0,J.difference)(t,["keywordInSubheadings","linksHasExternals","linksNotAllExternals","linksHasInternal","titleSentiment","titleHasNumber","contentHasTOC"]):t}(r);new Be.Analyzer({i18n:i,analysis:c}).analyzeSome(c,l).then((function(e){a.update(l.getKeyword(),e,!0);var t=a.getScore(r.keyword);r.isProduct&&(t+=r.isReviewEnabled?1:0,t+=r.hasProductSchema?1:0),o[n]=t}))}})),t()})).then((function(){fetch(rankMath.api.root+"rankmath/v1/updateSeoScore",{method:"POST",headers:{"Content-Type":"application/json","X-WP-Nonce":rankMath.restNonce},body:JSON.stringify({action:"rank_math_update_seo_score",postScores:o})}).then((function(e){if(!e.ok)throw new Error(e.statusText);return e.json()})).then((function(){d("SEO Scores updated"),y(t,n,r,a)})).catch((function(e){d(e.message)}))}));y(t,n,r,a)},y=function e(t,n,r,a,o){if(0!==n.length){r=r||1;var i=n.shift(),l="deactivate"===i?"Deactivating "+o:"Importing "+i+" from "+o,s=(0,J.floor)(100/p);"recalculate"===i&&(l="Starting SEO score recalculation"),d(l),fetch(rankMath.ajaxurl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({perform:i,pluginSlug:t,paged:r,action:"rank_math_import_plugin",security:rankMath.security})}).then((function(e){if(!e.ok)throw new Error(e.statusText);return e.json()})).then((function(l){var u=1;l&&l.page&&l.page<l.total_pages&&(u=l.page+1,n.unshift(i)),l&&l.total_pages&&(s=Math.ceil(s/l.total_pages)),c((function(e){return e+s})),"recalculate"===i&&l.total_items>0?h(l.data,o,n,r,a):("recalculate"===i&&0===l.total_items&&(l.message=(0,re.__)("No posts found without SEO score.","rank-math")),d(l.success?l.message:l.error),e(t,n,u,a,o))})).catch((function(r){d(r.message),e(t,n,null,a,o)}))}else a()},b=function e(t){var n=(0,J.keys)(s),r=n[0],a=s[r],o=(0,J.keys)(s)[0];if(delete s[o],0===n.length)return d("Import finished. Click on the button below to continue the Setup Wizard."),void t();y(o,a,null,(function(){e(t)}),r)};return(0,K.useEffect)((function(){d("Import started..."),b((function(){c(100)})),n(!0)}),[]),(0,K.useEffect)((function(){var e=document.querySelector("#import-progress-textarea");e.scrollTop=e.scrollHeight-e.clientHeight-20}),[d]),wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{id:"import-progress-bar"},wp.element.createElement("div",{id:"importProgress"},wp.element.createElement("div",{id:"importBar",style:{width:l+"%"}})),wp.element.createElement("span",{className:"left"},wp.element.createElement("strong",null,(0,re.__)("Importing: ","rank-math")),wp.element.createElement("span",{className:"plugin-from"},(0,J.map)(m,(function(e,t){return wp.element.createElement(K.Fragment,{key:t},e,t<u.length-1&&", ")})))),wp.element.createElement("span",{className:"right"},wp.element.createElement("span",{className:"number"},l),(0,re.__)("% Completed","rank-math"))),wp.element.createElement(ae.TextareaControl,{disabled:!0,rows:8,value:a,id:"import-progress-textarea"}))};function Ye(e){return Ye="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ye(e)}function $e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ke(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$e(Object(n),!0).forEach((function(t){qe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$e(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function qe(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Ye(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Ye(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ye(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ze(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Je(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Je(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Je(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Xe=function(e){var t=(0,J.map)((0,J.entries)(e.importablePlugins),(function(e){var t=Ze(e,2),n=t[0],r=t[1],a=r.name,o=r.choices,i=r.isActive;if(r.checked){var l=(0,J.map)((0,J.entries)(o),(function(e){var t=Ze(e,2);return{id:t[0],label:t[1]}})),c=function(e,t,n){var r;return r="aio-rich-snippet"===e?(0,re.sprintf)((0,re.__)("Import meta data from the %s plugin.","rank-math"),t):(0,re.sprintf)((0,re.__)("Import settings and meta data from the %s plugin.","rank-math"),t),r+=" "+(0,re.sprintf)((0,re.__)('The process may take a few minutes if you have a large number of posts or pages <a href="%1$s">Learn more about the import process here.</a>',"rank-math"),q("seo-import","SW Import Step")),n&&(r+="<br>"+(0,re.sprintf)((0,re.__)("%s plugin will be disabled automatically moving forward to avoid conflicts. <strong>It is thus recommended to import the data you need now.</strong>","rank-math"),t)),r}(n,a,i);return Ke(Ke({},r),{},{plugin:n,metaOptions:l,metaDescription:c})}}));return(0,J.filter)(t,Boolean)};function Qe(e){return function(e){if(Array.isArray(e))return nt(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||tt(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function et(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||tt(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tt(e,t){if(e){if("string"==typeof e)return nt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?nt(e,t):void 0}}function nt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var rt=function(e){var t=e.data,n=e.skipStep,r=(0,J.reduce)((0,J.entries)(t.importablePlugins),(function(e,t){var n=et(t,2),r=n[0],a=n[1];return a.checked?(e[r]=[].concat(Qe((0,J.keys)(null==a?void 0:a.choices)||[]),["recalculate"]),e):e}),{}),a=et((0,K.useState)(r),2),o=a[0],i=a[1],l=et((0,K.useState)(!1),2),c=l[0],s=l[1],u=et((0,K.useState)(!1),2),m=u[0],p=u[1];return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"import-plugin"},wp.element.createElement("div",{className:"field-th"},wp.element.createElement("h3",null,(0,re.__)("Input Data From:","rank-math"))),wp.element.createElement("div",{className:"field-td"},(0,J.map)(Xe(t),(function(e,t){return wp.element.createElement(Fe,{key:t,options:e,selectedPlugins:o,setSelectedPlugins:i})})),c&&wp.element.createElement(Ve,{selectedPlugins:o,setImportComplete:p}))),wp.element.createElement("footer",{className:"form-footer wp-core-ui rank-math-ui"},!m&&wp.element.createElement(ae.Button,{variant:"secondary",className:"button-deactivate-plugins","data-deactivate-message":(0,re.__)("Deactivating Plugins…","rank-math"),onClick:n},(0,re.__)("Skip, Don't Import Now","rank-math")),wp.element.createElement(ae.Button,{variant:"primary",className:"button-import",onClick:function(){if(!m)return!(rankMath.isConfigured&&!window.confirm(rankMath.confirm))&&((0,J.isEmpty)(o)?(window.alert((0,re.__)("Please select plugin to import data.","rank-math")),!1):void s(!0));n()},disabled:c&&!m},m?(0,re.__)("Continue","rank-math"):(0,re.__)("Start Import","rank-math"))))},at=n(3379),ot=n.n(at),it=n(7795),lt=n.n(it),ct=n(569),st=n.n(ct),ut=n(3565),mt=n.n(ut),pt=n(9216),ft=n.n(pt),dt=n(4589),ht=n.n(dt),yt=n(4653),bt={};bt.styleTagTransform=ht(),bt.setAttributes=mt(),bt.insert=st().bind(null,"head"),bt.domAPI=lt(),bt.insertStyleElement=ft();ot()(yt.Z,bt),yt.Z&&yt.Z.locals&&yt.Z.locals;var gt=function(e){var t=e.title,n=e.description,r=e.link;return wp.element.createElement("header",{className:"rank-math-tab-header"},wp.element.createElement("h2",null,t),wp.element.createElement("p",null,n,r&&wp.element.createElement(React.Fragment,null," ",wp.element.createElement("a",{href:r,target:"_blank",rel:"noreferrer"},(0,re.__)("Learn more","rank-math")),".")))};var vt=["validate","afterSave"];function wt(){return wt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},wt.apply(this,arguments)}function _t(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function kt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return St(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return St(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function St(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Et=function(e,t){return"resetting"===t?(0,re.__)("Resetting…","rank-math"):"resetted"===t?(0,re.__)("Resetted","rank-math"):e.children},Ot=function(e,t){return"updating"===t?(0,re.__)("Updating…","rank-math"):"updated"===t?(0,re.__)("Updated","rank-math"):e.children},jt=(0,X.compose)((0,Q.withSelect)((function(e){return{settings:e("rank-math-settings").getdirtySettings()}})),(0,Q.withDispatch)((function(e,t){var n=t.type,r=t.settings,a=t.footer.applyButton;return{saveSettings:function(t){t("updating"),te()({method:"POST",path:"/rankmath/v1/updateSettings",data:{type:n,settings:r[n]}}).then((function(n){return t("updated"),n.error?(function(e,t,n,r){t=t||"error",r=r||!1;var a=me()('<div class="notice notice-'+t+' is-dismissible"><p>'+e+"</p></div>").hide();n.next(".notice").remove(),n.after(a),a.slideDown(),me()(document).trigger("wp-updates-notice-added"),me()("html,body").animate({scrollTop:a.offset().top-50},"slow"),r&&setTimeout((function(){a.fadeOut((function(){a.remove()}))}),r)}(n.error,"error",me()(".wp-heading-inline")),void me()("html, body").animate({scrollTop:0},"fast")):n?(n&&!n.error&&a.afterSave&&a.afterSave(),t("updated"),void e("rank-math-settings").resetdirtySettings()):(t(""),void window.alert((0,re.__)("Something went wrong! Please try again.","rank-math")))}))},resetSettings:function(e){e("resetting"),te()({method:"POST",path:"/rankmath/v1/resetSettings",data:{type:n}}).then((function(t){if(!t)return e(""),void window.alert((0,re.__)("Something went wrong! Please try again.","rank-math"));e("resetted"),window.location.reload()}))}}})))((function(e){var t=kt((0,K.useState)(""),2),n=t[0],r=t[1],a=e.settings,o=e.resetSettings,i=e.saveSettings,l=e.footer,c=l.applyButton,s=c.validate,u=(c.afterSave,_t(c,vt));return(0,K.useEffect)((function(){(0,J.includes)(["updated","resetted"],n)&&setTimeout((function(){return r("")}),1e3)}),[n]),wp.element.createElement("footer",{className:"form-footer rank-math-ui"},wp.element.createElement(ae.Button,wt({onClick:function(){o(r)}},l.discardButton,{children:Et(l.discardButton,n)})),wp.element.createElement(ae.Button,wt({variant:"primary",onClick:function(){(!s||s())&&i(r)},disabled:(0,J.isEmpty)(a)},u,{children:Ot(u,n)})))})),Pt=n(4184),xt=n.n(Pt);function At(e){return At="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},At(e)}var Ct=["id","type","content","Component","isDisabled"];function Nt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Dt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Nt(Object(n),!0).forEach((function(t){Tt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Nt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Tt(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==At(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==At(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===At(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Rt(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}var It=(0,X.compose)((0,Q.withSelect)((function(e,t){var n=e("rank-math-settings").getAppData();return{field:t.field,settingType:t.settingType,settings:n}})),(0,Q.withDispatch)((function(e,t){var n=t.settings,r=t.settingType;return{updateSetting:function(t,a){n[r][t]=a,e("rank-math-settings").updateSettings(n)}}})))((function(e){var t,n=e.field,r=e.settingType,a=e.settings,o=n.id,i=n.type,l=n.content,c=n.Component,s=n.isDisabled,u=Rt(n,Ct),m=(null===(t=a[r])||void 0===t?void 0:t[o])||"",p=function(t){return e.updateSetting(o,t)},f=function(){var e={toggle:"checked",checkbox:"checked"}[i]||"value",t=(0,J.includes)(["component","group"],i);return Dt(Dt({},u),{},Tt(Tt({id:o},e,u.value||m),"onChange",u.onChange||!s&&p),t&&{settingType:r})},d={file:window.rankMathComponents.UploadFile,text:window.rankMathComponents.TextControl,select:window.rankMathComponents.SelectControl,toggle:window.rankMathComponents.ToggleControl,select_search:window.rankMathComponents.SelectWithSearch,multicheck:window.rankMathComponents.CheckboxList,multicheck_inline:window.rankMathComponents.CheckboxList,radio_inline:window.rankMathComponents.ToggleGroupControl,repeatable_group:window.rankMathComponents.RepeatableGroup,group:window.rankMathComponents.Group,checkbox:window.rankMathComponents.CheckboxControl}[i];return d?wp.element.createElement(d,f()):"component"===i?wp.element.createElement(c,f()):"raw"===i?(0,K.createElement)(l):null})),Mt=["relation"];function Ut(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Ft(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ft(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ft(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Bt(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function Lt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Wt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Wt(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Wt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var zt=n(5317),Ht={};Ht.styleTagTransform=ht(),Ht.setAttributes=mt(),Ht.insert=st().bind(null,"head"),Ht.domAPI=lt(),Ht.insertStyleElement=ft();ot()(zt.Z,Ht),zt.Z&&zt.Z.locals&&zt.Z.locals;function Gt(e){return Gt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Gt(e)}function Vt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Yt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Vt(Object(n),!0).forEach((function(t){$t(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Vt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function $t(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Gt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Gt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Gt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Kt=function(e){var t=e.settingType,n=e.fields,r=e.settings,a=void 0===r?null:r;return wp.element.createElement("div",{className:"field-wrap form-table wp-core-ui rank-math-ui"},wp.element.createElement("div",{id:"field-metabox-rank-math-".concat(t),className:"field-metabox field-list"},(0,J.map)(n,(function(e){var n=e.id,r=e.type,o=e.name,i=e.desc,l=e.classes,c=e.content,s=e.dep,u=function(e,t){var n=(0,Q.useSelect)((function(e){return e("rank-math-settings").getAppData()}));return(0,J.some)(e,(function(e){var r=Lt(e,1)[0];return n[t][r]}))}(e.disableDep,t);if(!s||function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=e.relation,a=Bt(e,Mt);n=(0,J.isNull)(n)?(0,Q.useSelect)((function(e){return e("rank-math-settings").getAppData()})):n;var o=function(e){var r,a=Ut(e,2),o=a[0],i=a[1],l=(0,J.isUndefined)(t)?n[o]:null===(r=n[t])||void 0===r?void 0:r[o];return(0,J.isArray)(i)?(0,J.includes)(i,l):i===l};return"and"===r?(0,J.every)((0,J.entries)(a),o):(0,J.some)((0,J.entries)(a),o)}(s,t,a)){if("raw"===r)return c;var m=xt()("field-row",l,$t($t({"field-disabled":u},"field-id-"+n,n),"field-type-"+r,r));return wp.element.createElement("div",{key:n,className:m},o&&wp.element.createElement("div",{className:"field-th"},wp.element.createElement("label",{htmlFor:n},o)),wp.element.createElement("div",{className:"field-td"},wp.element.createElement(It,{settingType:t,field:Yt(Yt({},e),{},{isDisabled:u})}),i&&wp.element.createElement("p",{className:"field-description",dangerouslySetInnerHTML:{__html:i}})))}}))))};function qt(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return t=(0,ie.applyFilters)("rank_math_sanitize_settings",t,e,n),null!==r&&(r=(0,ie.applyFilters)("rank_math_sanitize_settings_value",r,e,n)),r=null===r?t:r,(0,ie.doAction)("rank_math_settings_changed",e,t,n),{type:"RANK_MATH_SETTINGS_DATA",key:e,value:t,settingsKey:n,settingsValue:r}}function Zt(e,t){return(0,ie.doAction)("rank_math_update_app_ui",e,t),{type:"RANK_MATH_APP_UI",key:e,value:t}}function Jt(e){return Jt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Jt(e)}function Xt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Qt(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Jt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Jt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Jt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function en(e){return qt("settings",e,"settings")}function tn(e,t){var n=wp.data.select("rank-math-settings").getModules();return n[e].isActive=t,(0,J.forEach)(n,(function(t,r){if((0,J.includes)(t.dep_modules,e)){var a=!1;(0,J.forEach)(t.dep_modules,(function(e){n[e].isActive||(a=!0)})),n[r].isDisabled=a,n[r].disabled=a}})),qt("modules",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Xt(Object(n),!0).forEach((function(t){Qt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Xt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},n))}function nn(){return qt("dirtySettings",{})}function rn(){return{type:"RESET_STORE"}}function an(e){return Zt("isLoaded",e)}function on(e){return Zt("view",e)}function ln(e){return Zt("currentStep",e)}function cn(e){return cn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cn(e)}function sn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function un(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?sn(Object(n),!0).forEach((function(t){mn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):sn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function mn(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==cn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==cn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===cn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var pn={header_code:"301",status:"active",sources:[{comparison:"exact"}]},fn={roleCapabilities:(0,J.get)(rankMath,"roleCapabilities",{}),redirections:rankMath.redirections||pn,modules:(0,J.get)(rankMath,"modulesList",{}),dirtySettings:{}},dn=un(un({},fn),{},{redirections:pn});function hn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:fn,t=arguments.length>1?arguments[1]:void 0,n=un({},e.dirtySettings);return!1!==t.settingsKey&&(n=t.settingsValue),"RANK_MATH_SETTINGS_DATA"===t.type?"dirtySettings"===t.key?un(un({},e),{},{dirtySettings:t.value}):un(un({},e),{},mn(mn({},t.key,t.value),"dirtySettings",n)):"RESET_STORE"===t.type?dn:e}function yn(e){return yn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},yn(e)}function bn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function gn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?bn(Object(n),!0).forEach((function(t){vn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):bn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function vn(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==yn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==yn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===yn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var wn={currentStep:"getting-started"};function _n(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:wn,t=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===t.type?gn(gn({},e),{},vn({},t.key,t.value)):e}function kn(e){return e.appData}function Sn(e){return e.appData.dirtySettings}function En(e){return e.appData.settings}function On(e){return e.appData.modules}function jn(e){return e.appUi.isLoaded}function Pn(e){return e.appUi.view}function xn(e){return e.appUi.currentStep}(0,Q.registerStore)("rank-math-settings",{reducer:(0,Q.combineReducers)(t),selectors:r,actions:e});var An=function(e){var t=e.type,n=e.header,r=e.footer,a=e.fields,o=void 0===a?[]:a,i=e.settings,l=void 0===i?null:i;return wp.element.createElement(React.Fragment,null,n&&wp.element.createElement(gt,n),wp.element.createElement(Kt,{settingType:t,fields:o,settings:l}),r&&wp.element.createElement(jt,{type:t,footer:r}))};function Cn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Nn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Nn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Nn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Dn=function(){return wp.element.createElement("a",{target:"_blank",rel:"noreferrer",href:q("how-to-setup-your-site","SW Your Site Setup KB")},(0,re.__)("Click here to learn how to setup Rank Math properly","rank-math"))},Tn=function(){var e=Cn((0,K.useState)(""),2),t=e[0],n=e[1];return wp.element.createElement("form",{onSubmit:function(e){e.preventDefault(),window.open("".concat(q("kb-search","SW Your Site Search"),"&q=").concat(t),"_blank","noreferrer")},className:"search-form wp-core-ui rank-math-ui"},wp.element.createElement("label",{htmlFor:"rank-math-search-input"},(0,re.__)("Search the Knowledge Base for answers to your questions:","rank-math")),wp.element.createElement(ae.TextControl,{autoCorrect:"off",autoComplete:"off",autoCapitalize:"none",variant:"regular-text",spellCheck:!1,value:t,onChange:n,placeholder:(0,re.__)("Type here to search…","rank-math")}),wp.element.createElement(ae.Button,{type:"submit",variant:"primary",disabled:!t},(0,re.__)("Search","rank-math")))},Rn=function(){var e=function(e,t){return wp.element.createElement(React.Fragment,null,wp.element.createElement("span",{className:"rm-icon rm-icon-".concat(e)}),t)},t=[{name:"help-panel-video",title:e("video",(0,re.__)("Setup Tutorial","rank-math")),view:Dn},{name:"help-panel-knowledge",title:e("post",(0,re.__)("Knowledge Base","rank-math")),view:Tn}];return wp.element.createElement(ne.TabPanel,{tabs:t},(function(e){var t=e.name,n=e.view;return wp.element.createElement("div",{className:"rank-math-tabs-content rank-math-custom"},wp.element.createElement("div",{id:t,className:"rank-math-tab"},wp.element.createElement(n,null)))}))};function In(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Mn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Mn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Mn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Un=function(e){var t=e.data,n=e.updateData,r=In((0,K.useState)(!1),2),a=r[0],o=r[1],i=(0,J.map)(function(e){return[{id:"site_type",type:"select",name:(0,re.sprintf)((0,re.__)("%1$s is a…","rank-math"),rankMath.blogName),options:{blog:(0,re.__)("Personal Blog","rank-math"),news:(0,re.__)("Community Blog/News Site","rank-math"),portfolio:(0,re.__)("Personal Portfolio","rank-math"),business:(0,re.__)("Small Business Site","rank-math"),webshop:(0,re.__)("Webshop","rank-math"),otherpersonal:(0,re.__)("Other Personal Website","rank-math"),otherbusiness:(0,re.__)("Other Business Website","rank-math")}},{id:"business_type",type:"select_search",name:(0,re.__)("Business Type","rank-math"),desc:(0,re.__)('Select the type that best describes your business. If you can\'t find one that applies exactly, use the generic "Organization" or "Local Business" types.',"rank-math"),options:e.businessTypesChoices,dep:{site_type:["news","buisness","webshop","otherbusiness"]}},{id:"website_name",type:"text",name:(0,re.__)("Website Name","rank-math"),desc:(0,re.__)("Enter the name of your site to appear in search results.","rank-math")},{id:"website_alternate_name",type:"text",name:(0,re.__)("Website Alternate Name","rank-math"),desc:(0,re.__)("An alternate version of your site name (for example, an acronym or shorter name).","rank-math")},{id:"company_name",type:"text",name:(0,re.__)("Person/Organization Name","rank-math"),desc:(0,re.__)("Your name or company name intended to feature in Google's Knowledge Panel.","rank-math")},{id:"company_logo",type:"file",name:(0,re.__)("Logo for Google","rank-math"),description:(0,re.__)("<strong>Min Size: 112Χ112px</strong>.<br />A squared image is preferred by the search engines.","rank-math")},{id:"open_graph_image",type:"file",name:(0,re.__)("Default Social Share Image","rank-math"),description:(0,re.__)("When a featured image or an OpenGraph Image is not set for individual posts/pages/CPTs, this image will be used as a fallback thumbnail when your post is shared on Facebook. <strong>The recommended image size is 1200 x 630 pixels.</strong>","rank-math")}]}(t),(function(e){return e.value=t[e.id],"file"===e.type?e.onChange=function(t){return n(e.id,t.url),n(e.id+"_id",t.id),!0}:e.onChange=function(t){return n(e.id,t)},e}));return wp.element.createElement(React.Fragment,null,!t.isWhitelabel&&wp.element.createElement("div",{className:"rank-math-wizard-tutorial"},wp.element.createElement("header",null,(0,re.__)("If you are new to Rank Math,","rank-math")," ",wp.element.createElement(ae.Button,{variant:"link",onClick:function(){return o((function(e){return!e}))}},(0,re.__)("click here","rank-math"))," ",(0,re.__)("to learn more.","rank-math")),a&&wp.element.createElement(Rn,null)),wp.element.createElement(An,{fields:i,settings:t}))},Fn=function(e){var t=e.authUrl,n=e.isAuthorized,r=[(0,re.__)("Verify site ownership on Google Search Console in a single click","rank-math"),(0,re.__)("Track page and keyword rankings with the Advanced Analytics module","rank-math"),(0,re.__)("Easily set up Google Analytics without using another 3rd party plugin","rank-math"),(0,re.__)("Automatically submit sitemaps to the Google Search Console","rank-math"),wp.element.createElement("a",{key:"help-analytics",target:"_blank",rel:"noreferrer",href:q("help-analytics","SW Analytics Step Benefits")},(0,re.__)("Learn more about the benefits of connecting your account here.","rank-math"))];return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"connect-wrap"},n?wp.element.createElement(ae.Button,{variant:"primary"},(0,re.__)("Disconnect Account","rank-math")):wp.element.createElement(ae.Button,{href:t,variant:"animate",className:"rank-math-authorize-account"},(0,re.__)("Connect Google Services","rank-math"))),wp.element.createElement("div",{id:"rank-math-pro-cta",className:"analytics"},wp.element.createElement("div",{className:"rank-math-cta-box width-100 no-shadow no-padding no-border"},wp.element.createElement("h3",null,(0,re.__)("Benefits of Connecting Google Account","rank-math")),wp.element.createElement("ul",null,(0,J.map)(r,(function(e,t){return wp.element.createElement("li",{key:t},e)}))))),wp.element.createElement(ae.PrivacyBox,null))},Bn=function(e){var t=e.isSiteUrlValid,n=e.activateUrl,r=[(0,re.__)("Verify site ownership on Google Search Console in a single click","rank-math"),(0,re.__)("Track page and keyword rankings with the Advanced Analytics module","rank-math"),(0,re.__)("Easily set up Google Analytics without using another 3rd party plugin","rank-math"),(0,re.__)("Automatically submit sitemaps to the Google Search Console","rank-math"),(0,re.__)("Free keyword suggestions when entering a focus keyword","rank-math"),(0,re.__)("Use our revolutionary SEO Analyzer to scan your website for SEO errors","rank-math"),wp.element.createElement("a",{key:"learn-more",target:"_blank",rel:"noreferrer",href:q("free-account-benefits","SW Analytics Step")},(0,re.__)("Learn more about the benefits of connecting your account here.","rank-math"))];return wp.element.createElement(React.Fragment,null,wp.element.createElement(ae.InvalidSiteUrlNotice,{isSiteUrlValid:t}),wp.element.createElement("div",{className:"wp-core-ui rank-math-ui connect-wrap"},wp.element.createElement(ae.Button,{href:n,variant:"animate",disabled:!t},(0,re.__)("Connect Your Rank Math Account","rank-math"))),wp.element.createElement("div",{id:"rank-math-pro-cta",className:"analytics"},wp.element.createElement("div",{className:"rank-math-cta-box width-100 no-shadow no-padding no-border"},wp.element.createElement("h3",null,(0,re.__)("Benefits of Connecting Rank Math Account","rank-math")),wp.element.createElement("ul",null,(0,J.map)(r,(function(e,t){return wp.element.createElement("li",{key:t},e)}))))),wp.element.createElement(ae.PrivacyBox,null))},Ln=function(e){var t,n=e.data,r=e.updateData,a=n.searchConsole;return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"field-row field-type-select"},wp.element.createElement("div",{className:"field-row-col"},wp.element.createElement(ae.SelectWithSearch,{value:a.profile,width:268,options:null!==(t=a.sites)&&void 0!==t?t:{},onChange:function(e){a.profile=e,r("searchConsole",a)},label:(0,re.__)("Site","rank-math"),className:"site-console-profile notrack"})),(0,ie.applyFilters)("rank_math_analytics_options_console","",n,r)),wp.element.createElement("div",{className:"field-row field-type-toggle"},wp.element.createElement("div",{className:"field-td"},wp.element.createElement(ae.ToggleControl,{checked:a.enable_index_status,disabled:!a.profile,onChange:function(e){a.enable_index_status=e,r("searchConsole",a)},className:"regular-text notrack",label:(0,re.__)("Enable the Index Status tab","rank-math")}),wp.element.createElement("div",{className:"field-description"},(0,re.__)("Enable this option to show the Index Status tab in the Analytics module. ","rank-math"),wp.element.createElement("a",{target:"_blank",rel:"noreferrer",href:q("url-inspection-api","SW Analytics Index Status Option")},(0,re.__)("Learn more.","rank-math"))))))};function Wn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return zn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return zn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Hn=function(e,t){pe("get_ga4_data_streams",{propertyID:e},"post").done((function(e){e.error?console.error(e.error):t(e.streams)}))},Gn=function(e){var t,n=e.data,r=e.updateData,a=n.analyticsData,o=n.allServices,i=a.account_id,l=null!==(t=a.property_id)&&void 0!==t?t:"",c=rankMath.isPro,s={},u=new Map;u.set("",(0,re.__)("Select Property","rank-math"));var m={},p=Wn((0,K.useState)({}),2),f=p[0],d=p[1];return(0,J.isEmpty)(o.accounts)||(0,J.forEach)(o.accounts,(function(e,t){s[t]=e.name+" ("+t+")",i===t&&(u.set("create-ga4-property",(0,re.__)("Create new GA4 Property","rank-math")),(0,J.forEach)(e.properties,(function(e,t){u.set(t,e.name)}))),i||(a.account_id=t,r("analyticsData",a))})),(0,K.useEffect)((function(){if(!(0,J.isEmpty)(f)){var e=f[0];a.view_id=e.id,a.stream_name=e.name,a.measurement_id=e.measurementId,r("analyticsData",a)}}),[f]),(0,J.isEmpty)(f)||(0,J.forEach)(f,(function(e){m[e.id]=e.name})),wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"field-row field-type-select"},wp.element.createElement("div",{className:"field-row-col"},wp.element.createElement(ae.SelectWithSearch,{value:i,options:s,onChange:function(e){a.account_id=e,r("analyticsData",a)},label:(0,re.__)("Account","rank-math"),className:"site-analytics-account notrack"})),wp.element.createElement("div",{className:"field-row-col"},wp.element.createElement(ae.SelectControl,{value:l,options:u,onChange:function(e){"create-ga4-property"!==e?(a.property_id=e,r("analyticsData",a),e&&Hn(e,d)):confirm((0,re.__)("Are you sure, you want to create a new GA4 Property?","rank-math"))&&pe("create_ga4_property",{accountID:i},"post").done((function(e){e.error?alert(e.error):(u.set(e.id,e.name),a.property_id=e.id,r("analyticsData",a),o.accounts[i].properties[e.id]={name:e.name,id:e.id,account_id:i,type:"GA4"},r("allServices",o),Hn(e.id,d))}))},label:(0,re.__)("Property","rank-math"),className:"site-analytics-property notrack"})),wp.element.createElement("div",{className:"field-row-col"},wp.element.createElement(ae.SelectControl,{value:a.view_id,options:m,onChange:function(e){a.view_id=e;var t=(0,J.find)(f,{id:e});a.stream_name=t.name,a.measurement_id=t.measurementId,r("analyticsData",a)},label:(0,re.__)("Data Stream","rank-math"),className:"site-analytics-view notrack"})),(0,ie.applyFilters)("rank_math_analytics_options_analytics","",n,r)),wp.element.createElement("div",{className:"field-row field-type-toggle"},wp.element.createElement("div",{className:"field-td"},wp.element.createElement(ae.ToggleControl,{checked:a.install_code,onChange:function(e){a.install_code=e,r("analyticsData",a)},className:"regular-text notrack",label:(0,re.__)("Install analytics code","rank-math")}),wp.element.createElement("div",{className:"field-description"},(0,re.__)("Enable this option only if you are not using any other plugin/theme to install Google Analytics code.","rank-math")))),a.install_code&&wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{"data-url":c?void 0:q("free-vs-pro","Anonymize IP"),className:"field-row field-type-toggle ".concat(c?"":"field-redirector-element")},wp.element.createElement("div",{className:"field-td"},wp.element.createElement(ae.ToggleControl,{disabled:!c,checked:a.anonymize_ip,onChange:function(e){a.anonymize_ip=e,r("analyticsData",a)},className:"regular-text notrack",label:wp.element.createElement(React.Fragment,null,(0,re.__)("Anonymize IP addresses","rank-math"),!c&&wp.element.createElement(ae.ProBadge,{href:q("pro","Anonymize IP")}))}))),wp.element.createElement("div",{"data-url":c?void 0:q("pro","Localjs IP"),className:"field-row field-type-toggle ".concat(c?"":"field-redirector-element")},wp.element.createElement("div",{className:"field-td"},wp.element.createElement(ae.ToggleControl,{disabled:!c,checked:a.local_ga_js,onChange:function(e){a.local_ga_js=e,r("analyticsData",a)},className:"regular-text notrack",label:wp.element.createElement(React.Fragment,null,(0,re.__)("Self-Hosted Analytics JS File","rank-math"),!c&&wp.element.createElement(ae.ProBadge,{href:q("pro","Localjs IP")}))}))),wp.element.createElement("div",{className:"field-row field-type-toggle"},wp.element.createElement("div",{className:"field-td"},wp.element.createElement(ae.ToggleControl,{checked:a.exclude_loggedin,onChange:function(e){a.exclude_loggedin=e,r("analyticsData",a)},className:"regular-text notrack",label:(0,re.__)("Exclude Logged-in users","rank-math")})))))},Vn=function(e){var t=e.data,n=e.updateData,r=rankMath.isPro;return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"field-row field-type-select"},wp.element.createElement("div",{className:"field-row-col"},(0,ie.applyFilters)("rank_math_analytics_adsense",wp.element.createElement(ae.SelectControl,{value:"",options:{"":(0,re.__)("Select Account","rank-math")},label:(0,re.__)("Account","rank-math"),className:"site-adsense-account notrack",disabled:!0}),t,n))),!r&&wp.element.createElement("div",{id:"rank-math-pro-cta",className:"no-margin"},wp.element.createElement("div",{className:"rank-math-cta-text"},wp.element.createElement(ae.ProBadge,{href:q("pro","AdSense Toggle")}),(0,re.__)("Google AdSense support is only available in Rank Math Pro's Advanced Analytics module.","rank-math"))))},Yn=document.createElement("div");function $n(e){return $n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$n(e)}function Kn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function qn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Kn(Object(n),!0).forEach((function(t){Zn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Kn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Zn(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==$n(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==$n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===$n(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Jn=function(e){var t=e.data,n=e.setTestConnection;return wp.element.createElement("div",{className:"connect-actions"},(0,J.map)(function(e){var t,n=e.reconnectGoogleUrl,r=e.isConsoleConnected,a=e.isAdsenseConnected,o=e.isAnalyticsConnected,i=[{id:"reconnect",link:(t=n,t&&"string"==typeof t&&(t=t.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,"").replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gim,""),Yn.innerHTML=t,t=Yn.textContent,Yn.textContent=""),t),classes:"rank-math-reconnect-google",text:(0,re.__)("Reconnect","rank-math")},{id:"disconnect",link:"#",classes:"rank-math-disconnect-google",text:(0,re.__)("Disconnect","rank-math")}];return"advanced"===e.setup_mode&&(r||a||o)&&i.push({id:"test_connections",link:"#",classes:"rank-math-test-connection-google",text:(0,re.__)("Test Connections","rank-math")}),(0,ie.applyFilters)("rank_math/analytics/connect_actions",i)}(t),(function(e){var r=e.id,a=e.link,o=e.text,i=e.classes;return wp.element.createElement("a",{key:o,href:a,className:"button button-link ".concat(i),onClick:function(e){return"disconnect"===r?(e.preventDefault(),confirm((0,re.__)("Are you sure you want to disconnect Google services from your site?","rank-math"))&&pe("disconnect_google").done((function(){window.location.reload()})),!1):"test_connections"===r?(e.preventDefault(),function(e,t){var n=(0,ie.applyFilters)("rank_math_test_connections",(0,J.compact)([e.isConsoleConnected&&{id:"search-console",action:"check_console_request"},e.isAnalyticsConnected&&{id:"analytics",action:"check_analytics_request"}]));(0,J.forEach)(n,(function(e){var n={};n[e.id]="loading",t(qn({},n)),pe(e.action,{},"post").done((function(r){r.success?t({}):(n[e.id]="failed",t(qn({},n)))}))}))}(t,n),!1):void 0}},o)})))},Xn=function(e){var t=e.reconnectUrl;return wp.element.createElement("p",{className:"warning"},wp.element.createElement("strong",{className:"warning"},(0,re.__)("Warning: ","rank-math")),wp.element.createElement("span",{dangerouslySetInnerHTML:{__html:(0,re.sprintf)((0,re.__)('You have not given the permission to fetch this data. Please <a href="%s">reconnect</a> with all required permissions.',"rank-math"),t)}}))};function Qn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return er(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return er(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function er(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var tr=function(e){var t=e.data,n=e.updateData,r=Qn((0,K.useState)({}),2),a=r[0],o=r[1];t.isConsoleConnected=!(0,J.isEmpty)(t.searchConsole.profile),t.isAnalyticsConnected=!(0,J.isEmpty)(t.analyticsData.view_id),t.isAdsenseConnected=!(0,J.isEmpty)(t.analyticsData.adsense_id);var i=t.hasConsolePermission,l=t.hasAnalyticsPermission,c=t.hasAdsensePermission,s=t.isConsoleConnected,u=t.isAnalyticsConnected,m=t.isAdsenseConnected,p=[{id:"search-console",connected:s,disabled:!i,title:(0,re.__)("Search Console","rank-math"),view:Ln},{id:"analytics",connected:u,disabled:!l,title:(0,re.__)("Analytics","rank-math"),view:Gn},{id:"adsence",connected:m,disabled:!c,title:(0,re.__)("AdSense","rank-math"),view:Vn}];return wp.element.createElement(React.Fragment,null,wp.element.createElement(Jn,{data:t,setTestConnection:o}),(0,J.map)(p,(function(e){var r=e.id,o=e.connected,i=e.disabled,l=e.title,c=e.view,s=!(0,J.isEmpty)(a[r])&&"loading"===a[r],u=!(0,J.isEmpty)(a[r])&&"failed"===a[r],m=xt()("rank-math-box no-padding rank-math-accordion rank-math-connect-".concat(r),{connected:o,disconnected:!o,disabled:i}),p=xt()("rank-math-connection-status",{"rank-math-connection-status-success":o&&!u,"rank-math-connection-status-error":!o||u}),f=o?(0,re.__)("Connected","rank-math"):(0,re.__)("Not Connected","rank-math");return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:m,tabIndex:0},wp.element.createElement("header",null,wp.element.createElement("h3",null,wp.element.createElement("span",{className:"rank-math-connection-status-wrap"},s?wp.element.createElement("svg",{className:"rank-math-spinner",viewBox:"0 0 100 100",width:"16",height:"16",xmlns:"http://www.w3.org/2000/svg",role:"presentation",focusable:"false"},wp.element.createElement("circle",{cx:"50",cy:"50",r:"50",vectorEffect:"non-scaling-stroke"}),wp.element.createElement("path",{d:"m 50 0 a 50 50 0 0 1 50 50",vectorEffect:"non-scaling-stroke"})):wp.element.createElement("span",{className:p,title:u?(0,re.__)("Some permissions are missing, please reconnect","rank-math"):f})),l)),wp.element.createElement("div",{className:"rank-math-accordion-content rank-math-".concat(r,"-content")},i&&wp.element.createElement(Xn,{reconnectUrl:t.reconnectGoogleUrl}),wp.element.createElement(c,{data:t,updateData:n}))))})))},nr=function(e){var t=e.data,n=e.updateData;if(t.showEmailReports)return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"field-row email-reports-header text-center"},wp.element.createElement("h1",null,(0,re.__)("Email Reports","rank-math")),wp.element.createElement("div",null,(0,re.__)("Receive Analytics reports periodically in email.","rank-math")," ",wp.element.createElement("a",{target:"_blank",rel:"noreferrer",href:q("seo-email-reporting","SW Analytics Step")},(0,re.__)("Learn more about Email Reports.","rank-math")))),wp.element.createElement("div",{className:"field-row field-type-toggle field-id-console-email-reports"},wp.element.createElement("div",{className:"field-th"},wp.element.createElement("label",{htmlFor:"console_email_reports"},(0,re.__)("Email Reports","rank-math"))),wp.element.createElement("div",{className:"field-td"},wp.element.createElement(ae.ToggleControl,{id:"console_email_reports",checked:t.console_email_reports,onChange:function(e){n("console_email_reports",e)}}))),(0,ie.applyFilters)("rank_math_analytics_options_email_report","",t,n))},rr=function(e){var t=e.data,n=e.updateData,r=t.isSiteConnected,a=t.isAuthorized,o=t.searchConsole,i=t.allServices,l=!(0,J.isEmpty)(o.profile);return r?a?((0,K.useEffect)((function(){l||pe("google_check_all_services").done((function(e){o.sites=e.sites,e.inSearchConsole?e.isVerified||pe("verify_site_console"):pe("add_site_console").done((function(e){o.sites=e.sites})),o.profile=e.sites[e.homeUrl],n("searchConsole",o),(0,J.isEmpty)(e.accounts)||(i.accounts=e.accounts,n("allServices",i))}))}),[]),wp.element.createElement("div",{className:"field-wrap form-table wp-core-ui rank-math-ui"},wp.element.createElement("div",{id:"field-metabox-rank-math-wizard",className:"field-metabox field-list"},wp.element.createElement(tr,{data:t,updateData:n}),wp.element.createElement(ae.PrivacyBox,{className:"width-100"}),wp.element.createElement(nr,{data:t,updateData:n})))):wp.element.createElement(Fn,t):wp.element.createElement(Bn,t)};function ar(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return or(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return or(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function or(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var ir=function(e){var t=e.data,n=e.updateData,r=(0,J.map)(function(e){var t=(0,J.map)((0,J.entries)(e.postTypes),(function(e){var t=ar(e,2);return{id:t[0],label:t[1]}})),n=(0,J.map)((0,J.entries)(e.taxonomies),(function(e){var t=ar(e,2);return{id:t[0],label:t[1]}}));return(0,ie.applyFilters)("rank_math_setup_wizard_sitemap_fields",[{id:"sitemap",type:"toggle",name:(0,re.__)("Sitemaps","rank-math"),desc:(0,re.__)("XML Sitemaps help search engines index your website&#039;s content more effectively.","rank-math")},{id:"include_images",type:"toggle",name:(0,re.__)("Include Images","rank-math"),desc:(0,re.__)("Include reference to images from the post content in sitemaps. This helps search engines index your images better.","rank-math"),classes:"features-child",dep:{sitemap:!0}},{id:"sitemap_post_types",type:"multicheck",name:(0,re.__)("Public Post Types","rank-math"),desc:(0,re.__)("Select post types to enable SEO options for them and include them in the sitemap.","rank-math"),options:t,classes:"features-child field-multicheck-inline multicheck-checked",dep:{sitemap:!0},toggleAll:!0},{id:"sitemap_taxonomies",type:"multicheck",name:(0,re.__)("Public Taxonomies","rank-math"),desc:(0,re.__)("Select taxonomies to enable SEO options for them and include them in the sitemap.","rank-math"),options:n,classes:"features-child field-multicheck-inline multicheck-checked",dep:{sitemap:!0},toggleAll:!0}],t)}(t),(function(e){return e.value=t[e.id],e.onChange=function(t){return n(e.id,t)},e}));return wp.element.createElement(An,{fields:r,settings:t})},lr=function(e){var t=e.data,n=e.updateData,r=(0,J.map)([{id:"noindex_empty_taxonomies",type:"toggle",name:(0,re.__)("Noindex Empty Category and Tag Archives","rank-math"),desc:(0,re.__)("Setting empty archives to <code>noindex</code> is useful for avoiding indexation of thin content pages and dilution of page rank. As soon as a post is added, the page is updated to <code>index</code>.","rank-math"),classes:"rank-math-advanced-option"},{id:"nofollow_external_links",type:"toggle",name:(0,re.__)("Nofollow External Links","rank-math"),desc:(0,re.__)('Automatically add <code>rel="nofollow"</code> attribute for external links appearing in your posts, pages, and other post types. The attribute is dynamically applied when the content is displayed, and the stored content is not changed.',"rank-math"),classes:"rank-math-advanced-option"},{id:"new_window_external_links",type:"toggle",name:(0,re.__)("Open External Links in New Tab/Window","rank-math"),desc:(0,re.__)('Automatically add a <code>target="_blank"</code> attribute to external links appearing in your posts, pages, and other post types. The attributes are applied when the content is displayed, which does not change the stored content.',"rank-math")}],(function(e){return e.value=t[e.id],e.onChange=function(t){return n(e.id,t)},e}));return wp.element.createElement(An,{fields:r,settings:t})},cr=wp.url;function sr(e){return sr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sr(e)}function ur(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function mr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ur(Object(n),!0).forEach((function(t){pr(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ur(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function pr(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==sr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==sr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===sr(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var fr=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e=e?"rank-math-"+e:"rank-math",t=mr(mr({},t),{},{page:e}),(0,cr.addQueryArgs)(rankMath.adminurl,t)},dr=[rankMath.isPro?{icon:"video-alt3",href:q("yt-link","SW Ready Step Upgrade"),text:(0,re.__)("Subscribe to Our YouTube Channel","rank-math")}:{icon:"star-filled pro",href:q("pro","SW Ready Step Upgrade"),text:wp.element.createElement("strong",{className:"pro-label"},(0,re.__)("Know more about the PRO version","rank-math"))},{icon:"facebook",href:q("fb-group","SW Ready Step Upgrade"),text:(0,re.__)("Join FREE Facebook Group","rank-math")},{icon:"welcome-learn-more",href:q("kb-seo-suite","SW Ready Step KB"),text:(0,re.__)("Rank Math Knowledge Base","rank-math")},{icon:"sos",href:q("support","SW Ready Step Support"),text:(0,re.__)("Get 24x7 Support","rank-math")}],hr=function(e){var t=e.data,n=e.skipStep,r=t.scoreImg,a=t.dashboardUrl,o=t.isWhitelabel,i="advanced"===t.setup_mode;return o?wp.element.createElement(React.Fragment,null,wp.element.createElement("p",null,(0,re.__)("Your site is now optimized","rank-math")),wp.element.createElement("footer",{className:"form-footer wp-core-ui rank-math-ui"},wp.element.createElement(ae.Button,{href:fr("options-general"),variant:"primary"},(0,re.__)("Proceed to Settings","rank-math")))):wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"wizard-next-steps wp-clearfix"},wp.element.createElement("div",{className:"score-100"},wp.element.createElement("a",{target:"_blank",rel:"noreferrer",href:q("score-100","SW Ready Score Image")},wp.element.createElement("img",{src:r,alt:(0,re.__)("Score 100","rank-math")}))),wp.element.createElement("div",{className:"learn-more"},wp.element.createElement("h2",null,(0,re.__)("Learn more","rank-math")),wp.element.createElement("ul",null,(0,J.map)(dr,(function(e){var t=e.icon,n=e.href,r=e.text;return wp.element.createElement("li",{key:r},wp.element.createElement("span",{className:"dashicons dashicons-".concat(t)}),wp.element.createElement("a",{href:n,target:"_blank",rel:"noreferrer"},r))}))))),wp.element.createElement("footer",{className:"form-footer wp-core-ui rank-math-ui"},wp.element.createElement(ae.Button,{variant:i?"secondary":"primary",className:i?"rank-math-return-dashboard":"rank-math-advanced-option",href:a},(0,re.__)("Return to dashboard","rank-math")),wp.element.createElement(ae.Button,{variant:"secondary",href:fr("",{view:"help"})},(0,re.__)("Proceed to Help Page","rank-math","rank-math")),i&&wp.element.createElement(ae.Button,{variant:"primary",className:"rank-math-advanced-option",onClick:n},(0,re.__)("Setup Advanced Options","rank-math"))))},yr=function(e){var t=e.data,n=e.updateData,r=e.saveData,a=e.skipStep,o=t.isWhitelabel;return wp.element.createElement(React.Fragment,null,wp.element.createElement("header",null,wp.element.createElement("h1",null,wp.element.createElement("i",{className:"dashicons dashicons-yes"})," ",(0,re.__)("Your site is ready! ","rank-math"),wp.element.createElement(ae.SocialShare,{isWhitelabel:o}))),wp.element.createElement("div",{className:"rank-math-additional-options"},wp.element.createElement("div",{className:"rank-math-auto-update-wrapper"},wp.element.createElement("h3",null,(0,re.__)("Enable auto update of the plugin","rank-math")),wp.element.createElement(ae.ToggleControl,{checked:t.enable_auto_update,onChange:function(e){n("enable_auto_update",e),r()}}))),wp.element.createElement("br",{className:"clear"}),wp.element.createElement(hr,{data:t,skipStep:a}))},br=function(e){var t=e.data,n=e.updateData,r=(0,J.map)(function(e){var t=e.roles,n=e.capabilities,r=(0,J.map)((0,J.keys)(n),(function(e){return{id:e,label:n[e]}})),a=[{id:"role_manager",type:"toggle",name:(0,re.__)("Role Manager","rank-math"),desc:(0,re.__)("The Role Manager allows you to use WordPress roles to control which of your site users can have edit or view access to Rank Math's settings.","rank-math")}];return(0,J.forEach)((0,J.keys)(t),(function(o){var i,l=(null===(i=e[o])||void 0===i?void 0:i.length)===(0,J.keys)(n).length;a.push({options:r,id:o,name:t[o],type:"multicheck_inline",toggleAll:!0,dep:{role_manager:!0},classes:"field-big-labels ".concat(l?"multicheck-checked":"")})})),a}(t),(function(e){return e.value=t[e.id],e.onChange=function(t){return n(e.id,t)},e}));return wp.element.createElement(An,{fields:r,settings:t})},gr=function(e){var t=e.data,n=e.updateData,r=(0,J.map)([{id:"404_monitor_title",type:"raw",content:wp.element.createElement("div",{key:"monitor-header",className:"field-row monitor-header text-center"},wp.element.createElement("h1",null,(0,re.__)("404 Monitor","rank-math")),wp.element.createElement("div",{className:"monitor-desc text-center"},(0,re.__)("Set default values for the 404 error monitor here.","rank-math")))},{id:"404-monitor",type:"toggle",name:(0,re.__)("404 Monitor","rank-math"),desc:(0,re.__)("The 404 monitor will let you see if visitors or search engines bump into any <code>404 Not Found</code> error while browsing your site.","rank-math")},{id:"redirection_title",type:"raw",content:wp.element.createElement("div",{key:"redirections-header",className:"field-row redirections-header text-center",style:{borderTop:0}},wp.element.createElement("br",null),wp.element.createElement("h1",null,(0,re.__)("Redirections","rank-math")),wp.element.createElement("div",{className:"redirections-desc text-center"},(0,re.__)("Set default values for the redirection module from here. ","rank-math"),wp.element.createElement("a",{target:"_blank",rel:"noreferrer",href:q("redirections","SW Redirection Step")},(0,re.__)("Learn more about Redirections.","rank-math"))))},{id:"redirections",type:"toggle",name:(0,re.__)("Redirections","rank-math"),desc:(0,re.__)("Set up temporary or permanent redirections. Combined with the 404 monitor, you can easily redirect faulty URLs on your site, or add custom redirections.","rank-math")}],(function(e){return e.value=t[e.id],e.onChange=function(t){return n(e.id,t)},e}));return wp.element.createElement(An,{fields:r,settings:t})};function vr(e){return vr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vr(e)}function wr(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==vr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==vr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===vr(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var _r=function(e){var t=[{id:"rich_snippet",type:"toggle",name:(0,re.__)("Schema Type","rank-math"),desc:(0,re.__)("Use automatic structured data to mark up content, to help Google better understand your content's context for display in Search. You can set different defaults for your posts here.","rank-math")}];return(0,J.forEach)((0,J.values)(e.accessiblePostTypes),(function(n){if("attachment"!==n){t.push(function(e,t){var n="pt_".concat(e,"_default_rich_snippet"),r=(0,re.sprintf)((0,re.__)("Schema Type for %s","rank-math"),(0,J.capitalize)(e)+"s");return"product"===e?{id:n,type:"radio_inline",name:r,desc:(0,re.__)("Default rich snippet selected when creating a new product.","rank-math"),options:{off:(0,re.__)("None","rank-math"),product:(0,re.__)("Product","rank-math")}}:{id:n,type:2===(0,J.values)(t).length?"radio_inline":"select_search",name:r,desc:(0,re.__)("Default rich snippet selected when creating a new post of this type.","rank-math"),options:t,dep:{rich_snippet:!0}}}(n,e.schemaTypes));var r=wr({relation:"and",rich_snippet:!0},"pt_".concat(n,"_default_rich_snippet"),"article"),a="person"===e.knowledgegraph_type?'<div class="notice notice-warning inline rank-math-notice" style="margin-left:0;color:#242628;"><p>'.concat((0,re.sprintf)((0,re.__)('Google does not allow Person as the Publisher for articles. Organization will be used instead. You can read more about this <a href="%s" target="_blank">here</a>.',"rank-math"),q("google-article-schema")),"</p></div>"):void 0;t.push({id:"pt_".concat(n,"_default_article_type"),type:"radio_inline",name:(0,re.__)("Article Type","rank-math"),options:{Article:(0,re.__)("Article","rank-math"),BlogPosting:(0,re.__)("Blog Post","rank-math"),NewsArticle:(0,re.__)("News Article","rank-math")},dep:r,desc:a})}})),t},kr=function(e){var t=e.data,n=e.updateData,r=(0,J.map)(_r(t),(function(e){return e.value=t[e.id],e.onChange=function(t){return n(e.id,t)},e}));return wp.element.createElement(An,{fields:r,settings:t})};function Sr(e){return Sr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Sr(e)}function Er(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Or(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Er(Object(n),!0).forEach((function(t){jr(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Er(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function jr(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Sr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Sr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Sr(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Pr=function(e,t){var n=null==t?void 0:t.isConfigured,r=(0,J.isUndefined)(t)||"advanced"===(null==t?void 0:t.setup_mode),a=!(0,J.isUndefined)(t)&&(null==t?void 0:t.addImport),o=[{name:"compatibility",slug:"requirements",title:(0,re.__)("Getting Started","rank-math"),view:Ce},{name:"import",title:(0,re.__)("Import","rank-math"),heading:(0,re.__)("Import SEO Settings","rank-math"),description:(0,re.__)("You can import SEO settings from the following plugins:","rank-math"),view:rt,isDisabled:!a},{name:"yoursite",title:(0,re.__)("Your Site","rank-math"),heading:(0,re.sprintf)((0,re.__)("Your Website: %s","rank-math"),rankMath.blogName),description:(0,re.__)("Let us know a few things about your site…","rank-math"),view:Un},{name:"analytics",title:(0,re.__)("Analytics","rank-math"),heading:(0,re.__)("Connect Google&trade; Services","rank-math"),description:(0,re.__)("Rank Math automates everything, use below button to connect your site with Google Search Console and Google Analytics. It will verify your site and submit sitemaps automatically. ","rank-math"),link:q("help-analytics","SW Analytics Step Description"),linkText:(0,re.__)("Read more about it here.","rank-math"),view:rr},{name:"sitemaps",title:(0,re.__)("Sitemaps","rank-math"),heading:(0,re.__)("Sitemap","rank-math"),description:(0,re.__)("Choose your Sitemap configuration and select which type of posts or pages you want to include in your Sitemaps.","rank-math"),link:q("configure-sitemaps","SW Sitemap Step"),view:ir,isDisabled:!r},{name:"optimization",title:(0,re.__)("Optimization","rank-math"),heading:(0,re.__)("SEO Tweaks","rank-math"),description:(0,re.__)("Automate some of your SEO tasks like making external links nofollow, redirecting attachment pages, etc.","rank-math"),link:q("seo-tweaks","SW Optimization Step"),view:lr,isDisabled:!r},{name:"ready",title:(0,re.__)("Ready","rank-math"),view:yr},{name:"ready1",title:(0,re.__)("Advanced Options","rank-math")},{name:"role",slug:"rolemanager",title:(0,re.__)("Role Manager","rank-math"),heading:(0,re.__)("Role Manager","rank-math"),description:(0,re.__)("Set capabilities here.","rank-math"),view:br},{name:"redirection",slug:"404redirection",title:(0,re.__)("404 + Redirection","rank-math"),view:gr},{name:"schema-markup",title:(0,re.__)("Schema Markup","rank-math"),heading:(0,re.__)("Schema Markup","rank-math"),description:(0,re.__)("Schema adds metadata to your website, resulting in rich search results and more traffic.","rank-math"),view:kr}],i=["ready1","role","redirection","schema-markup"],l=(0,J.findIndex)(o,(function(t){return t.name===e})),c=(0,J.includes)(i,e);return(0,J.compact)((0,J.map)(o,(function(e,t){return e.isDisabled||!c&&(0,J.includes)(i,e.name)||c&&!(0,J.includes)(i,e.name)?null:(e.disabled=!n,t<l?Or(Or({},e),{},{className:"is-done"}):e)})))},xr=function(e){var t=e.link,n=e.heading,r=e.description,a=e.linkText,o=void 0===a?(0,re.__)("Learn more.","rank-math"):a,i=e.className,l=void 0===i?"":i;return wp.element.createElement("header",null,wp.element.createElement("h1",{dangerouslySetInnerHTML:{__html:n}}),wp.element.createElement("p",{className:l},r,t&&wp.element.createElement("a",{href:t,target:"_blank",rel:"noreferrer"},o)))},Ar=function(){return wp.element.createElement("div",{className:"wrapper"},wp.element.createElement("div",{className:"main-content steps-progress-skeleton"},wp.element.createElement("header",null,wp.element.createElement("span",{className:"title"}),wp.element.createElement("span",{className:"sub-title"})),wp.element.createElement("div",{id:"field-metabox-rank-math"},(0,J.map)(Array.from({length:5}),(function(e,t){return wp.element.createElement("div",{key:t,className:"field-row"},wp.element.createElement("span",{className:"top"}),wp.element.createElement("span",{className:"bottom"}))})))))};function Cr(e){return Cr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Cr(e)}function Nr(e){return function(e){if(Array.isArray(e))return Dr(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Dr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Dr(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Dr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Tr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Rr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Tr(Object(n),!0).forEach((function(t){Ir(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Tr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ir(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Cr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Cr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Cr(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Mr=(0,X.compose)((0,Q.withSelect)((function(e,t){var n=t.searchParams.get("step")||"compatibility",r=e("rank-math-setup-wizard").getStepData(n);return Rr(Rr({},t),{},{steps:Pr(n,r),data:r,currentStep:n})})),(0,Q.withDispatch)((function(e,t){var n=t.steps,r=t.data,a=t.currentStep,o=t.navigate,i=t.searchParams,l=t.setSearchParams;return{onStepChange:function(e){(e="ready1"===e?"ready":e)!==a&&l((function(t){return(0,J.fromPairs)([].concat(Nr(t),[["step",e]]))}))},getStepData:function(t){t=t||a,te()({method:"POST",path:"/rankmath/v1/setupWizard/getStepData",data:{step:t}}).catch((function(e){alert(e.message)})).then((function(n){e("rank-math-setup-wizard").updateStepData(t,n)}))},getNextStep:function(){var e="role";if("ready"!==a){var t=(0,J.map)(n,(function(e){return e.name}));e=t[(0,J.findIndex)(t,(function(e){return e===a}))+1]}if(e)return e},skipStep:function(){var e=this.getNextStep(),t=i.get("page");o("/wp-admin/admin.php?page=".concat(t,"&step=").concat(e)),window.scrollTo({top:0,behavior:"auto"})},updateData:function(t,n){(0,J.isUndefined)(r)||(r[t]=n,e("rank-math-setup-wizard").updateStepData(a,r))},saveData:function(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,J.isEmpty)(n)||e("rank-math-setup-wizard").updateStepData(a,n),te()({method:"POST",path:"/rankmath/v1/setupWizard/updateStepData",data:{step:a,value:r}}).catch((function(e){alert(e.message)})).then((function(e){"ready"!==a&&((0,J.isBoolean)(e)?t.skipStep():window.location=e)}))}}})))((function(e){var t=e.currentStep,n=e.data,r=e.steps,a=e.getStepData,o=e.onStepChange,i=e.saveData,l=e.updateData,c=e.skipStep,s=e.getNextStep;return(0,K.useEffect)((function(){(0,J.isEmpty)(n)&&a();var e=s();e&&a(e)}),[t]),wp.element.createElement("div",{className:"rank-math-steps-progress-wrapper"},wp.element.createElement(ne.TabPanel,{tabs:r,key:t,initialTabName:t,className:"rank-math-steps-progress header",onSelect:o},(function(e){var r=e.name,a=e.slug,o=e.heading,s=e.view;return(0,J.isEmpty)(n)?wp.element.createElement(Ar,null):wp.element.createElement("div",{className:"wrapper"},wp.element.createElement("div",{className:"main-content wizard-content--".concat(a||r)},o&&wp.element.createElement(xr,e),wp.element.createElement(s,{data:n,saveData:i,updateData:l,skipStep:c})),!(0,J.includes)(["ready","compatibility","import"],t)&&wp.element.createElement(oe,{data:n,saveData:i,skipStep:c,currentStep:t}))})))})),Ur=function(e){if("ready"!==e.searchParams.get("step"))return wp.element.createElement("div",{className:"return-to-dashboard"},wp.element.createElement("a",{href:rankMath.adminurl+"?page=rank-math&view=modules"},(0,re.__)("Return to dashboard","rank-math")))};function Fr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Br(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Br(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Br(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Lr=function(){var e=Fr($({step:"compatibility"}),2),t=e[0],n=e[1],r=M();return wp.element.createElement(React.Fragment,null,wp.element.createElement(Mr,{searchParams:t,setSearchParams:n,navigate:r}),wp.element.createElement(Ur,{searchParams:t}))};function Wr(e,t){return{type:"RANK_MATH_APP_UI",key:e,value:t}}function zr(e){return zr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zr(e)}function Hr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Gr(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==zr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==zr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===zr(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Vr(e,t){return Wr(e,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Hr(Object(n),!0).forEach((function(t){Gr(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Hr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},t))}function Yr(e){return Wr("currentStep",e)}function $r(e){return $r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$r(e)}function Kr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function qr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Kr(Object(n),!0).forEach((function(t){Zr(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Kr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Zr(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==$r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==$r(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===$r(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Jr={currentStep:"getting-started"};function Xr(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Jr,t=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===t.type?qr(qr({},e),{},Zr({},t.key,t.value)):e}function Qr(e,t){return e.appUi[t]}function ea(e){return e.appUi.currentStep}var ta;(0,Q.register)((0,Q.createReduxStore)("rank-math-setup-wizard",{reducer:(0,Q.combineReducers)(o),selectors:i,actions:a}));ta=function(){(0,K.createRoot)(document.getElementById("rank-math-wizard-wrapper")).render(wp.element.createElement(React.Fragment,null,wp.element.createElement(Z,null),wp.element.createElement(G,null,wp.element.createElement(Lr,null))))},"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",ta):ta())}()}();