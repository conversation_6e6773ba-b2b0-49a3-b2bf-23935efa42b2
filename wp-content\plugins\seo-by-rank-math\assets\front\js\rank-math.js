!function(){"use strict";var n,t={n:function(n){var a=n&&n.__esModule?function(){return n.default}:function(){return n};return t.d(a,{a:a}),a},d:function(n,a){for(var e in a)t.o(a,e)&&!t.o(n,e)&&Object.defineProperty(n,e,{enumerable:!0,get:a[e]})},o:function(n,t){return Object.prototype.hasOwnProperty.call(n,t)}},a=jQuery;(n=t.n(a)())((function(){window.rankMathFront={init:function(){this.adminMenu()},adminMenu:function(){var t=n("#wp-admin-bar-rank-math-mark-me"),a=this;t.on("click",".mark-page-as a",(function(t){t.preventDefault(),a.ajax("mark_page_as",{objectID:rankMath.objectID,objectType:rankMath.objectType,what:n(this).attr("href").replace("#","")}),n(this).find(".dashicons").length?n(this).find(".dashicons").remove():n(this).prepend('<span class="dashicons dashicons-yes" style="font-family:dashicons;font-size:19px;line-height:28px;margin:0 3px 0 -4px;"></span>')}))},ajax:function(t,a,e){return n.ajax({url:rankMath.ajaxurl,type:e||"POST",dataType:"json",data:n.extend(!0,{action:"rank_math_"+t,security:rankMath.security},a)})}},window.rankMathFront.init()}))}();