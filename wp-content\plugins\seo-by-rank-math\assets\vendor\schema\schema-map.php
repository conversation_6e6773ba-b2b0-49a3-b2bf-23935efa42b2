{"version": "1.0.0", "properties": {"author": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Person"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Author Name", "placeholder": "%name%"}}}}, "image": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "ImageObject"}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Image URL"}}}}, "rating": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Rating"}}, "ratingValue": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating", "help": "Rating score"}}}, "worstRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Minimum", "help": "Rating minimum score", "placeholder": 1}}}, "bestRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Maximum", "help": "Rating maximum score", "placeholder": 5}}}}, "review": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Review"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Review"}}, "datePublished": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Published Date", "placeholder": "%date(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "dateModified": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Modified Date", "placeholder": "%modified(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "author": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Person"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Author Name", "placeholder": "%name%"}}}}, "reviewRating": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Rating"}}, "ratingValue": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating", "help": "Rating score"}}}, "worstRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Minimum", "help": "Rating minimum score", "placeholder": 1}}}, "bestRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Maximum", "help": "Rating maximum score", "placeholder": 5}}}}}, "bookEditions": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Editions", "help": "Either a specific edition of the written work, or the volume of the work"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Book"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Title", "help": "The title of the tome. Use for the title of the tome if it differs from the book. *Optional when tome has the same title as the book"}}}, "bookEdition": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Edition", "help": "The edition of the book"}}}, "isbn": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "ISBN", "help": "The ISBN of the print book"}}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "URL", "help": "URL specific to this edition if one exists"}}}, "author": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Person"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Author Name", "placeholder": "%name%"}}}}, "datePublished": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datepicker", "label": "Date Published", "help": "Date of first publication of this tome"}}}, "bookFormat": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "radio", "label": "Book Format", "desc": "The format of the book.", "options": {"https://schema.org/EBook": "eBook", "https://schema.org/Hardcover": "Hardcover", "https://schema.org/Paperback": "Paperback", "https://schema.org/AudioBook": "Audio Book"}, "default": "https://schema.org/Hardcover"}}}}, "provider": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"type": "radio", "label": "Course Provider", "classes": "show-property", "options": {"Organization": "Organization", "Person": "Person"}, "default": "Organization"}}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Course Provider Name"}}}, "sameAs": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Course Provider URL"}}}}, "virtual-location": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"value": "VirtualLocation"}}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Online Event URL", "help": "The URL of the online event, where people can join. This property is required if your event is happening online"}}}}, "address": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Address"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "PostalAddress"}}, "streetAddress": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Street Address"}}}, "addressLocality": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Locality"}}}, "addressRegion": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Region"}}}, "postalCode": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Postal Code"}}}, "addressCountry": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Country"}}}}, "physical-location": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Place"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Venue Name", "help": "The venue name."}}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Venue URL", "help": "Website URL of the venue"}}}, "address": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Address"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "PostalAddress"}}, "streetAddress": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Street Address"}}}, "addressLocality": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Locality"}}}, "addressRegion": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Region"}}}, "postalCode": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Postal Code"}}}, "addressCountry": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Country"}}}}}, "event-performer": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Performer Information"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"type": "radio", "label": "Performer", "classes": "show-property", "options": {"Organization": "Organization", "Person": "Person"}, "default": "Person"}}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Name"}}}, "sameAs": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Website or Social Link"}}}}, "offers": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Offers"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Offer"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Name"}}}, "category": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Category"}}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "URL"}}}, "price": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Price"}}}, "priceCurrency": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "availability": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "select", "label": "Availability", "help": "Offer availability", "classes": "col-4", "options": {"": "None", "InStock": "In Stock", "SoldOut": "Sold Out", "PreOrder": "Preorder"}, "default": "InStock"}}}, "validFrom": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "datepicker", "label": "Price Valid From", "help": "The date after which the price will no longer be available"}}}, "priceValidUntil": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "datepicker", "label": "Price Valid Until", "help": "The date after which the price will no longer be available"}}}, "inventoryLevel": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Inventory Level"}}}}, "monetary-amount-unit": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Salary"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "QuantitativeValue"}}, "value": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Salary (Recommended)", "help": "Insert amount, e.g. 50.00, or a salary range, e.g. 40.00-50.00", "classes": "col-4"}}}, "unitText": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "select", "label": "Payroll (Recommended)", "help": "Salary amount is for", "options": {"": "None", "YEAR": "Yearly", "MONTH": "Monthly", "WEEK": "Weekly", "DAY": "Daily", "HOUR": "Hourly"}, "classes": "col-4"}}}}, "monetary-amount": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "MonetaryAmount"}}, "currency": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "<PERSON><PERSON>", "help": "ISO 4217 Currency code. Example: EUR", "classes": "col-4"}}}, "value": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Salary"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "QuantitativeValue"}}, "value": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Salary (Recommended)", "help": "Insert amount, e.g. 50.00, or a salary range, e.g. 40.00-50.00", "classes": "col-4"}}}, "unitText": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "select", "label": "Payroll (Recommended)", "help": "Salary amount is for", "options": {"": "None", "YEAR": "Yearly", "MONTH": "Monthly", "WEEK": "Weekly", "DAY": "Daily", "HOUR": "Hourly"}, "classes": "col-4"}}}}}, "hiring-organization": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Organization"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Hiring Organization", "help": "The name of the company. Leave empty to use your own company information.", "classes": "col-4"}}}, "sameAs": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Organization URL (Recommended)", "help": "The URL of the organization offering the job position. Leave empty to use your own company information", "classes": "col-6"}}}, "logo": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Organization Logo (Recommended)", "help": "Logo URL of the organization offering the job position. Leave empty to use your own company information", "classes": "col-6"}}}}, "brand": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Brand"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Brand Name"}}}}, "calories": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "NutritionInformation"}}, "calories": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Calories", "help": "The number of calories in the recipe. Optional."}}}}, "video-object": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Video"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "VideoObject"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Name", "help": "A recipe video Name", "classes": "col-6"}}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "textarea", "label": "Description", "help": "A recipe video Description"}}}, "embedUrl": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Video URL", "help": "A video URL. Optional.", "classes": "col-6"}}}, "contentUrl": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Content URL", "help": "A URL pointing to the actual video media file", "classes": "col-6"}}}, "thumbnailUrl": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Recipe Video Thumbnail", "help": "A recipe video thumbnail URL", "classes": "col-6"}}}, "uploadDate": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datepicker", "label": "Video Upload Date", "classes": "col-6"}}}}, "instructionText": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "HowtoStep"}}, "text": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "textarea"}}}}, "instructions": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Recipe Instructions", "help": "Either a specific edition of the written work, or the volume of the work"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "HowToSection"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Name", "help": "The title of the tome. Use for the title of the tome if it differs from the book.<br>*Optional when tome has the same title as the book"}}}, "itemListElement": {"map": {"isArray": true, "isGroup": true, "isRequired": false, "isRecommended": false, "arrayMap": "instructionText", "classes": "show-add-property-group", "field": {"label": "Instruction Texts"}}}}, "geo-coordinates": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Geo Cordinates"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "GeoCoordinates"}}, "latitude": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Latitude"}}}, "longitude": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Longitude"}}}}, "opening-hours": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Tim<PERSON>"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "OpeningHoursSpecification"}}, "dayOfWeek": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "select", "multiple": true, "label": "Open Days", "options": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "default": []}}}, "opens": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "timepicker", "label": "Opening Time", "classes": "col-6", "placeholder": "09:00 AM"}}}, "closes": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "timepicker", "label": "Closing Time", "classes": "col-6", "placeholder": "05:00 PM"}}}}, "cuisine": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "cuisine": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}}, "schemas": {"Article": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "headline": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Headline", "placeholder": "%seo_title%"}}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "textarea", "label": "Description", "placeholder": "%seo_description%"}}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"type": "radio", "label": "Article Type", "classes": "show-property", "options": {"Article": "Article", "BlogPosting": "Blog Post", "NewsArticle": "News Article"}, "notice": {"status": "warning", "className": "article-notice", "content": "Google does not allow <PERSON> as the Publisher for articles. Organization will be used instead."}}}}, "author": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Person"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Author Name", "placeholder": "%name%"}}}}, "datePublished": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Published Date", "classes": "hide-group", "default": "%date(Y-m-d\\TH:i:sP)%"}}}, "dateModified": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Modified Date", "classes": "hide-group", "default": "%modified(Y-m-d\\TH:i:sP)%"}}}, "image": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "ImageObject"}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Image URL", "placeholder": "%post_thumbnail%"}}}}}, "Book": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Headline", "placeholder": "%seo_title%"}}}, "reviewLocation": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "save": "metadata", "field": {"type": "select", "label": "Review Location", "help": "The review or rating must be displayed on the page to comply with Google's Schema guidelines.", "options": {"bottom": "Below Content", "top": "Above Content", "both": "Above and Below Content", "custom": "Custom (use shortcode)"}, "default": "custom"}}}, "reviewLocationShortcode": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "save": "metadata", "field": {"type": "text", "label": "Shortcode", "help": "You can use the Schema Block in the block editor, or You can use the Schema Block in the block editor, or copy and paste this in the content. This shortcode will work on this page only.", "disabled": "disabled"}, "value": "[rank_math_rich_snippet]", "dependency": [{"field": "reviewLocation", "value": "custom"}]}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "URL"}}}, "author": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Person"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Author Name", "placeholder": "%name%"}}}}, "review": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Review"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Review"}}, "datePublished": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Published Date", "placeholder": "%date(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "dateModified": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Modified Date", "placeholder": "%modified(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "author": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Person"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Author Name", "placeholder": "%name%"}}}}, "reviewRating": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Rating"}}, "ratingValue": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating", "help": "Rating score"}}}, "worstRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Minimum", "help": "Rating minimum score", "placeholder": 1}}}, "bestRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Maximum", "help": "Rating maximum score", "placeholder": 5}}}}}, "hasPart": {"map": {"isArray": true, "isGroup": true, "isRequired": false, "isRecommended": false, "arrayMap": "bookEditions", "arrayProps": {"map": {"classes": "book-edition-single"}}, "field": {"label": "Editions"}}}, "image": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "ImageObject"}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Image URL", "placeholder": "%post_thumbnail%"}}}}}, "Course": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Headline", "placeholder": "%seo_title%"}}}, "reviewLocation": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "save": "metadata", "field": {"type": "select", "label": "Review Location", "help": "The review or rating must be displayed on the page to comply with Google's Schema guidelines.", "options": {"bottom": "Below Content", "top": "Above Content", "both": "Above and Below Content", "custom": "Custom (use shortcode)"}, "default": "custom"}}}, "reviewLocationShortcode": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "save": "metadata", "field": {"type": "text", "label": "Shortcode", "help": "You can use the Schema Block in the block editor, or copy and paste this in the content. This shortcode will work on this page only.", "disabled": "disabled"}, "value": "[rank_math_rich_snippet]", "dependency": [{"field": "reviewLocation", "value": "custom"}]}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "textarea", "label": "Description", "placeholder": "%seo_description%"}}}, "provider": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"type": "radio", "label": "Course Provider", "classes": "show-property", "options": {"Organization": "Organization", "Person": "Person"}, "default": "Organization"}}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Course Provider Name"}}}, "sameAs": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Course Provider URL"}}}}, "image": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "ImageObject"}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Image URL", "placeholder": "%post_thumbnail%"}}}}, "review": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Review"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Review"}}, "datePublished": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Published Date", "placeholder": "%date(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "dateModified": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Modified Date", "placeholder": "%modified(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "author": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Person"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Author Name", "placeholder": "%name%"}}}}, "reviewRating": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Rating"}}, "ratingValue": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating", "help": "Rating score"}}}, "worstRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Minimum", "help": "Rating minimum score", "placeholder": 1}}}, "bestRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Maximum", "help": "Rating maximum score", "placeholder": 5}}}}}}, "Event": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Headline", "placeholder": "%seo_title%"}}}, "reviewLocation": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "save": "metadata", "field": {"type": "select", "label": "Review Location", "help": "The review or rating must be displayed on the page to comply with Google's Schema guidelines.", "options": {"bottom": "Below Content", "top": "Above Content", "both": "Above and Below Content", "custom": "Custom (use shortcode)"}, "default": "custom"}}}, "reviewLocationShortcode": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "save": "metadata", "field": {"type": "text", "label": "Shortcode", "help": "You can use the Schema Block in the block editor, or copy and paste this in the content. This shortcode will work on this page only.", "disabled": "disabled"}, "value": "[rank_math_rich_snippet]", "dependency": [{"field": "reviewLocation", "value": "custom"}]}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "textarea", "label": "Description", "placeholder": "%seo_description%"}}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "select", "label": "Event Type", "help": "Type of the event", "classes": "show-property col-4", "options": {"Event": "Event", "BusinessEvent": "Business Event", "ChildrensEvent": "Childrens Event", "ComedyEvent": "Comedy Event", "DanceEvent": "Dance Event", "DeliveryEvent": "Delivery Event", "EducationEvent": "Education Event", "ExhibitionEvent": "Exhibition Event", "Festival": "Festival", "FoodEvent": "Food Event", "LiteraryEvent": "Literary Event", "MusicEvent": "Music Event", "PublicationEvent": "Publication Event", "SaleEvent": "Sale Event", "ScreeningEvent": "Screening Event", "SocialEvent": "Social Event", "SportsEvent": "Sports Event", "TheaterEvent": "Theater Event", "VisualArtsEvent": "Visual Arts Event"}}}}, "eventStatus": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "select", "label": "Event Status", "help": "Current status of the event (optional)", "options": {"": "None", "EventScheduled": "Scheduled", "EventCancelled": "Cancelled", "EventPostponed": "Postponed", "EventRescheduled": "Rescheduled", "EventMovedOnline": "Moved Online"}, "classes": "col-4", "default": "EventScheduled"}}}, "eventAttendanceMode": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "select", "label": "Event Attendance Mode", "help": "Indicates whether the event occurs online, offline at a physical location, or a mix of both online and offline.", "options": {"Offline": "Offline", "Online": "Online", "MixedEventAttendanceMode": "Online + Offline"}, "default": "Offline", "classes": "col-4"}}}, "VirtualLocation": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header", "dependency": [{"field": "eventAttendanceMode", "value": ["Online", "MixedEventAttendanceMode"]}]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"value": "VirtualLocation"}}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Online Event URL", "help": "The URL of the online event, where people can join. This property is required if your event is happening online"}}}}, "location": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header", "dependency": [{"field": "eventAttendanceMode", "value": ["Offline", "MixedEventAttendanceMode"]}]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Place"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Venue Name", "help": "The venue name."}}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Venue URL", "help": "Website URL of the venue"}}}, "address": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Address"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "PostalAddress"}}, "streetAddress": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Street Address"}}}, "addressLocality": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Locality"}}}, "addressRegion": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Region"}}}, "postalCode": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Postal Code"}}}, "addressCountry": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Country"}}}}}, "performer": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Performer Information"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"type": "radio", "label": "Performer", "classes": "show-property", "options": {"Organization": "Organization", "Person": "Person"}, "default": "Person"}}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Name"}}}, "sameAs": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Website or Social Link"}}}}, "startDate": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Start Date", "help": "Date and time of the event", "classes": "col-4"}}}, "endDate": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "End Date", "help": "End date and time of the event", "classes": "col-4"}}}, "offers": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Offers"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Offer"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Name", "classes": "hide-group", "placeholder": "General Admission"}}}, "category": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Category", "classes": "hide-group", "placeholder": "primary"}}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "URL"}}}, "price": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Price"}}}, "priceCurrency": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "availability": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "select", "label": "Availability", "help": "Offer availability", "classes": "col-4", "options": {"": "None", "InStock": "In Stock", "SoldOut": "Sold Out", "PreOrder": "Preorder"}, "default": "InStock"}}}, "validFrom": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "datepicker", "label": "Price Valid From", "help": "The date after which the price will no longer be available"}}}, "priceValidUntil": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "datepicker", "label": "Price Valid Until", "help": "The date after which the price will no longer be available", "classes": "hide-group"}}}, "inventoryLevel": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Inventory Level"}}}}, "review": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Review"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Review"}}, "datePublished": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Published Date", "placeholder": "%date(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "dateModified": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Modified Date", "placeholder": "%modified(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "author": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Person"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Author Name", "placeholder": "%name%"}}}}, "reviewRating": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Rating"}}, "ratingValue": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating", "help": "Rating score"}}}, "worstRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Minimum", "help": "Rating minimum score", "placeholder": 1}}}, "bestRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Maximum", "help": "Rating maximum score", "placeholder": 5}}}}}, "image": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "ImageObject"}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Image URL", "placeholder": "%post_thumbnail%"}}}}}, "JobPosting": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "title": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Headline", "placeholder": "%seo_title%"}}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "textarea", "label": "Description", "placeholder": "%seo_description%"}}}, "baseSalary": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "MonetaryAmount"}}, "currency": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "<PERSON><PERSON>", "help": "ISO 4217 Currency code. Example: EUR", "classes": "col-4"}}}, "value": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Salary"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "QuantitativeValue"}}, "value": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Salary (Recommended)", "help": "Insert amount, e.g. 50.00, or a salary range, e.g. 40.00-50.00", "classes": "col-4"}}}, "unitText": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "select", "label": "Payroll (Recommended)", "help": "Salary amount is for", "options": {"": "None", "YEAR": "Yearly", "MONTH": "Monthly", "WEEK": "Weekly", "DAY": "Daily", "HOUR": "Hourly"}, "classes": "col-4"}}}}}, "datePosted": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datepicker", "label": "Date Posted", "help": "The original date on which employer posted the job. You can leave it empty to use the post publication date as job posted date", "classes": "col-4"}}}, "validThrough": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datepicker", "label": "Expiry Posted", "help": "The date when the job posting will expire. If a job posting never expires, or you do not know when the job will expire, do not include this property", "classes": "col-4"}}}, "unpublish": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "save": "metadata", "field": {"type": "select", "label": "Unpublish when expired", "options": {"on": "Yes", "off": "No"}, "help": "If checked, post status will be changed to Draft and its URL will return a 404 error, as required by the Rich Result guidelines", "classes": "col-4", "default": "on"}}}, "employmentType": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "select", "multiple": true, "label": "Employment Type (Recommended)", "help": "Type of employment. You can choose more than one value", "options": {"": "None", "FULL_TIME": "Full Time", "PART_TIME": "Part Time", "CONTRACTOR": "Contractor", "TEMPORARY": "Temporary", "INTERN": "Intern", "VOLUNTEER": "Volunteer", "PER_DIEM": "<PERSON>", "OTHER": "Other"}, "default": []}}}, "hiringOrganization": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Organization"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Hiring Organization", "help": "The name of the company. Leave empty to use your own company information.", "classes": "col-4"}}}, "sameAs": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Organization URL (Recommended)", "help": "The URL of the organization offering the job position. Leave empty to use your own company information", "classes": "col-6"}}}, "logo": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Organization Logo (Recommended)", "help": "Logo URL of the organization offering the job position. Leave empty to use your own company information", "classes": "col-6"}}}}, "id": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Posting ID (Recommended)", "help": "The hiring organization's unique identifier for the job. Leave empty to use the post ID", "classes": "col-6"}}}, "jobLocation": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Place"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Venue Name", "help": "The venue name.", "classes": "hide-group"}}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Venue URL", "help": "Website URL of the venue", "classes": "hide-group"}}}, "address": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Address"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "PostalAddress"}}, "streetAddress": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Street Address"}}}, "addressLocality": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Locality"}}}, "addressRegion": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Region"}}}, "postalCode": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Postal Code"}}}, "addressCountry": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Country"}}}}}, "image": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "ImageObject"}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Image URL", "placeholder": "%post_thumbnail%"}}}}}, "Music": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Headline", "placeholder": "%seo_title%"}}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "textarea", "label": "Description", "placeholder": "%seo_description%"}}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "URL"}}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "radio", "label": "Music Type", "classes": "show-property", "options": {"MusicGroup": "MusicGroup", "MusicAlbum": "MusicAlbum"}, "default": "MusicGroup"}}}, "image": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "ImageObject"}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Image URL", "placeholder": "%post_thumbnail%"}}}}}, "Person": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Headline", "placeholder": "%seo_title%"}}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "textarea", "label": "Description", "placeholder": "%seo_description%"}}}, "email": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Email"}}}, "address": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Address"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "PostalAddress"}}, "streetAddress": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Street Address"}}}, "addressLocality": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Locality"}}}, "addressRegion": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Region"}}}, "postalCode": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Postal Code"}}}, "addressCountry": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Country"}}}}, "gender": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Gender", "classes": "col-6"}}}, "jobTitle": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Job title", "help": "The job title of the person (for example, Financial Manager).", "classes": "col-6"}}}}, "Product": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Product name", "placeholder": "%seo_title%"}}}, "reviewLocation": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "save": "metadata", "field": {"type": "select", "label": "Review Location", "help": "The review or rating must be displayed on the page to comply with Google's Schema guidelines.", "options": {"bottom": "Below Content", "top": "Above Content", "both": "Above and Below Content", "custom": "Custom (use shortcode)"}, "default": "custom"}}}, "reviewLocationShortcode": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "save": "metadata", "field": {"type": "text", "label": "Shortcode", "help": "You can use the Schema Block in the block editor, or copy and paste this in the content. This shortcode will work on this page only.", "disabled": "disabled"}, "value": "[rank_math_rich_snippet]", "dependency": [{"field": "reviewLocation", "value": "custom"}]}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "textarea", "label": "Description", "placeholder": "%seo_description%"}}}, "sku": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Product SKU"}}}, "brand": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Brand"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Brand Name"}}}}, "image": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "ImageObject"}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Image URL", "placeholder": "%post_thumbnail%"}}}}, "gtin8": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Gtin", "classes": "hide-group"}}}, "mpn": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "MPN", "classes": "hide-group"}}}, "isbn": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "ISBN", "classes": "hide-group"}}}, "offers": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Offers"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Offer"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Name", "classes": "hide-group"}}}, "category": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Category", "classes": "hide-group"}}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "URL", "classes": "hide-group", "placeholder": "%url%"}}}, "price": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Price"}}}, "priceCurrency": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "availability": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "select", "label": "Availability", "help": "Offer availability", "classes": "col-4", "options": {"": "None", "InStock": "In Stock", "SoldOut": "Sold Out", "PreOrder": "Preorder"}, "default": "InStock"}}}, "validFrom": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "datepicker", "label": "Price Valid From", "help": "The date after which the price will no longer be available", "classes": "hide-group"}}}, "priceValidUntil": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "datepicker", "label": "Price Valid Until", "help": "The date after which the price will no longer be available"}}}, "inventoryLevel": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Inventory Level", "classes": "hide-group"}}}}, "review": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Review"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Review"}}, "datePublished": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Published Date", "placeholder": "%date(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "dateModified": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Modified Date", "placeholder": "%modified(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "author": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Person"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Author Name", "placeholder": "%name%"}}}}, "reviewRating": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Rating"}}, "ratingValue": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating", "help": "Rating score"}}}, "worstRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Minimum", "help": "Rating minimum score", "placeholder": 1}}}, "bestRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Maximum", "help": "Rating maximum score", "placeholder": 5}}}}}}, "Recipe": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Headline", "placeholder": "%seo_title%"}}}, "reviewLocation": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "save": "metadata", "field": {"type": "select", "label": "Review Location", "help": "The review or rating must be displayed on the page to comply with Google's Schema guidelines.", "options": {"bottom": "Below Content", "top": "Above Content", "both": "Above and Below Content", "custom": "Custom (use shortcode)"}, "default": "custom"}}}, "reviewLocationShortcode": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "save": "metadata", "field": {"type": "text", "label": "Shortcode", "help": "You can use the Schema Block in the block editor, or copy and paste this in the content. This shortcode will work on this page only.", "disabled": "disabled"}, "value": "[rank_math_rich_snippet]", "dependency": [{"field": "reviewLocation", "value": "custom"}]}}, "datePublished": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Published Date", "classes": "hide-group", "default": "%date(Y-m-d\\TH:i:sP)%"}}}, "author": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Person"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Author Name", "placeholder": "%name%"}}}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "textarea", "label": "Description", "placeholder": "%seo_description%"}}}, "prepTime": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Preparation Time", "help": "ISO 8601 duration format. Example: PT1H30M", "classes": "col-4"}}}, "cookTime": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Cooking Time", "help": "ISO 8601 duration format. Example: PT1H30M", "classes": "col-4"}}}, "totalTime": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Total Time", "help": "ISO 8601 duration format. Example: PT1H30M", "classes": "col-4"}}}, "recipeCategory": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Type", "help": "Type of dish, for example appetizer, or dessert.", "classes": "col-4"}}}, "recipeCuisine": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "<PERSON><PERSON><PERSON><PERSON>", "help": "The cuisine of the recipe (for example, French or Ethiopian).", "classes": "col-4"}}}, "keywords": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Keywords", "help": "Other terms for your recipe such as the season, the holiday, or other descriptors. Separate multiple entries with commas.", "classes": "col-4"}}}, "recipeYield": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "<PERSON><PERSON><PERSON>", "help": "Quantity produced by the recipe, for example \"4 servings\"", "classes": "col-4"}}}, "nutrition": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "NutritionInformation"}}, "calories": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Calories", "help": "The number of calories in the recipe. Optional."}}}}, "recipeIngredient": {"map": {"isArray": true, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "show-add-property", "field": {"label": "Recipe Ingredients", "help": "Recipe ingredients, add one item per line"}}}, "review": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Review"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Review"}}, "datePublished": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Published Date", "placeholder": "%date(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "dateModified": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Modified Date", "placeholder": "%modified(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "author": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Person"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Author Name", "placeholder": "%name%"}}}}, "reviewRating": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Rating"}}, "ratingValue": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating", "help": "Rating score"}}}, "worstRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Minimum", "help": "Rating minimum score", "placeholder": 1}}}, "bestRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Maximum", "help": "Rating maximum score", "placeholder": 5}}}}}, "video": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Video"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "VideoObject"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Name", "help": "A recipe video Name", "classes": "col-6"}}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "textarea", "label": "Description", "help": "A recipe video Description"}}}, "embedUrl": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Video URL", "help": "A video URL. Optional.", "classes": "col-6"}}}, "contentUrl": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Content URL", "help": "A URL pointing to the actual video media file", "classes": "col-6"}}}, "thumbnailUrl": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Recipe Video Thumbnail", "help": "A recipe video thumbnail URL", "classes": "col-6"}}}, "uploadDate": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datepicker", "label": "Video Upload Date", "classes": "col-6"}}}}, "instructionType": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "radio", "label": "Instruction Type", "options": {"SingleField": "Single Field", "HowToStep": "How To Step"}, "default": "SingleField"}}}, "instructionsSingleField": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "textarea", "label": "Recipe Instructions"}, "dependency": [{"field": "instructionType", "value": "SingleField"}]}}, "instructionsHowToStep": {"map": {"isArray": true, "isGroup": true, "isRequired": false, "isRecommended": false, "arrayMap": "instructions", "field": {"label": "Recipe Instructions"}, "dependency": [{"field": "instructionType", "value": ["HowToStep"]}]}}, "image": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "ImageObject"}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Image URL", "placeholder": "%post_thumbnail%"}}}}}, "Restaurant": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Headline", "placeholder": "%seo_title%"}}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "textarea", "label": "Description", "placeholder": "%seo_description%"}}}, "telephone": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Phone Number"}}}, "priceRange": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Price Range", "classes": "col-4"}}}, "address": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Address"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "PostalAddress"}}, "streetAddress": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Street Address"}}}, "addressLocality": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Locality"}}}, "addressRegion": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Region"}}}, "postalCode": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Postal Code"}}}, "addressCountry": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "text", "label": "Country"}}}}, "geo": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Geo Cordinates"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "GeoCoordinates"}}, "latitude": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Latitude"}}}, "longitude": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Longitude"}}}}, "openingHoursSpecification": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Tim<PERSON>"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "OpeningHoursSpecification"}}, "dayOfWeek": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "select", "multiple": true, "label": "Open Days", "options": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "default": []}}}, "opens": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "timepicker", "label": "Opening Time", "classes": "col-6", "placeholder": "09:00 AM"}}}, "closes": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "timepicker", "label": "Closing Time", "classes": "col-6", "placeholder": "05:00 PM"}}}}, "servesCuisine": {"map": {"isArray": true, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "show-add-property"}}, "hasMenu": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Menu URL", "help": "URL pointing to the menu of the restaurant.", "classes": "col-6"}}}, "image": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "ImageObject"}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Image URL", "placeholder": "%post_thumbnail%"}}}}}, "Service": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Headline", "placeholder": "%seo_title%"}}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "textarea", "label": "Description", "placeholder": "%seo_description%"}}}, "serviceType": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Service Type", "help": "The type of service being offered, e.g. veterans' benefits, emergency relief, etc.", "classes": "col-4"}}}, "offers": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Offers"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Offer"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Name", "classes": "hide-group"}}}, "category": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Category", "classes": "hide-group"}}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "URL", "classes": "hide-group"}}}, "price": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Price"}}}, "priceCurrency": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "availability": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "select", "label": "Availability", "help": "Offer availability", "classes": ["col-4", "hide-group"], "options": {"": "None", "InStock": "In Stock", "SoldOut": "Sold Out", "PreOrder": "Preorder"}, "default": "InStock"}}}, "validFrom": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "datepicker", "label": "Price Valid From", "help": "The date after which the price will no longer be available", "classes": "hide-group"}}}, "priceValidUntil": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "datepicker", "label": "Price Valid Until", "help": "The date after which the price will no longer be available", "classes": "hide-group"}}}, "inventoryLevel": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Inventory Level", "classes": "hide-group"}}}}, "image": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "ImageObject"}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Image URL", "placeholder": "%post_thumbnail%"}}}}}, "SoftwareApplication": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Headline", "placeholder": "%seo_title%"}}}, "reviewLocation": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "save": "metadata", "field": {"type": "select", "label": "Review Location", "help": "The review or rating must be displayed on the page to comply with Google's Schema guidelines.", "options": {"bottom": "Below Content", "top": "Above Content", "both": "Above and Below Content", "custom": "Custom (use shortcode)"}, "default": "custom"}}}, "reviewLocationShortcode": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "save": "metadata", "field": {"type": "text", "label": "Shortcode", "help": "You can use the Schema Block in the block editor, or copy and paste this in the content. This shortcode will work on this page only.", "disabled": "disabled"}, "value": "[rank_math_rich_snippet]", "dependency": [{"field": "reviewLocation", "value": "custom"}]}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "textarea", "label": "Description", "placeholder": "%seo_description%"}}}, "operatingSystem": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Operating System", "help": "For example, \"Windows 7\", \"OSX 10.6\", \"Android 1.6\"", "classes": "col-6"}}}, "applicationCategory": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Application Category", "help": "For example, \"Game\", \"Multimedia\"", "classes": "col-6"}}}, "offers": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Offers"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Offer"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Name", "classes": "hide-group"}}}, "category": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Category", "classes": "hide-group"}}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "URL", "classes": "hide-group"}}}, "price": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "Price"}}}, "priceCurrency": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "availability": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "select", "label": "Availability", "help": "Offer availability", "classes": ["col-4", "hide-group"], "options": {"": "None", "InStock": "In Stock", "SoldOut": "Sold Out", "PreOrder": "Preorder"}, "default": "InStock"}}}, "validFrom": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "datepicker", "label": "Price Valid From", "help": "The date after which the price will no longer be available", "classes": "hide-group"}}}, "priceValidUntil": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "datepicker", "label": "Price Valid Until", "help": "The date after which the price will no longer be available", "classes": "hide-group"}}}, "inventoryLevel": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Inventory Level", "classes": "hide-group"}}}}, "review": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "field": {"label": "Review"}}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Review"}}, "datePublished": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Published Date", "placeholder": "%date(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "dateModified": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "datetimepicker", "label": "Modified Date", "placeholder": "%modified(Y-m-d\\TH:i:sP)%", "classes": "hide-group"}}}, "author": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Person"}}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Author Name", "placeholder": "%name%"}}}}, "reviewRating": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": "hide-group-header"}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "Rating"}}, "ratingValue": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating", "help": "Rating score"}}}, "worstRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Minimum", "help": "Rating minimum score", "placeholder": 1}}}, "bestRating": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"type": "number", "label": "Rating Maximum", "help": "Rating maximum score", "placeholder": 5}}}}}, "image": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false, "classes": ["hide-group-header", "hide-group"]}, "@type": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "value": "ImageObject"}}, "url": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Image URL", "placeholder": "%post_thumbnail%"}}}}}, "VideoObject": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}, "name": {"map": {"isArray": false, "isGroup": false, "isRequired": true, "isRecommended": false, "field": {"label": "Headline", "placeholder": "%seo_title%"}}}, "description": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": true, "field": {"type": "textarea", "label": "Description", "placeholder": "%seo_description%"}}}, "uploadDate": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Upload Date", "classes": "hide-group", "placeholder": "%date(Y-m-d\\TH:i:sP)%"}}}, "contentUrl": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Content URL", "help": "A URL pointing to the actual video media file", "classes": "col-6"}}}, "embedUrl": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Embed URL", "help": "A URL pointing to the embeddable player for the video", "classes": "col-6"}}}, "duration": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Duration", "help": "ISO 8601 duration format. Example: 1H30M", "classes": "col-6"}}}, "interactionCount": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Views", "help": "Number of views", "classes": "col-6"}}}, "thumbnailUrl": {"map": {"isArray": false, "isGroup": false, "isRequired": false, "isRecommended": false, "field": {"label": "Video Thumbnail", "help": "A video thumbnail URL", "classes": "hide-group", "placeholder": "%post_thumbnail%"}}}}, "WooCommerceProduct": {"map": {"isArray": false, "isGroup": true, "isRequired": false, "isRecommended": false}}}}