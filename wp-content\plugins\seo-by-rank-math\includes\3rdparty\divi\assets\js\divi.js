!function(){var t={4184:function(t,e){var r;!function(){"use strict";var n={}.hasOwnProperty;function a(){for(var t=[],e=0;e<arguments.length;e++){var r=arguments[e];if(r){var i=typeof r;if("string"===i||"number"===i)t.push(r);else if(Array.isArray(r)){if(r.length){var o=a.apply(null,r);o&&t.push(o)}}else if("object"===i){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){t.push(r.toString());continue}for(var u in r)n.call(r,u)&&r[u]&&t.push(u)}}}return t.join(" ")}t.exports?(a.default=a,t.exports=a):void 0===(r=function(){return a}.apply(e,[]))||(t.exports=r)}()}},e={};function r(n){var a=e[n];if(void 0!==a)return a.exports;var i=e[n]={exports:{}};return t[n](i,i.exports,r),i.exports}r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,{a:e}),e},r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},function(){"use strict";var t={};r.r(t),r.d(t,{deleteSchema:function(){return Pe},lockModifiedDate:function(){return G},refreshResults:function(){return ie},resetDirtyMetadata:function(){return z},resetRedirection:function(){return ot},resetStore:function(){return H},saveSchema:function(){return Te},saveTemplate:function(){return De},schemaUpdated:function(){return ye},setEditingSchemaId:function(){return ve},setEditorTab:function(){return be},setTemplateTab:function(){return we},setVersion:function(){return oe},toggleFrontendScore:function(){return U},toggleIsDiviPageSettingsBarActive:function(){return ue},toggleIsDiviRankMathModalActive:function(){return ce},toggleLoaded:function(){return re},toggleSchemaEditor:function(){return de},toggleSchemaTemplates:function(){return ge},toggleSnippetEditor:function(){return At},updateAIScore:function(){return Ee},updateAdvancedRobots:function(){return F},updateAnalysisScore:function(){return L},updateAppData:function(){return j},updateAppUi:function(){return R},updateBreadcrumbTitle:function(){return K},updateCanonicalUrl:function(){return N},updateDescription:function(){return et},updateEditSchema:function(){return _e},updateEditSchemas:function(){return ke},updateFacebookDescription:function(){return q},updateFacebookHasOverlay:function(){return Y},updateFacebookImage:function(){return Q},updateFacebookImageID:function(){return W},updateFacebookImageOverlay:function(){return J},updateFacebookTitle:function(){return V},updateFeaturedImage:function(){return rt},updateHasRedirect:function(){return ut},updateHighlightedParagraphs:function(){return se},updateKeywords:function(){return C},updatePermalink:function(){return Z},updatePillarContent:function(){return B},updatePostID:function(){return X},updatePrimaryTermID:function(){return nt},updateRedirection:function(){return at},updateRedirectionItem:function(){return it},updateRobots:function(){return $},updateSchemas:function(){return Se},updateSelectedKeyword:function(){return ne},updateSerpDescription:function(){return Ot},updateSerpSlug:function(){return It},updateSerpTitle:function(){return Et},updateSnippetPreviewType:function(){return Mt},updateSocialTab:function(){return ae},updateTitle:function(){return tt},updateTwitterAppCountry:function(){return ee},updateTwitterAppDescription:function(){return Ht},updateTwitterAppGoogleplayID:function(){return Xt},updateTwitterAppGoogleplayName:function(){return Zt},updateTwitterAppGoogleplayUrl:function(){return te},updateTwitterAppIpadID:function(){return Wt},updateTwitterAppIpadName:function(){return Yt},updateTwitterAppIpadUrl:function(){return Jt},updateTwitterAppIphoneID:function(){return Vt},updateTwitterAppIphoneName:function(){return qt},updateTwitterAppIphoneUrl:function(){return Qt},updateTwitterAuthor:function(){return Bt},updateTwitterCardType:function(){return jt},updateTwitterDescription:function(){return Ct},updateTwitterHasOverlay:function(){return Nt},updateTwitterImage:function(){return Lt},updateTwitterImageID:function(){return Ut},updateTwitterImageOverlay:function(){return Ft},updateTwitterPlayerSize:function(){return Kt},updateTwitterPlayerStreamCtype:function(){return zt},updateTwitterPlayerStreamUrl:function(){return Gt},updateTwitterPlayerUrl:function(){return $t},updateTwitterTitle:function(){return Rt},updateTwitterUseFacebook:function(){return xt}});var e={};r.r(e),r.d(e,{appData:function(){return je},appUi:function(){return Ne}});var n={};r.r(n),r.d(n,{getAdvancedRobots:function(){return Ve},getAnalysisScore:function(){return Ke},getAppData:function(){return Fe},getBreadcrumbTitle:function(){return Qe},getCanonicalUrl:function(){return qe},getDescription:function(){return gr},getDirtyMetadata:function(){return $e},getEditSchemas:function(){return Cr},getEditingSchema:function(){return Br},getEditorTab:function(){return Lr},getFacebookAuthor:function(){return cr},getFacebookDescription:function(){return ur},getFacebookHasOverlay:function(){return pr},getFacebookImage:function(){return lr},getFacebookImageID:function(){return sr},getFacebookImageOverlay:function(){return fr},getFacebookTitle:function(){return or},getFeaturedImage:function(){return yr},getFeaturedImageHtml:function(){return vr},getHighlightedParagraphs:function(){return ir},getKeywords:function(){return Ge},getPermalink:function(){return dr},getPillarContent:function(){return ze},getPostID:function(){return hr},getPreviewSchema:function(){return Ur},getPrimaryTermID:function(){return br},getRedirectionID:function(){return wr},getRedirectionItem:function(){return _r},getRedirectionType:function(){return kr},getRedirectionUrl:function(){return Sr},getRichSnippets:function(){return We},getRobots:function(){return He},getSchemas:function(){return Rr},getSelectedKeyword:function(){return Ze},getSerpDescription:function(){return Ir},getSerpSlug:function(){return Er},getSerpTitle:function(){return Dr},getShowScoreFrontend:function(){return Ye},getSnippetPreviewType:function(){return Ar},getSocialTab:function(){return tr},getTemplateTab:function(){return Nr},getTitle:function(){return mr},getTwitterAppCountry:function(){return on},getTwitterAppDescription:function(){return Wr},getTwitterAppGoogleplayID:function(){return rn},getTwitterAppGoogleplayName:function(){return nn},getTwitterAppGoogleplayUrl:function(){return an},getTwitterAppIpadID:function(){return Zr},getTwitterAppIpadName:function(){return tn},getTwitterAppIpadUrl:function(){return en},getTwitterAppIphoneID:function(){return Yr},getTwitterAppIphoneName:function(){return Jr},getTwitterAppIphoneUrl:function(){return Xr},getTwitterAuthor:function(){return zr},getTwitterCardType:function(){return $r},getTwitterDescription:function(){return Gr},getTwitterHasOverlay:function(){return qr},getTwitterImage:function(){return Vr},getTwitterImageID:function(){return Hr},getTwitterImageOverlay:function(){return Qr},getTwitterPlayerSize:function(){return cn},getTwitterPlayerStream:function(){return sn},getTwitterPlayerStreamCtype:function(){return ln},getTwitterPlayerUrl:function(){return un},getTwitterTitle:function(){return Kr},getTwitterUseFacebook:function(){return Fr},hasRedirect:function(){return Tr},hasSchemaUpdated:function(){return jr},isDiviPageSettingsBarActive:function(){return nr},isDiviRankMathModalActive:function(){return ar},isLoaded:function(){return Xe},isModifiedDateLocked:function(){return Je},isPro:function(){return rr},isRefreshing:function(){return er},isSchemaEditorOpen:function(){return Mr},isSchemaTemplatesOpen:function(){return xr},isSnippetEditorOpen:function(){return Or}});var a=jQuery,i=r.n(a),o=wp.apiFetch,u=r.n(o),c=wp.data,s=wp.mediaUtils,l=wp.element,p=wp.hooks,f=wp.components,h=(0,f.createSlotFill)("RankMathAfterEditor"),m=h.Fill,d=h.Slot,g=function(t){var e=t.children,r=t.className;return wp.element.createElement(m,null,wp.element.createElement(f.PanelRow,{className:r},e))};g.Slot=d;var y=g,v=(0,f.createSlotFill)("RankMathAdvancedTab"),b=v.Fill,w=v.Slot,k=function(t){var e=t.children,r=t.className;return wp.element.createElement(b,null,wp.element.createElement(f.PanelRow,{className:r},e))};k.Slot=w;var S=k,_=(0,f.createSlotFill)("RankMathAfterFocusKeyword"),T=_.Fill,P=_.Slot,D=function(t){var e=t.children,r=t.className;return wp.element.createElement(T,null,wp.element.createElement("div",{className:r},e))};D.Slot=P;var E=D,I=rankMathAnalyzer;function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function A(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,M(n.key),n)}}function M(t){var e=function(t,e){if("object"!==O(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==O(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===O(e)?e:String(e)}var x=new(function(){function t(){var e,r,n;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e=this,n=null,(r=M(r="map"))in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n}var e,r,n;return e=t,(r=[{key:"swap",value:function(t,e){var r=this;if(!(t=t||""))return"";var n=new RegExp(/%(([a-z0-9_-]+)\(([^)]*)\)|[^\s]+)%/,"giu");return t.replace(" %page%","").replace("%sep% %sep%","%sep%").replace(n,(function(t){return r.replace(e,t)})).trim()}},{key:"replace",value:function(t,e){var r=e.toLowerCase().slice(1,-1);return["term_description","user_description"].includes(r)?"undefined"!=typeof tinymce&&void 0!==tinymce.editors.rank_math_description_editor?tinymce.editors.rank_math_description_editor.getContent():i()("#description").val():r.includes("customfield(")?(r=r.replace("customfield(","").replace(")",""))in rankMath.customFields?rankMath.customFields[r]:"":(t=t||this.getMap(),(r="seo_description"===(r="seo_title"===(r=r.includes("(")?r.split("(")[0]:r)?"title":r)?"excerpt":r)in t?t[r]:"")}},{key:"getMap",value:function(){var t=this;return null!==this.map||(this.map={},i().each(rankMath.variables,(function(e,r){e=e.toLowerCase().replace(/%+/g,"").split("(")[0],t.map[e]=r.example}))),this.map}},{key:"setVariable",value:function(t,e){null!==this.map?this.map[t]=e:void 0!==rankMath.variables[t]&&(rankMath.variables[t].example=e)}}])&&A(e.prototype,r),n&&A(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}());function j(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return e=(0,p.applyFilters)("rank_math_sanitize_data",e,t,r),null!==n&&(n=(0,p.applyFilters)("rank_math_sanitize_meta_value",n,t,r)),n=null===n?e:n,(0,p.doAction)("rank_math_data_changed",t,e,r),{type:"RANK_MATH_APP_DATA",key:t,value:e,metaKey:r,metaValue:n}}function R(t,e){return(0,p.doAction)("rank_math_update_app_ui",t,e),{type:"RANK_MATH_APP_UI",key:t,value:e}}function C(t){return x.setVariable("focuskw",t.split(",")[0]),rankMathEditor.refresh("keyword"),j("keywords",t,"rank_math_focus_keyword")}function B(t){return j("pillarContent",t,"rank_math_pillar_content",!0===t?"on":"off")}function U(t){return j("showScoreFrontend",t,"rank_math_dont_show_seo_score",!0===t?"off":"on")}function L(t){return j("score",t,"rank_math_seo_score")}function N(t){return j("canonicalUrl",t,"rank_math_canonical_url")}function F(t){return j("advancedRobots",t,"rank_math_advanced_robots")}function $(t){return j("robots",t,"rank_math_robots",Object.keys(t))}function K(t){return j("breadcrumbTitle",t,"rank_math_breadcrumb_title")}function G(t){return j("lockModifiedDate",t,"rank_math_lock_modified_date")}function z(){return j("dirtyMetadata",{})}function H(t){return{type:"RESET_STORE",value:t}}function V(t){return j("facebookTitle",t,"rank_math_facebook_title")}function q(t){return j("facebookDescription",t,"rank_math_facebook_description")}function Q(t){return j("facebookImage",t,"rank_math_facebook_image")}function W(t){return j("facebookImageID",t,"rank_math_facebook_image_id")}function Y(t){return j("facebookHasOverlay",t,"rank_math_facebook_enable_image_overlay",!0===t?"on":"off")}function J(t){return j("facebookImageOverlay",t,"rank_math_facebook_image_overlay")}function X(t){return rankMath.objectID=t,j("postID",t)}function Z(t){return j("permalink",t,"permalink")}function tt(t){return j("title",t,"rank_math_title")}function et(t){return j("description",t,"rank_math_description")}function rt(t){return j("featuredImage",t)}function nt(t,e){return j("primaryTerm",parseInt(t),"rank_math_primary_"+e)}function at(t,e){return j(t,e)}function it(t){return R("redirectionItem",t)}function ot(){return R("redirectionItem",{})}function ut(t){return R("hasRedirect",t)}var ct=lodash,st=function(t){return t.replace(/<\/?[a-z][^>]*?>/gi,"\n")},lt=function(t){return t.replace(/<style[^>]*>([\S\s]*?)<\/style>/gim,"")},pt=function(t){return t.replace(/&nbsp;|&#160;/gi," ").replace(/\s{2,}/g," ").replace(/\s\./g,".").replace(/^\s+|\s+$/g,"")},ft=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return t.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,e)},ht=function(t){return t.replace(/[‘’‛`]/g,"'").replace(/[“”〝〞〟‟„]/g,'"')},mt=function(t){return t.replace(/<!--[\s\S]*?-->/g,"")},dt=function(t){return t.replace(/&\S+?;/g,"")};function gt(t){return(0,ct.isUndefined)(t)||!t?"":(0,ct.flow)([lt,ft,st,mt,dt,pt,ht])(t)}var yt=wp.autop,vt="[^<>&/\\[\\]\0- =]+?",bt=new RegExp("\\["+vt+"( [^\\]]+?)?\\]","g"),wt=new RegExp("\\[/"+vt+"\\]","g"),kt=function(t){return t.replace(bt,"").replace(wt,"")},St=function(t,e){var r=function(t,e){for(var r,n=/<p(?:[^>]+)?>(.*?)<\/p>/gi,a=[];null!==(r=n.exec(t));)a.push(r);return(0,ct.map)(a,(function(t){return e?gt(t[1]):t[1]}))}(t=(0,ct.flow)([kt,mt,yt.autop])(t),e=e||!1);return 0<r.length?r:[e?gt(t):t]},_t=document.createElement("div");function Tt(t){return t&&"string"==typeof t&&(t=t.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,"").replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gim,""),_t.innerHTML=t,t=_t.textContent,_t.textContent=""),t}var Pt=function(t,e){return t=(t=gt(t)).replace(/\r?\n|\r/g," "),e?(0,ct.truncate)(t,{length:e,separator:" "}):t},Dt=function(t){if((0,ct.isEmpty)(t))return"";t=ft(t),t=lt(t),t=(0,ct.unescape)(t).replace(/\[caption[^\]]*\](.*)\[\/caption\]/g,"");var e=(0,ct.filter)(St(t,!0),(function(t){return""!==t.trim()}));if(!e.length)return"";var r=rankMathEditor.getPrimaryKeyword();if(""!==r){var n=(0,ct.filter)(e,(function(t){return(0,ct.includes)(t.toLowerCase(),r.toLowerCase())}));if(0<n.length)return Pt(n[0],160)}return Pt(e[0],160)};function Et(t){return t=x.swap(""!==t?t:rankMath.assessor.serpData.titleTemplate),rankMathEditor.refresh("title"),R("serpTitle",Tt(t))}function It(t){return t=""!==t?t:rankMathEditor.assessor.dataCollector.getSlug(),rankMathEditor.refresh("permalink"),R("serpSlug",t)}function Ot(t){return t=x.swap(function(t){var e=rankMathEditor.assessor.dataCollector.getData(),r=e.excerpt,n=Dt(e.content),a=(0,ct.isUndefined)(r)||(0,ct.isEmpty)(r)?n:(0,ct.unescape)(r);if(x.setVariable("excerpt",a),x.setVariable("seo_description",a),""!==(t=Tt((0,p.applyFilters)("rankMath/description",t))))return gt(t);if(!(0,ct.isUndefined)(r)&&!(0,ct.isEmpty)(r))return gt(r);var i=(0,ct.unescape)(rankMath.assessor.serpData.descriptionTemplate);return(0,ct.isUndefined)(i)||""===i?n:gt(i)}(t)),rankMathEditor.refresh("description"),R("serpDescription",t)}function At(t){return R("isSnippetEditorOpen",t)}function Mt(t){return R("snippetPreviewType",t)}function xt(t){return j("twitterUseFacebook",t,"rank_math_twitter_use_facebook",!0===t?"on":"off")}function jt(t){return j("twitterCardType",t,"rank_math_twitter_card_type")}function Rt(t){return j("twitterTitle",t,"rank_math_twitter_title")}function Ct(t){return j("twitterDescription",t,"rank_math_twitter_description")}function Bt(t){return j("twitterAuthor",t,"rank_math_twitter_author")}function Ut(t){return j("twitterImageID",t,"rank_math_twitter_image_id")}function Lt(t){return j("twitterImage",t,"rank_math_twitter_image")}function Nt(t){return j("twitterHasOverlay",t,"rank_math_twitter_enable_image_overlay",!0===t?"on":"off")}function Ft(t){return j("twitterImageOverlay",t,"rank_math_twitter_image_overlay")}function $t(t){return j("twitterPlayerUrl",t,"rank_math_twitter_player_url")}function Kt(t){return j("twitterPlayerSize",t,"rank_math_twitter_player_size")}function Gt(t){return j("twitterPlayerStream",t,"rank_math_twitter_player_stream")}function zt(t){return j("twitterPlayerStreamCtype",t,"rank_math_twitter_player_stream_ctype")}function Ht(t){return j("twitterAppDescription",t,"rank_math_twitter_app_description")}function Vt(t){return j("twitterAppIphoneID",t,"rank_math_twitter_app_iphone_id")}function qt(t){return j("twitterAppIphoneName",t,"rank_math_twitter_app_iphone_name")}function Qt(t){return j("twitterAppIphoneUrl",t,"rank_math_twitter_app_iphone_url")}function Wt(t){return j("twitterAppIpadID",t,"rank_math_twitter_app_ipad_id")}function Yt(t){return j("twitterAppIpadName",t,"rank_math_twitter_app_ipad_name")}function Jt(t){return j("twitterAppIpadUrl",t,"rank_math_twitter_app_ipad_url")}function Xt(t){return j("twitterAppGoogleplayID",t,"rank_math_twitter_app_googleplay_id")}function Zt(t){return j("twitterAppGoogleplayName",t,"rank_math_twitter_app_googleplay_name")}function te(t){return j("twitterAppGoogleplayUrl",t,"rank_math_twitter_app_googleplay_url")}function ee(t){return j("twitterAppCountry",t,"rank_math_twitter_app_country")}function re(t){return R("isLoaded",t)}function ne(t){return R("selectedKeyword",t)}function ae(t){return R("socialTab",t)}function ie(){return R("refreshResults",Date.now())}function oe(){return R("isPro",!0)}function ue(t){return R("isDiviPageSettingsBarActive",t)}function ce(t){return R("isDiviRankMathModalActive",t)}function se(t){return R("highlightedParagraphs",t)}var le=wp.i18n;function pe(t){return pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pe(t)}function fe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function he(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fe(Object(r),!0).forEach((function(e){me(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fe(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function me(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==pe(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==pe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===pe(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function de(t){return R("isSchemaEditorOpen",t)}function ge(t){return R("isSchemaTemplatesOpen",t)}function ye(t){return R("schemaUpdated",t)}function ve(t){return R("editingSchemaId",t)}function be(t){return R("editorTab",t)}function we(t){return R("templateTab",t)}function ke(t){return R("editSchemas",t)}function Se(t){return j("schemas",t)}function _e(t,e){var r=he({},(0,c.select)("rank-math").getEditSchemas());return r[t]=e,R("editSchemas",r)}function Te(t,e){var r=he({},(0,c.select)("rank-math").getSchemas());return r[t]=e,j("schemas",r)}function Pe(t){var e=he({},(0,c.select)("rank-math").getSchemas());return delete e[t],(0,p.doAction)("rank_math_schema_trash",t),j("schemas",e,"rank_math_delete_"+t,"")}function De(t,e,r){return u()({method:"POST",path:"rankmath/v1/saveTemplate",data:{schema:t,postId:r}}).then((function(r){e({loading:!1,showNotice:!0,postId:r.id}),setTimeout((function(){e({showNotice:!1}),(0,ct.get)(rankMath,"isTemplateScreen",!1)&&(document.title=(0,le.__)("Edit Schema","rank-math"),window.history.pushState(null,"",r.link.replace(/&amp;/g,"&")))}),2e3),rankMath.schemaTemplates.push({schema:t,title:t.metadata.title,type:t["@type"]})})),e({loading:!0}),{type:"DONT_WANT_TO_DO_SOMETHING"}}function Ee(t){return j("contentAIScore",t,"rank_math_contentai_score",t)}function Ie(t){return Ie="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ie(t)}function Oe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ae(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Oe(Object(r),!0).forEach((function(e){Me(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Oe(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Me(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Ie(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Ie(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Ie(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var xe=function(t){var e=t.assessor.serpData,r=t.assessor.hasRedirection;return{postID:null,title:e.title?e.title:e.titleTemplate,description:e.description,keywords:e.focusKeywords?e.focusKeywords:"",pillarContent:e.pillarContent,featuredImage:"",permalink:!1,primaryTerm:e.primaryTerm,robots:e.robots,advancedRobots:e.advancedRobots,canonicalUrl:e.canonicalUrl,breadcrumbTitle:e.breadcrumbTitle,showScoreFrontend:e.showScoreFrontend,lockModifiedDate:e.lockModifiedDate,redirectionID:r?(0,ct.get)(t.assessor,"redirection.id",""):"",redirectionType:r?(0,ct.get)(t.assessor,"redirection.header_code",""):"",redirectionUrl:r?(0,ct.get)(t.assessor,"redirection.url_to",""):"",facebookTitle:e.facebookTitle,facebookImage:e.facebookImage,facebookImageID:e.facebookImageID,facebookAuthor:e.facebookAuthor,facebookDescription:e.facebookDescription,facebookHasOverlay:e.facebookHasOverlay,facebookImageOverlay:e.facebookImageOverlay,twitterTitle:e.twitterTitle,twitterImage:e.twitterImage,twitterAuthor:e.twitterAuthor,twitterImageID:e.twitterImageID,twitterCardType:e.twitterCardType,twitterUseFacebook:e.twitterUseFacebook,twitterDescription:e.twitterDescription,twitterHasOverlay:e.twitterHasOverlay,twitterImageOverlay:e.twitterImageOverlay,twitterPlayerUrl:e.twitterPlayerUrl,twitterPlayerSize:e.twitterPlayerSize,twitterPlayerStream:e.twitterPlayerStream,twitterPlayerStreamCtype:e.twitterPlayerStreamCtype,twitterAppDescription:e.twitterAppDescription,twitterAppIphoneName:e.twitterAppIphoneName,twitterAppIphoneID:e.twitterAppIphoneID,twitterAppIphoneUrl:e.twitterAppIphoneUrl,twitterAppIpadName:e.twitterAppIpadName,twitterAppIpadID:e.twitterAppIpadID,twitterAppIpadUrl:e.twitterAppIpadUrl,twitterAppGoogleplayName:e.twitterAppGoogleplayName,twitterAppGoogleplayID:e.twitterAppGoogleplayID,twitterAppGoogleplayUrl:e.twitterAppGoogleplayUrl,twitterAppCountry:e.twitterAppCountry,schemas:(0,ct.get)(t,"schemas",{}),score:0,dirtyMetadata:{}}};function je(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:xe(rankMath),e=arguments.length>1?arguments[1]:void 0,r=Ae({},t.dirtyMetadata);return!1!==e.metaKey&&(r[e.metaKey]=e.metaValue),"RESET_STORE"===e.type?Ae({},xe(e.value)):"RANK_MATH_APP_DATA"===e.type?"dirtyMetadata"===e.key?Ae(Ae({},t),{},{dirtyMetadata:e.value}):Ae(Ae({},t),{},Me(Me({},e.key,e.value),"dirtyMetadata",r)):t}function Re(t){return Re="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Re(t)}function Ce(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Be(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ce(Object(r),!0).forEach((function(e){Ue(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ce(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ue(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Re(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Re(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Re(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Le={isLoaded:!1,isPro:!1,selectedKeyword:{tag:"",index:0,data:{value:""}},hasRedirect:rankMath.assessor.hasRedirection&&!(0,ct.isEmpty)((0,ct.get)(rankMath.assessor,"redirection.id",""))&&!(0,ct.isEmpty)((0,ct.get)(rankMath.assessor,"redirection.url_to","")),serpTitle:"",serpSlug:"",serpDescription:(0,ct.get)(rankMath.assessor,"serpData.description",""),isSnippetEditorOpen:!1,snippetPreviewType:"",refreshResults:"",redirectionItem:{},socialTab:"facebook",highlightedParagraphs:[],editorTab:"",templateTab:"",editSchemas:{},editingSchemaId:"",isSchemaEditorOpen:!1,isSchemaTemplatesOpen:!1,schemaUpdated:!1,isDiviRankMathModalActive:!1,isDiviPageSettingsBarActive:!1};function Ne(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Le,e=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===e.type?Be(Be({},t),{},Ue({},e.key,e.value)):t}function Fe(t){return t.appData}function $e(t){return t.appData.dirtyMetadata}function Ke(t){return t.appData.score}function Ge(t){return t.appData.keywords}function ze(t){return t.appData.pillarContent}function He(t){return t.appData.robots}function Ve(t){return t.appData.advancedRobots}function qe(t){return t.appData.canonicalUrl}function Qe(t){return t.appData.breadcrumbTitle}function We(t){return"todo"}function Ye(t){return t.appData.showScoreFrontend}function Je(t){return t.appData.lockModifiedDate}function Xe(t){return t.appUi.isLoaded}function Ze(t){return t.appUi.selectedKeyword}function tr(t){return t.appUi.socialTab}function er(t){return t.appUi.refreshResults}function rr(t){return t.appUi.isPro}function nr(t){return t.appUi.isDiviPageSettingsBarActive}function ar(t){return t.appUi.isDiviRankMathModalActive}function ir(t){return t.appUi.highlightedParagraphs}function or(t){return t.appData.facebookTitle}function ur(t){return t.appData.facebookDescription}function cr(t){return t.appData.facebookAuthor}function sr(t){return t.appData.facebookImageID}function lr(t){return t.appData.facebookImage}function pr(t){return t.appData.facebookHasOverlay}function fr(t){return""!==t.appData.facebookImageOverlay?t.appData.facebookImageOverlay:"play"}function hr(t){return t.appData.postID}function mr(t){return t.appData.title}function dr(t){return t.appData.permalink}function gr(t){return t.appData.description}function yr(t){return t.appData.featuredImage}function vr(t){var e=t.appData.featuredImage;return'<img src="'.concat(e.source_url,'" alt="').concat(e.alt_text,'" >')}function br(t){return t.appData.primaryTerm}function wr(t){return String(t.appData.redirectionID)}function kr(t){return t.appData.redirectionType}function Sr(t){return t.appData.redirectionUrl}function _r(t){return t.appUi.redirectionItem}function Tr(t){return t.appUi.hasRedirect}var Pr=wp.url;function Dr(t){return Tt(t.appUi.serpTitle)}function Er(t){return(0,Pr.safeDecodeURIComponent)(t.appUi.serpSlug)}function Ir(t){return t.appUi.serpDescription}function Or(t){return t.appUi.isSnippetEditorOpen}function Ar(t){return t.appUi.snippetPreviewType}function Mr(t){return t.appUi.isSchemaEditorOpen}function xr(t){return t.appUi.isSchemaTemplatesOpen}function jr(t){return t.appUi.schemaUpdated}function Rr(t){return t.appData.schemas}function Cr(t){return t.appUi.editSchemas}function Br(t){return{id:t.appUi.editingSchemaId,data:t.appUi.editSchemas[t.appUi.editingSchemaId]}}function Ur(t){return t.appData.schemas[t.appUi.editingSchemaId]}function Lr(t){return t.appUi.editorTab}function Nr(t){return t.appUi.templateTab}function Fr(t){return t.appData.twitterUseFacebook}function $r(t){return t.appData.twitterCardType}function Kr(t){return t.appData.twitterTitle}function Gr(t){return t.appData.twitterDescription}function zr(t){return t.appData.twitterAuthor}function Hr(t){return t.appData.twitterImageID}function Vr(t){return t.appData.twitterImage}function qr(t){return t.appData.twitterHasOverlay}function Qr(t){return""!==t.appData.twitterImageOverlay?t.appData.twitterImageOverlay:"play"}function Wr(t){return t.appData.twitterAppDescription}function Yr(t){return t.appData.twitterAppIphoneID}function Jr(t){return t.appData.twitterAppIphoneName}function Xr(t){return t.appData.twitterAppIphoneUrl}function Zr(t){return t.appData.twitterAppIpadID}function tn(t){return t.appData.twitterAppIpadName}function en(t){return t.appData.twitterAppIpadUrl}function rn(t){return t.appData.twitterAppGoogleplayID}function nn(t){return t.appData.twitterAppGoogleplayName}function an(t){return t.appData.twitterAppGoogleplayUrl}function on(t){return t.appData.twitterAppCountry}function un(t){return t.appData.twitterPlayerUrl}function cn(t){return t.appData.twitterPlayerSize}function sn(t){return t.appData.twitterPlayerStream}function ln(t){return t.appData.twitterPlayerStreamCtype}var pn=(0,c.registerStore)("rank-math",{reducer:(0,c.combineReducers)(e),selectors:n,actions:t});function fn(){return pn}var hn={};(0,ct.isUndefined)(rankMath.assessor)||(0,ct.forEach)(rankMath.assessor.diacritics,(function(t,e){return hn[e]=new RegExp(t,"g")}));var mn=function(t){if((0,ct.isUndefined)(t))return t;for(var e in hn)t=t.replace(hn[e],e);return t},dn={"&amp;":"&","&quot;":'"',"&#39;":"'"},gn=/&(?:amp|quot|#(0+)?39);/g,yn=RegExp(gn.source);var vn=function(t){return t&&yn.test(t)?t.replace(gn,(function(t){return dn[t]||"'"})):t||""};function bn(t){return bn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bn(t)}function wn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(a=n.key,i=void 0,i=function(t,e){if("object"!==bn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==bn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(a,"string"),"symbol"===bn(i)?i:String(i)),n)}var a,i}var kn=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.analyzer=new I.Analyzer({i18n:le,analyses:rankMath.assessor.researchesTests}),this.dataCollector=e,this.registerRefresh(),this.updateKeywordResult=this.updateKeywordResult.bind(this),this.sanitizeData=this.sanitizeData.bind(this),(0,p.addAction)("rankMath_analysis_keywordUsage_updated","rank-math",this.updateKeywordResult),(0,p.addFilter)("rank_math_sanitize_meta_value","rank-math",this.sanitizeData),(0,p.addFilter)("rank_math_sanitize_data","rank-math",this.sanitizeData)}var e,r,n;return e=t,r=[{key:"updateKeywordResult",value:function(t,e){rankMathEditor.resultManager.update(t,{keywordNotUsed:e}),t===this.getSelectedKeyword().toLowerCase()&&(0,c.dispatch)("rank-math").refreshResults()}},{key:"sanitizeData",value:function(t,e){return"schemas"===e||(0,ct.isObject)(t)||(0,ct.isEmpty)(t)?t:(r=t,(0,ct.isUndefined)(r)?"":(0,ct.flow)([lt,ft,st,mt])(r));var r}},{key:"getPaper",value:function(t,e){var r=fn().getState(),n=this.dataCollector.getData(),a=new I.Paper("",{locale:rankMath.localeFull});a.setTitle(r.appUi.serpTitle),a.setPermalink(n.slug),a.setDescription(r.appUi.serpDescription),a.setUrl(n.permalink),a.setText(vn((0,p.applyFilters)("rank_math_content",n.content))),a.setKeyword(t),a.setKeywords(e),a.setSchema(r.appData.schemas),(0,ct.isUndefined)(n.featuredImage)||(a.setThumbnail(n.featuredImage.source_url),a.setThumbnailAltText(mn(n.featuredImage.alt_text)));var i=(0,c.select)("rank-math-content-ai");if(!(0,ct.isEmpty)(i)){var o=i.getData(),u=i.getScore();a.setContentAI(u||!(0,ct.isEmpty)(o.keyword))}return a}},{key:"registerRefresh",value:function(){var t=this;this.refresh=(0,ct.debounce)((function(e){var r=fn().getState();if(!1!==r.appUi.isLoaded){var n=r.appData.keywords.split(","),a=[];(0,p.doAction)("rank_math_"+e+"_refresh"),n.map((function(e,r){var i=t.getPaper(mn(e),n),o=0===r?rankMath.assessor.researchesTests:t.filterTests(t.getSecondaryKeywordTests());a.push(t.analyzer.analyzeSome(o,i).then((function(t){rankMathEditor.resultManager.update(i.getKeyword(),t,0===r),0===r&&(0,c.dispatch)("rank-math").updateAnalysisScore(rankMathEditor.resultManager.getScore(i.getKeyword()))}))),Promise.all(a).then((function(){(0,c.dispatch)("rank-math").refreshResults()}))}))}}),500)}},{key:"getSecondaryKeywordTests",value:function(){return["keywordInContent","lengthContent","keywordInSubheadings","keywordDensity","lengthPermalink","linksHasExternals","linksNotAllExternals","linksHasInternal","titleSentiment","titleHasPowerWords","titleHasNumber","contentHasTOC","contentHasShortParagraphs","contentHasAssets"]}},{key:"getPrimaryKeyword",value:function(){var t=fn().getState().appData.keywords;return t?mn(t.split(",")[0]):""}},{key:"getSelectedKeyword",value:function(){var t=fn().getState(),e=""!==t.appUi.selectedKeyword.data.value?t.appUi.selectedKeyword.data.value:t.appData.keywords.split(",")[0];return mn(e)}},{key:"getResearch",value:function(t){return this.analyzer.researcher.getResearch(t)}},{key:"filterTests",value:function(t){return(0,ct.intersection)(t,rankMath.assessor.researchesTests)}}],r&&wn(e.prototype,r),n&&wn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),Sn=kn,_n=function(){return!(0,ct.isNull)(document.getElementById("site-editor"))&&(0,ct.endsWith)(wp.data.select("core/edit-site").getEditedPostId(),"//home")};function Tn(t){return Tn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tn(t)}function Pn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(a=n.key,i=void 0,i=function(t,e){if("object"!==Tn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Tn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(a,"string"),"symbol"===Tn(i)?i:String(i)),n)}var a,i}var Dn=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),(rankMath.is_front_page||_n())&&((0,p.addFilter)("rankMath_analysis_contentLength","rank-math",this.contentLength),(0,p.addFilter)("rankMath_analysis_contentLength_boundaries","rank-math",this.contentLengthBoundary))}var e,r,n;return e=t,(r=[{key:"contentLength",value:function(t){return{hasScore:t.hasScore,failed:(0,le.__)("Content is %1$d words long. Consider using at least 300 words.","rank-math"),tooltipText:(0,le.__)("Minimum recommended content length should be 300 words.","rank-math"),emptyContent:(0,le.sprintf)((0,le.__)("Content should be %1$s long.","rank-math"),'<a href="https://rankmath.com/kb/score-100-in-tests/?utm_source=Plugin&utm_campaign=WP#overall-content-length" target="_blank">'+(0,le.__)("300 words","rank-math")+"</a>")}}},{key:"contentLengthBoundary",value:function(){return{recommended:{boundary:299,score:8},belowRecommended:{boundary:200,score:5},low:{boundary:50,score:2}}}}])&&Pn(e.prototype,r),n&&Pn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();function En(t){return En="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},En(t)}function In(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(a=n.key,i=void 0,i=function(t,e){if("object"!==En(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==En(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(a,"string"),"symbol"===En(i)?i:String(i)),n)}var a,i}var On=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,r,n;return e=t,(r=[{key:"setup",value:function(t){fn(),this.resultManager=new I.ResultManager,this.assessor=new Sn(t),new Dn,(0,p.doAction)("rank_math_loaded")}},{key:"refresh",value:function(t){this.assessor.refresh(t)}},{key:"getPrimaryKeyword",value:function(){return this.assessor.getPrimaryKeyword()}},{key:"getSelectedKeyword",value:function(){return this.assessor.getSelectedKeyword()}},{key:"updatePermalink",value:function(t){throw"Implement the function"}},{key:"updatePermalinkSanitize",value:function(t){throw"Implement the function"}}])&&In(e.prototype,r),n&&In(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();function An(t){return An="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},An(t)}function Mn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function xn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Mn(Object(r),!0).forEach((function(e){Un(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Mn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function jn(){jn=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof y?e:y,o=Object.create(i.prototype),u=new A(n||[]);return a(o,"_invoke",{value:D(t,r,u)}),o}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var f="suspendedStart",h="suspendedYield",m="executing",d="completed",g={};function y(){}function v(){}function b(){}var w={};s(w,o,(function(){return this}));var k=Object.getPrototypeOf,S=k&&k(k(M([])));S&&S!==r&&n.call(S,o)&&(w=S);var _=b.prototype=y.prototype=Object.create(w);function T(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function r(a,i,o,u){var c=p(t[a],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==An(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,o,u)}),(function(t){r("throw",t,o,u)})):e.resolve(l).then((function(t){s.value=t,o(s)}),(function(t){return r("throw",t,o,u)}))}u(c.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function D(e,r,n){var a=f;return function(i,o){if(a===m)throw new Error("Generator is already running");if(a===d){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var u=n.delegate;if(u){var c=E(u,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===f)throw a=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var s=p(e,r,n);if("normal"===s.type){if(a=n.done?d:h,s.arg===g)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=d,n.method="throw",n.arg=s.arg)}}}function E(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=p(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function M(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(An(e)+" is not iterable")}return v.prototype=b,a(_,"constructor",{value:b,configurable:!0}),a(b,"constructor",{value:v,configurable:!0}),v.displayName=s(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,c,"GeneratorFunction")),t.prototype=Object.create(_),t},e.awrap=function(t){return{__await:t}},T(P.prototype),s(P.prototype,u,(function(){return this})),e.AsyncIterator=P,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new P(l(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},T(_),s(_,c,"Generator"),s(_,o,(function(){return this})),s(_,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=M,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return u.type="throw",u.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],u=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;O(r)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:M(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function Rn(t,e,r,n,a,i,o){try{var u=t[i](o),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,a)}function Cn(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){Rn(i,n,a,o,u,"next",t)}function u(t){Rn(i,n,a,o,u,"throw",t)}o(void 0)}))}}function Bn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ln(n.key),n)}}function Un(t,e,r){return(e=Ln(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ln(t){var e=function(t,e){if("object"!==An(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==An(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===An(e)?e:String(e)}var Nn=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),Un(this,"initialize",!1),this.etSettingsData={title:"",excerpt:"",featuredImage:""},this._featuredImage=null,this._featuredImageInProcess=!1,this._data={id:!1,slug:!1,permalink:!1,content:!1,title:!1,excerpt:!1,featuredImage:!1},this.refresh=this.refresh.bind(this),this.savePost=this.savePost.bind(this),this.saveRedirection=this.saveRedirection.bind(this),this.saveSchemas=this.saveSchemas.bind(this),this.subscribeToDivi()}var e,r,n,a,o,s;return e=t,r=[{key:"collectPostData",value:function(){return{id:this.getPostID(),slug:this.getSlug(),permalink:this.getPermalink(),content:this.getContent(),title:this.getTitle(),excerpt:this.getExcerpt(),featuredImage:this.getFeaturedImage()}}},{key:"getPostID",value:function(){return parseInt((0,ct.get)(ETBuilderBackendDynamic,"postId",0))}},{key:"getTitle",value:function(){return this.etSettingsData.title}},{key:"setTitle",value:function(t){this.etSettingsData.title=t}},{key:"getContent",value:function(){var t=[];return this.getContentArea().find(".et_pb_section").each((function(){t.push(i()(this).html())})),t.join("")}},{key:"getContentArea",value:function(){if(this._etAppFrameElem||(this._etAppFrameElem=(0,ct.get)(ET_Builder,"Frames.app.frameElement",document.querySelector("iframe#et-fb-app-frame"))),!this._etAppFrameElem)return i()("<div />");var t=this._etAppFrameElem.contentWindow.document,e=i()((0,ct.isNull)(t.querySelector("#theme-builder-area--post_content"))?t.querySelector("body"):t.querySelector("#theme-builder-area--post_content"));return e.length<1?i()("<div />"):e}},{key:"getExcerpt",value:function(){return this.etSettingsData.excerpt}},{key:"setExcerpt",value:function(t){this.etSettingsData.excerpt=t}},{key:"getPermalink",value:function(){return rankMath.is_front_page?rankMath.homeUrl+"/":this.getSlug()?rankMath.permalinkFormat.replace(/%(postname|pagename)%/,this.getSlug()).trimRight("/"):""}},{key:"getSlug",value:function(){return(0,Pr.safeDecodeURIComponent)((0,c.select)("rank-math").getPermalink())}},{key:"getFeaturedImage",value:function(){if(this._featuredImage)return this._featuredImage;this.setFeaturedImage()}},{key:"setFeaturedImage",value:(s=Cn(jn().mark((function t(e){return jn().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0!==e){t.next=4;break}return t.next=3,this.fetchFeaturedImageId();case 3:e=t.sent;case 4:if(!this.isValidMediaId(e)||!e){t.next=10;break}return t.next=7,this.fetchWpMedia(e);case 7:this._featuredImage=t.sent,t.next=11;break;case 10:this._featuredImage=!1;case 11:case"end":return t.stop()}}),t,this)}))),function(t){return s.apply(this,arguments)})},{key:"fetchFeaturedImageId",value:(o=Cn(jn().mark((function t(){var e,r=this;return jn().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!this._featuredImageInProcess){t.next=2;break}return t.abrupt("return",this._featuredImage);case 2:return this._featuredImageInProcess=!0,e=null,t.next=6,u()({path:"/rankmath/v1/getFeaturedImageId",method:"POST",data:{postId:(0,ct.get)(ETBuilderBackendDynamic,"postId",0)}}).then((function(t){e=!!t.success&&t.featImgId,r._featuredImageInProcess=!1}));case 6:return t.abrupt("return",e);case 7:case"end":return t.stop()}}),t,this)}))),function(){return o.apply(this,arguments)})},{key:"fetchWpMedia",value:(a=Cn(jn().mark((function t(e){var r;return jn().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r={},t.next=3,u()({path:"/wp/v2/media/".concat(e),method:"GET"}).then((function(t){return r=t}));case 3:return t.abrupt("return",r);case 4:case"end":return t.stop()}}),t)}))),function(t){return a.apply(this,arguments)})},{key:"isValidMediaId",value:function(t){return"number"==typeof t&&0<t}},{key:"subscribeToDivi",value:function(){var t=this;this.setTitle((0,ct.get)(ETBuilderBackendDynamic,"postTitle","")),this.setExcerpt((0,ct.get)(ETBuilderBackendDynamic,"postMeta.post_excerpt","")),this.setFeaturedImage((0,ct.get)(ETBuilderBackendDynamic,"currentPage.thumbnailId",0)),(0,c.dispatch)("rank-math").updatePermalink(rankMath.postName),this.subscriber=(0,ct.debounce)(this.refresh,500),(0,c.subscribe)(this.subscriber),i()(".et-fb-page-settings-bar").find(".et-fb-button--save-draft, .et-fb-button--publish").on("click",(function(){setTimeout((function(){t.savePost(),t.saveRedirection(),t.saveSchemas()}),500)})),(0,p.addFilter)("et.builder.store.setting.update","rank-math",(function(e,r){if(e)switch(r){case"et_pb_post_settings_title":t.setTitle(e),t.subscriber();break;case"et_pb_post_settings_excerpt":t.setExcerpt(e),t.subscriber();break;case"et_pb_post_settings_image":t.setFeaturedImage(parseInt(e)),t.subscriber()}return e})),window.addEventListener("message",(function(e){"et_fb_section_content_change"===e.data.etBuilderEvent&&t.subscriber()}))}},{key:"refresh",value:function(){var t=xn({},this._data);this._data=this.collectPostData(),this.handleEditorChange(t),!(0,ct.isEqual)(t,this._data)&&t.id&&(0,c.dispatch)("rank-math").refreshResults()}},{key:"savePost",value:function(){var t=(0,c.select)("rank-math").getDirtyMetadata();(0,ct.isEmpty)(t)||(u()({method:"POST",path:"/rankmath/v1/updateMeta",data:{objectID:rankMath.objectID,objectType:rankMath.objectType,meta:t}}).then((function(t){(0,ct.isString)(t.slug)&&(0,c.dispatch)("rank-math").updatePermalink(t.slug),(0,p.doAction)("rank_math_metadata_updated",t)})),(0,c.dispatch)("rank-math").resetDirtyMetadata())}},{key:"saveRedirection",value:function(){var t=(0,c.select)("rank-math").getRedirectionItem();(0,ct.isEmpty)(t)||(t.objectID=this.getPostID(),t.redirectionSources=this.getPermalink(),u()({method:"POST",path:"/rankmath/v1/updateRedirection",data:t}).then((function(t){"delete"===t.action?(0,c.dispatch)("rank-math").updateRedirection("redirectionID",0):"new"===t.action&&(0,c.dispatch)("rank-math").updateRedirection("redirectionID",t.id)})),(0,c.dispatch)("rank-math").resetRedirection())}},{key:"saveSchemas",value:function(){var t=(0,c.select)("rank-math").getSchemas();if(!(0,ct.isEmpty)(t)&&!(0,ct.isEqual)(t,(0,ct.get)(rankMath,"schemas",{}))){var e=(0,c.select)("rank-math").getEditSchemas();u()({method:"POST",path:"/rankmath/v1/updateSchemas",data:{objectID:rankMath.objectID,objectType:rankMath.objectType,schemas:t}}).then((function(r){if((0,ct.isEmpty)(r))(0,c.dispatch)("rank-math").updateSchemas(t);else{var n=xn({},t),a=xn({},e);(0,ct.map)(r,(function(t,e){n["schema-"+t]=xn({},n[e]),a["schema-"+t]=xn({},a[e]),delete n[e],delete a[e]})),(0,c.dispatch)("rank-math").updateSchemas(n),(0,c.dispatch)("rank-math").updateEditSchemas(a)}}))}}},{key:"handleEditorChange",value:function(t){var e=this,r={id:"handleIDChange",slug:"handleSlugChange",title:"handleTitleChange",excerpt:"handleExcerptChange",content:"handleContentChange",featuredImage:"handleFeaturedImageChange"};if((0,ct.isInteger)(t.id))return this.initialize?void(0,ct.forEach)(r,(function(r,n){e._data[n]!==t[n]&&e[r](e._data[n])})):(this.initialize=!0,(0,ct.forEach)(r,(function(t,r){e[t](e._data[r])})),void rankMathEditor.refresh("init"));(0,c.dispatch)("rank-math").refreshResults()}},{key:"handleIDChange",value:function(t){(0,c.dispatch)("rank-math").updatePostID(t),(0,c.dispatch)("rank-math").toggleLoaded(!0)}},{key:"handleSlugChange",value:function(){rankMathEditor.refresh("permalink")}},{key:"handleTitleChange",value:function(t){x.setVariable("title",t),(0,c.dispatch)("rank-math").updateSerpTitle((0,c.select)("rank-math").getTitle()),rankMathEditor.refresh("title")}},{key:"handleExcerptChange",value:function(t){x.setVariable("excerpt",t),x.setVariable("excerpt_only",t),x.setVariable("wc_shortdesc",t),x.setVariable("seo_description",t),(0,c.dispatch)("rank-math").updateSerpDescription((0,c.select)("rank-math").getDescription())}},{key:"handleFeaturedImageChange",value:function(t){(0,c.dispatch)("rank-math").updateFeaturedImage(t),rankMathEditor.refresh("featuredImage")}},{key:"handleContentChange",value:function(){(0,c.dispatch)("rank-math").updateSerpDescription((0,c.select)("rank-math").getDescription()),rankMathEditor.refresh("content")}},{key:"getData",value:function(){return this._data}}],r&&Bn(e.prototype,r),n&&Bn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),Fn=Nn,$n=r(4184),Kn=r.n($n),Gn=function(){return wp.element.createElement("span",{className:"rank-math-rm-modal-toggle-button-icon",style:{display:"block",fill:"rgb(255, 255, 255)",width:"0px",height:"0px",marginTop:"-10px",marginLeft:"-1px"}},wp.element.createElement("svg",{viewBox:"0 0 462.03 462.03",xmlns:"http://www.w3.org/2000/svg",width:"20"},wp.element.createElement("g",null,wp.element.createElement("path",{d:"m462 234.84-76.17 3.43 13.43 21-127 81.18-126-52.93-146.26 60.97 10.14 24.34 136.1-56.71 128.57 54 138.69-88.61 13.43 21z"}),wp.element.createElement("path",{d:"m54.1 312.78 92.18-38.41 4.49 1.89v-54.58h-96.67zm210.9-223.57v235.05l7.26 3 89.43-57.05v-181zm-105.44 190.79 96.67 40.62v-165.19h-96.67z"}))))},zn=function(){return wp.element.createElement("button",{type:"button","data-tip":"Rank Math SEO",onClick:function(){(0,c.dispatch)("rank-math").toggleIsDiviRankMathModalActive(!(0,c.select)("rank-math").isDiviRankMathModalActive())},className:Kn()("rank-math-rm-modal-toggle-button","et_fb_ignore_iframe","et-fb-button","et-fb-button--elevate","et-fb-button--primary","et-fb-button--round","et-fb-button--Tooltip"),style:{width:"40px",height:"40px"}},wp.element.createElement(Gn,null))},Hn=function(){return wp.element.createElement("div",{className:"rank-math-rm-settings-bar"},wp.element.createElement(zn,null))};function Vn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,a=t}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return qn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return qn(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Qn=function(t){var e=Vn((0,l.useState)(!1),2),r=e[0],n=e[1];return(0,l.useEffect)((function(){(0,c.dispatch)("rank-math").lockModifiedDate(!1)}),[]),wp.element.createElement(React.Fragment,null,wp.element.createElement(f.Button,{className:"et-fb-button--success",variant:"tertiary",onClick:function(){return n(!r)}},wp.element.createElement(f.Dashicon,{icon:r?"arrow-down-alt2":"arrow-up-alt2"})),r&&wp.element.createElement(f.Button,{className:"et-fb-button--success rank-math-lock-modified-date",variant:"tertiary",onClick:function(){(0,c.dispatch)("rank-math").lockModifiedDate(!0);var e=window.ET_Builder.Frames.app.frameElement.contentWindow.ETBuilderBackend.conditionalTags;e.lock_modified_date=!0,t.publishButton.click(),n(!1),setTimeout((function(){delete e.lock_modified_date,(0,c.dispatch)("rank-math").lockModifiedDate(!1)}),3e3)}},(0,le.__)("Save (Lock Modified Date)","rank-math")))};function Wn(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(!t)return;if("string"==typeof t)return Yn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Yn(t,e)}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,a=function(){};return{s:a,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return o=t.done,t},e:function(t){u=!0,i=t},f:function(){try{o||null==r.return||r.return()}finally{if(u)throw i}}}}function Yn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Jn={init:function(){this.cacheProps(),this.toggleBodyClasses(),this.initSettingsBar(),this.addEventListeners(),this.initLockModifiedDate()},cacheProps:function(){this.$document=i()(document),this.$body=i()("body"),this.publishButton=i()(".et-fb-button--publish"),this.rmModalHiddingTimer=null,this.rmPrevModalActiveState=!1,this.rmSettingsBarMediaQuery=window.matchMedia("(min-width: 768px)"),this.rmSettingsBarRootSelector="#rank-math-rm-settings-bar-root",this.$rmSettingsBarRoot=i()(this.rmSettingsBarRootSelector).detach(),this.$etPageSettingsBar=i()(".et-fb-page-settings-bar"),this.$etPageSettingsBarToggleButton=this.$etPageSettingsBar.find(".et-fb-page-settings-bar__toggle-button"),this.$etPageSettingsBarColumn=this.$etPageSettingsBar.find(".et-fb-page-settings-bar__column"),this.etSettingsBarObserver=new MutationObserver(this.onEtSettingsBarClassAttrChange.bind(this))},toggleBodyClasses:function(){var t=this.isEtSettingsBarActive();this.$body.toggleClass("rank-math-et-settings-bar-is-active",t),this.$body.toggleClass("rank-math-et-settings-bar-is-inactive",!t)},initSettingsBar:function(){var t=this.getEtSettingsBarPosition();this.onRmSettingsBarMediaQueryChange(),(0,l.createRoot)(this.$rmSettingsBarRoot[0]).render(wp.element.createElement(Hn,null)),this.removePositionalClassNames(this.$body,"rank-math-et-settings-bar-is"),this.$body.addClass("rank-math-et-settings-bar-is-".concat(t)),this.attachRmSettingsBar(t)},initLockModifiedDate:function(){rankMath.showLockModifiedDate&&(this.publishButton.after('<div id="rank-math-lock-modified-date-wrapper"></div>'),(0,l.createRoot)(document.getElementById("rank-math-lock-modified-date-wrapper")).render(wp.element.createElement(Qn,{publishButton:this.publishButton})))},addEventListeners:function(){this.$document.on("click",this.onDocumentClick.bind(this)),this.rmSettingsBarMediaQuery.addListener(this.onRmSettingsBarMediaQueryChange.bind(this)),this.etSettingsBarObserver.observe(this.$etPageSettingsBar[0],{attributeFilter:["class"]})},onDocumentClick:function(t){this.hideModalOnOutsideClick(t.target)},onRmSettingsBarMediaQueryChange:function(){this.detachRmSettingsBar(),this.attachRmSettingsBar(this.getEtSettingsBarPosition())},onEtSettingsBarClassAttrChange:function(){var t=this.isEtSettingsBarActive(),e=this.getEtSettingsBarPosition();this.removePositionalClassNames(this.$body,"rank-math-et-settings-bar-is"),this.$body.addClass("rank-math-et-settings-bar-is-".concat(e)),(0,c.dispatch)("rank-math").toggleIsDiviPageSettingsBarActive(t),this.toggleBodyClasses(),this.detachRmSettingsBar(),this.isEtSettingsBarDragged()?(this.rmPrevModalActiveState=(0,c.select)("rank-math").isDiviRankMathModalActive(),this.rmPrevModalActiveState&&(this.rmModalHiddingTimer=setTimeout((function(){(0,c.dispatch)("rank-math").toggleIsDiviRankMathModalActive(!1)}),200))):(clearTimeout(this.rmModalHiddingTimer),this.attachRmSettingsBar(e),(0,c.dispatch)("rank-math").toggleIsDiviRankMathModalActive(this.rmPrevModalActiveState||(0,c.select)("rank-math").isDiviRankMathModalActive()),this.rmPrevModalActiveState=!1)},attachRmSettingsBar:function(t){this.isRmSettingsBarAttached()||(this.toggleRmSettingsBarClassNames(t),this.isEtSettingsBarActive()?this.rmSettingsBarMediaQuery.matches?this.$etPageSettingsBarColumn.filter(".et-fb-page-settings-bar__column--main").append(this.$rmSettingsBarRoot):this.$etPageSettingsBarColumn.filter(".et-fb-page-settings-bar__column--left").prepend(this.$rmSettingsBarRoot):this.$etPageSettingsBarToggleButton.after(this.$rmSettingsBarRoot))},detachRmSettingsBar:function(){this.isRmSettingsBarAttached()&&(this.$rmSettingsBarRoot=this.$etPageSettingsBar.find(this.rmSettingsBarRootSelector).detach())},toggleRmSettingsBarClassNames:function(t){this.removePositionalClassNames(this.$rmSettingsBarRoot),this.$rmSettingsBarRoot.addClass("rank-math-rm-settings-bar-root-".concat(t)),this.$rmSettingsBarRoot.toggleClass(["rank-math-rm-settings-bar-root-is-mobile","rank-math-rm-settings-bar-root-is-mobile-".concat(t)].join(" "),!this.rmSettingsBarMediaQuery.matches),this.$rmSettingsBarRoot.toggleClass(["rank-math-rm-settings-bar-root-is-desktop","rank-math-rm-settings-bar-root-is-desktop-".concat(t)].join(" "),this.rmSettingsBarMediaQuery.matches)},isRmSettingsBarAttached:function(){return i().contains(document.documentElement,this.$rmSettingsBarRoot[0])},isEtSettingsBarActive:function(){return this.$etPageSettingsBar.hasClass("et-fb-page-settings-bar--active")},isEtSettingsBarDragged:function(){return this.$etPageSettingsBar.hasClass("et-fb-page-settings-bar--dragged")&&!this.isEtSettingsBarActive()},removePositionalClassNames:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=["".concat(e,"-left"),"".concat(e,"-right"),"".concat(e,"-top"),"".concat(e,"-top-left"),"".concat(e,"-top-right"),"".concat(e,"-bottom"),"".concat(e,"-bottom-left"),"".concat(e,"-bottom-right")].join("|"),n=new RegExp("(".concat(r,")$"),"gim");t.removeClass((function(t,e){var r,a=[],i=Wn(e.split(" "));try{for(i.s();!(r=i.n()).done;){var o=r.value;n.test(o)&&a.push(o)}}catch(t){i.e(t)}finally{i.f()}return a}))},getEtSettingsBarPosition:function(){var t=this.$etPageSettingsBar;return t.hasClass("et-fb-page-settings-bar--horizontal")&&!t.hasClass("et-fb-page-settings-bar--top")?"bottom":t.hasClass("et-fb-page-settings-bar--top")&&!t.hasClass("et-fb-page-settings-bar--corner")?"top":t.hasClass("et-fb-page-settings-bar--bottom-corner")?t.hasClass("et-fb-page-settings-bar--left-corner")?"bottom-left":"bottom-right":t.hasClass("et-fb-page-settings-bar--top-corner")?t.hasClass("et-fb-page-settings-bar--left-corner")?"top-left":"top-right":t.hasClass("et-fb-page-settings-bar--vertical--right")?"right":t.hasClass("et-fb-page-settings-bar--vertical--left")?"left":""},hideModalOnOutsideClick:function(t){if((0,c.select)("rank-math").isDiviRankMathModalActive()){var e=".rank-math-rm-modal";i()(t).parents(e)||t.closest(".components-modal__screen-overlay.rank-math-modal-overlay")||t.closest(".rank-math-rm-modal-toggle-button")||t.contains(document.querySelector(e))||(0,c.dispatch)("rank-math").toggleIsDiviRankMathModalActive(!1)}}},Xn=(0,c.withSelect)((function(t){return{primaryTermId:t("rank-math").getPrimaryTermID()}}))((function(t){var e=t.taxonomySlug,r=t.primaryTermId,n=t.options;if(n.length<2||!e)return null;return wp.element.createElement(f.SelectControl,{className:"rank-math-primary-term-select",label:(0,le.__)("Select Primary Term","rank-math"),value:r,options:n,onChange:function(t){(0,c.dispatch)("rank-math").updatePrimaryTermID(parseInt(t),e)}})}));function Zn(t){return Zn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zn(t)}function ta(){ta=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof y?e:y,o=Object.create(i.prototype),u=new A(n||[]);return a(o,"_invoke",{value:D(t,r,u)}),o}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var f="suspendedStart",h="suspendedYield",m="executing",d="completed",g={};function y(){}function v(){}function b(){}var w={};s(w,o,(function(){return this}));var k=Object.getPrototypeOf,S=k&&k(k(M([])));S&&S!==r&&n.call(S,o)&&(w=S);var _=b.prototype=y.prototype=Object.create(w);function T(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function r(a,i,o,u){var c=p(t[a],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==Zn(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,o,u)}),(function(t){r("throw",t,o,u)})):e.resolve(l).then((function(t){s.value=t,o(s)}),(function(t){return r("throw",t,o,u)}))}u(c.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function D(e,r,n){var a=f;return function(i,o){if(a===m)throw new Error("Generator is already running");if(a===d){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var u=n.delegate;if(u){var c=E(u,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===f)throw a=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var s=p(e,r,n);if("normal"===s.type){if(a=n.done?d:h,s.arg===g)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=d,n.method="throw",n.arg=s.arg)}}}function E(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=p(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function M(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(Zn(e)+" is not iterable")}return v.prototype=b,a(_,"constructor",{value:b,configurable:!0}),a(b,"constructor",{value:v,configurable:!0}),v.displayName=s(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,c,"GeneratorFunction")),t.prototype=Object.create(_),t},e.awrap=function(t){return{__await:t}},T(P.prototype),s(P.prototype,u,(function(){return this})),e.AsyncIterator=P,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new P(l(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},T(_),s(_,c,"Generator"),s(_,o,(function(){return this})),s(_,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=M,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return u.type="throw",u.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],u=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;O(r)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:M(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function ea(t,e,r,n,a,i,o){try{var u=t[i](o),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,a)}function ra(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){ea(i,n,a,o,u,"next",t)}function u(t){ea(i,n,a,o,u,"throw",t)}o(void 0)}))}}function na(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ia(n.key),n)}}function aa(t,e,r){return(e=ia(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ia(t){var e=function(t,e){if("object"!==Zn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Zn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Zn(e)?e:String(e)}var oa=function(){function t(){var e=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),i()(document).on("click",".et-fb-button--toggle-setting",(function(){clearTimeout(e.initRecursionTimer),e.attemptsRun=0,e.init.call(e)}))}var e,r,n,a,o;return e=t,r=[{key:"init",value:(o=ra(ta().mark((function t(){return ta().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0!==this.hasPostPrimaryTaxonomySupport){t.next=3;break}return t.next=3,this.cacheTaxonomyData();case 3:if(this.hasPostPrimaryTaxonomySupport){t.next=5;break}return t.abrupt("return");case 5:if(this.attemptsRun++,!(this.attemptsRun>this.maxAttempts)){t.next=8;break}return t.abrupt("return");case 8:this.cacheDom()?(this.renderContainer(),this.renderComponent(),this.bindEvents()):this.initRecursionTimer=setTimeout(this.init.bind(this),this.attemptInterval);case 10:case"end":return t.stop()}}),t,this)}))),function(){return o.apply(this,arguments)})},{key:"cacheTaxonomyData",value:(a=ra(ta().mark((function t(){var e,r,n=this;return ta().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=(0,ct.get)(rankMath,"assessor.primaryTaxonomy.name")||"",r=(0,ct.get)(ETBuilderBackendDynamic,"getTaxonomies",{}),this.primaryTaxonomyTerms=r[e]||[],t.next=4,u()({path:"/wp/v2/taxonomies"}).then((function(t){n.primaryTaxonomy=Object.keys(r).includes(e)&&t[e]?t[e]:{}}));case 4:this.hasPostPrimaryTaxonomySupport=!i().isEmptyObject(this.primaryTaxonomy),this.primaryTaxonomyValue=(0,ct.get)(ETBuilderBackendDynamic,"pageSettingsValues.et_pb_post_settings_".concat(this.primaryTaxonomy.rest_base),"");case 6:case"end":return t.stop()}}),t,this)}))),function(){return a.apply(this,arguments)})},{key:"cacheDom",value:function(){return this.$diviSettingsModal=i()(document).find(".et-fb-modal__page-settings"),!!this.$diviSettingsModal.length&&(this.$diviTermSettingInputs=this.$diviSettingsModal.find(".et-fb-option--".concat(this.primaryTaxonomy.rest_base)),this.$diviTermSettingArea=this.$diviTermSettingInputs.parents(".et-fb-form__group"),this.$diviTermSettingArea.length||(this.$diviTermSettingArea=this.workaroundForFalseDiviTaxonomySelector()),this.$PrimaryTermSelectContainer=i()('<div id="rank-math-primary-term-input" />'),!0)}},{key:"workaroundForFalseDiviTaxonomySelector",value:function(){var t=this.primaryTaxonomy.name.toLowerCase();return this.$diviSettingsModal.find(".et-fb-form__label-text").filter((function(e,r){return i()(r).text().toLowerCase()===t})).parents(".et-fb-form__group")}},{key:"renderContainer",value:function(){this.$diviTermSettingArea.after(this.$PrimaryTermSelectContainer)}},{key:"renderComponent",value:function(t){var e={taxonomySlug:this.primaryTaxonomy.slug,options:this.formatActiveTerms(t)};(0,l.createRoot)(this.$PrimaryTermSelectContainer[0]).render((0,l.createElement)(Xn,e))}},{key:"bindEvents",value:function(){var t=this;(0,p.addFilter)("et.builder.store.setting.update","rank-math",(function(e,r){return"et_pb_post_settings_".concat(t.primaryTaxonomy.rest_base)===r&&t.renderComponent(e),e}))}},{key:"formatActiveTerms",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.primaryTaxonomyValue;return this.primaryTaxonomyTerms.length&&e.trim()?e.split(",").map((function(e){var r=(0,ct.find)(t.primaryTaxonomyTerms,["term_id",parseInt(e)])||{};return{label:r.name,value:r.term_id}})):[]}}],r&&na(e.prototype,r),n&&na(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();aa(oa,"initRecursionTimer",void 0),aa(oa,"attemptsRun",0),aa(oa,"maxAttempts",10),aa(oa,"attemptInterval",1e3);var ua=["className","children"];function ca(){return ca=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ca.apply(this,arguments)}function sa(t,e){if(null==t)return{};var r,n,a=function(t,e){if(null==t)return{};var r,n,a={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(a[r]=t[r]);return a}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(a[r]=t[r])}return a}var la=function(t){var e=t.className,r=t.children,n=sa(t,ua),a=Kn()("rank-math-rm-modal-button",e);return wp.element.createElement("button",ca({type:"button",className:a},n),r)},pa=function(){return wp.element.createElement("div",{className:"et-fb-icon et-fb-icon--close",style:{fill:"rgb(255, 255, 255)",width:"28px",minWidth:"28px",height:"28px",margin:"-6px"}},wp.element.createElement("svg",{viewBox:"0 0 28 28",preserveAspectRatio:"xMidYMid meet",shapeRendering:"geometricPrecision"},wp.element.createElement("g",null,wp.element.createElement("path",{d:"M15.59 14l4.08-4.082a1.124 1.124 0 0 0-1.587-1.588L14 12.411 9.918 8.329A1.124 1.124 0 0 0 8.33 9.92L12.411 14l-4.082 4.082a1.124 1.124 0 0 0 1.59 1.589L14 15.589l4.082 4.082a1.124 1.124 0 0 0 1.589-1.59L15.589 14h.001z",fillRule:"evenodd"}))))},fa=function(){var t=Kn()("rank-math-rm-modal-header-discard-button");return wp.element.createElement(la,{className:t,onClick:function(){(0,c.dispatch)("rank-math").toggleIsDiviRankMathModalActive(!1)}},wp.element.createElement(pa,null))},ha=function(){var t=Kn()("rank-math-rm-modal-header");return wp.element.createElement("header",{className:t},wp.element.createElement("div",{className:"rank-math-rm-modal-header"},wp.element.createElement("div",{className:"rank-math-rm-modal-header-title"},"Rank Math SEO"),wp.element.createElement("ul",{className:"rank-math-rm-modal-header-options"},wp.element.createElement("li",{className:"rank-math-rm-modal-header-option"},wp.element.createElement(fa,null)))))},ma=function(t){var e=t.children,r=Kn()("rank-math-rm-modal-content","rank-math-sidebar-panel");return wp.element.createElement("div",{className:r},e)},da=(0,c.withSelect)((function(t){return{rmUiActive:(0,t("rank-math").isDiviRankMathModalActive)()}}))((function(t){var e=t.rmUiActive,r=Kn()("rank-math-rm-modal",{"rank-math-rm-modal-is-hidden":!e}),n=Kn()("rank-math-rm-modal-inner");return wp.element.createElement("div",{className:r},wp.element.createElement("div",{className:n},wp.element.createElement(ha,null),wp.element.createElement(ma,null,(0,p.applyFilters)("rank_math_app",{})())))})),ga=(0,f.withFilters)("rankMath.diviAppModal")(da),ya=function(){return wp.element.createElement(f.SlotFillProvider,null,wp.element.createElement("div",{className:"rank-math-rm-app"},wp.element.createElement(ga,null)))};function va(t){return va="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},va(t)}function ba(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(a=n.key,i=void 0,i=function(t,e){if("object"!==va(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==va(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(a,"string"),"symbol"===va(i)?i:String(i)),n)}var a,i}function wa(){return wa="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=_a(t)););return t}(t,e);if(n){var a=Object.getOwnPropertyDescriptor(n,e);return a.get?a.get.call(arguments.length<3?t:r):a.value}},wa.apply(this,arguments)}function ka(t,e){return ka=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ka(t,e)}function Sa(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=_a(t);if(e){var a=_a(this).constructor;r=Reflect.construct(n,arguments,a)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===va(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function _a(t){return _a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},_a(t)}var Ta=function(){return s.MediaUpload},Pa=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ka(t,e)}(i,t);var e,r,n,a=Sa(i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),a.apply(this,arguments)}return e=i,(r=[{key:"setup",value:function(t){u().use(u().createRootURLMiddleware(rankMath.api.root)),u().use(u().createNonceMiddleware(rankMath.restNonce)),this.registerSlots=this.registerSlots.bind(this),(0,p.addAction)("rank_math_loaded","rank-math",this.registerSlots,0),(0,p.addFilter)("editor.MediaUpload","rank-math/replace-media-upload",Ta),wa(_a(i.prototype),"setup",this).call(this,t)}},{key:"registerSlots",value:function(){this.RankMathAfterEditor=y,this.RankMathAfterFocusKeyword=E,this.RankMathAdvancedTab=S,this.slots={AfterEditor:y,AfterFocusKeyword:E,AdvancedTab:S}}},{key:"updatePermalink",value:function(t){(0,c.dispatch)("rank-math").updatePermalink(t)}},{key:"updatePermalinkSanitize",value:function(t){t=this.assessor.getResearch("slugify")(t),(0,c.dispatch)("rank-math").updatePermalink(t)}}])&&ba(e.prototype,r),n&&ba(e,n),Object.defineProperty(e,"prototype",{writable:!1}),i}(On);window.addEventListener("message",(function(t){"et_builder_api_ready"===t.data.etBuilderEvent&&(wp.i18n.setLocaleData(wp.i18n.getLocaleData("seo-by-rank-math"),"rank-math"),window.rankMathEditor=new Pa,window.rankMathGutenberg=window.rankMathEditor,window.rankMathEditor.setup(new Fn),Jn.init(),new oa,(0,l.createRoot)(document.getElementById("rank-math-rm-app-root")).render((0,l.createElement)(ya)),(0,c.dispatch)("rank-math").refreshResults(),i()(".rank-math-rm-modal").draggable())}))}()}();