!function(){"use strict";var t={n:function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,{a:r}),r},d:function(e,r){for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r:function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{deleteSchema:function(){return ce},lockModifiedDate:function(){return D},refreshResults:function(){return Lt},resetDirtyMetadata:function(){return T},resetRedirection:function(){return z},resetStore:function(){return P},saveSchema:function(){return ue},saveTemplate:function(){return pe},schemaUpdated:function(){return te},setEditingSchemaId:function(){return ee},setEditorTab:function(){return re},setTemplateTab:function(){return ne},setVersion:function(){return zt},toggleFrontendScore:function(){return b},toggleIsDiviPageSettingsBarActive:function(){return Ht},toggleIsDiviRankMathModalActive:function(){return Bt},toggleLoaded:function(){return Nt},toggleSchemaEditor:function(){return Yt},toggleSchemaTemplates:function(){return Zt},toggleSnippetEditor:function(){return st},updateAIScore:function(){return le},updateAdvancedRobots:function(){return w},updateAnalysisScore:function(){return v},updateAppData:function(){return m},updateAppUi:function(){return h},updateBreadcrumbTitle:function(){return S},updateCanonicalUrl:function(){return k},updateDescription:function(){return F},updateEditSchema:function(){return oe},updateEditSchemas:function(){return ae},updateFacebookDescription:function(){return O},updateFacebookHasOverlay:function(){return E},updateFacebookImage:function(){return A},updateFacebookImageID:function(){return M},updateFacebookImageOverlay:function(){return j},updateFacebookTitle:function(){return I},updateFeaturedImage:function(){return N},updateHasRedirect:function(){return H},updateHighlightedParagraphs:function(){return Gt},updateKeywords:function(){return y},updatePermalink:function(){return C},updatePillarContent:function(){return g},updatePostID:function(){return U},updatePrimaryTermID:function(){return K},updateRedirection:function(){return x},updateRedirectionItem:function(){return L},updateRobots:function(){return _},updateSchemas:function(){return ie},updateSelectedKeyword:function(){return Kt},updateSerpDescription:function(){return lt},updateSerpSlug:function(){return pt},updateSerpTitle:function(){return ct},updateSnippetPreviewType:function(){return ft},updateSocialTab:function(){return xt},updateTitle:function(){return R},updateTwitterAppCountry:function(){return Ft},updateTwitterAppDescription:function(){return Pt},updateTwitterAppGoogleplayID:function(){return Ut},updateTwitterAppGoogleplayName:function(){return Ct},updateTwitterAppGoogleplayUrl:function(){return Rt},updateTwitterAppIpadID:function(){return Mt},updateTwitterAppIpadName:function(){return Et},updateTwitterAppIpadUrl:function(){return jt},updateTwitterAppIphoneID:function(){return It},updateTwitterAppIphoneName:function(){return Ot},updateTwitterAppIphoneUrl:function(){return At},updateTwitterAuthor:function(){return gt},updateTwitterCardType:function(){return mt},updateTwitterDescription:function(){return yt},updateTwitterHasOverlay:function(){return kt},updateTwitterImage:function(){return vt},updateTwitterImageID:function(){return bt},updateTwitterImageOverlay:function(){return wt},updateTwitterPlayerSize:function(){return St},updateTwitterPlayerStreamCtype:function(){return Tt},updateTwitterPlayerStreamUrl:function(){return Dt},updateTwitterPlayerUrl:function(){return _t},updateTwitterTitle:function(){return ht},updateTwitterUseFacebook:function(){return dt}});var r={};t.r(r),t.d(r,{appData:function(){return ye},appUi:function(){return _e}});var n={};t.r(n),t.d(n,{getAdvancedRobots:function(){return Ae},getAnalysisScore:function(){return Te},getAppData:function(){return Se},getBreadcrumbTitle:function(){return Ee},getCanonicalUrl:function(){return Me},getDescription:function(){return tr},getDirtyMetadata:function(){return De},getEditSchemas:function(){return vr},getEditingSchema:function(){return kr},getEditorTab:function(){return _r},getFacebookAuthor:function(){return Ve},getFacebookDescription:function(){return Ge},getFacebookHasOverlay:function(){return Qe},getFacebookImage:function(){return We},getFacebookImageID:function(){return qe},getFacebookImageOverlay:function(){return Je},getFacebookTitle:function(){return Be},getFeaturedImage:function(){return er},getFeaturedImageHtml:function(){return rr},getHighlightedParagraphs:function(){return He},getKeywords:function(){return Pe},getPermalink:function(){return Ze},getPillarContent:function(){return Ie},getPostID:function(){return Xe},getPreviewSchema:function(){return wr},getPrimaryTermID:function(){return nr},getRedirectionID:function(){return ar},getRedirectionItem:function(){return ur},getRedirectionType:function(){return ir},getRedirectionUrl:function(){return or},getRichSnippets:function(){return je},getRobots:function(){return Oe},getSchemas:function(){return br},getSelectedKeyword:function(){return Fe},getSerpDescription:function(){return fr},getSerpSlug:function(){return sr},getSerpTitle:function(){return lr},getShowScoreFrontend:function(){return Ue},getSnippetPreviewType:function(){return mr},getSocialTab:function(){return Ne},getTemplateTab:function(){return Sr},getTitle:function(){return Ye},getTwitterAppCountry:function(){return Br},getTwitterAppDescription:function(){return Ur},getTwitterAppGoogleplayID:function(){return Lr},getTwitterAppGoogleplayName:function(){return zr},getTwitterAppGoogleplayUrl:function(){return Hr},getTwitterAppIpadID:function(){return Nr},getTwitterAppIpadName:function(){return Kr},getTwitterAppIpadUrl:function(){return xr},getTwitterAppIphoneID:function(){return Cr},getTwitterAppIphoneName:function(){return Rr},getTwitterAppIphoneUrl:function(){return Fr},getTwitterAuthor:function(){return Or},getTwitterCardType:function(){return Tr},getTwitterDescription:function(){return Ir},getTwitterHasOverlay:function(){return Er},getTwitterImage:function(){return Mr},getTwitterImageID:function(){return Ar},getTwitterImageOverlay:function(){return jr},getTwitterPlayerSize:function(){return Vr},getTwitterPlayerStream:function(){return $r},getTwitterPlayerStreamCtype:function(){return qr},getTwitterPlayerUrl:function(){return Gr},getTwitterTitle:function(){return Pr},getTwitterUseFacebook:function(){return Dr},hasRedirect:function(){return cr},hasSchemaUpdated:function(){return gr},isDiviPageSettingsBarActive:function(){return Le},isDiviRankMathModalActive:function(){return ze},isLoaded:function(){return Re},isModifiedDateLocked:function(){return Ce},isPro:function(){return xe},isRefreshing:function(){return Ke},isSchemaEditorOpen:function(){return hr},isSchemaTemplatesOpen:function(){return yr},isSnippetEditorOpen:function(){return dr}});var a=jQuery,i=t.n(a),o=wp.data,u=wp.mediaUtils,c=wp.hooks,p=rankMathAnalyzer;function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function s(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,f(n.key),n)}}function f(t){var e=function(t,e){if("object"!==l(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==l(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===l(e)?e:String(e)}var d=new(function(){function t(){var e,r,n;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e=this,n=null,(r=f(r="map"))in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n}var e,r,n;return e=t,(r=[{key:"swap",value:function(t,e){var r=this;if(!(t=t||""))return"";var n=new RegExp(/%(([a-z0-9_-]+)\(([^)]*)\)|[^\s]+)%/,"giu");return t.replace(" %page%","").replace("%sep% %sep%","%sep%").replace(n,(function(t){return r.replace(e,t)})).trim()}},{key:"replace",value:function(t,e){var r=e.toLowerCase().slice(1,-1);return["term_description","user_description"].includes(r)?"undefined"!=typeof tinymce&&void 0!==tinymce.editors.rank_math_description_editor?tinymce.editors.rank_math_description_editor.getContent():i()("#description").val():r.includes("customfield(")?(r=r.replace("customfield(","").replace(")",""))in rankMath.customFields?rankMath.customFields[r]:"":(t=t||this.getMap(),(r="seo_description"===(r="seo_title"===(r=r.includes("(")?r.split("(")[0]:r)?"title":r)?"excerpt":r)in t?t[r]:"")}},{key:"getMap",value:function(){var t=this;return null!==this.map||(this.map={},i().each(rankMath.variables,(function(e,r){e=e.toLowerCase().replace(/%+/g,"").split("(")[0],t.map[e]=r.example}))),this.map}},{key:"setVariable",value:function(t,e){null!==this.map?this.map[t]=e:void 0!==rankMath.variables[t]&&(rankMath.variables[t].example=e)}}])&&s(e.prototype,r),n&&s(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}());function m(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return e=(0,c.applyFilters)("rank_math_sanitize_data",e,t,r),null!==n&&(n=(0,c.applyFilters)("rank_math_sanitize_meta_value",n,t,r)),n=null===n?e:n,(0,c.doAction)("rank_math_data_changed",t,e,r),{type:"RANK_MATH_APP_DATA",key:t,value:e,metaKey:r,metaValue:n}}function h(t,e){return(0,c.doAction)("rank_math_update_app_ui",t,e),{type:"RANK_MATH_APP_UI",key:t,value:e}}function y(t){return d.setVariable("focuskw",t.split(",")[0]),rankMathEditor.refresh("keyword"),m("keywords",t,"rank_math_focus_keyword")}function g(t){return m("pillarContent",t,"rank_math_pillar_content",!0===t?"on":"off")}function b(t){return m("showScoreFrontend",t,"rank_math_dont_show_seo_score",!0===t?"off":"on")}function v(t){return m("score",t,"rank_math_seo_score")}function k(t){return m("canonicalUrl",t,"rank_math_canonical_url")}function w(t){return m("advancedRobots",t,"rank_math_advanced_robots")}function _(t){return m("robots",t,"rank_math_robots",Object.keys(t))}function S(t){return m("breadcrumbTitle",t,"rank_math_breadcrumb_title")}function D(t){return m("lockModifiedDate",t,"rank_math_lock_modified_date")}function T(){return m("dirtyMetadata",{})}function P(t){return{type:"RESET_STORE",value:t}}function I(t){return m("facebookTitle",t,"rank_math_facebook_title")}function O(t){return m("facebookDescription",t,"rank_math_facebook_description")}function A(t){return m("facebookImage",t,"rank_math_facebook_image")}function M(t){return m("facebookImageID",t,"rank_math_facebook_image_id")}function E(t){return m("facebookHasOverlay",t,"rank_math_facebook_enable_image_overlay",!0===t?"on":"off")}function j(t){return m("facebookImageOverlay",t,"rank_math_facebook_image_overlay")}function U(t){return rankMath.objectID=t,m("postID",t)}function C(t){return m("permalink",t,"permalink")}function R(t){return m("title",t,"rank_math_title")}function F(t){return m("description",t,"rank_math_description")}function N(t){return m("featuredImage",t)}function K(t,e){return m("primaryTerm",parseInt(t),"rank_math_primary_"+e)}function x(t,e){return m(t,e)}function L(t){return h("redirectionItem",t)}function z(){return h("redirectionItem",{})}function H(t){return h("hasRedirect",t)}var B=lodash,G=function(t){return t.replace(/<\/?[a-z][^>]*?>/gi,"\n")},V=function(t){return t.replace(/<style[^>]*>([\S\s]*?)<\/style>/gim,"")},$=function(t){return t.replace(/&nbsp;|&#160;/gi," ").replace(/\s{2,}/g," ").replace(/\s\./g,".").replace(/^\s+|\s+$/g,"")},q=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return t.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,e)},W=function(t){return t.replace(/[‘’‛`]/g,"'").replace(/[“”〝〞〟‟„]/g,'"')},Q=function(t){return t.replace(/<!--[\s\S]*?-->/g,"")},J=function(t){return t.replace(/&\S+?;/g,"")};function X(t){return(0,B.isUndefined)(t)||!t?"":(0,B.flow)([V,q,G,Q,J,$,W])(t)}var Y=wp.autop,Z="[^<>&/\\[\\]\0- =]+?",tt=new RegExp("\\["+Z+"( [^\\]]+?)?\\]","g"),et=new RegExp("\\[/"+Z+"\\]","g"),rt=function(t){return t.replace(tt,"").replace(et,"")},nt=function(t,e){var r=function(t,e){for(var r,n=/<p(?:[^>]+)?>(.*?)<\/p>/gi,a=[];null!==(r=n.exec(t));)a.push(r);return(0,B.map)(a,(function(t){return e?X(t[1]):t[1]}))}(t=(0,B.flow)([rt,Q,Y.autop])(t),e=e||!1);return 0<r.length?r:[e?X(t):t]},at=document.createElement("div");function it(t){return t&&"string"==typeof t&&(t=t.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,"").replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gim,""),at.innerHTML=t,t=at.textContent,at.textContent=""),t}var ot=function(t,e){return t=(t=X(t)).replace(/\r?\n|\r/g," "),e?(0,B.truncate)(t,{length:e,separator:" "}):t},ut=function(t){if((0,B.isEmpty)(t))return"";t=q(t),t=V(t),t=(0,B.unescape)(t).replace(/\[caption[^\]]*\](.*)\[\/caption\]/g,"");var e=(0,B.filter)(nt(t,!0),(function(t){return""!==t.trim()}));if(!e.length)return"";var r=rankMathEditor.getPrimaryKeyword();if(""!==r){var n=(0,B.filter)(e,(function(t){return(0,B.includes)(t.toLowerCase(),r.toLowerCase())}));if(0<n.length)return ot(n[0],160)}return ot(e[0],160)};function ct(t){return t=d.swap(""!==t?t:rankMath.assessor.serpData.titleTemplate),rankMathEditor.refresh("title"),h("serpTitle",it(t))}function pt(t){return t=""!==t?t:rankMathEditor.assessor.dataCollector.getSlug(),rankMathEditor.refresh("permalink"),h("serpSlug",t)}function lt(t){return t=d.swap(function(t){var e=rankMathEditor.assessor.dataCollector.getData(),r=e.excerpt,n=ut(e.content),a=(0,B.isUndefined)(r)||(0,B.isEmpty)(r)?n:(0,B.unescape)(r);if(d.setVariable("excerpt",a),d.setVariable("seo_description",a),""!==(t=it((0,c.applyFilters)("rankMath/description",t))))return X(t);if(!(0,B.isUndefined)(r)&&!(0,B.isEmpty)(r))return X(r);var i=(0,B.unescape)(rankMath.assessor.serpData.descriptionTemplate);return(0,B.isUndefined)(i)||""===i?n:X(i)}(t)),rankMathEditor.refresh("description"),h("serpDescription",t)}function st(t){return h("isSnippetEditorOpen",t)}function ft(t){return h("snippetPreviewType",t)}function dt(t){return m("twitterUseFacebook",t,"rank_math_twitter_use_facebook",!0===t?"on":"off")}function mt(t){return m("twitterCardType",t,"rank_math_twitter_card_type")}function ht(t){return m("twitterTitle",t,"rank_math_twitter_title")}function yt(t){return m("twitterDescription",t,"rank_math_twitter_description")}function gt(t){return m("twitterAuthor",t,"rank_math_twitter_author")}function bt(t){return m("twitterImageID",t,"rank_math_twitter_image_id")}function vt(t){return m("twitterImage",t,"rank_math_twitter_image")}function kt(t){return m("twitterHasOverlay",t,"rank_math_twitter_enable_image_overlay",!0===t?"on":"off")}function wt(t){return m("twitterImageOverlay",t,"rank_math_twitter_image_overlay")}function _t(t){return m("twitterPlayerUrl",t,"rank_math_twitter_player_url")}function St(t){return m("twitterPlayerSize",t,"rank_math_twitter_player_size")}function Dt(t){return m("twitterPlayerStream",t,"rank_math_twitter_player_stream")}function Tt(t){return m("twitterPlayerStreamCtype",t,"rank_math_twitter_player_stream_ctype")}function Pt(t){return m("twitterAppDescription",t,"rank_math_twitter_app_description")}function It(t){return m("twitterAppIphoneID",t,"rank_math_twitter_app_iphone_id")}function Ot(t){return m("twitterAppIphoneName",t,"rank_math_twitter_app_iphone_name")}function At(t){return m("twitterAppIphoneUrl",t,"rank_math_twitter_app_iphone_url")}function Mt(t){return m("twitterAppIpadID",t,"rank_math_twitter_app_ipad_id")}function Et(t){return m("twitterAppIpadName",t,"rank_math_twitter_app_ipad_name")}function jt(t){return m("twitterAppIpadUrl",t,"rank_math_twitter_app_ipad_url")}function Ut(t){return m("twitterAppGoogleplayID",t,"rank_math_twitter_app_googleplay_id")}function Ct(t){return m("twitterAppGoogleplayName",t,"rank_math_twitter_app_googleplay_name")}function Rt(t){return m("twitterAppGoogleplayUrl",t,"rank_math_twitter_app_googleplay_url")}function Ft(t){return m("twitterAppCountry",t,"rank_math_twitter_app_country")}function Nt(t){return h("isLoaded",t)}function Kt(t){return h("selectedKeyword",t)}function xt(t){return h("socialTab",t)}function Lt(){return h("refreshResults",Date.now())}function zt(){return h("isPro",!0)}function Ht(t){return h("isDiviPageSettingsBarActive",t)}function Bt(t){return h("isDiviRankMathModalActive",t)}function Gt(t){return h("highlightedParagraphs",t)}var Vt=wp.i18n,$t=wp.apiFetch,qt=t.n($t);function Wt(t){return Wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wt(t)}function Qt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Jt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Qt(Object(r),!0).forEach((function(e){Xt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Qt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Xt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Wt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Wt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Wt(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Yt(t){return h("isSchemaEditorOpen",t)}function Zt(t){return h("isSchemaTemplatesOpen",t)}function te(t){return h("schemaUpdated",t)}function ee(t){return h("editingSchemaId",t)}function re(t){return h("editorTab",t)}function ne(t){return h("templateTab",t)}function ae(t){return h("editSchemas",t)}function ie(t){return m("schemas",t)}function oe(t,e){var r=Jt({},(0,o.select)("rank-math").getEditSchemas());return r[t]=e,h("editSchemas",r)}function ue(t,e){var r=Jt({},(0,o.select)("rank-math").getSchemas());return r[t]=e,m("schemas",r)}function ce(t){var e=Jt({},(0,o.select)("rank-math").getSchemas());return delete e[t],(0,c.doAction)("rank_math_schema_trash",t),m("schemas",e,"rank_math_delete_"+t,"")}function pe(t,e,r){return qt()({method:"POST",path:"rankmath/v1/saveTemplate",data:{schema:t,postId:r}}).then((function(r){e({loading:!1,showNotice:!0,postId:r.id}),setTimeout((function(){e({showNotice:!1}),(0,B.get)(rankMath,"isTemplateScreen",!1)&&(document.title=(0,Vt.__)("Edit Schema","rank-math"),window.history.pushState(null,"",r.link.replace(/&amp;/g,"&")))}),2e3),rankMath.schemaTemplates.push({schema:t,title:t.metadata.title,type:t["@type"]})})),e({loading:!0}),{type:"DONT_WANT_TO_DO_SOMETHING"}}function le(t){return m("contentAIScore",t,"rank_math_contentai_score",t)}function se(t){return se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},se(t)}function fe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function de(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fe(Object(r),!0).forEach((function(e){me(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fe(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function me(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==se(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==se(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===se(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var he=function(t){var e=t.assessor.serpData,r=t.assessor.hasRedirection;return{postID:null,title:e.title?e.title:e.titleTemplate,description:e.description,keywords:e.focusKeywords?e.focusKeywords:"",pillarContent:e.pillarContent,featuredImage:"",permalink:!1,primaryTerm:e.primaryTerm,robots:e.robots,advancedRobots:e.advancedRobots,canonicalUrl:e.canonicalUrl,breadcrumbTitle:e.breadcrumbTitle,showScoreFrontend:e.showScoreFrontend,lockModifiedDate:e.lockModifiedDate,redirectionID:r?(0,B.get)(t.assessor,"redirection.id",""):"",redirectionType:r?(0,B.get)(t.assessor,"redirection.header_code",""):"",redirectionUrl:r?(0,B.get)(t.assessor,"redirection.url_to",""):"",facebookTitle:e.facebookTitle,facebookImage:e.facebookImage,facebookImageID:e.facebookImageID,facebookAuthor:e.facebookAuthor,facebookDescription:e.facebookDescription,facebookHasOverlay:e.facebookHasOverlay,facebookImageOverlay:e.facebookImageOverlay,twitterTitle:e.twitterTitle,twitterImage:e.twitterImage,twitterAuthor:e.twitterAuthor,twitterImageID:e.twitterImageID,twitterCardType:e.twitterCardType,twitterUseFacebook:e.twitterUseFacebook,twitterDescription:e.twitterDescription,twitterHasOverlay:e.twitterHasOverlay,twitterImageOverlay:e.twitterImageOverlay,twitterPlayerUrl:e.twitterPlayerUrl,twitterPlayerSize:e.twitterPlayerSize,twitterPlayerStream:e.twitterPlayerStream,twitterPlayerStreamCtype:e.twitterPlayerStreamCtype,twitterAppDescription:e.twitterAppDescription,twitterAppIphoneName:e.twitterAppIphoneName,twitterAppIphoneID:e.twitterAppIphoneID,twitterAppIphoneUrl:e.twitterAppIphoneUrl,twitterAppIpadName:e.twitterAppIpadName,twitterAppIpadID:e.twitterAppIpadID,twitterAppIpadUrl:e.twitterAppIpadUrl,twitterAppGoogleplayName:e.twitterAppGoogleplayName,twitterAppGoogleplayID:e.twitterAppGoogleplayID,twitterAppGoogleplayUrl:e.twitterAppGoogleplayUrl,twitterAppCountry:e.twitterAppCountry,schemas:(0,B.get)(t,"schemas",{}),score:0,dirtyMetadata:{}}};function ye(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:he(rankMath),e=arguments.length>1?arguments[1]:void 0,r=de({},t.dirtyMetadata);return!1!==e.metaKey&&(r[e.metaKey]=e.metaValue),"RESET_STORE"===e.type?de({},he(e.value)):"RANK_MATH_APP_DATA"===e.type?"dirtyMetadata"===e.key?de(de({},t),{},{dirtyMetadata:e.value}):de(de({},t),{},me(me({},e.key,e.value),"dirtyMetadata",r)):t}function ge(t){return ge="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ge(t)}function be(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ve(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?be(Object(r),!0).forEach((function(e){ke(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):be(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ke(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==ge(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==ge(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===ge(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var we={isLoaded:!1,isPro:!1,selectedKeyword:{tag:"",index:0,data:{value:""}},hasRedirect:rankMath.assessor.hasRedirection&&!(0,B.isEmpty)((0,B.get)(rankMath.assessor,"redirection.id",""))&&!(0,B.isEmpty)((0,B.get)(rankMath.assessor,"redirection.url_to","")),serpTitle:"",serpSlug:"",serpDescription:(0,B.get)(rankMath.assessor,"serpData.description",""),isSnippetEditorOpen:!1,snippetPreviewType:"",refreshResults:"",redirectionItem:{},socialTab:"facebook",highlightedParagraphs:[],editorTab:"",templateTab:"",editSchemas:{},editingSchemaId:"",isSchemaEditorOpen:!1,isSchemaTemplatesOpen:!1,schemaUpdated:!1,isDiviRankMathModalActive:!1,isDiviPageSettingsBarActive:!1};function _e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:we,e=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===e.type?ve(ve({},t),{},ke({},e.key,e.value)):t}function Se(t){return t.appData}function De(t){return t.appData.dirtyMetadata}function Te(t){return t.appData.score}function Pe(t){return t.appData.keywords}function Ie(t){return t.appData.pillarContent}function Oe(t){return t.appData.robots}function Ae(t){return t.appData.advancedRobots}function Me(t){return t.appData.canonicalUrl}function Ee(t){return t.appData.breadcrumbTitle}function je(t){return"todo"}function Ue(t){return t.appData.showScoreFrontend}function Ce(t){return t.appData.lockModifiedDate}function Re(t){return t.appUi.isLoaded}function Fe(t){return t.appUi.selectedKeyword}function Ne(t){return t.appUi.socialTab}function Ke(t){return t.appUi.refreshResults}function xe(t){return t.appUi.isPro}function Le(t){return t.appUi.isDiviPageSettingsBarActive}function ze(t){return t.appUi.isDiviRankMathModalActive}function He(t){return t.appUi.highlightedParagraphs}function Be(t){return t.appData.facebookTitle}function Ge(t){return t.appData.facebookDescription}function Ve(t){return t.appData.facebookAuthor}function qe(t){return t.appData.facebookImageID}function We(t){return t.appData.facebookImage}function Qe(t){return t.appData.facebookHasOverlay}function Je(t){return""!==t.appData.facebookImageOverlay?t.appData.facebookImageOverlay:"play"}function Xe(t){return t.appData.postID}function Ye(t){return t.appData.title}function Ze(t){return t.appData.permalink}function tr(t){return t.appData.description}function er(t){return t.appData.featuredImage}function rr(t){var e=t.appData.featuredImage;return'<img src="'.concat(e.source_url,'" alt="').concat(e.alt_text,'" >')}function nr(t){return t.appData.primaryTerm}function ar(t){return String(t.appData.redirectionID)}function ir(t){return t.appData.redirectionType}function or(t){return t.appData.redirectionUrl}function ur(t){return t.appUi.redirectionItem}function cr(t){return t.appUi.hasRedirect}var pr=wp.url;function lr(t){return it(t.appUi.serpTitle)}function sr(t){return(0,pr.safeDecodeURIComponent)(t.appUi.serpSlug)}function fr(t){return t.appUi.serpDescription}function dr(t){return t.appUi.isSnippetEditorOpen}function mr(t){return t.appUi.snippetPreviewType}function hr(t){return t.appUi.isSchemaEditorOpen}function yr(t){return t.appUi.isSchemaTemplatesOpen}function gr(t){return t.appUi.schemaUpdated}function br(t){return t.appData.schemas}function vr(t){return t.appUi.editSchemas}function kr(t){return{id:t.appUi.editingSchemaId,data:t.appUi.editSchemas[t.appUi.editingSchemaId]}}function wr(t){return t.appData.schemas[t.appUi.editingSchemaId]}function _r(t){return t.appUi.editorTab}function Sr(t){return t.appUi.templateTab}function Dr(t){return t.appData.twitterUseFacebook}function Tr(t){return t.appData.twitterCardType}function Pr(t){return t.appData.twitterTitle}function Ir(t){return t.appData.twitterDescription}function Or(t){return t.appData.twitterAuthor}function Ar(t){return t.appData.twitterImageID}function Mr(t){return t.appData.twitterImage}function Er(t){return t.appData.twitterHasOverlay}function jr(t){return""!==t.appData.twitterImageOverlay?t.appData.twitterImageOverlay:"play"}function Ur(t){return t.appData.twitterAppDescription}function Cr(t){return t.appData.twitterAppIphoneID}function Rr(t){return t.appData.twitterAppIphoneName}function Fr(t){return t.appData.twitterAppIphoneUrl}function Nr(t){return t.appData.twitterAppIpadID}function Kr(t){return t.appData.twitterAppIpadName}function xr(t){return t.appData.twitterAppIpadUrl}function Lr(t){return t.appData.twitterAppGoogleplayID}function zr(t){return t.appData.twitterAppGoogleplayName}function Hr(t){return t.appData.twitterAppGoogleplayUrl}function Br(t){return t.appData.twitterAppCountry}function Gr(t){return t.appData.twitterPlayerUrl}function Vr(t){return t.appData.twitterPlayerSize}function $r(t){return t.appData.twitterPlayerStream}function qr(t){return t.appData.twitterPlayerStreamCtype}var Wr=(0,o.registerStore)("rank-math",{reducer:(0,o.combineReducers)(r),selectors:n,actions:e});function Qr(){return Wr}var Jr={};(0,B.isUndefined)(rankMath.assessor)||(0,B.forEach)(rankMath.assessor.diacritics,(function(t,e){return Jr[e]=new RegExp(t,"g")}));var Xr=function(t){if((0,B.isUndefined)(t))return t;for(var e in Jr)t=t.replace(Jr[e],e);return t},Yr={"&amp;":"&","&quot;":'"',"&#39;":"'"},Zr=/&(?:amp|quot|#(0+)?39);/g,tn=RegExp(Zr.source);var en=function(t){return t&&tn.test(t)?t.replace(Zr,(function(t){return Yr[t]||"'"})):t||""};function rn(t){return rn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},rn(t)}function nn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(a=n.key,i=void 0,i=function(t,e){if("object"!==rn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(a,"string"),"symbol"===rn(i)?i:String(i)),n)}var a,i}var an=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.analyzer=new p.Analyzer({i18n:Vt,analyses:rankMath.assessor.researchesTests}),this.dataCollector=e,this.registerRefresh(),this.updateKeywordResult=this.updateKeywordResult.bind(this),this.sanitizeData=this.sanitizeData.bind(this),(0,c.addAction)("rankMath_analysis_keywordUsage_updated","rank-math",this.updateKeywordResult),(0,c.addFilter)("rank_math_sanitize_meta_value","rank-math",this.sanitizeData),(0,c.addFilter)("rank_math_sanitize_data","rank-math",this.sanitizeData)}var e,r,n;return e=t,r=[{key:"updateKeywordResult",value:function(t,e){rankMathEditor.resultManager.update(t,{keywordNotUsed:e}),t===this.getSelectedKeyword().toLowerCase()&&(0,o.dispatch)("rank-math").refreshResults()}},{key:"sanitizeData",value:function(t,e){return"schemas"===e||(0,B.isObject)(t)||(0,B.isEmpty)(t)?t:(r=t,(0,B.isUndefined)(r)?"":(0,B.flow)([V,q,G,Q])(r));var r}},{key:"getPaper",value:function(t,e){var r=Qr().getState(),n=this.dataCollector.getData(),a=new p.Paper("",{locale:rankMath.localeFull});a.setTitle(r.appUi.serpTitle),a.setPermalink(n.slug),a.setDescription(r.appUi.serpDescription),a.setUrl(n.permalink),a.setText(en((0,c.applyFilters)("rank_math_content",n.content))),a.setKeyword(t),a.setKeywords(e),a.setSchema(r.appData.schemas),(0,B.isUndefined)(n.featuredImage)||(a.setThumbnail(n.featuredImage.source_url),a.setThumbnailAltText(Xr(n.featuredImage.alt_text)));var i=(0,o.select)("rank-math-content-ai");if(!(0,B.isEmpty)(i)){var u=i.getData(),l=i.getScore();a.setContentAI(l||!(0,B.isEmpty)(u.keyword))}return a}},{key:"registerRefresh",value:function(){var t=this;this.refresh=(0,B.debounce)((function(e){var r=Qr().getState();if(!1!==r.appUi.isLoaded){var n=r.appData.keywords.split(","),a=[];(0,c.doAction)("rank_math_"+e+"_refresh"),n.map((function(e,r){var i=t.getPaper(Xr(e),n),u=0===r?rankMath.assessor.researchesTests:t.filterTests(t.getSecondaryKeywordTests());a.push(t.analyzer.analyzeSome(u,i).then((function(t){rankMathEditor.resultManager.update(i.getKeyword(),t,0===r),0===r&&(0,o.dispatch)("rank-math").updateAnalysisScore(rankMathEditor.resultManager.getScore(i.getKeyword()))}))),Promise.all(a).then((function(){(0,o.dispatch)("rank-math").refreshResults()}))}))}}),500)}},{key:"getSecondaryKeywordTests",value:function(){return["keywordInContent","lengthContent","keywordInSubheadings","keywordDensity","lengthPermalink","linksHasExternals","linksNotAllExternals","linksHasInternal","titleSentiment","titleHasPowerWords","titleHasNumber","contentHasTOC","contentHasShortParagraphs","contentHasAssets"]}},{key:"getPrimaryKeyword",value:function(){var t=Qr().getState().appData.keywords;return t?Xr(t.split(",")[0]):""}},{key:"getSelectedKeyword",value:function(){var t=Qr().getState(),e=""!==t.appUi.selectedKeyword.data.value?t.appUi.selectedKeyword.data.value:t.appData.keywords.split(",")[0];return Xr(e)}},{key:"getResearch",value:function(t){return this.analyzer.researcher.getResearch(t)}},{key:"filterTests",value:function(t){return(0,B.intersection)(t,rankMath.assessor.researchesTests)}}],r&&nn(e.prototype,r),n&&nn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),on=an,un=function(){return!(0,B.isNull)(document.getElementById("site-editor"))&&(0,B.endsWith)(wp.data.select("core/edit-site").getEditedPostId(),"//home")};function cn(t){return cn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},cn(t)}function pn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(a=n.key,i=void 0,i=function(t,e){if("object"!==cn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==cn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(a,"string"),"symbol"===cn(i)?i:String(i)),n)}var a,i}var ln=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),(rankMath.is_front_page||un())&&((0,c.addFilter)("rankMath_analysis_contentLength","rank-math",this.contentLength),(0,c.addFilter)("rankMath_analysis_contentLength_boundaries","rank-math",this.contentLengthBoundary))}var e,r,n;return e=t,(r=[{key:"contentLength",value:function(t){return{hasScore:t.hasScore,failed:(0,Vt.__)("Content is %1$d words long. Consider using at least 300 words.","rank-math"),tooltipText:(0,Vt.__)("Minimum recommended content length should be 300 words.","rank-math"),emptyContent:(0,Vt.sprintf)((0,Vt.__)("Content should be %1$s long.","rank-math"),'<a href="https://rankmath.com/kb/score-100-in-tests/?utm_source=Plugin&utm_campaign=WP#overall-content-length" target="_blank">'+(0,Vt.__)("300 words","rank-math")+"</a>")}}},{key:"contentLengthBoundary",value:function(){return{recommended:{boundary:299,score:8},belowRecommended:{boundary:200,score:5},low:{boundary:50,score:2}}}}])&&pn(e.prototype,r),n&&pn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();function sn(t){return sn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},sn(t)}function fn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(a=n.key,i=void 0,i=function(t,e){if("object"!==sn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==sn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(a,"string"),"symbol"===sn(i)?i:String(i)),n)}var a,i}var dn=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,r,n;return e=t,(r=[{key:"setup",value:function(t){Qr(),this.resultManager=new p.ResultManager,this.assessor=new on(t),new ln,(0,c.doAction)("rank_math_loaded")}},{key:"refresh",value:function(t){this.assessor.refresh(t)}},{key:"getPrimaryKeyword",value:function(){return this.assessor.getPrimaryKeyword()}},{key:"getSelectedKeyword",value:function(){return this.assessor.getSelectedKeyword()}},{key:"updatePermalink",value:function(t){throw"Implement the function"}},{key:"updatePermalinkSanitize",value:function(t){throw"Implement the function"}}])&&fn(e.prototype,r),n&&fn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();function mn(t){return mn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mn(t)}function hn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function yn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hn(Object(r),!0).forEach((function(e){bn(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function gn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,vn(n.key),n)}}function bn(t,e,r){return(e=vn(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function vn(t){var e=function(t,e){if("object"!==mn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==mn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===mn(e)?e:String(e)}var kn=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),bn(this,"initialize",!1),this.dataUpdated=!1,this._data={id:!1,slug:!1,permalink:!1,content:!1,title:!1,excerpt:!1,featuredImage:!1},this.refresh=this.refresh.bind(this),this.savePost=this.savePost.bind(this),this.saveRedirection=this.saveRedirection.bind(this),this.saveSchemas=this.saveSchemas.bind(this),this.subscribeToElementor(),(0,c.addAction)("rank_math_elementor_before_save","rank-math",this.beforeSave),setTimeout(this.elementorPreviewLoaded.bind(this),5e3)}var e,r,n;return e=t,(r=[{key:"elementorPreviewLoaded",value:function(){var t=this;(0,c.addAction)("rank_math_data_changed","rank-math",(function(e){(0,B.includes)(["permalink","dirtyMetadata","score","lockModifiedDate"],e)||t.dataUpdated||t.activateSaveButton()})),(0,c.addAction)("rank_math_update_app_ui","rank-math",(function(e){"hasRedirect"===e&&t.activateSaveButton()}))}},{key:"activateSaveButton",value:function(){window.top.$e.internal("document/save/set-is-modified",{status:!0})}},{key:"collectGutenbergData",value:function(){return{id:this.getPostID(),slug:this.getSlug(),permalink:this.getPermalink(),content:this.getContent(),title:this.getTitle(),excerpt:this.getExcerpt(),featuredImage:this.getFeaturedImage()}}},{key:"getPostID",value:function(){return elementor.config.document.id}},{key:"getTitle",value:function(){return elementor.settings.page.model.get("post_title")}},{key:"getContent",value:function(){var t=this,e=[];return this.getContentArea().find('[data-element_type="widget"]').each((function(r,n){var a=i()(n).html();(a=a.trim())&&(a=a.replace(/<div[^>]*class=["'][^"']*elementor\x2Delement\x2Doverlay[^"']*["'][^>]*>[\s\S]*?<\/div>/gi,""),e.push(t.decodeEntities(a)))})),e.join("")}},{key:"decodeEntities",value:function(t){return t?t=t.replace("–","-"):""}},{key:"getContentArea",value:function(){if(this._contentArea)return this._contentArea;var t=elementor.$preview.contents().find('[data-elementor-type="'+ElementorConfig.document.type+'"]');return t.length<1?i()("<div />"):(this._contentArea=t,t)}},{key:"getExcerpt",value:function(){return elementor.settings.page.model.get("post_excerpt")}},{key:"getPermalink",value:function(){return rankMath.is_front_page?rankMath.homeUrl+"/":this.getSlug()?rankMath.permalinkFormat.replace(/%(postname|pagename)%/,this.getSlug()).trimRight("/"):""}},{key:"getSlug",value:function(){return(0,pr.safeDecodeURIComponent)((0,o.select)("rank-math").getPermalink())}},{key:"getFeaturedImage",value:function(){var t=elementor.settings.page.model.get("post_featured_image");if(!(0,B.isUndefined)(t)&&(t=""===t.id?0:parseInt(t.id),this.isValidMediaId(t))){var e=(0,o.select)("core").getMedia(t);if(!(0,B.isUndefined)(e))return e}}},{key:"isValidMediaId",value:function(t){return"number"==typeof t&&0<t}},{key:"subscribeToElementor",value:function(){(0,o.dispatch)("rank-math").updatePermalink(rankMath.postName),this.subscriber=(0,B.debounce)(this.refresh,500),(0,o.subscribe)(this.subscriber),elementor.settings.page.model.on("change",this.subscriber)}},{key:"beforeSave",value:function(){window.rankMathDataCollector.savePost(),window.rankMathDataCollector.saveRedirection(),window.rankMathDataCollector.saveSchemas()}},{key:"refresh",value:function(){var t=yn({},this._data);this._data=this.collectGutenbergData(),this.handleEditorChange(t),!(0,B.isEqual)(t,this._data)&&t.id&&elementor.channels.editor.trigger("status:change",!0)}},{key:"savePost",value:function(){var t=this,e=(0,o.select)("rank-math").getDirtyMetadata();(0,B.isEmpty)(e)||(qt()({method:"POST",path:"rankmath/v1/updateMeta",data:{objectID:rankMath.objectID,objectType:rankMath.objectType,meta:e,content:this.getContent()}}).then((function(e){t.dataUpdated=!0,(0,B.isString)(e.slug)&&(0,o.dispatch)("rank-math").updatePermalink(e.slug),(0,c.doAction)("rank_math_metadata_updated",e),t.dataUpdated=!1})),(0,o.dispatch)("rank-math").resetDirtyMetadata())}},{key:"saveRedirection",value:function(){var t=(0,o.select)("rank-math").getRedirectionItem();(0,B.isEmpty)(t)||(t.objectID=this.getPostID(),t.redirectionSources=this.getPermalink(),qt()({method:"POST",path:"rankmath/v1/updateRedirection",data:t}).then((function(t){"delete"===t.action?(0,o.dispatch)("rank-math").updateRedirection("redirectionID",0):"new"===t.action&&(0,o.dispatch)("rank-math").updateRedirection("redirectionID",t.id)})),(0,o.dispatch)("rank-math").resetRedirection())}},{key:"saveSchemas",value:function(){var t=this,e=(0,o.select)("rank-math").getSchemas();if(!(0,B.isEmpty)(e)&&(0,o.select)("rank-math").hasSchemaUpdated()){var r=(0,o.select)("rank-math").getEditSchemas();qt()({method:"POST",path:"rankmath/v1/updateSchemas",data:{objectID:rankMath.objectID,objectType:rankMath.objectType,schemas:e}}).then((function(n){if(t.dataUpdated=!0,(0,B.isEmpty)(n))(0,o.dispatch)("rank-math").updateSchemas(e);else{var a=yn({},e),i=yn({},r);(0,B.map)(n,(function(t,e){a["schema-"+t]=yn({},a[e]),i["schema-"+t]=yn({},i[e]),delete a[e],delete i[e]})),(0,o.dispatch)("rank-math").schemaUpdated(!1),(0,o.dispatch)("rank-math").updateSchemas(a),(0,o.dispatch)("rank-math").updateEditSchemas(i)}t.dataUpdated=!1}))}}},{key:"handleEditorChange",value:function(t){var e=this,r={id:"handleIDChange",slug:"handleSlugChange",title:"handleTitleChange",excerpt:"handleExcerptChange",content:"handleContentChange",featuredImage:"handleFeaturedImageChange"};if((0,B.isInteger)(t.id))return this.initialize?void(0,B.forEach)(r,(function(r,n){e._data[n]!==t[n]&&e[r](e._data[n])})):(this.initialize=!0,(0,B.forEach)(r,(function(t,r){e[t](e._data[r])})),void rankMathEditor.refresh("init"));(0,o.dispatch)("rank-math").refreshResults()}},{key:"handleIDChange",value:function(t){(0,o.dispatch)("rank-math").updatePostID(t),(0,o.dispatch)("rank-math").toggleLoaded(!0)}},{key:"handleSlugChange",value:function(){rankMathEditor.refresh("permalink")}},{key:"handleTitleChange",value:function(t){d.setVariable("title",t),(0,o.dispatch)("rank-math").updateSerpTitle((0,o.select)("rank-math").getTitle()),rankMathEditor.refresh("title")}},{key:"handleExcerptChange",value:function(t){d.setVariable("excerpt",t),d.setVariable("excerpt_only",t),d.setVariable("wc_shortdesc",t),d.setVariable("seo_description",t),(0,o.dispatch)("rank-math").updateSerpDescription((0,o.select)("rank-math").getDescription())}},{key:"handleFeaturedImageChange",value:function(t){(0,o.dispatch)("rank-math").updateFeaturedImage(t),rankMathEditor.refresh("featuredImage")}},{key:"handleContentChange",value:function(){(0,o.dispatch)("rank-math").updateSerpDescription((0,o.select)("rank-math").getDescription()),rankMathEditor.refresh("content")}},{key:"getData",value:function(){return this._data}}])&&gn(e.prototype,r),n&&gn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),wn=Marionette,_n=t.n(wn),Sn=wp.element,Dn=_n().ItemView.extend({root:null,template:!1,id:"elementor-panel-rank-math",className:"rank-math-elementor rank-math-sidebar-panel",initialize:function(){i()("#elementor-panel-elements-search-area").hide()},onShow:function(){this.root=(0,Sn.createRoot)(document.getElementById("elementor-panel-rank-math"));var t=(0,Sn.createElement)((0,c.applyFilters)("rank_math_app",{}));this.root.render(t)},onDestroy:function(){this.root.unmount(),i()("#elementor-panel-elements-search-area").show()}}),Tn=function(t){return t["rank-math"]={region:t.global.region,view:Dn,options:{}},t};function Pn(t){return Pn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pn(t)}function In(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,On(n.key),n)}}function On(t){var e=function(t,e){if("object"!==Pn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Pn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Pn(e)?e:String(e)}var An=function(){function t(){var e,r,n;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e=this,n={},(r=On(r="links"))in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,this.onThemeChange=this.onThemeChange.bind(this),elementor.settings.editorPreferences.model.on("change",this.onThemeChange)}var e,r,n;return e=t,(r=[{key:"onThemeChange",value:function(t){var e=this,r=(0,B.get)(t,"changed.ui_theme",!1);!1!==r&&(0,B.forEach)(rankMath.elementorDarkMode,(function(t,n){var a=e.getLink(n+"-css",t);"light"!==r?a.attr("media","auto"===r?"(prefers-color-scheme: dark)":"").appendTo(elementorCommon.elements.$body):a.remove()}))}},{key:"getLink",value:function(t,e){return this.links[t]||this.createLink(t,e),this.links[t]}},{key:"createLink",value:function(t,e){this.links[t]=i()("#"+t).length?i()("#"+t):null,this.links[t]||(this.links[t]=i()("<link>",{id:t,rel:"stylesheet",href:e}))}}])&&In(e.prototype,r),n&&In(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),Mn=wp.components;function En(t){return En="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},En(t)}function jn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(a=n.key,i=void 0,i=function(t,e){if("object"!==En(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==En(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(a,"string"),"symbol"===En(i)?i:String(i)),n)}var a,i}var Un=function(){function t(){var e=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),rankMath.showLockModifiedDate&&(this.addTopBarButton(),i()((function(){return e.init()})))}var e,r,n;return e=t,(r=[{key:"init",value:function(){this.previewLoaded(),this.onDocumentLoaded(),this.onContentChange()}},{key:"addTopBarButton",value:function(){var t=this,e=window.elementorV2;(0,B.isUndefined)(e)||e.editorAppBar.documentOptionsMenu.registerToggleAction({id:"rank-math-lock-modified-date",priority:10,useProps:function(){return{title:(0,Vt.__)("Update (Lock Modified Date)","rank-math"),icon:function(){return wp.element.createElement(Mn.Dashicon,{icon:"calendar"})},onClick:function(){t.triggerUpdate()}}}})}},{key:"previewLoaded",value:function(){var t=this;elementor.once("preview:loaded",(function(){elementor.getRegion("panel").currentView.footer.currentView.addSubMenuItem("saver-options",{icon:"eicon-date",name:"rank-math-lock-modified-date",title:(0,Vt.__)("Update (Lock Modified Date)","rank-math"),callback:function(e){e.currentTarget.classList.contains("elementor-disabled")||t.triggerUpdate()}})}))}},{key:"onDocumentLoaded",value:function(){var t=this;elementor.on("document:loaded",(function(e){t.activateButton("draft"===e.container.settings.get("post_status")),t.lockModifiedDate(!1)}))}},{key:"onContentChange",value:function(){var t=this;$e.commandsInternal.on("run:after",(function(e,r,n){switch(r){case"document/save/set-is-modified":t.activateButton(n.status);break;case"document/save/save":case"document/save/default":delete elementorCommon.ajax.requestConstants.lock_modified_date,t.lockModifiedDate(!1)}}))}},{key:"triggerUpdate",value:function(){this.lockModifiedDate(!0),elementorCommon.ajax.addRequestConstant("lock_modified_date",!0),$e.run("document/save/default")}},{key:"activateButton",value:function(t){var e=document.getElementById("elementor-panel-footer-sub-menu-item-rank-math-lock-modified-date");e&&(e.classList.remove("elementor-disabled"),t||e.classList.add("elementor-disabled"))}},{key:"lockModifiedDate",value:function(t){wp.data.dispatch("rank-math").lockModifiedDate(t)}}])&&jn(e.prototype,r),n&&jn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}(),Cn=(0,Mn.createSlotFill)("RankMathAfterEditor"),Rn=Cn.Fill,Fn=Cn.Slot,Nn=function(t){var e=t.children,r=t.className;return wp.element.createElement(Rn,null,wp.element.createElement(Mn.PanelRow,{className:r},e))};Nn.Slot=Fn;var Kn=Nn,xn=(0,Mn.createSlotFill)("RankMathAdvancedTab"),Ln=xn.Fill,zn=xn.Slot,Hn=function(t){var e=t.children,r=t.className;return wp.element.createElement(Ln,null,wp.element.createElement(Mn.PanelRow,{className:r},e))};Hn.Slot=zn;var Bn=Hn,Gn=(0,Mn.createSlotFill)("RankMathAfterFocusKeyword"),Vn=Gn.Fill,$n=Gn.Slot,qn=function(t){var e=t.children,r=t.className;return wp.element.createElement(Vn,null,wp.element.createElement("div",{className:r},e))};qn.Slot=$n;var Wn=qn;function Qn(t){return Qn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qn(t)}function Jn(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Xn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(a=n.key,i=void 0,i=function(t,e){if("object"!==Qn(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Qn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(a,"string"),"symbol"===Qn(i)?i:String(i)),n)}var a,i}function Yn(t,e,r){return e&&Xn(t.prototype,e),r&&Xn(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Zn(){return Zn="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=na(t)););return t}(t,e);if(n){var a=Object.getOwnPropertyDescriptor(n,e);return a.get?a.get.call(arguments.length<3?t:r):a.value}},Zn.apply(this,arguments)}function ta(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ea(t,e)}function ea(t,e){return ea=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ea(t,e)}function ra(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=na(t);if(e){var a=na(this).constructor;r=Reflect.construct(n,arguments,a)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===Qn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function na(t){return na=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},na(t)}var aa=function(){return u.MediaUpload},ia=function(t){ta(r,t);var e=ra(r);function r(){return Jn(this,r),e.apply(this,arguments)}return Yn(r,[{key:"setup",value:function(t){this.registerSlots=this.registerSlots.bind(this),(0,c.addAction)("rank_math_loaded","rank-math",this.registerSlots,0),(0,c.addFilter)("editor.MediaUpload","rank-math/replace-media-upload",aa),Zn(na(r.prototype),"setup",this).call(this,t)}},{key:"registerSlots",value:function(){this.RankMathAfterEditor=Kn,this.RankMathAfterFocusKeyword=Wn,this.RankMathAdvancedTab=Bn,this.slots={AfterEditor:Kn,AfterFocusKeyword:Wn,AdvancedTab:Bn}}},{key:"updatePermalink",value:function(t){(0,o.dispatch)("rank-math").updatePermalink(t)}},{key:"updatePermalinkSanitize",value:function(t){t=this.assessor.getResearch("slugify")(t),(0,o.dispatch)("rank-math").updatePermalink(t)}}]),r}(dn);new Un,i()((function(){window.rankMathEditor=new ia,window.rankMathGutenberg=window.rankMathEditor,new An,elementor.once("preview:loaded",(function(){$e.components.get("panel/elements").addTab("rank-math",{title:"SEO"}),window.rankMathDataCollector=new kn,window.rankMathEditor.setup(window.rankMathDataCollector),(0,o.dispatch)("rank-math").refreshResults()}))})),i()(window).on("elementor:init",(function(){elementor.hooks.addFilter("panel/elements/regionViews",Tn);var t=function(t){ta(r,$e.modules.hookUI.Before);var e=ra(r);function r(){return Jn(this,r),e.apply(this,arguments)}return Yn(r,[{key:"getCommand",value:function(){return"document/save/save"}},{key:"getId",value:function(){return"custom-updater-before-save"}},{key:"getConditions",value:function(){return!0}},{key:"apply",value:function(t,e){(0,c.doAction)("rank_math_elementor_before_save",t,e)}}]),r}();$e.hooks.registerUIBefore(new t)}))}();