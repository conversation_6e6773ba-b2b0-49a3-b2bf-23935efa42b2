!function(){"use strict";var t,e={n:function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,{a:n}),n},d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}},n=jQuery;(t=e.n(n)())((function(){var e=this;e.wrap=t(".rank-math-404-monitor-wrap"),e.wrap.on("click",".rank-math-404-delete",(function(e){e.preventDefault();var n=t(this),r=n.attr("href").replace("admin.php","admin-ajax.php").replace("action=delete","action=rank_math_delete_log").replace("page=","math=");t.ajax({url:r,type:"GET",success:function(e){e&&e.success&&n.closest("tr").fadeOut(800,(function(){t(this).remove()}))}})})),e.wrap.on("click",".rank-math-clear-logs",(function(e){if(e.preventDefault(),!confirm(rankMath.logConfirmClear))return!1;t(this).closest("form").append('<input type="hidden" name="action" value="clear_log">').submit()})),t("#doaction, #doaction2").on("click",(function(){"redirect"===t("#bulk-action-selector-top").val()&&t(this).closest("form").attr("action",rankMath.redirectionsUri)}))}))}();