!function(){"use strict";var t={n:function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,{a:n}),n},d:function(e,n){for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}},e=jQuery,n=t.n(e),r=lodash,i=function(t){var e=function(t){var e=(0,r.find)(rankMath.acf.headlines,(function(e,n){return t.key===n}));return e&&(e=parseInt(e,10)),(e<1||e>6)&&(e=!1),e}(t);return t.content=e?"<h"+e+">"+t.content+"</h"+e+">":"<p>"+t.content+"</p>",t},a=function(t){var e=t.$el.find("textarea")[0],n=e.id,r=e.value;return function(t){return"undefined"!=typeof tinyMCE&&void 0!==tinyMCE.editors&&0!==tinyMCE.editors.length&&null!==tinyMCE.get(t)&&!tinyMCE.get(t).isHidden()}(n)&&(r=tinyMCE.get(n)&&tinyMCE.get(n).getContent()||""),r};function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function u(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,c(r.key),r)}}function c(t){var e=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===o(e)?e:String(e)}var l=new(function(){function t(){var e,n,r;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e=this,r={},(n=c(n="cache"))in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r}var e,n,i;return e=t,(n=[{key:"refresh",value:function(t){var e=this,n=this.getUncached(t);0!==n.length&&window.wp.ajax.post("query-attachments",{query:{post__in:n}}).done((function(t){(0,r.each)(t,(function(t){e.setCache(t.id,t),window.RankMathACFAnalysis.refresh()}))}))}},{key:"get",value:function(t){var e=this.getCache(t);if(!e)return!1;var n=window.wp.media.attachment(t);return n.has("alt")&&(e.alt=n.get("alt")),n.has("title")&&(e.title=n.get("title")),e}},{key:"getAttachmentContent",value:function(t){var e="";if(l.get(t,"attachment")){var n=l.get(t,"attachment");e+='<img src="'+n.url+'" alt="'+n.alt+'" title="'+n.title+'">'}return e}},{key:"setCache",value:function(t,e){this.cache[t]=e}},{key:"getCache",value:function(t){return t in this.cache&&this.cache[t]}},{key:"getUncached",value:function(t){var e=this;return(t=(0,r.uniq)(t)).filter((function(t){return!1===e.get(t)}))}}])&&u(e.prototype,n),i&&u(e,i),Object.defineProperty(e,"prototype",{writable:!1}),t}());function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function y(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,(i=r.key,a=void 0,a=function(t,e){if("object"!==f(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(i,"string"),"symbol"===f(a)?a:String(a)),r)}var i,a}var p={text:function(t){return t=(0,r.map)(t,(function(t){return"text"!==t.type?t:(t.content=t.$el.find("input[type=text][id^=acf]").val(),t=i(t))}))},textarea:function(t){return t=(0,r.map)(t,(function(t){return"textarea"!==t.type||(t.content="<p>"+t.$el.find("textarea[id^=acf]").val()+"</p>"),t}))},email:function(t){return t=(0,r.map)(t,(function(t){return"email"!==t.type||(t.content=t.$el.find("input[type=email][id^=acf]").val()),t}))},url:function(t){return t=(0,r.map)(t,(function(t){if("url"!==t.type)return t;var e=t.$el.find("input[type=url][id^=acf]").val();return t.content=e?'<a href="'+e+'">'+e+"</a>":"",t}))},link:function(t){return(0,r.map)(t,(function(t){if("link"!==t.type)return t;var e=t.$el.find("input[type=hidden].input-title").val(),n=t.$el.find("input[type=hidden].input-url").val(),r=t.$el.find("input[type=hidden].input-target").val();return t.content='<a href="'+n+'" target="'+r+'">'+e+"</a>",t}))},wysiwyg:function(t){return t=(0,r.map)(t,(function(t){return"wysiwyg"!==t.type||(t.content=a(t)),t}))},image:function(t){var e=[];return t=(0,r.map)(t,(function(t){if("image"!==t.type)return t;t.content="";var n=t.$el.find("input[type=hidden]").val();return e.push(n),t.content+=l.getAttachmentContent(n),t})),l.refresh(e),t},gallery:function(t){var e=[];return t=(0,r.map)(t,(function(t){return"gallery"!==t.type||(t.content="",t.$el.find(".acf-gallery-attachment input[type=hidden]").each((function(){var r=n()(this).val();e.push(r),t.content+=l.getAttachmentContent(r)}))),t})),l.refresh(e),t},taxonomy:function(t){return t=(0,r.map)(t,(function(t){if("taxonomy"!==t.type)return t;var e=[];if(t.$el.find('.acf-taxonomy-field[data-type="multi_select"]').length>0){var n=acf.select2.version>=4?"select":"input";e=(0,r.map)(t.$el.find('.acf-taxonomy-field[data-type="multi_select"] '+n).select2("data"),"text")}else t.$el.find('.acf-taxonomy-field[data-type="checkbox"]').length>0?e=(0,r.map)(t.$el.find('.acf-taxonomy-field[data-type="checkbox"] input[type="checkbox"]:checked').next(),"textContent"):t.$el.find("input[type=checkbox]:checked").length>0?e=(0,r.map)(t.$el.find("input[type=checkbox]:checked").parent(),"textContent"):t.$el.find("select option:checked").length>0&&(e=(0,r.map)(t.$el.find("select option:checked"),"textContent"));return(e=(0,r.map)(e,(function(t){return t.trim()}))).length>0&&(t.content="<ul>\n<li>"+e.join("</li>\n<li>")+"</li>\n</ul>"),t}))}},s=new(function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,i,a;return e=t,(i=[{key:"getFieldContent",value:function(){var t=this.excludeNames(this.excludeTypes(this.getData())),e=(0,r.uniq)((0,r.map)(t,"type"));return n().each(e,(function(e,n){n in p&&(t=p[n](t))})),t}},{key:"append",value:function(t){var e=this.getFieldContent();return(0,r.each)(e,(function(e){void 0!==e.content&&""!==e.content&&(t+="\n"+e.content)})),t}},{key:"getData",value:function(){var t=["flexible_content","repeater","group"],e=[],i=[],a=(0,r.map)(acf.get_fields(),(function(r){var a=n().extend(!0,{},acf.get_data(n()(r)));return a.$el=n()(r),a.post_meta_key=a.name,-1===t.indexOf(a.type)?e.push(a):i.push(a),a}));return 0===i.length||(0,r.each)(e,(function(t){(0,r.each)(i,(function(e){n().contains(e.$el[0],t.$el[0])&&("flexible_content"!==e.type&&"repeater"!==e.type||(e.children=e.children||[],e.children.push(t),t.parent=e,t.post_meta_key=e.name+"_"+(e.children.length-1)+"_"+t.name),"group"===e.type&&(e.children=[t],t.parent=e,t.post_meta_key=e.name+"_"+t.name))}))})),a}},{key:"excludeTypes",value:function(t){return(0,r.filter)(t,(function(t){return!(0,r.includes)(rankMath.acf.blacklistTypes,t.type)}))}},{key:"excludeNames",value:function(t){return(0,r.filter)(t,(function(t){return!(0,r.includes)(rankMath.acf.names,t.name)}))}}])&&y(e.prototype,i),a&&y(e,a),Object.defineProperty(e,"prototype",{writable:!1}),t}()),h=wp.hooks;function d(t){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}function m(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,v(r.key),r)}}function v(t){var e=function(t,e){if("object"!==d(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==d(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===d(e)?e:String(e)}var b=function(){function t(){var e,i,a,o=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e=this,a=0,(i=v(i="analysisTimeout"))in e?Object.defineProperty(e,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[i]=a,this.maybeRefresh=this.maybeRefresh.bind(this),this.refresh=(0,r.debounce)(this.maybeRefresh,rankMath.acf.refreshRate),(0,h.addFilter)("rank_math_content","rank-math",s.append.bind(s),11),n()(".acf-field").on("change",(function(){o.refresh()}))}var e,i,a;return e=t,(i=[{key:"maybeRefresh",value:function(){rankMathEditor.refresh("content")}}])&&m(e.prototype,i),a&&m(e,a),Object.defineProperty(e,"prototype",{writable:!1}),t}();window.RankMathACFAnalysis=new b}();