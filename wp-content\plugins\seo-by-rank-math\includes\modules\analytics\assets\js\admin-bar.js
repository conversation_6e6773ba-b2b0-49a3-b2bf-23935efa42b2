!function(){var e={4184:function(e,t){var n;!function(){"use strict";var a={}.hasOwnProperty;function r(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var l=typeof n;if("string"===l||"number"===l)e.push(n);else if(Array.isArray(n)){if(n.length){var o=r.apply(null,n);o&&e.push(o)}}else if("object"===l){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var i in n)a.call(n,i)&&n[i]&&e.push(i)}}}return e.join(" ")}e.exports?(r.default=r,e.exports=r):void 0===(n=function(){return r}.apply(t,[]))||(e.exports=n)}()}},t={};function n(a){var r=t[a];if(void 0!==r)return r.exports;var l=t[a]={exports:{}};return e[a](l,l.exports,n),l.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";var e=jQuery,t=n.n(e),a=React,r=function(){return r=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)};var l=function(e){var t=e.animate,n=void 0===t||t,l=e.animateBegin,o=e.backgroundColor,i=void 0===o?"#f5f6f7":o,s=e.backgroundOpacity,c=void 0===s?1:s,m=e.baseUrl,u=void 0===m?"":m,p=e.children,d=e.foregroundColor,f=void 0===d?"#eee":d,h=e.foregroundOpacity,y=void 0===h?1:h,v=e.gradientRatio,w=void 0===v?2:v,E=e.gradientDirection,b=void 0===E?"left-right":E,g=e.uniqueKey,k=e.interval,_=void 0===k?.25:k,S=e.rtl,N=void 0!==S&&S,O=e.speed,x=void 0===O?1.2:O,R=e.style,C=void 0===R?{}:R,P=e.title,I=void 0===P?"Loading...":P,T=e.beforeMask,j=void 0===T?null:T,A=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n}(e,["animate","animateBegin","backgroundColor","backgroundOpacity","baseUrl","children","foregroundColor","foregroundOpacity","gradientRatio","gradientDirection","uniqueKey","interval","rtl","speed","style","title","beforeMask"]),M=g||Math.random().toString(36).substring(6),U=M+"-diff",F=M+"-animated-diff",D=M+"-aria",B=N?{transform:"scaleX(-1)"}:null,L="0; "+_+"; 1",G=x+"s",V="top-bottom"===b?"rotate(90)":void 0;return(0,a.createElement)("svg",r({"aria-labelledby":D,role:"img",style:r(r({},C),B)},A),I?(0,a.createElement)("title",{id:D},I):null,j&&(0,a.isValidElement)(j)?j:null,(0,a.createElement)("rect",{role:"presentation",x:"0",y:"0",width:"100%",height:"100%",clipPath:"url("+u+"#"+U+")",style:{fill:"url("+u+"#"+F+")"}}),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:U},p),(0,a.createElement)("linearGradient",{id:F,gradientTransform:V},(0,a.createElement)("stop",{offset:"0%",stopColor:i,stopOpacity:c},n&&(0,a.createElement)("animate",{attributeName:"offset",values:-w+"; "+-w+"; 1",keyTimes:L,dur:G,repeatCount:"indefinite",begin:l})),(0,a.createElement)("stop",{offset:"50%",stopColor:f,stopOpacity:y},n&&(0,a.createElement)("animate",{attributeName:"offset",values:-w/2+"; "+-w/2+"; "+(1+w/2),keyTimes:L,dur:G,repeatCount:"indefinite",begin:l})),(0,a.createElement)("stop",{offset:"100%",stopColor:i,stopOpacity:c},n&&(0,a.createElement)("animate",{attributeName:"offset",values:"0; 0; "+(1+w),keyTimes:L,dur:G,repeatCount:"indefinite",begin:l})))))},o=function(e){return e.children?(0,a.createElement)(l,r({},e)):(0,a.createElement)(i,r({},e))},i=function(e){return(0,a.createElement)(o,r({viewBox:"0 0 476 124"},e),(0,a.createElement)("rect",{x:"48",y:"8",width:"88",height:"6",rx:"3"}),(0,a.createElement)("rect",{x:"48",y:"26",width:"52",height:"6",rx:"3"}),(0,a.createElement)("rect",{x:"0",y:"56",width:"410",height:"6",rx:"3"}),(0,a.createElement)("rect",{x:"0",y:"72",width:"380",height:"6",rx:"3"}),(0,a.createElement)("rect",{x:"0",y:"88",width:"178",height:"6",rx:"3"}),(0,a.createElement)("circle",{cx:"20",cy:"20",r:"20"}))},s=o,c=wp.apiFetch,m=n.n(c),u=wp.hooks,p=wp.element,d=wp.i18n;function f(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=rankMath.links[e]||"";if(!n)return"#";if(!t)return n;var a={utm_source:"Plugin",utm_medium:encodeURIComponent(t),utm_campaign:"WP"};return n+"?"+Object.keys(a).map((function(e){return"".concat(e,"=").concat(a[e])})).join("&")}var h=lodash,y=n(4184),v=n.n(y),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return(e=-1===e.toString().indexOf(".")?e:e.toFixed(t)).toString().replace(".00","")},E=function(e){e=parseFloat(e);var t=0,n=["K","M","B","T","Q"],a=Math.abs(e);if((e=w(a))<1e3)return e;for(;e>=1e3&&++t<n.length;)e/=1e3;return 0===t?e:w(e)+n[t-1]},b=function(e){var t=e.total,n=void 0===t?0:t,a=e.difference,r=void 0===a?0:a,l=e.revert,o=void 0!==l&&l;n=(0,h.isUndefined)(n)?0:n,r=(0,h.isUndefined)(r)?0:r,o=!(0,h.isUndefined)(o)&&o;var i=Math.abs(r)!==r,s=v()("rank-math-item-difference",{up:!o&&!i&&r>0||o&&i,down:!o&&i||o&&!i&&r>0});return wp.element.createElement("div",{className:"rank-math-item-numbers"},wp.element.createElement("strong",{className:"text-large",title:(0,h.round)(n,2)},E(n)),wp.element.createElement("span",{className:s,title:(0,h.round)(r,2)},E(r)))};function g(e){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},g(e)}function k(){return k=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},k.apply(this,arguments)}function _(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,(r=a.key,l=void 0,l=function(e,t){if("object"!==g(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==g(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===g(l)?l:String(l)),a)}var r,l}function S(e,t){return S=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},S(e,t)}function N(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=O(e);if(t){var r=O(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return function(e,t){if(t&&("object"===g(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,n)}}function O(e){return O=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},O(e)}var x=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&S(e,t)}(l,e);var t,n,a,r=N(l);function l(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),(e=r.apply(this,arguments)).data=e.props.data,e}return t=l,(n=[{key:"render",value:function(){return wp.element.createElement(React.Fragment,null,this.getContentAiScore(),this.getSeoScore(),this.getSiteTraffic(),this.getSiteImpression(),this.getAveragePosition(),this.getIndexVerdict(),this.getPageSpeed())}},{key:"getContentAiScore",value:function(){return!(0,h.isUndefined)(this.data.contentAiScore)&&wp.element.createElement("div",{className:"rank-math-item content-ai-score"},wp.element.createElement("h3",null,(0,d.__)("Content AI score","rank-math"),wp.element.createElement("span",{className:"rank-math-tooltip"},wp.element.createElement("em",{className:"dashicons-before dashicons-editor-help"}),wp.element.createElement("span",null,(0,d.__)("Content AI Score.","rank-math")))),wp.element.createElement("div",{className:"score"},wp.element.createElement("strong",null,this.data.contentAiScore," / 100"),wp.element.createElement("div",{className:"score-wrapper"},wp.element.createElement("span",{className:"score-dot",style:{left:this.data.contentAiScore<13?13:this.data.contentAiScore+"%"}}))))}},{key:"getSeoScore",value:function(){var e=this.data.seo_score,t="rank-math-item seo-score "+this.getScoreClass(e);return wp.element.createElement("div",{className:t},wp.element.createElement("h3",null,(0,d.__)("SEO Score","rank-math"),wp.element.createElement("span",{className:"rank-math-tooltip"},wp.element.createElement("em",{className:"dashicons-before dashicons-editor-help"}),wp.element.createElement("span",null,(0,d.__)("Rank Math's SEO Score","rank-math")))),wp.element.createElement("div",{className:"score"},wp.element.createElement("strong",null,wp.element.createElement("span",null,e))))}},{key:"getSiteImpression",value:function(){if((0,h.isUndefined)(this.data.impressions)&&!rankMath.isAnalyticsConnected)return!1;var e=(0,h.get)(this.data,"impressions",0);return wp.element.createElement("div",{className:"rank-math-item"},wp.element.createElement("h3",null,(0,d.__)("Total Impressions","rank-math"),wp.element.createElement("span",{className:"rank-math-tooltip"},wp.element.createElement("em",{className:"dashicons-before dashicons-editor-help"}),wp.element.createElement("span",null,(0,d.__)("This is how many times your site showed up in the search results.","rank-math")))),wp.element.createElement("div",{className:"score"},wp.element.createElement(b,e)))}},{key:"getAveragePosition",value:function(){return!((0,h.isUndefined)(this.data.position)||!rankMath.isConsoleConnected)&&wp.element.createElement("div",{className:"rank-math-item"},wp.element.createElement("h3",null,(0,d.__)("Average Position","rank-math"),wp.element.createElement("span",{className:"rank-math-tooltip"},wp.element.createElement("em",{className:"dashicons-before dashicons-editor-help"}),wp.element.createElement("span",null,(0,d.__)("This is the average position of your site in the search results.","rank-math")))),wp.element.createElement("div",{className:"score"},wp.element.createElement(b,k({},this.data.position,{revert:!0}))))}},{key:"getSiteTraffic",value:function(){return(0,u.applyFilters)("rank-math-analytics-stats-site-traffic","")}},{key:"getIndexVerdict",value:function(){return(0,u.applyFilters)("rank-math-analytics-stats-index-verdict",wp.element.createElement("div",{className:"rank-math-item blur index-status"},wp.element.createElement("h3",null,(0,d.__)("Index Status","rank-math"),wp.element.createElement("span",{className:"rank-math-tooltip"},wp.element.createElement("em",{className:"dashicons-before dashicons-editor-help"}),wp.element.createElement("span",null,(0,d.__)("URL Inspection Status","rank-math")))),wp.element.createElement("div",{className:"verdict"},wp.element.createElement("i",{className:"indexing_state verdict indexing allowed undefined"}),wp.element.createElement("span",null,"undefined"))),this.data)}},{key:"getPageSpeed",value:function(){return(0,u.applyFilters)("rank-math-analytics-stats-pagespeed",wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{id:"rank-math-analytics-stats-pagespeed",className:"rank-math-single-tab rank-math-item blur"},wp.element.createElement("div",{className:"rank-math-box rank-math-pagespeed-box"},wp.element.createElement("div",{className:"rank-math-pagespeed-header"},wp.element.createElement("h3",null,(0,d.__)("PageSpeed","rank-math"),wp.element.createElement("span",{className:"rank-math-tooltip"},wp.element.createElement("em",{className:"dashicons-before dashicons-editor-help"}))),wp.element.createElement("span",null,"April 2, 2022")),wp.element.createElement("div",{className:"grid"},wp.element.createElement("div",{className:"col pagespeed-desktop"},wp.element.createElement("i",{className:"rm-icon rm-icon-desktop"}),wp.element.createElement("strong",{className:"pagespeed interactive-good"},"0 s"),wp.element.createElement("small",{className:"pagescore score-bad"},"0")),wp.element.createElement("div",{className:"col pagespeed-mobile"},wp.element.createElement("i",{className:"rm-icon rm-icon-mobile"}),wp.element.createElement("strong",{className:"pagespeed interactive-good"},"0 s"),wp.element.createElement("small",{className:"pagescore score-bad"},"0")))))))}},{key:"getScoreClass",value:function(e){return e>80?"great":e>50&&e<81?"good":"bad"}}])&&_(t.prototype,n),a&&_(t,a),Object.defineProperty(t,"prototype",{writable:!1}),l}(p.Component),R=x,C=wp.htmlEntities,P=wp.components;(0,P.withFilters)("rankMath.analytics.keywordAddRemoveButton")((function(e){var t=e.sequence;return wp.element.createElement(p.Fragment,null,t,wp.element.createElement(P.Button,{className:"button button-secondary button-small add-keyword",href:f("pro","Add KW Button"),target:"_blank"},wp.element.createElement("div",{className:"rank-math-tooltip"},wp.element.createElement("i",{className:"rm-icon rm-icon-plus"}),wp.element.createElement("span",null,(0,d.__)("Pro Feature","rank-math")))))})),(0,P.withFilters)("rankMath.analytics.keywordTitle")((function(e){var t=e.query;return wp.element.createElement("h4",null,(0,C.decodeEntities)(t))})),(0,P.withFilters)("rankMath.analytics.keywordDelete")((function(e){var t=e.sequence,n=e.query;return wp.element.createElement(p.Fragment,null,t,wp.element.createElement(P.Button,{className:"button button-secondary button-small add-keyword delete",title:(0,d.__)("Delete from Keyword Manager","rank-math"),onClick:function(){return(0,u.doAction)("rank_math_remove_keyword",n)}},wp.element.createElement("i",{className:"rm-icon rm-icon-trash"})))})),(0,P.withFilters)("rankMath.analytics.IndexingDataFooter")((function(){return wp.element.createElement("div",{className:"row-footer"},wp.element.createElement("table",null,wp.element.createElement("tbody",null,wp.element.createElement("tr",null,wp.element.createElement("td",{colSpan:"8"},wp.element.createElement("div",{className:"last-crawl-data"},wp.element.createElement("div",null,wp.element.createElement("strong",null,(0,d.__)("Google: ","rank-math")),wp.element.createElement("span",{className:"blurred"},(0,d.__)("Available in the PRO version","rank-math"))),wp.element.createElement("div",null,wp.element.createElement("strong",null,(0,d.__)("Last Crawl: ","rank-math")),wp.element.createElement("span",{className:"blurred"},(0,d.__)("PRO Feature","rank-math")))))))))}));function I(e){return I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},I(e)}function T(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,(r=a.key,l=void 0,l=function(e,t){if("object"!==I(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==I(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===I(l)?l:String(l)),a)}var r,l}function j(e,t){return j=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},j(e,t)}function A(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=M(e);if(t){var r=M(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return function(e,t){if(t&&("object"===I(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,n)}}function M(e){return M=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},M(e)}var U=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&j(e,t)}(l,e);var t,n,a,r=A(l);function l(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),r.apply(this,arguments)}return t=l,(n=[{key:"render",value:function(){return wp.element.createElement("div",{className:"inner-elements hidden"},wp.element.createElement("table",null,wp.element.createElement("tbody",null,wp.element.createElement("tr",null,wp.element.createElement("td",{colSpan:"8"},wp.element.createElement("div",{className:"indexing-data-wrapper"},this.getStatusResult(),this.getReferringURLs(),this.getMobileData(),this.richResultsData(),wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks top-20 width-50"},wp.element.createElement("h3",null,(0,d.__)("PRO Version offers Advanced Indexing Stats","rank-math")),wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,d.__)("Monitor metrics like Index Status, Last Crawl date, etc","rank-math")),wp.element.createElement("li",null,(0,d.__)("All the Indexing statistics about your content in one place","rank-math")),wp.element.createElement("li",null,(0,d.__)("Use data provided by Google instead of 3rd party tools","rank-math"))),wp.element.createElement("a",{href:f("pro","Index Status Tab Toggle"),target:"_blank",rel:"noreferrer",className:"button button-primary is-green"},(0,d.__)("Upgrade","rank-math"))))))))))}},{key:"getStatusResult",value:function(){return wp.element.createElement("div",{className:"indexing-data status"},wp.element.createElement("h4",null,"Index Status Result"),Array.from([{label:"Verdict",value:"NEUTRAL"},{label:"Robots Text State",value:"ROBOTS_TXT_STATE_UNSPECIFIED"},{label:"Indexing State",value:"INDEXING_STATE_UNSPECIFIED"},{label:"Last Crawl Time",value:"2022-01-09 05:46:12"},{label:"Page Fetch State",value:"PAGE_FETCH_STATE_UNSPECIFIED"},{label:"Google Canonical",value:"https://example.com/"},{label:"User Canonical",value:"https://example.com/"},{label:"Sitemap",value:"https://example.com/sitemap_index.xml"}]).map((function(e){return wp.element.createElement("div",{key:e.label},wp.element.createElement("span",{className:"label"},e.label),wp.element.createElement("span",{className:"result"},e.value))})))}},{key:"getReferringURLs",value:function(){return wp.element.createElement("div",{className:"indexing-data referring-urls"},wp.element.createElement("h4",null,"Referring URLs"),wp.element.createElement("ul",null,["https://example.com/test1","https://example.com/test2"].map((function(e,t){return wp.element.createElement("li",{key:t},e)}))))}},{key:"getMobileData",value:function(){return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"indexing-data crawled"},wp.element.createElement("h4",null,"Crawled As"),wp.element.createElement("div",null,"UNSPECIFIED")),wp.element.createElement("div",{className:"indexing-data status"},wp.element.createElement("h4",null,"Mobile Usability Result"),wp.element.createElement("div",null,wp.element.createElement("span",null,"Verdict"),wp.element.createElement("span",null,"Unspecified"))))}},{key:"richResultsData",value:function(){return wp.element.createElement("div",{className:"indexing-data detected-items"},wp.element.createElement("h4",null,"detectedItems"),wp.element.createElement("div",{className:"rich-results-wrapper"},wp.element.createElement("div",{className:"rich-results-header"},wp.element.createElement("h4",null,"Rich Result Types"),wp.element.createElement("h4",null,"Items")),wp.element.createElement("div",{className:"rich-results-data"},wp.element.createElement("div",{className:"inner-wrapper"},wp.element.createElement("h4",null,"Breadcrumbs"),wp.element.createElement("div",{className:"schema-data"},wp.element.createElement("strong",null,"Name"),wp.element.createElement("span",null,"Unnamed item"))),wp.element.createElement("div",{className:"inner-wrapper"},wp.element.createElement("h4",null,"Review snippets"),wp.element.createElement("div",{className:"schema-data"},wp.element.createElement("strong",null,"Name"),wp.element.createElement("span",null,"Issues"),wp.element.createElement("div",{className:"sub-issues"},wp.element.createElement("span",null,"Unnamed item"),wp.element.createElement("span",{className:"schema-issues"},wp.element.createElement("strong",null,"Issue Message"),wp.element.createElement("strong",null,"Severity"),wp.element.createElement("div",{className:"issue-details"},wp.element.createElement("span",{className:"error"},wp.element.createElement("span",null,"Item does not support reviews"),wp.element.createElement("span",null,"ERROR")),wp.element.createElement("span",{className:"warning"},wp.element.createElement("span",null,"Missing reviewed item name"),wp.element.createElement("span",null,"WARNING"))))))))))}}])&&T(t.prototype,n),a&&T(t,a),Object.defineProperty(t,"prototype",{writable:!1}),l}(p.Component);(0,P.withFilters)("rankMath.analytics.IndexingDataToggle")(U);function F(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,l,o,i=[],s=!0,c=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(a=l.call(n)).done)&&(i.push(a.value),i.length!==t);s=!0);}catch(e){c=!0,r=e}finally{try{if(!s&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return D(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return D(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}function B(e){return B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},B(e)}function L(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,(r=a.key,l=void 0,l=function(e,t){if("object"!==B(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==B(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===B(l)?l:String(l)),a)}var r,l}window.rankMath=window.rankMath||{},window.rankMath.analyticsHelpers=window.rankMath.analyticsHelpers||{},window.rankMath.analyticsHelpers={translateText:function(e,t){if("page_fetch_state"!==t)return e;var n={PAGE_FETCH_STATE_UNSPECIFIED:(0,d.__)("Unknown fetch state","rank-math"),SUCCESSFUL:(0,d.__)("Successful fetch","rank-math"),SOFT_404:(0,d.__)("Soft 404","rank-math"),BLOCKED_ROBOTS_TXT:(0,d.__)("Blocked by robots.txt","rank-math"),NOT_FOUND:(0,d.__)("Not found (404)","rank-math"),ACCESS_DENIED:(0,d.__)("Blocked due to unauthorized request (401)","rank-math"),SERVER_ERROR:(0,d.__)("Server error (5xx)","rank-math"),REDIRECT_ERROR:(0,d.__)("Redirection error","rank-math"),ACCESS_FORBIDDEN:(0,d.__)("Blocked due to access forbidden (403)","rank-math"),BLOCKED_4XX:(0,d.__)("Blocked due to other 4xx issue (not 403, 404)","rank-math"),INTERNAL_CRAWL_ERROR:(0,d.__)("Internal error","rank-math"),INVALID_URL:(0,d.__)("Invalid URL","rank-math")};return(0,h.has)(n,e)?n[e]:e},convertValue:function(e){return e?e.includes("UNSPECIFIED")?(0,d.__)("Unspecified","rank-math"):"NEUTRAL"===e?(0,d.__)("Excluded","rank-math"):e:(0,d.__)("Not available","rank-math")},noDataMessage:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return t=t||(0,d.sprintf)((0,d.__)("No data to display. Check back later or try to update data manually from %s","rank-math"),'<a href="'+rankMath.adminurl+'?page=rank-math-options-general#setting-panel-analytics"><strong>'+(0,d.__)("Rank Math > General Settings > Analytics > Click 'Update data manually' button.","rank-math")+"</strong></a>"),wp.element.createElement("div",{id:"rank-math-pro-cta",className:"rank-math-analytics-notice"},wp.element.createElement("div",{className:"rank-math-cta-table woocommerce-table rank-math-table"},wp.element.createElement("div",{className:"rank-math-cta-header"},wp.element.createElement("h2",null,e)),wp.element.createElement("div",{className:"rank-math-cta-body",dangerouslySetInnerHTML:{__html:t}})))},elementObserver:function(e,t){var n=F(t,2),a=n[0],r=n[1];return(0,p.useEffect)((function(){var t=e.current;if(t){new IntersectionObserver((function(e){F(e,1)[0].isIntersecting&&!1===a&&r(!0)})).observe(t)}}),[a]),a}};var G=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.init(),this.addContentLoader(),this.events()}var n,a,r;return n=e,(a=[{key:"init",value:function(){t()("body").prepend(this.analyticsWrapper()),m()({method:"GET",path:"rankmath/v1/an/post/"+rankMath.objectID}).then((function(e){e.errorMessage?t()("#rank-math-analytics-stats-wrapper").remove():((0,p.createRoot)(document.getElementById("rank-math-analytics-stats")).render((0,p.createElement)((function(){return wp.element.createElement("div",{className:"rank-math-analytics-wrapper"},wp.element.createElement(R,{data:e}))}))),(0,u.doAction)("rank-math-analytics-stats",e))})).catch((function(){t()("#rank-math-analytics-stats-wrapper").remove()}))}},{key:"analyticsWrapper",value:function(){var e=rankMath.hideFrontendStats?"hide-stats":"";return'<div id="rank-math-analytics-stats-wrapper" class="'.concat(e,'">\n\t\t\t\t<div class="top-icons">\t\n\t\t\t\t\t<a href="').concat(f("analytics-stats-bar","Analytics Stats Bar KB"),'" target="_blank" class="rank-math-stats-bar-help">\n\t\t\t\t\t\t<em class="dashicons-before dashicons-editor-help" title="').concat((0,d.__)("Learn More","rank-math"),'"></em>\n\t\t\t\t\t</a>\n\t\t\t\t\t<a href="#" class="rank-math-analytics-disable-stats" title="').concat((0,d.__)("Close Permanently","rank-math"),'">\n\t\t\t\t\t\t<span class="dashicons dashicons-no-alt"></span>\n\t\t\t\t\t</a>\n\t\t\t\t</div>\n\t\t\t\t<a href="#" class="rank-math-analytics-close-stats">\n\t\t\t\t\t<span class="dashicons dashicons-arrow-up-alt2" title="').concat((0,d.__)("Hide Analytics Stats","rank-math"),'"></span>\n\t\t\t\t\t<svg viewBox="0 0 462.03 462.03" xmlns="http://www.w3.org/2000/svg"><title>').concat((0,d.__)("Show Analytics Stats","rank-math"),'</title><g><path d="m462 234.84-76.17 3.43 13.43 21-127 81.18-126-52.93-146.26 60.97 10.14 24.34 136.1-56.71 128.57 54 138.69-88.61 13.43 21z"></path><path d="m54.1 312.78 92.18-38.41 4.49 1.89v-54.58h-96.67zm210.9-223.57v235.05l7.26 3 89.43-57.05v-181zm-105.44 190.79 96.67 40.62v-165.19h-96.67z"></path></g></svg>\n\t\t\t\t</a>\n\n\t\t\t\t<div id="rank-math-analytics-stats-content">\n\t\t\t\t\t<div id="rank-math-analytics-stats" class="rank-math-analytics"></div>\n\t\t\t\t\t').concat(this.proContent(),"\n\t\t\t\t</div>\n\t\t\t</div>")}},{key:"addContentLoader",value:function(){for(var e=[],t=0;t<4;t++)e.push(wp.element.createElement(s,{animate:!0,backgroundColor:"#f0f2f4",foregroundColor:"#f0f2f4",style:{width:"23%",height:"83px",padding:"1rem"}},wp.element.createElement("rect",{x:"0",y:"0",rx:"0",ry:"0",width:"100%",height:"100%"})));(0,p.createRoot)(document.getElementById("rank-math-analytics-stats")).render((0,p.createElement)((function(){return wp.element.createElement("div",{className:"rank-math-analytics-wrapper"},e)})))}},{key:"proContent",value:function(){return(0,u.applyFilters)("rank_math_is_pro",!1)?"":'<div class="rank-math-analytics-stats-footer">\n\t\t\t<p>\n\t\t\t'.concat((0,d.sprintf)((0,d.__)("Advanced Stats are available in the PRO version, %1$s.","rank-math"),'<a href="'+f("analytics-stats-bar","Analytics Stats Bar")+'" target="_blank" rel="noreferrer" class="button button-primary">'+(0,d.__)("learn More","rank-math")+"</a>"),'\n\t\t\t</p>\n\t\t\t<a href="').concat(f("analytics-stats-bar","Analytics Stats Bar"),'" target="_blank" rel="noreferrer" class="button button-primary">\n\t\t\t\t').concat((0,d.__)("Upgrade to PRO","rank-math"),"\n\t\t\t</a>\n\t\t\t</div>\n\t\t")}},{key:"events",value:function(){var e=t()("#rank-math-analytics-stats-wrapper");t()(".rank-math-analytics-close-stats").on("click",(function(t){return t.preventDefault(),e.toggleClass("hide-stats"),m()({method:"POST",path:"rankmath/v1/an/removeFrontendStats/",data:{toggleBar:!0,hide:e.hasClass("hide-stats")}}),!1})),t()(".rank-math-analytics-disable-stats").on("click",(function(t){return t.preventDefault(),confirm((0,d.__)("Are you sure you want to do this? This action will close the Stats bar permanently. Instead, you can use the toggle icon to minimize it.","rank-math"))&&(e.remove(),m()({method:"POST",path:"rankmath/v1/an/removeFrontendStats/"})),!1}))}}])&&L(n.prototype,a),r&&L(n,r),Object.defineProperty(n,"prototype",{writable:!1}),e}();t()((function(){new G}))}()}();