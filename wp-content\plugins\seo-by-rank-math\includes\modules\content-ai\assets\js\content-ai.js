!function(){var e={634:function(e){e.exports=function(){"use strict";function e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function t(t){for(var a=1;a<arguments.length;a++){var r=null!=arguments[a]?arguments[a]:{};a%2?e(Object(r),!0).forEach((function(e){n(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function n(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var a="​";const r=(e,t,n,a)=>(e=""+e,t=""+t,a&&(e=e.trim(),t=t.trim()),n?e==t:e.toLowerCase()==t.toLowerCase()),i=(e,t)=>e&&Array.isArray(e)&&e.map((e=>o(e,t)));function o(e,t){var n,a={};for(n in e)t.indexOf(n)<0&&(a[n]=e[n]);return a}function s(e){var t=document.createElement("div");return e.replace(/\&#?[0-9a-z]+;/gi,(function(e){return t.innerHTML=e,t.innerText}))}function l(e){return(new DOMParser).parseFromString(e.trim(),"text/html").body.firstElementChild}function c(e,t){for(t=t||"previous";e=e[t+"Sibling"];)if(3==e.nodeType)return e}function u(e){return"string"==typeof e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/`|'/g,"&#039;"):e}function m(e){var t=Object.prototype.toString.call(e).split(" ")[1].slice(0,-1);return e===Object(e)&&"Array"!=t&&"Function"!=t&&"RegExp"!=t&&"HTMLUnknownElement"!=t}function d(e,t,n){function a(e,t){for(var n in t)if(t.hasOwnProperty(n)){if(m(t[n])){m(e[n])?a(e[n],t[n]):e[n]=Object.assign({},t[n]);continue}if(Array.isArray(t[n])){e[n]=Object.assign([],t[n]);continue}e[n]=t[n]}}return e instanceof Object||(e={}),a(e,t),n&&a(e,n),e}function p(){const e=[],t={};for(let n of arguments)for(let a of n)m(a)?t[a.value]||(e.push(a),t[a.value]=1):e.includes(a)||e.push(a);return e}function h(e){return String.prototype.normalize?"string"==typeof e?e.normalize("NFD").replace(/[\u0300-\u036f]/g,""):void 0:e}var g=()=>/(?=.*chrome)(?=.*android)/i.test(navigator.userAgent);function f(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(e=>(e^crypto.getRandomValues(new Uint8Array(1))[0]&15>>e/4).toString(16)))}function _(e){return e&&e.classList&&e.classList.contains(this.settings.classNames.tag)}function b(e,t){var n=window.getSelection();return t=t||n.getRangeAt(0),"string"==typeof e&&(e=document.createTextNode(e)),t&&(t.deleteContents(),t.insertNode(e)),e}function w(e,t,n){return e?(t&&(e.__tagifyTagData=n?t:d({},e.__tagifyTagData||{},t)),e.__tagifyTagData):(console.warn("tag element doesn't exist",e,t),t)}function v(e){if(e&&e.parentNode){var t=e,n=window.getSelection(),a=n.getRangeAt(0);n.rangeCount&&(a.setStartAfter(t),a.collapse(!0),n.removeAllRanges(),n.addRange(a))}}function k(e,t){e.forEach((e=>{if(w(e.previousSibling)||!e.previousSibling){var n=document.createTextNode(a);e.before(n),t&&v(n)}}))}var y={delimiters:",",pattern:null,tagTextProp:"value",maxTags:1/0,callbacks:{},addTagOnBlur:!0,onChangeAfterBlur:!0,duplicates:!1,whitelist:[],blacklist:[],enforceWhitelist:!1,userInput:!0,keepInvalidTags:!1,createInvalidTags:!0,mixTagsAllowedAfter:/,|\.|\:|\s/,mixTagsInterpolator:["[[","]]"],backspace:!0,skipInvalid:!1,pasteAsTags:!0,editTags:{clicks:2,keepInvalid:!0},transformTag:()=>{},trim:!0,a11y:{focusableTags:!1},mixMode:{insertAfterTag:" "},autoComplete:{enabled:!0,rightKey:!1},classNames:{namespace:"tagify",mixMode:"tagify--mix",selectMode:"tagify--select",input:"tagify__input",focus:"tagify--focus",tagNoAnimation:"tagify--noAnim",tagInvalid:"tagify--invalid",tagNotAllowed:"tagify--notAllowed",scopeLoading:"tagify--loading",hasMaxTags:"tagify--hasMaxTags",hasNoTags:"tagify--noTags",empty:"tagify--empty",inputInvalid:"tagify__input--invalid",dropdown:"tagify__dropdown",dropdownWrapper:"tagify__dropdown__wrapper",dropdownHeader:"tagify__dropdown__header",dropdownFooter:"tagify__dropdown__footer",dropdownItem:"tagify__dropdown__item",dropdownItemActive:"tagify__dropdown__item--active",dropdownItemHidden:"tagify__dropdown__item--hidden",dropdownInital:"tagify__dropdown--initial",tag:"tagify__tag",tagText:"tagify__tag-text",tagX:"tagify__tag__removeBtn",tagLoading:"tagify__tag--loading",tagEditing:"tagify__tag--editable",tagFlash:"tagify__tag--flash",tagHide:"tagify__tag--hide"},dropdown:{classname:"",enabled:2,maxItems:10,searchKeys:["value","searchBy"],fuzzySearch:!0,caseSensitive:!1,accentedSearch:!0,includeSelectedTags:!1,highlightFirst:!1,closeOnSelect:!0,clearOnSelect:!0,position:"all",appendTarget:null},hooks:{beforeRemoveTag:()=>Promise.resolve(),beforePaste:()=>Promise.resolve(),suggestionClick:()=>Promise.resolve()}};function E(){this.dropdown={};for(let e in this._dropdown)this.dropdown[e]="function"==typeof this._dropdown[e]?this._dropdown[e].bind(this):this._dropdown[e];this.dropdown.refs()}var C={refs(){this.DOM.dropdown=this.parseTemplate("dropdown",[this.settings]),this.DOM.dropdown.content=this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-wrapper']")},getHeaderRef(){return this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-header']")},getFooterRef(){return this.DOM.dropdown.querySelector("[data-selector='tagify-suggestions-footer']")},getAllSuggestionsRefs(){return[...this.DOM.dropdown.content.querySelectorAll(this.settings.classNames.dropdownItemSelector)]},show(e){var t,n,a,i=this.settings,o="mix"==i.mode&&!i.enforceWhitelist,s=!i.whitelist||!i.whitelist.length,l="manual"==i.dropdown.position;if(e=void 0===e?this.state.inputText:e,!(s&&!o&&!i.templates.dropdownItemNoMatch||!1===i.dropdown.enable||this.state.isLoading||this.settings.readonly)){if(clearTimeout(this.dropdownHide__bindEventsTimeout),this.suggestedListItems=this.dropdown.filterListItems(e),e&&!this.suggestedListItems.length&&(this.trigger("dropdown:noMatch",e),i.templates.dropdownItemNoMatch&&(a=i.templates.dropdownItemNoMatch.call(this,{value:e}))),!a){if(this.suggestedListItems.length)e&&o&&!this.state.editing.scope&&!r(this.suggestedListItems[0].value,e)&&this.suggestedListItems.unshift({value:e});else{if(!e||!o||this.state.editing.scope)return this.input.autocomplete.suggest.call(this),void this.dropdown.hide();this.suggestedListItems=[{value:e}]}n=""+(m(t=this.suggestedListItems[0])?t.value:t),i.autoComplete&&n&&0==n.indexOf(e)&&this.input.autocomplete.suggest.call(this,t)}this.dropdown.fill(a),i.dropdown.highlightFirst&&this.dropdown.highlightOption(this.DOM.dropdown.content.querySelector(i.classNames.dropdownItemSelector)),this.state.dropdown.visible||setTimeout(this.dropdown.events.binding.bind(this)),this.state.dropdown.visible=e||!0,this.state.dropdown.query=e,this.setStateSelection(),l||setTimeout((()=>{this.dropdown.position(),this.dropdown.render()})),setTimeout((()=>{this.trigger("dropdown:show",this.DOM.dropdown)}))}},hide(e){var t=this.DOM,n=t.scope,a=t.dropdown,r="manual"==this.settings.dropdown.position&&!e;if(a&&document.body.contains(a)&&!r)return window.removeEventListener("resize",this.dropdown.position),this.dropdown.events.binding.call(this,!1),n.setAttribute("aria-expanded",!1),a.parentNode.removeChild(a),setTimeout((()=>{this.state.dropdown.visible=!1}),100),this.state.dropdown.query=this.state.ddItemData=this.state.ddItemElm=this.state.selection=null,this.state.tag&&this.state.tag.value.length&&(this.state.flaggedTags[this.state.tag.baseOffset]=this.state.tag),this.trigger("dropdown:hide",a),this},toggle(e){this.dropdown[this.state.dropdown.visible&&!e?"hide":"show"]()},render(){var e,t,n=((t=this.DOM.dropdown.cloneNode(!0)).style.cssText="position:fixed; top:-9999px; opacity:0",document.body.appendChild(t),e=t.clientHeight,t.parentNode.removeChild(t),e),a=this.settings;return"number"==typeof a.dropdown.enabled&&a.dropdown.enabled>=0?(this.DOM.scope.setAttribute("aria-expanded",!0),document.body.contains(this.DOM.dropdown)||(this.DOM.dropdown.classList.add(a.classNames.dropdownInital),this.dropdown.position(n),a.dropdown.appendTarget.appendChild(this.DOM.dropdown),setTimeout((()=>this.DOM.dropdown.classList.remove(a.classNames.dropdownInital)))),this):this},fill(e){e="string"==typeof e?e:this.dropdown.createListHTML(e||this.suggestedListItems);var t,n=this.settings.templates.dropdownContent.call(this,e);this.DOM.dropdown.content.innerHTML=(t=n)?t.replace(/\>[\r\n ]+\</g,"><").split(/>\s+</).join("><").trim():""},fillHeaderFooter(){var e=this.dropdown.filterListItems(this.state.dropdown.query),t=this.parseTemplate("dropdownHeader",[e]),n=this.parseTemplate("dropdownFooter",[e]),a=this.dropdown.getHeaderRef(),r=this.dropdown.getFooterRef();t&&a?.parentNode.replaceChild(t,a),n&&r?.parentNode.replaceChild(n,r)},refilter(e){e=e||this.state.dropdown.query||"",this.suggestedListItems=this.dropdown.filterListItems(e),this.dropdown.fill(),this.suggestedListItems.length||this.dropdown.hide(),this.trigger("dropdown:updated",this.DOM.dropdown)},position(e){var t=this.settings.dropdown;if("manual"!=t.position){var n,a,r,i,o,s,l=this.DOM.dropdown,c=t.placeAbove,u=t.appendTarget===document.body,m=u?window.pageYOffset:t.appendTarget.scrollTop,d=document.fullscreenElement||document.webkitFullscreenElement||document.documentElement,p=d.clientHeight,h=Math.max(d.clientWidth||0,window.innerWidth||0)>480?t.position:"all",g=this.DOM["input"==h?"input":"scope"];if(e=e||l.clientHeight,this.state.dropdown.visible){if("text"==h?(r=(n=function(){const e=document.getSelection();if(e.rangeCount){const t=e.getRangeAt(0),n=t.startContainer,a=t.startOffset;let r,i;if(a>0)return i=document.createRange(),i.setStart(n,a-1),i.setEnd(n,a),r=i.getBoundingClientRect(),{left:r.right,top:r.top,bottom:r.bottom};if(n.getBoundingClientRect)return n.getBoundingClientRect()}return{left:-9999,top:-9999}}()).bottom,a=n.top,i=n.left,o="auto"):(s=function(e){for(var t=0,n=0;e&&e!=d;)t+=e.offsetLeft||0,n+=e.offsetTop||0,e=e.parentNode;return{left:t,top:n}}(t.appendTarget),a=(n=g.getBoundingClientRect()).top-s.top,r=n.bottom-1-s.top,i=n.left-s.left,o=n.width+"px"),!u){let e=function(){for(var e=0,n=t.appendTarget.parentNode;n;)e+=n.scrollTop||0,n=n.parentNode;return e}();a+=e,r+=e}a=Math.floor(a),r=Math.ceil(r),c=void 0===c?p-n.bottom<e:c,l.style.cssText="left:"+(i+window.pageXOffset)+"px; width:"+o+";"+(c?"top: "+(a+m)+"px":"top: "+(r+m)+"px"),l.setAttribute("placement",c?"top":"bottom"),l.setAttribute("position",h)}}},events:{binding(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];var t=this.dropdown.events.callbacks,n=this.listeners.dropdown=this.listeners.dropdown||{position:this.dropdown.position.bind(this,null),onKeyDown:t.onKeyDown.bind(this),onMouseOver:t.onMouseOver.bind(this),onMouseLeave:t.onMouseLeave.bind(this),onClick:t.onClick.bind(this),onScroll:t.onScroll.bind(this)},a=e?"addEventListener":"removeEventListener";"manual"!=this.settings.dropdown.position&&(document[a]("scroll",n.position,!0),window[a]("resize",n.position),window[a]("keydown",n.onKeyDown)),this.DOM.dropdown[a]("mouseover",n.onMouseOver),this.DOM.dropdown[a]("mouseleave",n.onMouseLeave),this.DOM.dropdown[a]("mousedown",n.onClick),this.DOM.dropdown.content[a]("scroll",n.onScroll)},callbacks:{onKeyDown(e){if(this.state.hasFocus&&!this.state.composing){var t=this.DOM.dropdown.querySelector(this.settings.classNames.dropdownItemActiveSelector),n=this.dropdown.getSuggestionDataByNode(t);switch(e.key){case"ArrowDown":case"ArrowUp":case"Down":case"Up":e.preventDefault();var a=this.dropdown.getAllSuggestionsRefs(),r="ArrowUp"==e.key||"Up"==e.key;t&&(t=this.dropdown.getNextOrPrevOption(t,!r)),t&&t.matches(this.settings.classNames.dropdownItemSelector)||(t=a[r?a.length-1:0]),this.dropdown.highlightOption(t,!0);break;case"Escape":case"Esc":this.dropdown.hide();break;case"ArrowRight":if(this.state.actions.ArrowLeft)return;case"Tab":if("mix"!=this.settings.mode&&t&&!this.settings.autoComplete.rightKey&&!this.state.editing){e.preventDefault();var i=this.dropdown.getMappedValue(n);return this.input.autocomplete.set.call(this,i),!1}return!0;case"Enter":e.preventDefault(),this.settings.hooks.suggestionClick(e,{tagify:this,tagData:n,suggestionElm:t}).then((()=>{if(t)return this.dropdown.selectOption(t),t=this.dropdown.getNextOrPrevOption(t,!r),void this.dropdown.highlightOption(t);this.dropdown.hide(),"mix"!=this.settings.mode&&this.addTags(this.state.inputText.trim(),!0)})).catch((e=>e));break;case"Backspace":{if("mix"==this.settings.mode||this.state.editing.scope)return;const e=this.input.raw.call(this);""!=e&&8203!=e.charCodeAt(0)||(!0===this.settings.backspace?this.removeTags():"edit"==this.settings.backspace&&setTimeout(this.editTag.bind(this),0))}}}},onMouseOver(e){var t=e.target.closest(this.settings.classNames.dropdownItemSelector);t&&this.dropdown.highlightOption(t)},onMouseLeave(e){this.dropdown.highlightOption()},onClick(e){if(0==e.button&&e.target!=this.DOM.dropdown&&e.target!=this.DOM.dropdown.content){var t=e.target.closest(this.settings.classNames.dropdownItemSelector),n=this.dropdown.getSuggestionDataByNode(t);this.state.actions.selectOption=!0,setTimeout((()=>this.state.actions.selectOption=!1),50),this.settings.hooks.suggestionClick(e,{tagify:this,tagData:n,suggestionElm:t}).then((()=>{t?this.dropdown.selectOption(t,e):this.dropdown.hide()})).catch((e=>console.warn(e)))}},onScroll(e){var t=e.target,n=t.scrollTop/(t.scrollHeight-t.parentNode.clientHeight)*100;this.trigger("dropdown:scroll",{percentage:Math.round(n)})}}},getSuggestionDataByNode(e){var t=e&&e.getAttribute("value");return this.suggestedListItems.find((e=>e.value==t))||null},getNextOrPrevOption(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];var n=this.dropdown.getAllSuggestionsRefs(),a=n.findIndex((t=>t===e));return t?n[a+1]:n[a-1]},highlightOption(e,t){var n,a=this.settings.classNames.dropdownItemActive;if(this.state.ddItemElm&&(this.state.ddItemElm.classList.remove(a),this.state.ddItemElm.removeAttribute("aria-selected")),!e)return this.state.ddItemData=null,this.state.ddItemElm=null,void this.input.autocomplete.suggest.call(this);n=this.dropdown.getSuggestionDataByNode(e),this.state.ddItemData=n,this.state.ddItemElm=e,e.classList.add(a),e.setAttribute("aria-selected",!0),t&&(e.parentNode.scrollTop=e.clientHeight+e.offsetTop-e.parentNode.clientHeight),this.settings.autoComplete&&(this.input.autocomplete.suggest.call(this,n),this.dropdown.position())},selectOption(e,t){var n=this.settings.dropdown,a=n.clearOnSelect,r=n.closeOnSelect;if(!e)return this.addTags(this.state.inputText,!0),void(r&&this.dropdown.hide());t=t||{};var i=e.getAttribute("value"),o="noMatch"==i,s=this.suggestedListItems.find((e=>(e.value??e)==i));this.trigger("dropdown:select",{data:s,elm:e,event:t}),i&&(s||o)?(this.state.editing?this.onEditTagDone(null,d({__isValid:!0},this.normalizeTags([s])[0])):this["mix"==this.settings.mode?"addMixTags":"addTags"]([s||this.input.raw.call(this)],a),this.DOM.input.parentNode&&(setTimeout((()=>{this.DOM.input.focus(),this.toggleFocusClass(!0)})),r&&setTimeout(this.dropdown.hide.bind(this)),e.addEventListener("transitionend",(()=>{this.dropdown.fillHeaderFooter(),setTimeout((()=>e.remove()),100)}),{once:!0}),e.classList.add(this.settings.classNames.dropdownItemHidden))):r&&setTimeout(this.dropdown.hide.bind(this))},selectAll(e){this.suggestedListItems.length=0,this.dropdown.hide(),this.dropdown.filterListItems("");var t=this.dropdown.filterListItems("");return e||(t=this.state.dropdown.suggestions),this.addTags(t,!0),this},filterListItems(e,t){var n,a,r,i,o,s=this.settings,l=s.dropdown,c=(t=t||{},[]),u=[],d=s.whitelist,p=l.maxItems>=0?l.maxItems:1/0,g=l.searchKeys,f=0;if(!(e="select"==s.mode&&this.value.length&&this.value[0][s.tagTextProp]==e?"":e)||!g.length)return c=l.includeSelectedTags?d:d.filter((e=>!this.isTagDuplicate(m(e)?e.value:e))),this.state.dropdown.suggestions=c,c.slice(0,p);function _(e,t){return t.toLowerCase().split(" ").every((t=>e.includes(t.toLowerCase())))}for(o=l.caseSensitive?""+e:(""+e).toLowerCase();f<d.length;f++){let e,s;n=d[f]instanceof Object?d[f]:{value:d[f]};let p=Object.keys(n).some((e=>g.includes(e)))?g:["value"];l.fuzzySearch&&!t.exact?(r=p.reduce(((e,t)=>e+" "+(n[t]||"")),"").toLowerCase().trim(),l.accentedSearch&&(r=h(r),o=h(o)),e=0==r.indexOf(o),s=r===o,a=_(r,o)):(e=!0,a=p.some((e=>{var a=""+(n[e]||"");return l.accentedSearch&&(a=h(a),o=h(o)),l.caseSensitive||(a=a.toLowerCase()),s=a===o,t.exact?a===o:0==a.indexOf(o)}))),i=!l.includeSelectedTags&&this.isTagDuplicate(m(n)?n.value:n),a&&!i&&(s&&e?u.push(n):"startsWith"==l.sortby&&e?c.unshift(n):c.push(n))}return this.state.dropdown.suggestions=u.concat(c),"function"==typeof l.sortby?l.sortby(u.concat(c),o):u.concat(c).slice(0,p)},getMappedValue(e){var t=this.settings.dropdown.mapValueTo;return t?"function"==typeof t?t(e):e[t]||e.value:e.value},createListHTML(e){return d([],e).map(((e,n)=>{"string"!=typeof e&&"number"!=typeof e||(e={value:e});var a=this.dropdown.getMappedValue(e);return a="string"==typeof a?u(a):a,this.settings.templates.dropdownItem.apply(this,[t(t({},e),{},{mappedValue:a}),this])})).join("")}};const S="@yaireo/tagify/";var x,T={empty:"empty",exceed:"number of tags exceeded",pattern:"pattern mismatch",duplicate:"already exists",notAllowed:"not allowed"},O={wrapper:(e,t)=>`<tags class="${t.classNames.namespace} ${t.mode?`${t.classNames[t.mode+"Mode"]}`:""} ${e.className}"\n                    ${t.readonly?"readonly":""}\n                    ${t.disabled?"disabled":""}\n                    ${t.required?"required":""}\n                    ${"select"===t.mode?"spellcheck='false'":""}\n                    tabIndex="-1">\n            <span ${!t.readonly&&t.userInput?"contenteditable":""} tabIndex="0" data-placeholder="${t.placeholder||"&#8203;"}" aria-placeholder="${t.placeholder||""}"\n                class="${t.classNames.input}"\n                role="textbox"\n                aria-autocomplete="both"\n                aria-multiline="${"mix"==t.mode}"></span>\n                &#8203;\n        </tags>`,tag(e,t){let n=t.settings;return`<tag title="${e.title||e.value}"\n                    contenteditable='false'\n                    spellcheck='false'\n                    tabIndex="${n.a11y.focusableTags?0:-1}"\n                    class="${n.classNames.tag} ${e.class||""}"\n                    ${this.getAttributes(e)}>\n            <x title='' class="${n.classNames.tagX}" role='button' aria-label='remove tag'></x>\n            <div>\n                <span class="${n.classNames.tagText}">${e[n.tagTextProp]||e.value}</span>\n            </div>\n        </tag>`},dropdown(e){var t=e.dropdown,n="manual"==t.position,a=`${e.classNames.dropdown}`;return`<div class="${n?"":a} ${t.classname}" role="listbox" aria-labelledby="dropdown">\n                    <div data-selector='tagify-suggestions-wrapper' class="${e.classNames.dropdownWrapper}"></div>\n                </div>`},dropdownContent(e){var t=this.settings,n=this.state.dropdown.suggestions;return`\n            ${t.templates.dropdownHeader.call(this,n)}\n            ${e}\n            ${t.templates.dropdownFooter.call(this,n)}\n        `},dropdownItem(e){return`<div ${this.getAttributes(e)}\n                    class='${this.settings.classNames.dropdownItem} ${e.class?e.class:""}'\n                    tabindex="0"\n                    role="option">${e.mappedValue||e.value}</div>`},dropdownHeader(e){return`<header data-selector='tagify-suggestions-header' class="${this.settings.classNames.dropdownHeader}"></header>`},dropdownFooter(e){var t=e.length-this.settings.dropdown.maxItems;return t>0?`<footer data-selector='tagify-suggestions-footer' class="${this.settings.classNames.dropdownFooter}">\n                ${t} more items. Refine your search.\n            </footer>`:""},dropdownItemNoMatch:null},P={customBinding(){this.customEventsList.forEach((e=>{this.on(e,this.settings.callbacks[e])}))},binding(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];var t,n=this.events.callbacks,a=e?"addEventListener":"removeEventListener";if(!this.state.mainEvents||!e){for(var r in this.state.mainEvents=e,e&&!this.listeners.main&&(this.events.bindGlobal.call(this),this.settings.isJQueryPlugin&&jQuery(this.DOM.originalInput).on("tagify.removeAllTags",this.removeAllTags.bind(this))),t=this.listeners.main=this.listeners.main||{focus:["input",n.onFocusBlur.bind(this)],keydown:["input",n.onKeydown.bind(this)],click:["scope",n.onClickScope.bind(this)],dblclick:["scope",n.onDoubleClickScope.bind(this)],paste:["input",n.onPaste.bind(this)],drop:["input",n.onDrop.bind(this)],compositionstart:["input",n.onCompositionStart.bind(this)],compositionend:["input",n.onCompositionEnd.bind(this)]})this.DOM[t[r][0]][a](r,t[r][1]);clearInterval(this.listeners.main.originalInputValueObserverInterval),this.listeners.main.originalInputValueObserverInterval=setInterval(n.observeOriginalInputValue.bind(this),500);var i=this.listeners.main.inputMutationObserver||new MutationObserver(n.onInputDOMChange.bind(this));i.disconnect(),"mix"==this.settings.mode&&i.observe(this.DOM.input,{childList:!0})}},bindGlobal(e){var t,n=this.events.callbacks,a=e?"removeEventListener":"addEventListener";if(this.listeners&&(e||!this.listeners.global))for(t of(this.listeners.global=this.listeners.global||[{type:this.isIE?"keydown":"input",target:this.DOM.input,cb:n[this.isIE?"onInputIE":"onInput"].bind(this)},{type:"keydown",target:window,cb:n.onWindowKeyDown.bind(this)},{type:"blur",target:this.DOM.input,cb:n.onFocusBlur.bind(this)},{type:"click",target:document,cb:n.onClickAnywhere.bind(this)}],this.listeners.global))t.target[a](t.type,t.cb)},unbindGlobal(){this.events.bindGlobal.call(this,!0)},callbacks:{onFocusBlur(e){var t=this.settings,n=e.target?this.trim(e.target.textContent):"",a=this.value?.[0]?.[t.tagTextProp],r=e.type,i=t.dropdown.enabled>=0,o={relatedTarget:e.relatedTarget},s=this.state.actions.selectOption&&(i||!t.dropdown.closeOnSelect),l=this.state.actions.addNew&&i,c=e.relatedTarget&&_.call(this,e.relatedTarget)&&this.DOM.scope.contains(e.relatedTarget);if("blur"==r){if(e.relatedTarget===this.DOM.scope)return this.dropdown.hide(),void this.DOM.input.focus();this.postUpdate(),t.onChangeAfterBlur&&this.triggerChangeEvent()}if(!s&&!l)if(this.state.hasFocus="focus"==r&&+new Date,this.toggleFocusClass(this.state.hasFocus),"mix"!=t.mode){if("focus"==r)return this.trigger("focus",o),void(0!==t.dropdown.enabled&&t.userInput||this.dropdown.show(this.value.length?"":void 0));"blur"==r&&(this.trigger("blur",o),this.loading(!1),"select"==t.mode&&(c&&(this.removeTags(),n=""),a===n&&(n="")),n&&!this.state.actions.selectOption&&t.addTagOnBlur&&this.addTags(n,!0)),this.DOM.input.removeAttribute("style"),this.dropdown.hide()}else"focus"==r?this.trigger("focus",o):"blur"==e.type&&(this.trigger("blur",o),this.loading(!1),this.dropdown.hide(),this.state.dropdown.visible=void 0,this.setStateSelection())},onCompositionStart(e){this.state.composing=!0},onCompositionEnd(e){this.state.composing=!1},onWindowKeyDown(e){var t,n=document.activeElement,a=_.call(this,n)&&this.DOM.scope.contains(document.activeElement),r=a&&n.hasAttribute("readonly");if(a&&!r)switch(t=n.nextElementSibling,e.key){case"Backspace":this.settings.readonly||(this.removeTags(n),(t||this.DOM.input).focus());break;case"Enter":setTimeout(this.editTag.bind(this),0,n)}},onKeydown(e){var t=this.settings;if(!this.state.composing&&t.userInput){"select"==t.mode&&t.enforceWhitelist&&this.value.length&&"Tab"!=e.key&&e.preventDefault();var n=this.trim(e.target.textContent);if(this.trigger("keydown",{event:e}),"mix"==t.mode){switch(e.key){case"Left":case"ArrowLeft":this.state.actions.ArrowLeft=!0;break;case"Delete":case"Backspace":if(this.state.editing)return;var a=document.getSelection(),r="Delete"==e.key&&a.anchorOffset==(a.anchorNode.length||0),i=a.anchorNode.previousSibling,o=1==a.anchorNode.nodeType||!a.anchorOffset&&i&&1==i.nodeType&&a.anchorNode.previousSibling;s(this.DOM.input.innerHTML);var l,u,m,d=this.getTagElms(),p=1===a.anchorNode.length&&a.anchorNode.nodeValue==String.fromCharCode(8203);if("edit"==t.backspace&&o)return l=1==a.anchorNode.nodeType?null:a.anchorNode.previousElementSibling,setTimeout(this.editTag.bind(this),0,l),void e.preventDefault();if(g()&&o instanceof Element)return m=c(o),o.hasAttribute("readonly")||o.remove(),this.DOM.input.focus(),void setTimeout((()=>{v(m),this.DOM.input.click()}));if("BR"==a.anchorNode.nodeName)return;if((r||o)&&1==a.anchorNode.nodeType?u=0==a.anchorOffset?r?d[0]:null:d[Math.min(d.length,a.anchorOffset)-1]:r?u=a.anchorNode.nextElementSibling:o instanceof Element&&(u=o),3==a.anchorNode.nodeType&&!a.anchorNode.nodeValue&&a.anchorNode.previousElementSibling&&e.preventDefault(),(o||r)&&!t.backspace)return void e.preventDefault();if("Range"!=a.type&&!a.anchorOffset&&a.anchorNode==this.DOM.input&&"Delete"!=e.key)return void e.preventDefault();if("Range"!=a.type&&u&&u.hasAttribute("readonly"))return void v(c(u));"Delete"==e.key&&p&&w(a.anchorNode.nextSibling)&&this.removeTags(a.anchorNode.nextSibling),clearTimeout(x),x=setTimeout((()=>{var e=document.getSelection();s(this.DOM.input.innerHTML),!r&&e.anchorNode.previousSibling,this.value=[].map.call(d,((e,t)=>{var n=w(e);if(e.parentNode||n.readonly)return n;this.trigger("remove",{tag:e,index:t,data:n})})).filter((e=>e))}),20)}return!0}switch(e.key){case"Backspace":"select"==t.mode&&t.enforceWhitelist&&this.value.length?this.removeTags():this.state.dropdown.visible&&"manual"!=t.dropdown.position||""!=e.target.textContent&&8203!=n.charCodeAt(0)||(!0===t.backspace?this.removeTags():"edit"==t.backspace&&setTimeout(this.editTag.bind(this),0));break;case"Esc":case"Escape":if(this.state.dropdown.visible)return;e.target.blur();break;case"Down":case"ArrowDown":this.state.dropdown.visible||this.dropdown.show();break;case"ArrowRight":{let e=this.state.inputSuggestion||this.state.ddItemData;if(e&&t.autoComplete.rightKey)return void this.addTags([e],!0);break}case"Tab":{let a="select"==t.mode;if(!n||a)return!0;e.preventDefault()}case"Enter":if(this.state.dropdown.visible&&"manual"!=t.dropdown.position)return;e.preventDefault(),setTimeout((()=>{this.state.dropdown.visible||this.state.actions.selectOption||this.addTags(n,!0)}))}}},onInput(e){this.postUpdate();var t=this.settings;if("mix"==t.mode)return this.events.callbacks.onMixTagsInput.call(this,e);var n=this.input.normalize.call(this),a=n.length>=t.dropdown.enabled,r={value:n,inputElm:this.DOM.input},i=this.validateTag({value:n});"select"==t.mode&&this.toggleScopeValidation(i),r.isValid=i,this.state.inputText!=n&&(this.input.set.call(this,n,!1),-1!=n.search(t.delimiters)?this.addTags(n)&&this.input.set.call(this):t.dropdown.enabled>=0&&this.dropdown[a?"show":"hide"](n),this.trigger("input",r))},onMixTagsInput(e){var t,n,a,r,i,o,s,l,c=this.settings,u=this.value.length,m=this.getTagElms(),p=document.createDocumentFragment(),h=window.getSelection().getRangeAt(0),f=[].map.call(m,(e=>w(e).value));if("deleteContentBackward"==e.inputType&&g()&&this.events.callbacks.onKeydown.call(this,{target:e.target,key:"Backspace"}),k(this.getTagElms()),this.value.slice().forEach((e=>{e.readonly&&!f.includes(e.value)&&p.appendChild(this.createTagElem(e))})),p.childNodes.length&&(h.insertNode(p),this.setRangeAtStartEnd(!1,p.lastChild)),m.length!=u)return this.value=[].map.call(this.getTagElms(),(e=>w(e))),void this.update({withoutChangeEvent:!0});if(this.hasMaxTags())return!0;if(window.getSelection&&(o=window.getSelection()).rangeCount>0&&3==o.anchorNode.nodeType){if((h=o.getRangeAt(0).cloneRange()).collapse(!0),h.setStart(o.focusNode,0),a=(t=h.toString().slice(0,h.endOffset)).split(c.pattern).length-1,(n=t.match(c.pattern))&&(r=t.slice(t.lastIndexOf(n[n.length-1]))),r){if(this.state.actions.ArrowLeft=!1,this.state.tag={prefix:r.match(c.pattern)[0],value:r.replace(c.pattern,"")},this.state.tag.baseOffset=o.baseOffset-this.state.tag.value.length,l=this.state.tag.value.match(c.delimiters))return this.state.tag.value=this.state.tag.value.replace(c.delimiters,""),this.state.tag.delimiters=l[0],this.addTags(this.state.tag.value,c.dropdown.clearOnSelect),void this.dropdown.hide();i=this.state.tag.value.length>=c.dropdown.enabled;try{s=(s=this.state.flaggedTags[this.state.tag.baseOffset]).prefix==this.state.tag.prefix&&s.value[0]==this.state.tag.value[0],this.state.flaggedTags[this.state.tag.baseOffset]&&!this.state.tag.value&&delete this.state.flaggedTags[this.state.tag.baseOffset]}catch(e){}(s||a<this.state.mixMode.matchedPatternCount)&&(i=!1)}else this.state.flaggedTags={};this.state.mixMode.matchedPatternCount=a}setTimeout((()=>{this.update({withoutChangeEvent:!0}),this.trigger("input",d({},this.state.tag,{textContent:this.DOM.input.textContent})),this.state.tag&&this.dropdown[i?"show":"hide"](this.state.tag.value)}),10)},onInputIE(e){var t=this;setTimeout((function(){t.events.callbacks.onInput.call(t,e)}))},observeOriginalInputValue(){this.DOM.originalInput.parentNode||this.destroy(),this.DOM.originalInput.value!=this.DOM.originalInput.tagifyValue&&this.loadOriginalValues()},onClickAnywhere(e){e.target==this.DOM.scope||this.DOM.scope.contains(e.target)||(this.toggleFocusClass(!1),this.state.hasFocus=!1)},onClickScope(e){var t=this.settings,n=e.target.closest("."+t.classNames.tag),a=+new Date-this.state.hasFocus;if(e.target!=this.DOM.scope){if(!e.target.classList.contains(t.classNames.tagX))return n?(this.trigger("click",{tag:n,index:this.getNodeIndex(n),data:w(n),event:e}),void(1!==t.editTags&&1!==t.editTags.clicks||this.events.callbacks.onDoubleClickScope.call(this,e))):void(e.target==this.DOM.input&&("mix"==t.mode&&this.fixFirefoxLastTagNoCaret(),a>500)?this.state.dropdown.visible?this.dropdown.hide():0===t.dropdown.enabled&&"mix"!=t.mode&&this.dropdown.show(this.value.length?"":void 0):"select"!=t.mode||0!==t.dropdown.enabled||this.state.dropdown.visible||this.dropdown.show());this.removeTags(e.target.parentNode)}else this.DOM.input.focus()},onPaste(e){e.preventDefault();var t,n,a=this.settings;if("select"==a.mode&&a.enforceWhitelist||!a.userInput)return!1;a.readonly||(t=e.clipboardData||window.clipboardData,n=t.getData("Text"),a.hooks.beforePaste(e,{tagify:this,pastedText:n,clipboardData:t}).then((t=>{void 0===t&&(t=n),t&&(this.injectAtCaret(t,window.getSelection().getRangeAt(0)),"mix"==this.settings.mode?this.events.callbacks.onMixTagsInput.call(this,e):this.settings.pasteAsTags?this.addTags(this.state.inputText+t,!0):this.state.inputText=t)})).catch((e=>e)))},onDrop(e){e.preventDefault()},onEditTagInput(e,t){var n=e.closest("."+this.settings.classNames.tag),a=this.getNodeIndex(n),r=w(n),i=this.input.normalize.call(this,e),o={[this.settings.tagTextProp]:i,__tagId:r.__tagId},s=this.validateTag(o);this.editTagChangeDetected(d(r,o))||!0!==e.originalIsValid||(s=!0),n.classList.toggle(this.settings.classNames.tagInvalid,!0!==s),r.__isValid=s,n.title=!0===s?r.title||r.value:s,i.length>=this.settings.dropdown.enabled&&(this.state.editing&&(this.state.editing.value=i),this.dropdown.show(i)),this.trigger("edit:input",{tag:n,index:a,data:d({},this.value[a],{newValue:i}),event:t})},onEditTagPaste(e,t){var n=(t.clipboardData||window.clipboardData).getData("Text");t.preventDefault();var a=b(n);this.setRangeAtStartEnd(!1,a)},onEditTagFocus(e){this.state.editing={scope:e,input:e.querySelector("[contenteditable]")}},onEditTagBlur(e){if(this.state.hasFocus||this.toggleFocusClass(),this.DOM.scope.contains(e)){var t,n,a=this.settings,r=e.closest("."+a.classNames.tag),i=w(r),o=this.input.normalize.call(this,e),s={[a.tagTextProp]:o,__tagId:i.__tagId},l=i.__originalData,c=this.editTagChangeDetected(d(i,s)),u=this.validateTag(s);if(o)if(c){if(t=this.hasMaxTags(),n=d({},l,{[a.tagTextProp]:this.trim(o),__isValid:u}),a.transformTag.call(this,n,l),!0!==(u=(!t||!0===l.__isValid)&&this.validateTag(n))){if(this.trigger("invalid",{data:n,tag:r,message:u}),a.editTags.keepInvalid)return;a.keepInvalidTags?n.__isValid=u:n=l}else a.keepInvalidTags&&(delete n.title,delete n["aria-invalid"],delete n.class);this.onEditTagDone(r,n)}else this.onEditTagDone(r,l);else this.onEditTagDone(r)}},onEditTagkeydown(e,t){if(!this.state.composing)switch(this.trigger("edit:keydown",{event:e}),e.key){case"Esc":case"Escape":t.parentNode.replaceChild(t.__tagifyTagData.__originalHTML,t),this.state.editing=!1;case"Enter":case"Tab":e.preventDefault(),e.target.blur()}},onDoubleClickScope(e){var t,n,a=e.target.closest("."+this.settings.classNames.tag),r=w(a),i=this.settings;a&&i.userInput&&!1!==r.editable&&(t=a.classList.contains(this.settings.classNames.tagEditing),n=a.hasAttribute("readonly"),"select"==i.mode||i.readonly||t||n||!this.settings.editTags||this.editTag(a),this.toggleFocusClass(!0),this.trigger("dblclick",{tag:a,index:this.getNodeIndex(a),data:w(a)}))},onInputDOMChange(e){e.forEach((e=>{e.addedNodes.forEach((e=>{if("<div><br></div>"==e.outerHTML)e.replaceWith(document.createElement("br"));else if(1==e.nodeType&&e.querySelector(this.settings.classNames.tagSelector)){let t=document.createTextNode("");3==e.childNodes[0].nodeType&&"BR"!=e.previousSibling.nodeName&&(t=document.createTextNode("\n")),e.replaceWith(t,...[...e.childNodes].slice(0,-1)),v(t)}else if(_.call(this,e))if(3!=e.previousSibling?.nodeType||e.previousSibling.textContent||e.previousSibling.remove(),e.previousSibling&&"BR"==e.previousSibling.nodeName){e.previousSibling.replaceWith("\n"+a);let t=e.nextSibling,n="";for(;t;)n+=t.textContent,t=t.nextSibling;n.trim()&&v(e.previousSibling)}else e.previousSibling&&!w(e.previousSibling)||e.before(a)})),e.removedNodes.forEach((e=>{e&&"BR"==e.nodeName&&_.call(this,t)&&(this.removeTags(t),this.fixFirefoxLastTagNoCaret())}))}));var t=this.DOM.input.lastChild;t&&""==t.nodeValue&&t.remove(),t&&"BR"==t.nodeName||this.DOM.input.appendChild(document.createElement("br"))}}};function A(e,t){if(!e){console.warn("Tagify:","input element not found",e);const t=new Proxy(this,{get:()=>()=>t});return t}if(e.__tagify)return console.warn("Tagify: ","input element is already Tagified - Same instance is returned.",e),e.__tagify;var n;d(this,function(e){var t=document.createTextNode("");function n(e,n,a){a&&n.split(/\s+/g).forEach((n=>t[e+"EventListener"].call(t,n,a)))}return{off(e,t){return n("remove",e,t),this},on(e,t){return t&&"function"==typeof t&&n("add",e,t),this},trigger(n,a,r){var i;if(r=r||{cloneData:!0},n)if(e.settings.isJQueryPlugin)"remove"==n&&(n="removeTag"),jQuery(e.DOM.originalInput).triggerHandler(n,[a]);else{try{var o="object"==typeof a?a:{value:a};if((o=r.cloneData?d({},o):o).tagify=this,a.event&&(o.event=this.cloneEvent(a.event)),a instanceof Object)for(var s in a)a[s]instanceof HTMLElement&&(o[s]=a[s]);i=new CustomEvent(n,{detail:o})}catch(e){console.warn(e)}t.dispatchEvent(i)}}}}(this)),this.isFirefox=/firefox|fxios/i.test(navigator.userAgent)&&!/seamonkey/i.test(navigator.userAgent),this.isIE=window.document.documentMode,t=t||{},this.getPersistedData=(n=t.id,e=>{let t,a="/"+e;if(1==localStorage.getItem(S+n+"/v",1))try{t=JSON.parse(localStorage[S+n+a])}catch(e){}return t}),this.setPersistedData=(e=>e?(localStorage.setItem(S+e+"/v",1),(t,n)=>{let a="/"+n,r=JSON.stringify(t);t&&n&&(localStorage.setItem(S+e+a,r),dispatchEvent(new Event("storage")))}):()=>{})(t.id),this.clearPersistedData=(e=>t=>{const n=S+"/"+e+"/";if(t)localStorage.removeItem(n+t);else for(let e in localStorage)e.includes(n)&&localStorage.removeItem(e)})(t.id),this.applySettings(e,t),this.state={inputText:"",editing:!1,composing:!1,actions:{},mixMode:{},dropdown:{},flaggedTags:{}},this.value=[],this.listeners={},this.DOM={},this.build(e),E.call(this),this.getCSSVars(),this.loadOriginalValues(),this.events.customBinding.call(this),this.events.binding.call(this),e.autofocus&&this.DOM.input.focus(),e.__tagify=this}return A.prototype={_dropdown:C,getSetTagData:w,helpers:{sameStr:r,removeCollectionProp:i,omit:o,isObject:m,parseHTML:l,escapeHTML:u,extend:d,concatWithoutDups:p,getUID:f,isNodeTag:_},customEventsList:["change","add","remove","invalid","input","click","keydown","focus","blur","edit:input","edit:beforeUpdate","edit:updated","edit:start","edit:keydown","dropdown:show","dropdown:hide","dropdown:select","dropdown:updated","dropdown:noMatch","dropdown:scroll"],dataProps:["__isValid","__removed","__originalData","__originalHTML","__tagId"],trim(e){return this.settings.trim&&e&&"string"==typeof e?e.trim():e},parseHTML:l,templates:O,parseTemplate(e,t){return l((e=this.settings.templates[e]||e).apply(this,t))},set whitelist(e){const t=e&&Array.isArray(e);this.settings.whitelist=t?e:[],this.setPersistedData(t?e:[],"whitelist")},get whitelist(){return this.settings.whitelist},generateClassSelectors(e){for(let t in e){let n=t;Object.defineProperty(e,n+"Selector",{get(){return"."+this[n].split(" ")[0]}})}},applySettings(e,n){y.templates=this.templates;var a=d({},y,"mix"==n.mode?{dropdown:{position:"text"}}:{}),r=this.settings=d({},a,n);if(r.disabled=e.hasAttribute("disabled"),r.readonly=r.readonly||e.hasAttribute("readonly"),r.placeholder=u(e.getAttribute("placeholder")||r.placeholder||""),r.required=e.hasAttribute("required"),this.generateClassSelectors(r.classNames),void 0===r.dropdown.includeSelectedTags&&(r.dropdown.includeSelectedTags=r.duplicates),this.isIE&&(r.autoComplete=!1),["whitelist","blacklist"].forEach((t=>{var n=e.getAttribute("data-"+t);n&&(n=n.split(r.delimiters))instanceof Array&&(r[t]=n)})),"autoComplete"in n&&!m(n.autoComplete)&&(r.autoComplete=y.autoComplete,r.autoComplete.enabled=n.autoComplete),"mix"==r.mode&&(r.pattern=r.pattern||/@/,r.autoComplete.rightKey=!0,r.delimiters=n.delimiters||null,r.tagTextProp&&!r.dropdown.searchKeys.includes(r.tagTextProp)&&r.dropdown.searchKeys.push(r.tagTextProp)),e.pattern)try{r.pattern=new RegExp(e.pattern)}catch(e){}if(r.delimiters){r._delimiters=r.delimiters;try{r.delimiters=new RegExp(this.settings.delimiters,"g")}catch(e){}}r.disabled&&(r.userInput=!1),this.TEXTS=t(t({},T),r.texts||{}),("select"!=r.mode||n.dropdown?.enabled)&&r.userInput||(r.dropdown.enabled=0),r.dropdown.appendTarget=n.dropdown?.appendTarget||document.body;let i=this.getPersistedData("whitelist");Array.isArray(i)&&(this.whitelist=Array.isArray(r.whitelist)?p(r.whitelist,i):i)},getAttributes(e){var t,n=this.getCustomAttributes(e),a="";for(t in n)a+=" "+t+(void 0!==e[t]?`="${n[t]}"`:"");return a},getCustomAttributes(e){if(!m(e))return"";var t,n={};for(t in e)"__"!=t.slice(0,2)&&"class"!=t&&e.hasOwnProperty(t)&&void 0!==e[t]&&(n[t]=u(e[t]));return n},setStateSelection(){var e=window.getSelection(),t={anchorOffset:e.anchorOffset,anchorNode:e.anchorNode,range:e.getRangeAt&&e.rangeCount&&e.getRangeAt(0)};return this.state.selection=t,t},getCSSVars(){var e,t=getComputedStyle(this.DOM.scope,null);this.CSSVars={tagHideTransition:(e=>{let t=e.value;return"s"==e.unit?1e3*t:t})(function(e){if(!e)return{};var t=(e=e.trim().split(" ")[0]).split(/\d+/g).filter((e=>e)).pop().trim();return{value:+e.split(t).filter((e=>e))[0].trim(),unit:t}}((e="tag-hide-transition",t.getPropertyValue("--"+e))))}},build(e){var t=this.DOM;this.settings.mixMode.integrated?(t.originalInput=null,t.scope=e,t.input=e):(t.originalInput=e,t.originalInput_tabIndex=e.tabIndex,t.scope=this.parseTemplate("wrapper",[e,this.settings]),t.input=t.scope.querySelector(this.settings.classNames.inputSelector),e.parentNode.insertBefore(t.scope,e),e.tabIndex=-1)},destroy(){this.events.unbindGlobal.call(this),this.DOM.scope.parentNode.removeChild(this.DOM.scope),this.DOM.originalInput.tabIndex=this.DOM.originalInput_tabIndex,delete this.DOM.originalInput.__tagify,this.dropdown.hide(!0),clearTimeout(this.dropdownHide__bindEventsTimeout),clearInterval(this.listeners.main.originalInputValueObserverInterval)},loadOriginalValues(e){var t,n=this.settings;if(this.state.blockChangeEvent=!0,void 0===e){const t=this.getPersistedData("value");e=t&&!this.DOM.originalInput.value?t:n.mixMode.integrated?this.DOM.input.textContent:this.DOM.originalInput.value}if(this.removeAllTags(),e)if("mix"==n.mode)this.parseMixTags(e),(t=this.DOM.input.lastChild)&&"BR"==t.tagName||this.DOM.input.insertAdjacentHTML("beforeend","<br>");else{try{JSON.parse(e)instanceof Array&&(e=JSON.parse(e))}catch(e){}this.addTags(e,!0).forEach((e=>e&&e.classList.add(n.classNames.tagNoAnimation)))}else this.postUpdate();this.state.lastOriginalValueReported=n.mixMode.integrated?"":this.DOM.originalInput.value},cloneEvent(e){var t={};for(var n in e)"path"!=n&&(t[n]=e[n]);return t},loading(e){return this.state.isLoading=e,this.DOM.scope.classList[e?"add":"remove"](this.settings.classNames.scopeLoading),this},tagLoading(e,t){return e&&e.classList[t?"add":"remove"](this.settings.classNames.tagLoading),this},toggleClass(e,t){"string"==typeof e&&this.DOM.scope.classList.toggle(e,t)},toggleScopeValidation(e){var t=!0===e||void 0===e;!this.settings.required&&e&&e===this.TEXTS.empty&&(t=!0),this.toggleClass(this.settings.classNames.tagInvalid,!t),this.DOM.scope.title=t?"":e},toggleFocusClass(e){this.toggleClass(this.settings.classNames.focus,!!e)},triggerChangeEvent:function(){if(!this.settings.mixMode.integrated){var e=this.DOM.originalInput,t=this.state.lastOriginalValueReported!==e.value,n=new CustomEvent("change",{bubbles:!0});t&&(this.state.lastOriginalValueReported=e.value,n.simulated=!0,e._valueTracker&&e._valueTracker.setValue(Math.random()),e.dispatchEvent(n),this.trigger("change",this.state.lastOriginalValueReported),e.value=this.state.lastOriginalValueReported)}},events:P,fixFirefoxLastTagNoCaret(){},setRangeAtStartEnd(e,t){if(t){e="number"==typeof e?e:!!e,t=t.lastChild||t;var n=document.getSelection();if(n.focusNode instanceof Element&&!this.DOM.input.contains(n.focusNode))return!0;try{n.rangeCount>=1&&["Start","End"].forEach((a=>n.getRangeAt(0)["set"+a](t,e||t.length)))}catch(e){}}},insertAfterTag(e,t){if(t=t||this.settings.mixMode.insertAfterTag,e&&e.parentNode&&t)return t="string"==typeof t?document.createTextNode(t):t,e.parentNode.insertBefore(t,e.nextSibling),t},editTagChangeDetected(e){var t=e.__originalData;for(var n in t)if(!this.dataProps.includes(n)&&e[n]!=t[n])return!0;return!1},getTagTextNode(e){return e.querySelector(this.settings.classNames.tagTextSelector)},setTagTextNode(e,t){this.getTagTextNode(e).innerHTML=u(t)},editTag(e,t){e=e||this.getLastTag(),t=t||{},this.dropdown.hide();var n=this.settings,a=this.getTagTextNode(e),r=this.getNodeIndex(e),i=w(e),o=this.events.callbacks,s=this,l=!0;if(a){if(!(i instanceof Object&&"editable"in i)||i.editable)return i=w(e,{__originalData:d({},i),__originalHTML:e.cloneNode(!0)}),w(i.__originalHTML,i.__originalData),a.setAttribute("contenteditable",!0),e.classList.add(n.classNames.tagEditing),a.addEventListener("focus",o.onEditTagFocus.bind(this,e)),a.addEventListener("blur",(function(){setTimeout((()=>o.onEditTagBlur.call(s,s.getTagTextNode(e))))})),a.addEventListener("input",o.onEditTagInput.bind(this,a)),a.addEventListener("paste",o.onEditTagPaste.bind(this,a)),a.addEventListener("keydown",(t=>o.onEditTagkeydown.call(this,t,e))),a.addEventListener("compositionstart",o.onCompositionStart.bind(this)),a.addEventListener("compositionend",o.onCompositionEnd.bind(this)),t.skipValidation||(l=this.editTagToggleValidity(e)),a.originalIsValid=l,this.trigger("edit:start",{tag:e,index:r,data:i,isValid:l}),a.focus(),this.setRangeAtStartEnd(!1,a),this}else console.warn("Cannot find element in Tag template: .",n.classNames.tagTextSelector)},editTagToggleValidity(e,t){var n;if(t=t||w(e))return(n=!("__isValid"in t)||!0===t.__isValid)||this.removeTagsFromValue(e),this.update(),e.classList.toggle(this.settings.classNames.tagNotAllowed,!n),t.__isValid=n,t.__isValid;console.warn("tag has no data: ",e,t)},onEditTagDone(e,t){t=t||{};var n={tag:e=e||this.state.editing.scope,index:this.getNodeIndex(e),previousData:w(e),data:t};this.trigger("edit:beforeUpdate",n,{cloneData:!1}),this.state.editing=!1,delete t.__originalData,delete t.__originalHTML,e&&t[this.settings.tagTextProp]?(e=this.replaceTag(e,t),this.editTagToggleValidity(e,t),this.settings.a11y.focusableTags?e.focus():v(e)):e&&this.removeTags(e),this.trigger("edit:updated",n),this.dropdown.hide(),this.settings.keepInvalidTags&&this.reCheckInvalidTags()},replaceTag(e,t){t&&t.value||(t=e.__tagifyTagData),t.__isValid&&1!=t.__isValid&&d(t,this.getInvalidTagAttrs(t,t.__isValid));var n=this.createTagElem(t);return e.parentNode.replaceChild(n,e),this.updateValueByDOMTags(),n},updateValueByDOMTags(){this.value.length=0,[].forEach.call(this.getTagElms(),(e=>{e.classList.contains(this.settings.classNames.tagNotAllowed.split(" ")[0])||this.value.push(w(e))})),this.update()},injectAtCaret(e,t){return!(t=t||this.state.selection?.range)&&e?(this.appendMixTags(e),this):(b(e,t),this.setRangeAtStartEnd(!1,e),this.updateValueByDOMTags(),this.update(),this)},input:{set(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];var n=this.settings.dropdown.closeOnSelect;this.state.inputText=e,t&&(this.DOM.input.innerHTML=u(""+e)),!e&&n&&this.dropdown.hide.bind(this),this.input.autocomplete.suggest.call(this),this.input.validate.call(this)},raw(){return this.DOM.input.textContent},validate(){var e=!this.state.inputText||!0===this.validateTag({value:this.state.inputText});return this.DOM.input.classList.toggle(this.settings.classNames.inputInvalid,!e),e},normalize(e){var t=e||this.DOM.input,n=[];t.childNodes.forEach((e=>3==e.nodeType&&n.push(e.nodeValue))),n=n.join("\n");try{n=n.replace(/(?:\r\n|\r|\n)/g,this.settings.delimiters.source.charAt(0))}catch(e){}return n=n.replace(/\s/g," "),this.trim(n)},autocomplete:{suggest(e){if(this.settings.autoComplete.enabled){"string"==typeof(e=e||{value:""})&&(e={value:e});var t=this.dropdown.getMappedValue(e);if("number"!=typeof t){var n=t.substr(0,this.state.inputText.length).toLowerCase(),a=t.substring(this.state.inputText.length);t&&this.state.inputText&&n==this.state.inputText.toLowerCase()?(this.DOM.input.setAttribute("data-suggest",a),this.state.inputSuggestion=e):(this.DOM.input.removeAttribute("data-suggest"),delete this.state.inputSuggestion)}}},set(e){var t=this.DOM.input.getAttribute("data-suggest"),n=e||(t?this.state.inputText+t:null);return!!n&&("mix"==this.settings.mode?this.replaceTextWithNode(document.createTextNode(this.state.tag.prefix+n)):(this.input.set.call(this,n),this.setRangeAtStartEnd(!1,this.DOM.input)),this.input.autocomplete.suggest.call(this),this.dropdown.hide(),!0)}}},getTagIdx(e){return this.value.findIndex((t=>t.__tagId==(e||{}).__tagId))},getNodeIndex(e){var t=0;if(e)for(;e=e.previousElementSibling;)t++;return t},getTagElms(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a="."+[...this.settings.classNames.tag.split(" "),...t].join(".");return[].slice.call(this.DOM.scope.querySelectorAll(a))},getLastTag(){var e=this.DOM.scope.querySelectorAll(`${this.settings.classNames.tagSelector}:not(.${this.settings.classNames.tagHide}):not([readonly])`);return e[e.length-1]},isTagDuplicate(e,t,n){var a=0;if("select"==this.settings.mode)return!1;for(let i of this.value)r(this.trim(""+e),i.value,t)&&n!=i.__tagId&&a++;return a},getTagIndexByValue(e){var t=[],n=this.settings.dropdown.caseSensitive;return this.getTagElms().forEach(((a,i)=>{a.__tagifyTagData&&r(this.trim(a.__tagifyTagData.value),e,n)&&t.push(i)})),t},getTagElmByValue(e){var t=this.getTagIndexByValue(e)[0];return this.getTagElms()[t]},flashTag(e){e&&(e.classList.add(this.settings.classNames.tagFlash),setTimeout((()=>{e.classList.remove(this.settings.classNames.tagFlash)}),100))},isTagBlacklisted(e){return e=this.trim(e.toLowerCase()),this.settings.blacklist.filter((t=>(""+t).toLowerCase()==e)).length},isTagWhitelisted(e){return!!this.getWhitelistItem(e)},getWhitelistItem(e,t,n){t=t||"value";var a,i=this.settings;return(n=n||i.whitelist).some((n=>{var o="string"==typeof n?n:n[t]||n.value;if(r(o,e,i.dropdown.caseSensitive,i.trim))return a="string"==typeof n?{value:n}:n,!0})),a||"value"!=t||"value"==i.tagTextProp||(a=this.getWhitelistItem(e,i.tagTextProp,n)),a},validateTag(e){var t=this.settings,n="value"in e?"value":t.tagTextProp,a=this.trim(e[n]+"");return(e[n]+"").trim()?"mix"!=t.mode&&t.pattern&&t.pattern instanceof RegExp&&!t.pattern.test(a)?this.TEXTS.pattern:!t.duplicates&&this.isTagDuplicate(a,t.dropdown.caseSensitive,e.__tagId)?this.TEXTS.duplicate:this.isTagBlacklisted(a)||t.enforceWhitelist&&!this.isTagWhitelisted(a)?this.TEXTS.notAllowed:!t.validate||t.validate(e):this.TEXTS.empty},getInvalidTagAttrs(e,t){return{"aria-invalid":!0,class:`${e.class||""} ${this.settings.classNames.tagNotAllowed}`.trim(),title:t}},hasMaxTags(){return this.value.length>=this.settings.maxTags&&this.TEXTS.exceed},setReadonly(e,t){var n=this.settings;document.activeElement.blur(),n[t||"readonly"]=e,this.DOM.scope[(e?"set":"remove")+"Attribute"](t||"readonly",!0),this.settings.userInput=!0,this.setContentEditable(!e)},setContentEditable(e){this.settings.userInput&&(this.DOM.input.contentEditable=e,this.DOM.input.tabIndex=e?0:-1)},setDisabled(e){this.setReadonly(e,"disabled")},normalizeTags(e){var t=this.settings,n=t.whitelist,a=t.delimiters,r=t.mode,i=t.tagTextProp,o=[],s=!!n&&n[0]instanceof Object,l=Array.isArray(e),c=l&&e[0].value,u=e=>(e+"").split(a).filter((e=>e)).map((e=>({[i]:this.trim(e),value:this.trim(e)})));if("number"==typeof e&&(e=e.toString()),"string"==typeof e){if(!e.trim())return[];e=u(e)}else l&&(e=[].concat(...e.map((e=>null!=e.value?e:u(e)))));return s&&!c&&(e.forEach((e=>{var t=o.map((e=>e.value)),n=this.dropdown.filterListItems.call(this,e[i],{exact:!0});this.settings.duplicates||(n=n.filter((e=>!t.includes(e.value))));var a=n.length>1?this.getWhitelistItem(e[i],i,n):n[0];a&&a instanceof Object?o.push(a):"mix"!=r&&(null==e.value&&(e.value=e[i]),o.push(e))})),o.length&&(e=o)),e},parseMixTags(e){var t=this.settings,n=t.mixTagsInterpolator,a=t.duplicates,r=t.transformTag,i=t.enforceWhitelist,o=t.maxTags,s=t.tagTextProp,l=[];e=e.split(n[0]).map(((e,t)=>{var c,u,m,d=e.split(n[1]),p=d[0],h=l.length==o;try{if(p==+p)throw Error;u=JSON.parse(p)}catch(e){u=this.normalizeTags(p)[0]||{value:p}}if(r.call(this,u),h||!(d.length>1)||i&&!this.isTagWhitelisted(u.value)||!a&&this.isTagDuplicate(u.value)){if(e)return t?n[0]+e:e}else u[c=u[s]?s:"value"]=this.trim(u[c]),m=this.createTagElem(u),l.push(u),m.classList.add(this.settings.classNames.tagNoAnimation),d[0]=m.outerHTML,this.value.push(u);return d.join("")})).join(""),this.DOM.input.innerHTML=e,this.DOM.input.appendChild(document.createTextNode("")),this.DOM.input.normalize();var c=this.getTagElms();return c.forEach(((e,t)=>w(e,l[t]))),this.update({withoutChangeEvent:!0}),k(c,this.state.hasFocus),e},replaceTextWithNode(e,t){if(this.state.tag||t){t=t||this.state.tag.prefix+this.state.tag.value;var n,a,r=this.state.selection||window.getSelection(),i=r.anchorNode,o=this.state.tag.delimiters?this.state.tag.delimiters.length:0;return i.splitText(r.anchorOffset-o),-1==(n=i.nodeValue.lastIndexOf(t))||(a=i.splitText(n),e&&i.parentNode.replaceChild(e,a)),!0}},selectTag(e,t){var n=this.settings;if(!n.enforceWhitelist||this.isTagWhitelisted(t.value)){this.input.set.call(this,t[n.tagTextProp]||t.value,!0),this.state.actions.selectOption&&setTimeout((()=>this.setRangeAtStartEnd(!1,this.DOM.input)));var a=this.getLastTag();return a?this.replaceTag(a,t):this.appendTag(e),this.value[0]=t,this.update(),this.trigger("add",{tag:e,data:t}),[e]}},addEmptyTag(e){var t=d({value:""},e||{}),n=this.createTagElem(t);w(n,t),this.appendTag(n),this.editTag(n,{skipValidation:!0})},addTags(e,t,n){var a=[],r=this.settings,i=[],o=document.createDocumentFragment();if(n=n||r.skipInvalid,!e||0==e.length)return a;switch(e=this.normalizeTags(e),r.mode){case"mix":return this.addMixTags(e);case"select":t=!1,this.removeAllTags()}return this.DOM.input.removeAttribute("style"),e.forEach((e=>{var t,s={},l=Object.assign({},e,{value:e.value+""});if(e=Object.assign({},l),r.transformTag.call(this,e),e.__isValid=this.hasMaxTags()||this.validateTag(e),!0!==e.__isValid){if(n)return;if(d(s,this.getInvalidTagAttrs(e,e.__isValid),{__preInvalidData:l}),e.__isValid==this.TEXTS.duplicate&&this.flashTag(this.getTagElmByValue(e.value)),!r.createInvalidTags)return void i.push(e.value)}if("readonly"in e&&(e.readonly?s["aria-readonly"]=!0:delete e.readonly),t=this.createTagElem(e,s),a.push(t),"select"==r.mode)return this.selectTag(t,e);o.appendChild(t),e.__isValid&&!0===e.__isValid?(this.value.push(e),this.trigger("add",{tag:t,index:this.value.length-1,data:e})):(this.trigger("invalid",{data:e,index:this.value.length,tag:t,message:e.__isValid}),r.keepInvalidTags||setTimeout((()=>this.removeTags(t,!0)),1e3)),this.dropdown.position()})),this.appendTag(o),this.update(),e.length&&t&&(this.input.set.call(this,r.createInvalidTags?"":i.join(r._delimiters)),this.setRangeAtStartEnd(!1,this.DOM.input)),r.dropdown.enabled&&this.dropdown.refilter(),a},addMixTags(e){if((e=this.normalizeTags(e))[0].prefix||this.state.tag)return this.prefixedTextToTag(e[0]);var t=document.createDocumentFragment();return e.forEach((e=>{var n=this.createTagElem(e);t.appendChild(n)})),this.appendMixTags(t),t},appendMixTags(e){var t=!!this.state.selection;t?this.injectAtCaret(e):(this.DOM.input.focus(),(t=this.setStateSelection()).range.setStart(this.DOM.input,t.range.endOffset),t.range.setEnd(this.DOM.input,t.range.endOffset),this.DOM.input.appendChild(e),this.updateValueByDOMTags(),this.update())},prefixedTextToTag(e){var t,n=this.settings,a=this.state.tag.delimiters;if(n.transformTag.call(this,e),e.prefix=e.prefix||this.state.tag?this.state.tag.prefix:(n.pattern.source||n.pattern)[0],t=this.createTagElem(e),this.replaceTextWithNode(t)||this.DOM.input.appendChild(t),setTimeout((()=>t.classList.add(this.settings.classNames.tagNoAnimation)),300),this.value.push(e),this.update(),!a){var r=this.insertAfterTag(t)||t;setTimeout(v,0,r)}return this.state.tag=null,this.trigger("add",d({},{tag:t},{data:e})),t},appendTag(e){var t=this.DOM,n=t.input;t.scope.insertBefore(e,n)},createTagElem(e,n){e.__tagId=f();var a,r=d({},e,t({value:u(e.value+"")},n));return function(e){for(var t,n=document.createNodeIterator(e,NodeFilter.SHOW_TEXT,null,!1);t=n.nextNode();)t.textContent.trim()||t.parentNode.removeChild(t)}(a=this.parseTemplate("tag",[r,this])),w(a,e),a},reCheckInvalidTags(){var e=this.settings;this.getTagElms(e.classNames.tagNotAllowed).forEach(((t,n)=>{var a=w(t),r=this.hasMaxTags(),i=this.validateTag(a),o=!0===i&&!r;if("select"==e.mode&&this.toggleScopeValidation(i),o)return a=a.__preInvalidData?a.__preInvalidData:{value:a.value},this.replaceTag(t,a);t.title=r||i}))},removeTags(e,t,n){var a,r=this.settings;if(e=e&&e instanceof HTMLElement?[e]:e instanceof Array?e:e?[e]:[this.getLastTag()],a=e.reduce(((e,t)=>{t&&"string"==typeof t&&(t=this.getTagElmByValue(t));var n=w(t);return t&&n&&!n.readonly&&e.push({node:t,idx:this.getTagIdx(n),data:w(t,{__removed:!0})}),e}),[]),n="number"==typeof n?n:this.CSSVars.tagHideTransition,"select"==r.mode&&(n=0,this.input.set.call(this)),1==a.length&&"select"!=r.mode&&a[0].node.classList.contains(r.classNames.tagNotAllowed)&&(t=!0),a.length)return r.hooks.beforeRemoveTag(a,{tagify:this}).then((()=>{function e(e){e.node.parentNode&&(e.node.parentNode.removeChild(e.node),t?r.keepInvalidTags&&this.trigger("remove",{tag:e.node,index:e.idx}):(this.trigger("remove",{tag:e.node,index:e.idx,data:e.data}),this.dropdown.refilter(),this.dropdown.position(),this.DOM.input.normalize(),r.keepInvalidTags&&this.reCheckInvalidTags()))}n&&n>10&&1==a.length?function(t){t.node.style.width=parseFloat(window.getComputedStyle(t.node).width)+"px",document.body.clientTop,t.node.classList.add(r.classNames.tagHide),setTimeout(e.bind(this),n,t)}.call(this,a[0]):a.forEach(e.bind(this)),t||(this.removeTagsFromValue(a.map((e=>e.node))),this.update(),"select"==r.mode&&this.setContentEditable(!0))})).catch((e=>{}))},removeTagsFromDOM(){[].slice.call(this.getTagElms()).forEach((e=>e.parentNode.removeChild(e)))},removeTagsFromValue(e){(e=Array.isArray(e)?e:[e]).forEach((e=>{var t=w(e),n=this.getTagIdx(t);n>-1&&this.value.splice(n,1)}))},removeAllTags(e){e=e||{},this.value=[],"mix"==this.settings.mode?this.DOM.input.innerHTML="":this.removeTagsFromDOM(),this.dropdown.refilter(),this.dropdown.position(),this.state.dropdown.visible&&setTimeout((()=>{this.DOM.input.focus()})),"select"==this.settings.mode&&(this.input.set.call(this),this.setContentEditable(!0)),this.update(e)},postUpdate(){this.state.blockChangeEvent=!1;var e=this.settings,t=e.classNames,n="mix"==e.mode?e.mixMode.integrated?this.DOM.input.textContent:this.DOM.originalInput.value.trim():this.value.length+this.input.raw.call(this).length;this.toggleClass(t.hasMaxTags,this.value.length>=e.maxTags),this.toggleClass(t.hasNoTags,!this.value.length),this.toggleClass(t.empty,!n),"select"==e.mode&&this.toggleScopeValidation(this.value?.[0]?.__isValid)},setOriginalInputValue(e){var t=this.DOM.originalInput;this.settings.mixMode.integrated||(t.value=e,t.tagifyValue=t.value,this.setPersistedData(e,"value"))},update(e){clearTimeout(this.debouncedUpdateTimeout),this.debouncedUpdateTimeout=setTimeout(function(){var t=this.getInputValue();this.setOriginalInputValue(t),this.settings.onChangeAfterBlur&&(e||{}).withoutChangeEvent||this.state.blockChangeEvent||this.triggerChangeEvent(),this.postUpdate()}.bind(this),100)},getInputValue(){var e=this.getCleanValue();return"mix"==this.settings.mode?this.getMixedTagsAsString(e):e.length?this.settings.originalInputValueFormat?this.settings.originalInputValueFormat(e):JSON.stringify(e):""},getCleanValue(e){return i(e||this.value,this.dataProps)},getMixedTagsAsString(){var e="",t=this,n=this.settings,a=n.originalInputValueFormat||JSON.stringify,r=n.mixTagsInterpolator;return function n(i){i.childNodes.forEach((i=>{if(1==i.nodeType){const s=w(i);if("BR"==i.tagName&&(e+="\r\n"),s&&_.call(t,i)){if(s.__removed)return;e+=r[0]+a(o(s,t.dataProps))+r[1]}else i.getAttribute("style")||["B","I","U"].includes(i.tagName)?e+=i.textContent:"DIV"!=i.tagName&&"P"!=i.tagName||(e+="\r\n",n(i))}else e+=i.textContent}))}(this.DOM.input),e}},A.prototype.removeTag=A.prototype.removeTags,A}()},184:function(e,t){var n;!function(){"use strict";var a={}.hasOwnProperty;function r(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n)){if(n.length){var o=r.apply(null,n);o&&e.push(o)}}else if("object"===i){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var s in n)a.call(n,s)&&n[s]&&e.push(s)}}}return e.join(" ")}e.exports?(r.default=r,e.exports=r):void 0===(n=function(){return r}.apply(t,[]))||(e.exports=n)}()},787:function(e,t,n){var a;(function(){function r(e){"use strict";var t={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic 'section-' prefix",type:"string"},rawPrefixHeaderId:{defaultValue:!1,describe:'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',type:"boolean"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},rawHeaderId:{defaultValue:!1,describe:"Remove only spaces, ' and \" from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids",type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},excludeTrailingPunctuationFromURLs:{defaultValue:!1,describe:"Excludes trailing punctuation from links generated with autoLinking",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,description:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,description:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,description:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,description:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,description:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",description:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,description:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,description:"Open all links in new windows",type:"boolean"},backslashEscapesHTMLTags:{defaultValue:!1,description:"Support for HTML Tag escaping. ex: <div>foo</div>",type:"boolean"},emoji:{defaultValue:!1,description:"Enable emoji support. Ex: `this is a :smile: emoji`",type:"boolean"},underline:{defaultValue:!1,description:"Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`",type:"boolean"},completeHTMLDocument:{defaultValue:!1,description:"Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags",type:"boolean"},metadata:{defaultValue:!1,description:"Enable support for document metadata (defined at the top of the document between `«««` and `»»»` or between `---` and `---`).",type:"boolean"},splitAdjacentBlockquotes:{defaultValue:!1,description:"Split adjacent blockquote blocks",type:"boolean"}};if(!1===e)return JSON.parse(JSON.stringify(t));var n={};for(var a in t)t.hasOwnProperty(a)&&(n[a]=t[a].defaultValue);return n}var i={},o={},s={},l=r(!0),c="vanilla",u={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0,backslashEscapesHTMLTags:!0,emoji:!0,splitAdjacentBlockquotes:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:r(!0),allOn:function(){"use strict";var e=r(!0),t={};for(var n in e)e.hasOwnProperty(n)&&(t[n]=!0);return t}()};function m(e,t){"use strict";var n=t?"Error in "+t+" extension->":"Error in unnamed extension",a={valid:!0,error:""};i.helper.isArray(e)||(e=[e]);for(var r=0;r<e.length;++r){var o=n+" sub-extension "+r+": ",s=e[r];if("object"!=typeof s)return a.valid=!1,a.error=o+"must be an object, but "+typeof s+" given",a;if(!i.helper.isString(s.type))return a.valid=!1,a.error=o+'property "type" must be a string, but '+typeof s.type+" given",a;var l=s.type=s.type.toLowerCase();if("language"===l&&(l=s.type="lang"),"html"===l&&(l=s.type="output"),"lang"!==l&&"output"!==l&&"listener"!==l)return a.valid=!1,a.error=o+"type "+l+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',a;if("listener"===l){if(i.helper.isUndefined(s.listeners))return a.valid=!1,a.error=o+'. Extensions of type "listener" must have a property called "listeners"',a}else if(i.helper.isUndefined(s.filter)&&i.helper.isUndefined(s.regex))return a.valid=!1,a.error=o+l+' extensions must define either a "regex" property or a "filter" method',a;if(s.listeners){if("object"!=typeof s.listeners)return a.valid=!1,a.error=o+'"listeners" property must be an object but '+typeof s.listeners+" given",a;for(var c in s.listeners)if(s.listeners.hasOwnProperty(c)&&"function"!=typeof s.listeners[c])return a.valid=!1,a.error=o+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+c+" must be a function but "+typeof s.listeners[c]+" given",a}if(s.filter){if("function"!=typeof s.filter)return a.valid=!1,a.error=o+'"filter" must be a function, but '+typeof s.filter+" given",a}else if(s.regex){if(i.helper.isString(s.regex)&&(s.regex=new RegExp(s.regex,"g")),!(s.regex instanceof RegExp))return a.valid=!1,a.error=o+'"regex" property must either be a string or a RegExp object, but '+typeof s.regex+" given",a;if(i.helper.isUndefined(s.replace))return a.valid=!1,a.error=o+'"regex" extensions must implement a replace string or function',a}}return a}function d(e,t){"use strict";return"¨E"+t.charCodeAt(0)+"E"}i.helper={},i.extensions={},i.setOption=function(e,t){"use strict";return l[e]=t,this},i.getOption=function(e){"use strict";return l[e]},i.getOptions=function(){"use strict";return l},i.resetOptions=function(){"use strict";l=r(!0)},i.setFlavor=function(e){"use strict";if(!u.hasOwnProperty(e))throw Error(e+" flavor was not found");i.resetOptions();var t=u[e];for(var n in c=e,t)t.hasOwnProperty(n)&&(l[n]=t[n])},i.getFlavor=function(){"use strict";return c},i.getFlavorOptions=function(e){"use strict";if(u.hasOwnProperty(e))return u[e]},i.getDefaultOptions=function(e){"use strict";return r(e)},i.subParser=function(e,t){"use strict";if(i.helper.isString(e)){if(void 0===t){if(o.hasOwnProperty(e))return o[e];throw Error("SubParser named "+e+" not registered!")}o[e]=t}},i.extension=function(e,t){"use strict";if(!i.helper.isString(e))throw Error("Extension 'name' must be a string");if(e=i.helper.stdExtName(e),i.helper.isUndefined(t)){if(!s.hasOwnProperty(e))throw Error("Extension named "+e+" is not registered!");return s[e]}"function"==typeof t&&(t=t()),i.helper.isArray(t)||(t=[t]);var n=m(t,e);if(!n.valid)throw Error(n.error);s[e]=t},i.getAllExtensions=function(){"use strict";return s},i.removeExtension=function(e){"use strict";delete s[e]},i.resetExtensions=function(){"use strict";s={}},i.validateExtension=function(e){"use strict";var t=m(e,null);return!!t.valid||(console.warn(t.error),!1)},i.hasOwnProperty("helper")||(i.helper={}),i.helper.isString=function(e){"use strict";return"string"==typeof e||e instanceof String},i.helper.isFunction=function(e){"use strict";return e&&"[object Function]"==={}.toString.call(e)},i.helper.isArray=function(e){"use strict";return Array.isArray(e)},i.helper.isUndefined=function(e){"use strict";return void 0===e},i.helper.forEach=function(e,t){"use strict";if(i.helper.isUndefined(e))throw new Error("obj param is required");if(i.helper.isUndefined(t))throw new Error("callback param is required");if(!i.helper.isFunction(t))throw new Error("callback param must be a function/closure");if("function"==typeof e.forEach)e.forEach(t);else if(i.helper.isArray(e))for(var n=0;n<e.length;n++)t(e[n],n,e);else{if("object"!=typeof e)throw new Error("obj does not seem to be an array or an iterable object");for(var a in e)e.hasOwnProperty(a)&&t(e[a],a,e)}},i.helper.stdExtName=function(e){"use strict";return e.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()},i.helper.escapeCharactersCallback=d,i.helper.escapeCharacters=function(e,t,n){"use strict";var a="(["+t.replace(/([\[\]\\])/g,"\\$1")+"])";n&&(a="\\\\"+a);var r=new RegExp(a,"g");return e=e.replace(r,d)},i.helper.unescapeHTMLEntities=function(e){"use strict";return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")};var p=function(e,t,n,a){"use strict";var r,i,o,s,l,c=a||"",u=c.indexOf("g")>-1,m=new RegExp(t+"|"+n,"g"+c.replace(/g/g,"")),d=new RegExp(t,c.replace(/g/g,"")),p=[];do{for(r=0;o=m.exec(e);)if(d.test(o[0]))r++||(s=(i=m.lastIndex)-o[0].length);else if(r&&! --r){l=o.index+o[0].length;var h={left:{start:s,end:i},match:{start:i,end:o.index},right:{start:o.index,end:l},wholeMatch:{start:s,end:l}};if(p.push(h),!u)return p}}while(r&&(m.lastIndex=i));return p};i.helper.matchRecursiveRegExp=function(e,t,n,a){"use strict";for(var r=p(e,t,n,a),i=[],o=0;o<r.length;++o)i.push([e.slice(r[o].wholeMatch.start,r[o].wholeMatch.end),e.slice(r[o].match.start,r[o].match.end),e.slice(r[o].left.start,r[o].left.end),e.slice(r[o].right.start,r[o].right.end)]);return i},i.helper.replaceRecursiveRegExp=function(e,t,n,a,r){"use strict";if(!i.helper.isFunction(t)){var o=t;t=function(){return o}}var s=p(e,n,a,r),l=e,c=s.length;if(c>0){var u=[];0!==s[0].wholeMatch.start&&u.push(e.slice(0,s[0].wholeMatch.start));for(var m=0;m<c;++m)u.push(t(e.slice(s[m].wholeMatch.start,s[m].wholeMatch.end),e.slice(s[m].match.start,s[m].match.end),e.slice(s[m].left.start,s[m].left.end),e.slice(s[m].right.start,s[m].right.end))),m<c-1&&u.push(e.slice(s[m].wholeMatch.end,s[m+1].wholeMatch.start));s[c-1].wholeMatch.end<e.length&&u.push(e.slice(s[c-1].wholeMatch.end)),l=u.join("")}return l},i.helper.regexIndexOf=function(e,t,n){"use strict";if(!i.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(t instanceof RegExp==!1)throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";var a=e.substring(n||0).search(t);return a>=0?a+(n||0):a},i.helper.splitAtIndex=function(e,t){"use strict";if(!i.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";return[e.substring(0,t),e.substring(t)]},i.helper.encodeEmailAddress=function(e){"use strict";var t=[function(e){return"&#"+e.charCodeAt(0)+";"},function(e){return"&#x"+e.charCodeAt(0).toString(16)+";"},function(e){return e}];return e=e.replace(/./g,(function(e){if("@"===e)e=t[Math.floor(2*Math.random())](e);else{var n=Math.random();e=n>.9?t[2](e):n>.45?t[1](e):t[0](e)}return e}))},i.helper.padEnd=function(e,t,n){"use strict";return t>>=0,n=String(n||" "),e.length>t?String(e):((t-=e.length)>n.length&&(n+=n.repeat(t/n.length)),String(e)+n.slice(0,t))},"undefined"==typeof console&&(console={warn:function(e){"use strict";alert(e)},log:function(e){"use strict";alert(e)},error:function(e){"use strict";throw e}}),i.helper.regexes={asteriskDashAndColon:/([*_:~])/g},i.helper.emojis={"+1":"👍","-1":"👎",100:"💯",1234:"🔢","1st_place_medal":"🥇","2nd_place_medal":"🥈","3rd_place_medal":"🥉","8ball":"🎱",a:"🅰️",ab:"🆎",abc:"🔤",abcd:"🔡",accept:"🉑",aerial_tramway:"🚡",airplane:"✈️",alarm_clock:"⏰",alembic:"⚗️",alien:"👽",ambulance:"🚑",amphora:"🏺",anchor:"⚓️",angel:"👼",anger:"💢",angry:"😠",anguished:"😧",ant:"🐜",apple:"🍎",aquarius:"♒️",aries:"♈️",arrow_backward:"◀️",arrow_double_down:"⏬",arrow_double_up:"⏫",arrow_down:"⬇️",arrow_down_small:"🔽",arrow_forward:"▶️",arrow_heading_down:"⤵️",arrow_heading_up:"⤴️",arrow_left:"⬅️",arrow_lower_left:"↙️",arrow_lower_right:"↘️",arrow_right:"➡️",arrow_right_hook:"↪️",arrow_up:"⬆️",arrow_up_down:"↕️",arrow_up_small:"🔼",arrow_upper_left:"↖️",arrow_upper_right:"↗️",arrows_clockwise:"🔃",arrows_counterclockwise:"🔄",art:"🎨",articulated_lorry:"🚛",artificial_satellite:"🛰",astonished:"😲",athletic_shoe:"👟",atm:"🏧",atom_symbol:"⚛️",avocado:"🥑",b:"🅱️",baby:"👶",baby_bottle:"🍼",baby_chick:"🐤",baby_symbol:"🚼",back:"🔙",bacon:"🥓",badminton:"🏸",baggage_claim:"🛄",baguette_bread:"🥖",balance_scale:"⚖️",balloon:"🎈",ballot_box:"🗳",ballot_box_with_check:"☑️",bamboo:"🎍",banana:"🍌",bangbang:"‼️",bank:"🏦",bar_chart:"📊",barber:"💈",baseball:"⚾️",basketball:"🏀",basketball_man:"⛹️",basketball_woman:"⛹️&zwj;♀️",bat:"🦇",bath:"🛀",bathtub:"🛁",battery:"🔋",beach_umbrella:"🏖",bear:"🐻",bed:"🛏",bee:"🐝",beer:"🍺",beers:"🍻",beetle:"🐞",beginner:"🔰",bell:"🔔",bellhop_bell:"🛎",bento:"🍱",biking_man:"🚴",bike:"🚲",biking_woman:"🚴&zwj;♀️",bikini:"👙",biohazard:"☣️",bird:"🐦",birthday:"🎂",black_circle:"⚫️",black_flag:"🏴",black_heart:"🖤",black_joker:"🃏",black_large_square:"⬛️",black_medium_small_square:"◾️",black_medium_square:"◼️",black_nib:"✒️",black_small_square:"▪️",black_square_button:"🔲",blonde_man:"👱",blonde_woman:"👱&zwj;♀️",blossom:"🌼",blowfish:"🐡",blue_book:"📘",blue_car:"🚙",blue_heart:"💙",blush:"😊",boar:"🐗",boat:"⛵️",bomb:"💣",book:"📖",bookmark:"🔖",bookmark_tabs:"📑",books:"📚",boom:"💥",boot:"👢",bouquet:"💐",bowing_man:"🙇",bow_and_arrow:"🏹",bowing_woman:"🙇&zwj;♀️",bowling:"🎳",boxing_glove:"🥊",boy:"👦",bread:"🍞",bride_with_veil:"👰",bridge_at_night:"🌉",briefcase:"💼",broken_heart:"💔",bug:"🐛",building_construction:"🏗",bulb:"💡",bullettrain_front:"🚅",bullettrain_side:"🚄",burrito:"🌯",bus:"🚌",business_suit_levitating:"🕴",busstop:"🚏",bust_in_silhouette:"👤",busts_in_silhouette:"👥",butterfly:"🦋",cactus:"🌵",cake:"🍰",calendar:"📆",call_me_hand:"🤙",calling:"📲",camel:"🐫",camera:"📷",camera_flash:"📸",camping:"🏕",cancer:"♋️",candle:"🕯",candy:"🍬",canoe:"🛶",capital_abcd:"🔠",capricorn:"♑️",car:"🚗",card_file_box:"🗃",card_index:"📇",card_index_dividers:"🗂",carousel_horse:"🎠",carrot:"🥕",cat:"🐱",cat2:"🐈",cd:"💿",chains:"⛓",champagne:"🍾",chart:"💹",chart_with_downwards_trend:"📉",chart_with_upwards_trend:"📈",checkered_flag:"🏁",cheese:"🧀",cherries:"🍒",cherry_blossom:"🌸",chestnut:"🌰",chicken:"🐔",children_crossing:"🚸",chipmunk:"🐿",chocolate_bar:"🍫",christmas_tree:"🎄",church:"⛪️",cinema:"🎦",circus_tent:"🎪",city_sunrise:"🌇",city_sunset:"🌆",cityscape:"🏙",cl:"🆑",clamp:"🗜",clap:"👏",clapper:"🎬",classical_building:"🏛",clinking_glasses:"🥂",clipboard:"📋",clock1:"🕐",clock10:"🕙",clock1030:"🕥",clock11:"🕚",clock1130:"🕦",clock12:"🕛",clock1230:"🕧",clock130:"🕜",clock2:"🕑",clock230:"🕝",clock3:"🕒",clock330:"🕞",clock4:"🕓",clock430:"🕟",clock5:"🕔",clock530:"🕠",clock6:"🕕",clock630:"🕡",clock7:"🕖",clock730:"🕢",clock8:"🕗",clock830:"🕣",clock9:"🕘",clock930:"🕤",closed_book:"📕",closed_lock_with_key:"🔐",closed_umbrella:"🌂",cloud:"☁️",cloud_with_lightning:"🌩",cloud_with_lightning_and_rain:"⛈",cloud_with_rain:"🌧",cloud_with_snow:"🌨",clown_face:"🤡",clubs:"♣️",cocktail:"🍸",coffee:"☕️",coffin:"⚰️",cold_sweat:"😰",comet:"☄️",computer:"💻",computer_mouse:"🖱",confetti_ball:"🎊",confounded:"😖",confused:"😕",congratulations:"㊗️",construction:"🚧",construction_worker_man:"👷",construction_worker_woman:"👷&zwj;♀️",control_knobs:"🎛",convenience_store:"🏪",cookie:"🍪",cool:"🆒",policeman:"👮",copyright:"©️",corn:"🌽",couch_and_lamp:"🛋",couple:"👫",couple_with_heart_woman_man:"💑",couple_with_heart_man_man:"👨&zwj;❤️&zwj;👨",couple_with_heart_woman_woman:"👩&zwj;❤️&zwj;👩",couplekiss_man_man:"👨&zwj;❤️&zwj;💋&zwj;👨",couplekiss_man_woman:"💏",couplekiss_woman_woman:"👩&zwj;❤️&zwj;💋&zwj;👩",cow:"🐮",cow2:"🐄",cowboy_hat_face:"🤠",crab:"🦀",crayon:"🖍",credit_card:"💳",crescent_moon:"🌙",cricket:"🏏",crocodile:"🐊",croissant:"🥐",crossed_fingers:"🤞",crossed_flags:"🎌",crossed_swords:"⚔️",crown:"👑",cry:"😢",crying_cat_face:"😿",crystal_ball:"🔮",cucumber:"🥒",cupid:"💘",curly_loop:"➰",currency_exchange:"💱",curry:"🍛",custard:"🍮",customs:"🛃",cyclone:"🌀",dagger:"🗡",dancer:"💃",dancing_women:"👯",dancing_men:"👯&zwj;♂️",dango:"🍡",dark_sunglasses:"🕶",dart:"🎯",dash:"💨",date:"📅",deciduous_tree:"🌳",deer:"🦌",department_store:"🏬",derelict_house:"🏚",desert:"🏜",desert_island:"🏝",desktop_computer:"🖥",male_detective:"🕵️",diamond_shape_with_a_dot_inside:"💠",diamonds:"♦️",disappointed:"😞",disappointed_relieved:"😥",dizzy:"💫",dizzy_face:"😵",do_not_litter:"🚯",dog:"🐶",dog2:"🐕",dollar:"💵",dolls:"🎎",dolphin:"🐬",door:"🚪",doughnut:"🍩",dove:"🕊",dragon:"🐉",dragon_face:"🐲",dress:"👗",dromedary_camel:"🐪",drooling_face:"🤤",droplet:"💧",drum:"🥁",duck:"🦆",dvd:"📀","e-mail":"📧",eagle:"🦅",ear:"👂",ear_of_rice:"🌾",earth_africa:"🌍",earth_americas:"🌎",earth_asia:"🌏",egg:"🥚",eggplant:"🍆",eight_pointed_black_star:"✴️",eight_spoked_asterisk:"✳️",electric_plug:"🔌",elephant:"🐘",email:"✉️",end:"🔚",envelope_with_arrow:"📩",euro:"💶",european_castle:"🏰",european_post_office:"🏤",evergreen_tree:"🌲",exclamation:"❗️",expressionless:"😑",eye:"👁",eye_speech_bubble:"👁&zwj;🗨",eyeglasses:"👓",eyes:"👀",face_with_head_bandage:"🤕",face_with_thermometer:"🤒",fist_oncoming:"👊",factory:"🏭",fallen_leaf:"🍂",family_man_woman_boy:"👪",family_man_boy:"👨&zwj;👦",family_man_boy_boy:"👨&zwj;👦&zwj;👦",family_man_girl:"👨&zwj;👧",family_man_girl_boy:"👨&zwj;👧&zwj;👦",family_man_girl_girl:"👨&zwj;👧&zwj;👧",family_man_man_boy:"👨&zwj;👨&zwj;👦",family_man_man_boy_boy:"👨&zwj;👨&zwj;👦&zwj;👦",family_man_man_girl:"👨&zwj;👨&zwj;👧",family_man_man_girl_boy:"👨&zwj;👨&zwj;👧&zwj;👦",family_man_man_girl_girl:"👨&zwj;👨&zwj;👧&zwj;👧",family_man_woman_boy_boy:"👨&zwj;👩&zwj;👦&zwj;👦",family_man_woman_girl:"👨&zwj;👩&zwj;👧",family_man_woman_girl_boy:"👨&zwj;👩&zwj;👧&zwj;👦",family_man_woman_girl_girl:"👨&zwj;👩&zwj;👧&zwj;👧",family_woman_boy:"👩&zwj;👦",family_woman_boy_boy:"👩&zwj;👦&zwj;👦",family_woman_girl:"👩&zwj;👧",family_woman_girl_boy:"👩&zwj;👧&zwj;👦",family_woman_girl_girl:"👩&zwj;👧&zwj;👧",family_woman_woman_boy:"👩&zwj;👩&zwj;👦",family_woman_woman_boy_boy:"👩&zwj;👩&zwj;👦&zwj;👦",family_woman_woman_girl:"👩&zwj;👩&zwj;👧",family_woman_woman_girl_boy:"👩&zwj;👩&zwj;👧&zwj;👦",family_woman_woman_girl_girl:"👩&zwj;👩&zwj;👧&zwj;👧",fast_forward:"⏩",fax:"📠",fearful:"😨",feet:"🐾",female_detective:"🕵️&zwj;♀️",ferris_wheel:"🎡",ferry:"⛴",field_hockey:"🏑",file_cabinet:"🗄",file_folder:"📁",film_projector:"📽",film_strip:"🎞",fire:"🔥",fire_engine:"🚒",fireworks:"🎆",first_quarter_moon:"🌓",first_quarter_moon_with_face:"🌛",fish:"🐟",fish_cake:"🍥",fishing_pole_and_fish:"🎣",fist_raised:"✊",fist_left:"🤛",fist_right:"🤜",flags:"🎏",flashlight:"🔦",fleur_de_lis:"⚜️",flight_arrival:"🛬",flight_departure:"🛫",floppy_disk:"💾",flower_playing_cards:"🎴",flushed:"😳",fog:"🌫",foggy:"🌁",football:"🏈",footprints:"👣",fork_and_knife:"🍴",fountain:"⛲️",fountain_pen:"🖋",four_leaf_clover:"🍀",fox_face:"🦊",framed_picture:"🖼",free:"🆓",fried_egg:"🍳",fried_shrimp:"🍤",fries:"🍟",frog:"🐸",frowning:"😦",frowning_face:"☹️",frowning_man:"🙍&zwj;♂️",frowning_woman:"🙍",middle_finger:"🖕",fuelpump:"⛽️",full_moon:"🌕",full_moon_with_face:"🌝",funeral_urn:"⚱️",game_die:"🎲",gear:"⚙️",gem:"💎",gemini:"♊️",ghost:"👻",gift:"🎁",gift_heart:"💝",girl:"👧",globe_with_meridians:"🌐",goal_net:"🥅",goat:"🐐",golf:"⛳️",golfing_man:"🏌️",golfing_woman:"🏌️&zwj;♀️",gorilla:"🦍",grapes:"🍇",green_apple:"🍏",green_book:"📗",green_heart:"💚",green_salad:"🥗",grey_exclamation:"❕",grey_question:"❔",grimacing:"😬",grin:"😁",grinning:"😀",guardsman:"💂",guardswoman:"💂&zwj;♀️",guitar:"🎸",gun:"🔫",haircut_woman:"💇",haircut_man:"💇&zwj;♂️",hamburger:"🍔",hammer:"🔨",hammer_and_pick:"⚒",hammer_and_wrench:"🛠",hamster:"🐹",hand:"✋",handbag:"👜",handshake:"🤝",hankey:"💩",hatched_chick:"🐥",hatching_chick:"🐣",headphones:"🎧",hear_no_evil:"🙉",heart:"❤️",heart_decoration:"💟",heart_eyes:"😍",heart_eyes_cat:"😻",heartbeat:"💓",heartpulse:"💗",hearts:"♥️",heavy_check_mark:"✔️",heavy_division_sign:"➗",heavy_dollar_sign:"💲",heavy_heart_exclamation:"❣️",heavy_minus_sign:"➖",heavy_multiplication_x:"✖️",heavy_plus_sign:"➕",helicopter:"🚁",herb:"🌿",hibiscus:"🌺",high_brightness:"🔆",high_heel:"👠",hocho:"🔪",hole:"🕳",honey_pot:"🍯",horse:"🐴",horse_racing:"🏇",hospital:"🏥",hot_pepper:"🌶",hotdog:"🌭",hotel:"🏨",hotsprings:"♨️",hourglass:"⌛️",hourglass_flowing_sand:"⏳",house:"🏠",house_with_garden:"🏡",houses:"🏘",hugs:"🤗",hushed:"😯",ice_cream:"🍨",ice_hockey:"🏒",ice_skate:"⛸",icecream:"🍦",id:"🆔",ideograph_advantage:"🉐",imp:"👿",inbox_tray:"📥",incoming_envelope:"📨",tipping_hand_woman:"💁",information_source:"ℹ️",innocent:"😇",interrobang:"⁉️",iphone:"📱",izakaya_lantern:"🏮",jack_o_lantern:"🎃",japan:"🗾",japanese_castle:"🏯",japanese_goblin:"👺",japanese_ogre:"👹",jeans:"👖",joy:"😂",joy_cat:"😹",joystick:"🕹",kaaba:"🕋",key:"🔑",keyboard:"⌨️",keycap_ten:"🔟",kick_scooter:"🛴",kimono:"👘",kiss:"💋",kissing:"😗",kissing_cat:"😽",kissing_closed_eyes:"😚",kissing_heart:"😘",kissing_smiling_eyes:"😙",kiwi_fruit:"🥝",koala:"🐨",koko:"🈁",label:"🏷",large_blue_circle:"🔵",large_blue_diamond:"🔷",large_orange_diamond:"🔶",last_quarter_moon:"🌗",last_quarter_moon_with_face:"🌜",latin_cross:"✝️",laughing:"😆",leaves:"🍃",ledger:"📒",left_luggage:"🛅",left_right_arrow:"↔️",leftwards_arrow_with_hook:"↩️",lemon:"🍋",leo:"♌️",leopard:"🐆",level_slider:"🎚",libra:"♎️",light_rail:"🚈",link:"🔗",lion:"🦁",lips:"👄",lipstick:"💄",lizard:"🦎",lock:"🔒",lock_with_ink_pen:"🔏",lollipop:"🍭",loop:"➿",loud_sound:"🔊",loudspeaker:"📢",love_hotel:"🏩",love_letter:"💌",low_brightness:"🔅",lying_face:"🤥",m:"Ⓜ️",mag:"🔍",mag_right:"🔎",mahjong:"🀄️",mailbox:"📫",mailbox_closed:"📪",mailbox_with_mail:"📬",mailbox_with_no_mail:"📭",man:"👨",man_artist:"👨&zwj;🎨",man_astronaut:"👨&zwj;🚀",man_cartwheeling:"🤸&zwj;♂️",man_cook:"👨&zwj;🍳",man_dancing:"🕺",man_facepalming:"🤦&zwj;♂️",man_factory_worker:"👨&zwj;🏭",man_farmer:"👨&zwj;🌾",man_firefighter:"👨&zwj;🚒",man_health_worker:"👨&zwj;⚕️",man_in_tuxedo:"🤵",man_judge:"👨&zwj;⚖️",man_juggling:"🤹&zwj;♂️",man_mechanic:"👨&zwj;🔧",man_office_worker:"👨&zwj;💼",man_pilot:"👨&zwj;✈️",man_playing_handball:"🤾&zwj;♂️",man_playing_water_polo:"🤽&zwj;♂️",man_scientist:"👨&zwj;🔬",man_shrugging:"🤷&zwj;♂️",man_singer:"👨&zwj;🎤",man_student:"👨&zwj;🎓",man_teacher:"👨&zwj;🏫",man_technologist:"👨&zwj;💻",man_with_gua_pi_mao:"👲",man_with_turban:"👳",tangerine:"🍊",mans_shoe:"👞",mantelpiece_clock:"🕰",maple_leaf:"🍁",martial_arts_uniform:"🥋",mask:"😷",massage_woman:"💆",massage_man:"💆&zwj;♂️",meat_on_bone:"🍖",medal_military:"🎖",medal_sports:"🏅",mega:"📣",melon:"🍈",memo:"📝",men_wrestling:"🤼&zwj;♂️",menorah:"🕎",mens:"🚹",metal:"🤘",metro:"🚇",microphone:"🎤",microscope:"🔬",milk_glass:"🥛",milky_way:"🌌",minibus:"🚐",minidisc:"💽",mobile_phone_off:"📴",money_mouth_face:"🤑",money_with_wings:"💸",moneybag:"💰",monkey:"🐒",monkey_face:"🐵",monorail:"🚝",moon:"🌔",mortar_board:"🎓",mosque:"🕌",motor_boat:"🛥",motor_scooter:"🛵",motorcycle:"🏍",motorway:"🛣",mount_fuji:"🗻",mountain:"⛰",mountain_biking_man:"🚵",mountain_biking_woman:"🚵&zwj;♀️",mountain_cableway:"🚠",mountain_railway:"🚞",mountain_snow:"🏔",mouse:"🐭",mouse2:"🐁",movie_camera:"🎥",moyai:"🗿",mrs_claus:"🤶",muscle:"💪",mushroom:"🍄",musical_keyboard:"🎹",musical_note:"🎵",musical_score:"🎼",mute:"🔇",nail_care:"💅",name_badge:"📛",national_park:"🏞",nauseated_face:"🤢",necktie:"👔",negative_squared_cross_mark:"❎",nerd_face:"🤓",neutral_face:"😐",new:"🆕",new_moon:"🌑",new_moon_with_face:"🌚",newspaper:"📰",newspaper_roll:"🗞",next_track_button:"⏭",ng:"🆖",no_good_man:"🙅&zwj;♂️",no_good_woman:"🙅",night_with_stars:"🌃",no_bell:"🔕",no_bicycles:"🚳",no_entry:"⛔️",no_entry_sign:"🚫",no_mobile_phones:"📵",no_mouth:"😶",no_pedestrians:"🚷",no_smoking:"🚭","non-potable_water":"🚱",nose:"👃",notebook:"📓",notebook_with_decorative_cover:"📔",notes:"🎶",nut_and_bolt:"🔩",o:"⭕️",o2:"🅾️",ocean:"🌊",octopus:"🐙",oden:"🍢",office:"🏢",oil_drum:"🛢",ok:"🆗",ok_hand:"👌",ok_man:"🙆&zwj;♂️",ok_woman:"🙆",old_key:"🗝",older_man:"👴",older_woman:"👵",om:"🕉",on:"🔛",oncoming_automobile:"🚘",oncoming_bus:"🚍",oncoming_police_car:"🚔",oncoming_taxi:"🚖",open_file_folder:"📂",open_hands:"👐",open_mouth:"😮",open_umbrella:"☂️",ophiuchus:"⛎",orange_book:"📙",orthodox_cross:"☦️",outbox_tray:"📤",owl:"🦉",ox:"🐂",package:"📦",page_facing_up:"📄",page_with_curl:"📃",pager:"📟",paintbrush:"🖌",palm_tree:"🌴",pancakes:"🥞",panda_face:"🐼",paperclip:"📎",paperclips:"🖇",parasol_on_ground:"⛱",parking:"🅿️",part_alternation_mark:"〽️",partly_sunny:"⛅️",passenger_ship:"🛳",passport_control:"🛂",pause_button:"⏸",peace_symbol:"☮️",peach:"🍑",peanuts:"🥜",pear:"🍐",pen:"🖊",pencil2:"✏️",penguin:"🐧",pensive:"😔",performing_arts:"🎭",persevere:"😣",person_fencing:"🤺",pouting_woman:"🙎",phone:"☎️",pick:"⛏",pig:"🐷",pig2:"🐖",pig_nose:"🐽",pill:"💊",pineapple:"🍍",ping_pong:"🏓",pisces:"♓️",pizza:"🍕",place_of_worship:"🛐",plate_with_cutlery:"🍽",play_or_pause_button:"⏯",point_down:"👇",point_left:"👈",point_right:"👉",point_up:"☝️",point_up_2:"👆",police_car:"🚓",policewoman:"👮&zwj;♀️",poodle:"🐩",popcorn:"🍿",post_office:"🏣",postal_horn:"📯",postbox:"📮",potable_water:"🚰",potato:"🥔",pouch:"👝",poultry_leg:"🍗",pound:"💷",rage:"😡",pouting_cat:"😾",pouting_man:"🙎&zwj;♂️",pray:"🙏",prayer_beads:"📿",pregnant_woman:"🤰",previous_track_button:"⏮",prince:"🤴",princess:"👸",printer:"🖨",purple_heart:"💜",purse:"👛",pushpin:"📌",put_litter_in_its_place:"🚮",question:"❓",rabbit:"🐰",rabbit2:"🐇",racehorse:"🐎",racing_car:"🏎",radio:"📻",radio_button:"🔘",radioactive:"☢️",railway_car:"🚃",railway_track:"🛤",rainbow:"🌈",rainbow_flag:"🏳️&zwj;🌈",raised_back_of_hand:"🤚",raised_hand_with_fingers_splayed:"🖐",raised_hands:"🙌",raising_hand_woman:"🙋",raising_hand_man:"🙋&zwj;♂️",ram:"🐏",ramen:"🍜",rat:"🐀",record_button:"⏺",recycle:"♻️",red_circle:"🔴",registered:"®️",relaxed:"☺️",relieved:"😌",reminder_ribbon:"🎗",repeat:"🔁",repeat_one:"🔂",rescue_worker_helmet:"⛑",restroom:"🚻",revolving_hearts:"💞",rewind:"⏪",rhinoceros:"🦏",ribbon:"🎀",rice:"🍚",rice_ball:"🍙",rice_cracker:"🍘",rice_scene:"🎑",right_anger_bubble:"🗯",ring:"💍",robot:"🤖",rocket:"🚀",rofl:"🤣",roll_eyes:"🙄",roller_coaster:"🎢",rooster:"🐓",rose:"🌹",rosette:"🏵",rotating_light:"🚨",round_pushpin:"📍",rowing_man:"🚣",rowing_woman:"🚣&zwj;♀️",rugby_football:"🏉",running_man:"🏃",running_shirt_with_sash:"🎽",running_woman:"🏃&zwj;♀️",sa:"🈂️",sagittarius:"♐️",sake:"🍶",sandal:"👡",santa:"🎅",satellite:"📡",saxophone:"🎷",school:"🏫",school_satchel:"🎒",scissors:"✂️",scorpion:"🦂",scorpius:"♏️",scream:"😱",scream_cat:"🙀",scroll:"📜",seat:"💺",secret:"㊙️",see_no_evil:"🙈",seedling:"🌱",selfie:"🤳",shallow_pan_of_food:"🥘",shamrock:"☘️",shark:"🦈",shaved_ice:"🍧",sheep:"🐑",shell:"🐚",shield:"🛡",shinto_shrine:"⛩",ship:"🚢",shirt:"👕",shopping:"🛍",shopping_cart:"🛒",shower:"🚿",shrimp:"🦐",signal_strength:"📶",six_pointed_star:"🔯",ski:"🎿",skier:"⛷",skull:"💀",skull_and_crossbones:"☠️",sleeping:"😴",sleeping_bed:"🛌",sleepy:"😪",slightly_frowning_face:"🙁",slightly_smiling_face:"🙂",slot_machine:"🎰",small_airplane:"🛩",small_blue_diamond:"🔹",small_orange_diamond:"🔸",small_red_triangle:"🔺",small_red_triangle_down:"🔻",smile:"😄",smile_cat:"😸",smiley:"😃",smiley_cat:"😺",smiling_imp:"😈",smirk:"😏",smirk_cat:"😼",smoking:"🚬",snail:"🐌",snake:"🐍",sneezing_face:"🤧",snowboarder:"🏂",snowflake:"❄️",snowman:"⛄️",snowman_with_snow:"☃️",sob:"😭",soccer:"⚽️",soon:"🔜",sos:"🆘",sound:"🔉",space_invader:"👾",spades:"♠️",spaghetti:"🍝",sparkle:"❇️",sparkler:"🎇",sparkles:"✨",sparkling_heart:"💖",speak_no_evil:"🙊",speaker:"🔈",speaking_head:"🗣",speech_balloon:"💬",speedboat:"🚤",spider:"🕷",spider_web:"🕸",spiral_calendar:"🗓",spiral_notepad:"🗒",spoon:"🥄",squid:"🦑",stadium:"🏟",star:"⭐️",star2:"🌟",star_and_crescent:"☪️",star_of_david:"✡️",stars:"🌠",station:"🚉",statue_of_liberty:"🗽",steam_locomotive:"🚂",stew:"🍲",stop_button:"⏹",stop_sign:"🛑",stopwatch:"⏱",straight_ruler:"📏",strawberry:"🍓",stuck_out_tongue:"😛",stuck_out_tongue_closed_eyes:"😝",stuck_out_tongue_winking_eye:"😜",studio_microphone:"🎙",stuffed_flatbread:"🥙",sun_behind_large_cloud:"🌥",sun_behind_rain_cloud:"🌦",sun_behind_small_cloud:"🌤",sun_with_face:"🌞",sunflower:"🌻",sunglasses:"😎",sunny:"☀️",sunrise:"🌅",sunrise_over_mountains:"🌄",surfing_man:"🏄",surfing_woman:"🏄&zwj;♀️",sushi:"🍣",suspension_railway:"🚟",sweat:"😓",sweat_drops:"💦",sweat_smile:"😅",sweet_potato:"🍠",swimming_man:"🏊",swimming_woman:"🏊&zwj;♀️",symbols:"🔣",synagogue:"🕍",syringe:"💉",taco:"🌮",tada:"🎉",tanabata_tree:"🎋",taurus:"♉️",taxi:"🚕",tea:"🍵",telephone_receiver:"📞",telescope:"🔭",tennis:"🎾",tent:"⛺️",thermometer:"🌡",thinking:"🤔",thought_balloon:"💭",ticket:"🎫",tickets:"🎟",tiger:"🐯",tiger2:"🐅",timer_clock:"⏲",tipping_hand_man:"💁&zwj;♂️",tired_face:"😫",tm:"™️",toilet:"🚽",tokyo_tower:"🗼",tomato:"🍅",tongue:"👅",top:"🔝",tophat:"🎩",tornado:"🌪",trackball:"🖲",tractor:"🚜",traffic_light:"🚥",train:"🚋",train2:"🚆",tram:"🚊",triangular_flag_on_post:"🚩",triangular_ruler:"📐",trident:"🔱",triumph:"😤",trolleybus:"🚎",trophy:"🏆",tropical_drink:"🍹",tropical_fish:"🐠",truck:"🚚",trumpet:"🎺",tulip:"🌷",tumbler_glass:"🥃",turkey:"🦃",turtle:"🐢",tv:"📺",twisted_rightwards_arrows:"🔀",two_hearts:"💕",two_men_holding_hands:"👬",two_women_holding_hands:"👭",u5272:"🈹",u5408:"🈴",u55b6:"🈺",u6307:"🈯️",u6708:"🈷️",u6709:"🈶",u6e80:"🈵",u7121:"🈚️",u7533:"🈸",u7981:"🈲",u7a7a:"🈳",umbrella:"☔️",unamused:"😒",underage:"🔞",unicorn:"🦄",unlock:"🔓",up:"🆙",upside_down_face:"🙃",v:"✌️",vertical_traffic_light:"🚦",vhs:"📼",vibration_mode:"📳",video_camera:"📹",video_game:"🎮",violin:"🎻",virgo:"♍️",volcano:"🌋",volleyball:"🏐",vs:"🆚",vulcan_salute:"🖖",walking_man:"🚶",walking_woman:"🚶&zwj;♀️",waning_crescent_moon:"🌘",waning_gibbous_moon:"🌖",warning:"⚠️",wastebasket:"🗑",watch:"⌚️",water_buffalo:"🐃",watermelon:"🍉",wave:"👋",wavy_dash:"〰️",waxing_crescent_moon:"🌒",wc:"🚾",weary:"😩",wedding:"💒",weight_lifting_man:"🏋️",weight_lifting_woman:"🏋️&zwj;♀️",whale:"🐳",whale2:"🐋",wheel_of_dharma:"☸️",wheelchair:"♿️",white_check_mark:"✅",white_circle:"⚪️",white_flag:"🏳️",white_flower:"💮",white_large_square:"⬜️",white_medium_small_square:"◽️",white_medium_square:"◻️",white_small_square:"▫️",white_square_button:"🔳",wilted_flower:"🥀",wind_chime:"🎐",wind_face:"🌬",wine_glass:"🍷",wink:"😉",wolf:"🐺",woman:"👩",woman_artist:"👩&zwj;🎨",woman_astronaut:"👩&zwj;🚀",woman_cartwheeling:"🤸&zwj;♀️",woman_cook:"👩&zwj;🍳",woman_facepalming:"🤦&zwj;♀️",woman_factory_worker:"👩&zwj;🏭",woman_farmer:"👩&zwj;🌾",woman_firefighter:"👩&zwj;🚒",woman_health_worker:"👩&zwj;⚕️",woman_judge:"👩&zwj;⚖️",woman_juggling:"🤹&zwj;♀️",woman_mechanic:"👩&zwj;🔧",woman_office_worker:"👩&zwj;💼",woman_pilot:"👩&zwj;✈️",woman_playing_handball:"🤾&zwj;♀️",woman_playing_water_polo:"🤽&zwj;♀️",woman_scientist:"👩&zwj;🔬",woman_shrugging:"🤷&zwj;♀️",woman_singer:"👩&zwj;🎤",woman_student:"👩&zwj;🎓",woman_teacher:"👩&zwj;🏫",woman_technologist:"👩&zwj;💻",woman_with_turban:"👳&zwj;♀️",womans_clothes:"👚",womans_hat:"👒",women_wrestling:"🤼&zwj;♀️",womens:"🚺",world_map:"🗺",worried:"😟",wrench:"🔧",writing_hand:"✍️",x:"❌",yellow_heart:"💛",yen:"💴",yin_yang:"☯️",yum:"😋",zap:"⚡️",zipper_mouth_face:"🤐",zzz:"💤",octocat:'<img alt=":octocat:" height="20" width="20" align="absmiddle" src="https://assets-cdn.github.com/images/icons/emoji/octocat.png">',showdown:"<span style=\"font-family: 'Anonymous Pro', monospace; text-decoration: underline; text-decoration-style: dashed; text-decoration-color: #3e8b8a;text-underline-position: under;\">S</span>"},i.Converter=function(e){"use strict";var t={},n=[],a=[],r={},o=c,d={parsed:{},raw:"",format:""};function p(e,t){if(t=t||null,i.helper.isString(e)){if(t=e=i.helper.stdExtName(e),i.extensions[e])return console.warn("DEPRECATION WARNING: "+e+" is an old extension that uses a deprecated loading method.Please inform the developer that the extension should be updated!"),void function(e,t){"function"==typeof e&&(e=e(new i.Converter));i.helper.isArray(e)||(e=[e]);var r=m(e,t);if(!r.valid)throw Error(r.error);for(var o=0;o<e.length;++o)switch(e[o].type){case"lang":n.push(e[o]);break;case"output":a.push(e[o]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}}(i.extensions[e],e);if(i.helper.isUndefined(s[e]))throw Error('Extension "'+e+'" could not be loaded. It was either not found or is not a valid extension.');e=s[e]}"function"==typeof e&&(e=e()),i.helper.isArray(e)||(e=[e]);var r=m(e,t);if(!r.valid)throw Error(r.error);for(var o=0;o<e.length;++o){switch(e[o].type){case"lang":n.push(e[o]);break;case"output":a.push(e[o])}if(e[o].hasOwnProperty("listeners"))for(var l in e[o].listeners)e[o].listeners.hasOwnProperty(l)&&h(l,e[o].listeners[l])}}function h(e,t){if(!i.helper.isString(e))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+typeof e+" given");if("function"!=typeof t)throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+typeof t+" given");r.hasOwnProperty(e)||(r[e]=[]),r[e].push(t)}!function(){for(var n in e=e||{},l)l.hasOwnProperty(n)&&(t[n]=l[n]);if("object"!=typeof e)throw Error("Converter expects the passed parameter to be an object, but "+typeof e+" was passed instead.");for(var a in e)e.hasOwnProperty(a)&&(t[a]=e[a]);t.extensions&&i.helper.forEach(t.extensions,p)}(),this._dispatch=function(e,t,n,a){if(r.hasOwnProperty(e))for(var i=0;i<r[e].length;++i){var o=r[e][i](e,t,this,n,a);o&&void 0!==o&&(t=o)}return t},this.listen=function(e,t){return h(e,t),this},this.makeHtml=function(e){if(!e)return e;var r={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:n,outputModifiers:a,converter:this,ghCodeBlocks:[],metadata:{parsed:{},raw:"",format:""}};return e=(e=(e=(e=(e=e.replace(/¨/g,"¨T")).replace(/\$/g,"¨D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/\u00A0/g,"&nbsp;"),t.smartIndentationFix&&(e=function(e){var t=e.match(/^\s*/)[0].length,n=new RegExp("^\\s{0,"+t+"}","gm");return e.replace(n,"")}(e)),e="\n\n"+e+"\n\n",e=(e=i.subParser("detab")(e,t,r)).replace(/^[ \t]+$/gm,""),i.helper.forEach(n,(function(n){e=i.subParser("runExtension")(n,e,t,r)})),e=i.subParser("metadata")(e,t,r),e=i.subParser("hashPreCodeTags")(e,t,r),e=i.subParser("githubCodeBlocks")(e,t,r),e=i.subParser("hashHTMLBlocks")(e,t,r),e=i.subParser("hashCodeTags")(e,t,r),e=i.subParser("stripLinkDefinitions")(e,t,r),e=i.subParser("blockGamut")(e,t,r),e=i.subParser("unhashHTMLSpans")(e,t,r),e=(e=(e=i.subParser("unescapeSpecialChars")(e,t,r)).replace(/¨D/g,"$$")).replace(/¨T/g,"¨"),e=i.subParser("completeHTMLDocument")(e,t,r),i.helper.forEach(a,(function(n){e=i.subParser("runExtension")(n,e,t,r)})),d=r.metadata,e},this.makeMarkdown=this.makeMd=function(e,t){if(e=(e=(e=e.replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/>[ \t]+</,">¨NBSP;<"),!t){if(!window||!window.document)throw new Error("HTMLParser is undefined. If in a webworker or nodejs environment, you need to provide a WHATWG DOM and HTML such as JSDOM");t=window.document}var n=t.createElement("div");n.innerHTML=e;var a={preList:function(e){for(var t=e.querySelectorAll("pre"),n=[],a=0;a<t.length;++a)if(1===t[a].childElementCount&&"code"===t[a].firstChild.tagName.toLowerCase()){var r=t[a].firstChild.innerHTML.trim(),o=t[a].firstChild.getAttribute("data-language")||"";if(""===o)for(var s=t[a].firstChild.className.split(" "),l=0;l<s.length;++l){var c=s[l].match(/^language-(.+)$/);if(null!==c){o=c[1];break}}r=i.helper.unescapeHTMLEntities(r),n.push(r),t[a].outerHTML='<precode language="'+o+'" precodenum="'+a.toString()+'"></precode>'}else n.push(t[a].innerHTML),t[a].innerHTML="",t[a].setAttribute("prenum",a.toString());return n}(n)};!function e(t){for(var n=0;n<t.childNodes.length;++n){var a=t.childNodes[n];3===a.nodeType?/\S/.test(a.nodeValue)?(a.nodeValue=a.nodeValue.split("\n").join(" "),a.nodeValue=a.nodeValue.replace(/(\s)+/g,"$1")):(t.removeChild(a),--n):1===a.nodeType&&e(a)}}(n);for(var r=n.childNodes,o="",s=0;s<r.length;s++)o+=i.subParser("makeMarkdown.node")(r[s],a);return o},this.setOption=function(e,n){t[e]=n},this.getOption=function(e){return t[e]},this.getOptions=function(){return t},this.addExtension=function(e,t){p(e,t=t||null)},this.useExtension=function(e){p(e)},this.setFlavor=function(e){if(!u.hasOwnProperty(e))throw Error(e+" flavor was not found");var n=u[e];for(var a in o=e,n)n.hasOwnProperty(a)&&(t[a]=n[a])},this.getFlavor=function(){return o},this.removeExtension=function(e){i.helper.isArray(e)||(e=[e]);for(var t=0;t<e.length;++t){for(var r=e[t],o=0;o<n.length;++o)n[o]===r&&n[o].splice(o,1);for(;0<a.length;++o)a[0]===r&&a[0].splice(o,1)}},this.getAllExtensions=function(){return{language:n,output:a}},this.getMetadata=function(e){return e?d.raw:d.parsed},this.getMetadataFormat=function(){return d.format},this._setMetadataPair=function(e,t){d.parsed[e]=t},this._setMetadataFormat=function(e){d.format=e},this._setMetadataRaw=function(e){d.raw=e}},i.subParser("anchors",(function(e,t,n){"use strict";var a=function(e,a,r,o,s,l,c){if(i.helper.isUndefined(c)&&(c=""),r=r.toLowerCase(),e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)o="";else if(!o){if(r||(r=a.toLowerCase().replace(/ ?\n/g," ")),o="#"+r,i.helper.isUndefined(n.gUrls[r]))return e;o=n.gUrls[r],i.helper.isUndefined(n.gTitles[r])||(c=n.gTitles[r])}var u='<a href="'+(o=o.replace(i.helper.regexes.asteriskDashAndColon,i.helper.escapeCharactersCallback))+'"';return""!==c&&null!==c&&(u+=' title="'+(c=(c=c.replace(/"/g,"&quot;")).replace(i.helper.regexes.asteriskDashAndColon,i.helper.escapeCharactersCallback))+'"'),t.openLinksInNewWindow&&!/^#/.test(o)&&(u+=' rel="noopener noreferrer" target="¨E95Eblank"'),u+=">"+a+"</a>"};return e=(e=(e=(e=(e=n.converter._dispatch("anchors.before",e,t,n)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g,a)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,a)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,a)).replace(/\[([^\[\]]+)]()()()()()/g,a),t.ghMentions&&(e=e.replace(/(^|\s)(\\)?(@([a-z\d]+(?:[a-z\d.-]+?[a-z\d]+)*))/gim,(function(e,n,a,r,o){if("\\"===a)return n+r;if(!i.helper.isString(t.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");var s=t.ghMentionsLink.replace(/\{u}/g,o),l="";return t.openLinksInNewWindow&&(l=' rel="noopener noreferrer" target="¨E95Eblank"'),n+'<a href="'+s+'"'+l+">"+r+"</a>"}))),e=n.converter._dispatch("anchors.after",e,t,n)}));var h=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+?\.[^'">\s]+?)()(\1)?(?=\s|$)(?!["<>])/gi,g=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+\.[^'">\s]+?)([.!?,()\[\]])?(\1)?(?=\s|$)(?!["<>])/gi,f=/()<(((https?|ftp|dict):\/\/|www\.)[^'">\s]+)()>()/gi,_=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gim,b=/<()(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi,w=function(e){"use strict";return function(t,n,a,r,o,s,l){var c=a=a.replace(i.helper.regexes.asteriskDashAndColon,i.helper.escapeCharactersCallback),u="",m="",d=n||"",p=l||"";return/^www\./i.test(a)&&(a=a.replace(/^www\./i,"http://www.")),e.excludeTrailingPunctuationFromURLs&&s&&(u=s),e.openLinksInNewWindow&&(m=' rel="noopener noreferrer" target="¨E95Eblank"'),d+'<a href="'+a+'"'+m+">"+c+"</a>"+u+p}},v=function(e,t){"use strict";return function(n,a,r){var o="mailto:";return a=a||"",r=i.subParser("unescapeSpecialChars")(r,e,t),e.encodeEmails?(o=i.helper.encodeEmailAddress(o+r),r=i.helper.encodeEmailAddress(r)):o+=r,a+'<a href="'+o+'">'+r+"</a>"}};i.subParser("autoLinks",(function(e,t,n){"use strict";return e=(e=(e=n.converter._dispatch("autoLinks.before",e,t,n)).replace(f,w(t))).replace(b,v(t,n)),e=n.converter._dispatch("autoLinks.after",e,t,n)})),i.subParser("simplifiedAutoLinks",(function(e,t,n){"use strict";return t.simplifiedAutoLink?(e=n.converter._dispatch("simplifiedAutoLinks.before",e,t,n),e=(e=t.excludeTrailingPunctuationFromURLs?e.replace(g,w(t)):e.replace(h,w(t))).replace(_,v(t,n)),e=n.converter._dispatch("simplifiedAutoLinks.after",e,t,n)):e})),i.subParser("blockGamut",(function(e,t,n){"use strict";return e=n.converter._dispatch("blockGamut.before",e,t,n),e=i.subParser("blockQuotes")(e,t,n),e=i.subParser("headers")(e,t,n),e=i.subParser("horizontalRule")(e,t,n),e=i.subParser("lists")(e,t,n),e=i.subParser("codeBlocks")(e,t,n),e=i.subParser("tables")(e,t,n),e=i.subParser("hashHTMLBlocks")(e,t,n),e=i.subParser("paragraphs")(e,t,n),e=n.converter._dispatch("blockGamut.after",e,t,n)})),i.subParser("blockQuotes",(function(e,t,n){"use strict";e=n.converter._dispatch("blockQuotes.before",e,t,n),e+="\n\n";var a=/(^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+/gm;return t.splitAdjacentBlockquotes&&(a=/^ {0,3}>[\s\S]*?(?:\n\n)/gm),e=e.replace(a,(function(e){return e=(e=(e=e.replace(/^[ \t]*>[ \t]?/gm,"")).replace(/¨0/g,"")).replace(/^[ \t]+$/gm,""),e=i.subParser("githubCodeBlocks")(e,t,n),e=(e=(e=i.subParser("blockGamut")(e,t,n)).replace(/(^|\n)/g,"$1  ")).replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,(function(e,t){var n=t;return n=(n=n.replace(/^  /gm,"¨0")).replace(/¨0/g,"")})),i.subParser("hashBlock")("<blockquote>\n"+e+"\n</blockquote>",t,n)})),e=n.converter._dispatch("blockQuotes.after",e,t,n)})),i.subParser("codeBlocks",(function(e,t,n){"use strict";e=n.converter._dispatch("codeBlocks.before",e,t,n);return e=(e=(e+="¨0").replace(/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=¨0))/g,(function(e,a,r){var o=a,s=r,l="\n";return o=i.subParser("outdent")(o,t,n),o=i.subParser("encodeCode")(o,t,n),o=(o=(o=i.subParser("detab")(o,t,n)).replace(/^\n+/g,"")).replace(/\n+$/g,""),t.omitExtraWLInCodeBlocks&&(l=""),o="<pre><code>"+o+l+"</code></pre>",i.subParser("hashBlock")(o,t,n)+s}))).replace(/¨0/,""),e=n.converter._dispatch("codeBlocks.after",e,t,n)})),i.subParser("codeSpans",(function(e,t,n){"use strict";return void 0===(e=n.converter._dispatch("codeSpans.before",e,t,n))&&(e=""),e=e.replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,(function(e,a,r,o){var s=o;return s=(s=s.replace(/^([ \t]*)/g,"")).replace(/[ \t]*$/g,""),s=a+"<code>"+(s=i.subParser("encodeCode")(s,t,n))+"</code>",s=i.subParser("hashHTMLSpans")(s,t,n)})),e=n.converter._dispatch("codeSpans.after",e,t,n)})),i.subParser("completeHTMLDocument",(function(e,t,n){"use strict";if(!t.completeHTMLDocument)return e;e=n.converter._dispatch("completeHTMLDocument.before",e,t,n);var a="html",r="<!DOCTYPE HTML>\n",i="",o='<meta charset="utf-8">\n',s="",l="";for(var c in void 0!==n.metadata.parsed.doctype&&(r="<!DOCTYPE "+n.metadata.parsed.doctype+">\n","html"!==(a=n.metadata.parsed.doctype.toString().toLowerCase())&&"html5"!==a||(o='<meta charset="utf-8">')),n.metadata.parsed)if(n.metadata.parsed.hasOwnProperty(c))switch(c.toLowerCase()){case"doctype":break;case"title":i="<title>"+n.metadata.parsed.title+"</title>\n";break;case"charset":o="html"===a||"html5"===a?'<meta charset="'+n.metadata.parsed.charset+'">\n':'<meta name="charset" content="'+n.metadata.parsed.charset+'">\n';break;case"language":case"lang":s=' lang="'+n.metadata.parsed[c]+'"',l+='<meta name="'+c+'" content="'+n.metadata.parsed[c]+'">\n';break;default:l+='<meta name="'+c+'" content="'+n.metadata.parsed[c]+'">\n'}return e=r+"<html"+s+">\n<head>\n"+i+o+l+"</head>\n<body>\n"+e.trim()+"\n</body>\n</html>",e=n.converter._dispatch("completeHTMLDocument.after",e,t,n)})),i.subParser("detab",(function(e,t,n){"use strict";return e=(e=(e=(e=(e=(e=n.converter._dispatch("detab.before",e,t,n)).replace(/\t(?=\t)/g,"    ")).replace(/\t/g,"¨A¨B")).replace(/¨B(.+?)¨A/g,(function(e,t){for(var n=t,a=4-n.length%4,r=0;r<a;r++)n+=" ";return n}))).replace(/¨A/g,"    ")).replace(/¨B/g,""),e=n.converter._dispatch("detab.after",e,t,n)})),i.subParser("ellipsis",(function(e,t,n){"use strict";return e=(e=n.converter._dispatch("ellipsis.before",e,t,n)).replace(/\.\.\./g,"…"),e=n.converter._dispatch("ellipsis.after",e,t,n)})),i.subParser("emoji",(function(e,t,n){"use strict";if(!t.emoji)return e;return e=(e=n.converter._dispatch("emoji.before",e,t,n)).replace(/:([\S]+?):/g,(function(e,t){return i.helper.emojis.hasOwnProperty(t)?i.helper.emojis[t]:e})),e=n.converter._dispatch("emoji.after",e,t,n)})),i.subParser("encodeAmpsAndAngles",(function(e,t,n){"use strict";return e=(e=(e=(e=(e=n.converter._dispatch("encodeAmpsAndAngles.before",e,t,n)).replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;")).replace(/<(?![a-z\/?$!])/gi,"&lt;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;"),e=n.converter._dispatch("encodeAmpsAndAngles.after",e,t,n)})),i.subParser("encodeBackslashEscapes",(function(e,t,n){"use strict";return e=(e=(e=n.converter._dispatch("encodeBackslashEscapes.before",e,t,n)).replace(/\\(\\)/g,i.helper.escapeCharactersCallback)).replace(/\\([`*_{}\[\]()>#+.!~=|-])/g,i.helper.escapeCharactersCallback),e=n.converter._dispatch("encodeBackslashEscapes.after",e,t,n)})),i.subParser("encodeCode",(function(e,t,n){"use strict";return e=(e=n.converter._dispatch("encodeCode.before",e,t,n)).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,i.helper.escapeCharactersCallback),e=n.converter._dispatch("encodeCode.after",e,t,n)})),i.subParser("escapeSpecialCharsWithinTagAttributes",(function(e,t,n){"use strict";return e=(e=(e=n.converter._dispatch("escapeSpecialCharsWithinTagAttributes.before",e,t,n)).replace(/<\/?[a-z\d_:-]+(?:[\s]+[\s\S]+?)?>/gi,(function(e){return e.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,i.helper.escapeCharactersCallback)}))).replace(/<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi,(function(e){return e.replace(/([\\`*_~=|])/g,i.helper.escapeCharactersCallback)})),e=n.converter._dispatch("escapeSpecialCharsWithinTagAttributes.after",e,t,n)})),i.subParser("githubCodeBlocks",(function(e,t,n){"use strict";return t.ghCodeBlocks?(e=n.converter._dispatch("githubCodeBlocks.before",e,t,n),e=(e=(e+="¨0").replace(/(?:^|\n)(?: {0,3})(```+|~~~+)(?: *)([^\s`~]*)\n([\s\S]*?)\n(?: {0,3})\1/g,(function(e,a,r,o){var s=t.omitExtraWLInCodeBlocks?"":"\n";return o=i.subParser("encodeCode")(o,t,n),o="<pre><code"+(r?' class="'+r+" language-"+r+'"':"")+">"+(o=(o=(o=i.subParser("detab")(o,t,n)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+s+"</code></pre>",o=i.subParser("hashBlock")(o,t,n),"\n\n¨G"+(n.ghCodeBlocks.push({text:e,codeblock:o})-1)+"G\n\n"}))).replace(/¨0/,""),n.converter._dispatch("githubCodeBlocks.after",e,t,n)):e})),i.subParser("hashBlock",(function(e,t,n){"use strict";return e=(e=n.converter._dispatch("hashBlock.before",e,t,n)).replace(/(^\n+|\n+$)/g,""),e="\n\n¨K"+(n.gHtmlBlocks.push(e)-1)+"K\n\n",e=n.converter._dispatch("hashBlock.after",e,t,n)})),i.subParser("hashCodeTags",(function(e,t,n){"use strict";e=n.converter._dispatch("hashCodeTags.before",e,t,n);return e=i.helper.replaceRecursiveRegExp(e,(function(e,a,r,o){var s=r+i.subParser("encodeCode")(a,t,n)+o;return"¨C"+(n.gHtmlSpans.push(s)-1)+"C"}),"<code\\b[^>]*>","</code>","gim"),e=n.converter._dispatch("hashCodeTags.after",e,t,n)})),i.subParser("hashElement",(function(e,t,n){"use strict";return function(e,t){var a=t;return a=(a=(a=a.replace(/\n\n/g,"\n")).replace(/^\n/,"")).replace(/\n+$/g,""),a="\n\n¨K"+(n.gHtmlBlocks.push(a)-1)+"K\n\n"}})),i.subParser("hashHTMLBlocks",(function(e,t,n){"use strict";e=n.converter._dispatch("hashHTMLBlocks.before",e,t,n);var a=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"],r=function(e,t,a,r){var i=e;return-1!==a.search(/\bmarkdown\b/)&&(i=a+n.converter.makeHtml(t)+r),"\n\n¨K"+(n.gHtmlBlocks.push(i)-1)+"K\n\n"};t.backslashEscapesHTMLTags&&(e=e.replace(/\\<(\/?[^>]+?)>/g,(function(e,t){return"&lt;"+t+"&gt;"})));for(var o=0;o<a.length;++o)for(var s,l=new RegExp("^ {0,3}(<"+a[o]+"\\b[^>]*>)","im"),c="<"+a[o]+"\\b[^>]*>",u="</"+a[o]+">";-1!==(s=i.helper.regexIndexOf(e,l));){var m=i.helper.splitAtIndex(e,s),d=i.helper.replaceRecursiveRegExp(m[1],r,c,u,"im");if(d===m[1])break;e=m[0].concat(d)}return e=e.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,i.subParser("hashElement")(e,t,n)),e=(e=i.helper.replaceRecursiveRegExp(e,(function(e){return"\n\n¨K"+(n.gHtmlBlocks.push(e)-1)+"K\n\n"}),"^ {0,3}\x3c!--","--\x3e","gm")).replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,i.subParser("hashElement")(e,t,n)),e=n.converter._dispatch("hashHTMLBlocks.after",e,t,n)})),i.subParser("hashHTMLSpans",(function(e,t,n){"use strict";function a(e){return"¨C"+(n.gHtmlSpans.push(e)-1)+"C"}return e=(e=(e=(e=(e=n.converter._dispatch("hashHTMLSpans.before",e,t,n)).replace(/<[^>]+?\/>/gi,(function(e){return a(e)}))).replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,(function(e){return a(e)}))).replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,(function(e){return a(e)}))).replace(/<[^>]+?>/gi,(function(e){return a(e)})),e=n.converter._dispatch("hashHTMLSpans.after",e,t,n)})),i.subParser("unhashHTMLSpans",(function(e,t,n){"use strict";e=n.converter._dispatch("unhashHTMLSpans.before",e,t,n);for(var a=0;a<n.gHtmlSpans.length;++a){for(var r=n.gHtmlSpans[a],i=0;/¨C(\d+)C/.test(r);){var o=RegExp.$1;if(r=r.replace("¨C"+o+"C",n.gHtmlSpans[o]),10===i){console.error("maximum nesting of 10 spans reached!!!");break}++i}e=e.replace("¨C"+a+"C",r)}return e=n.converter._dispatch("unhashHTMLSpans.after",e,t,n)})),i.subParser("hashPreCodeTags",(function(e,t,n){"use strict";e=n.converter._dispatch("hashPreCodeTags.before",e,t,n);return e=i.helper.replaceRecursiveRegExp(e,(function(e,a,r,o){var s=r+i.subParser("encodeCode")(a,t,n)+o;return"\n\n¨G"+(n.ghCodeBlocks.push({text:e,codeblock:s})-1)+"G\n\n"}),"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),e=n.converter._dispatch("hashPreCodeTags.after",e,t,n)})),i.subParser("headers",(function(e,t,n){"use strict";e=n.converter._dispatch("headers.before",e,t,n);var a=isNaN(parseInt(t.headerLevelStart))?1:parseInt(t.headerLevelStart),r=t.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,o=t.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm;e=(e=e.replace(r,(function(e,r){var o=i.subParser("spanGamut")(r,t,n),s=t.noHeaderId?"":' id="'+l(r)+'"',c="<h"+a+s+">"+o+"</h"+a+">";return i.subParser("hashBlock")(c,t,n)}))).replace(o,(function(e,r){var o=i.subParser("spanGamut")(r,t,n),s=t.noHeaderId?"":' id="'+l(r)+'"',c=a+1,u="<h"+c+s+">"+o+"</h"+c+">";return i.subParser("hashBlock")(u,t,n)}));var s=t.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm;function l(e){var a,r;if(t.customizedHeaderId){var o=e.match(/\{([^{]+?)}\s*$/);o&&o[1]&&(e=o[1])}return a=e,r=i.helper.isString(t.prefixHeaderId)?t.prefixHeaderId:!0===t.prefixHeaderId?"section-":"",t.rawPrefixHeaderId||(a=r+a),a=t.ghCompatibleHeaderId?a.replace(/ /g,"-").replace(/&amp;/g,"").replace(/¨T/g,"").replace(/¨D/g,"").replace(/[&+$,\/:;=?@"#{}|^¨~\[\]`\\*)(%.!'<>]/g,"").toLowerCase():t.rawHeaderId?a.replace(/ /g,"-").replace(/&amp;/g,"&").replace(/¨T/g,"¨").replace(/¨D/g,"$").replace(/["']/g,"-").toLowerCase():a.replace(/[^\w]/g,"").toLowerCase(),t.rawPrefixHeaderId&&(a=r+a),n.hashLinkCounts[a]?a=a+"-"+n.hashLinkCounts[a]++:n.hashLinkCounts[a]=1,a}return e=e.replace(s,(function(e,r,o){var s=o;t.customizedHeaderId&&(s=o.replace(/\s?\{([^{]+?)}\s*$/,""));var c=i.subParser("spanGamut")(s,t,n),u=t.noHeaderId?"":' id="'+l(o)+'"',m=a-1+r.length,d="<h"+m+u+">"+c+"</h"+m+">";return i.subParser("hashBlock")(d,t,n)})),e=n.converter._dispatch("headers.after",e,t,n)})),i.subParser("horizontalRule",(function(e,t,n){"use strict";e=n.converter._dispatch("horizontalRule.before",e,t,n);var a=i.subParser("hashBlock")("<hr />",t,n);return e=(e=(e=e.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,a)).replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,a)).replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,a),e=n.converter._dispatch("horizontalRule.after",e,t,n)})),i.subParser("images",(function(e,t,n){"use strict";function a(e,t,a,r,o,s,l,c){var u=n.gUrls,m=n.gTitles,d=n.gDimensions;if(a=a.toLowerCase(),c||(c=""),e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)r="";else if(""===r||null===r){if(""!==a&&null!==a||(a=t.toLowerCase().replace(/ ?\n/g," ")),r="#"+a,i.helper.isUndefined(u[a]))return e;r=u[a],i.helper.isUndefined(m[a])||(c=m[a]),i.helper.isUndefined(d[a])||(o=d[a].width,s=d[a].height)}t=t.replace(/"/g,"&quot;").replace(i.helper.regexes.asteriskDashAndColon,i.helper.escapeCharactersCallback);var p='<img src="'+(r=r.replace(i.helper.regexes.asteriskDashAndColon,i.helper.escapeCharactersCallback))+'" alt="'+t+'"';return c&&i.helper.isString(c)&&(p+=' title="'+(c=c.replace(/"/g,"&quot;").replace(i.helper.regexes.asteriskDashAndColon,i.helper.escapeCharactersCallback))+'"'),o&&s&&(p+=' width="'+(o="*"===o?"auto":o)+'"',p+=' height="'+(s="*"===s?"auto":s)+'"'),p+=" />"}return e=(e=(e=(e=(e=(e=n.converter._dispatch("images.before",e,t,n)).replace(/!\[([^\]]*?)] ?(?:\n *)?\[([\s\S]*?)]()()()()()/g,a)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,(function(e,t,n,r,i,o,s,l){return a(e,t,n,r=r.replace(/\s/g,""),i,o,s,l)}))).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,a)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,a)).replace(/!\[([^\[\]]+)]()()()()()/g,a),e=n.converter._dispatch("images.after",e,t,n)})),i.subParser("italicsAndBold",(function(e,t,n){"use strict";function a(e,t,n){return t+e+n}return e=n.converter._dispatch("italicsAndBold.before",e,t,n),e=t.literalMidWordUnderscores?(e=(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,(function(e,t){return a(t,"<strong><em>","</em></strong>")}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(e,t){return a(t,"<strong>","</strong>")}))).replace(/\b_(\S[\s\S]*?)_\b/g,(function(e,t){return a(t,"<em>","</em>")})):(e=(e=e.replace(/___(\S[\s\S]*?)___/g,(function(e,t){return/\S$/.test(t)?a(t,"<strong><em>","</em></strong>"):e}))).replace(/__(\S[\s\S]*?)__/g,(function(e,t){return/\S$/.test(t)?a(t,"<strong>","</strong>"):e}))).replace(/_([^\s_][\s\S]*?)_/g,(function(e,t){return/\S$/.test(t)?a(t,"<em>","</em>"):e})),e=t.literalMidWordAsterisks?(e=(e=e.replace(/([^*]|^)\B\*\*\*(\S[\s\S]*?)\*\*\*\B(?!\*)/g,(function(e,t,n){return a(n,t+"<strong><em>","</em></strong>")}))).replace(/([^*]|^)\B\*\*(\S[\s\S]*?)\*\*\B(?!\*)/g,(function(e,t,n){return a(n,t+"<strong>","</strong>")}))).replace(/([^*]|^)\B\*(\S[\s\S]*?)\*\B(?!\*)/g,(function(e,t,n){return a(n,t+"<em>","</em>")})):(e=(e=e.replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,(function(e,t){return/\S$/.test(t)?a(t,"<strong><em>","</em></strong>"):e}))).replace(/\*\*(\S[\s\S]*?)\*\*/g,(function(e,t){return/\S$/.test(t)?a(t,"<strong>","</strong>"):e}))).replace(/\*([^\s*][\s\S]*?)\*/g,(function(e,t){return/\S$/.test(t)?a(t,"<em>","</em>"):e})),e=n.converter._dispatch("italicsAndBold.after",e,t,n)})),i.subParser("lists",(function(e,t,n){"use strict";function a(e,a){n.gListLevel++,e=e.replace(/\n{2,}$/,"\n");var r=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0| {0,3}([*+-]|\d+[.])[ \t]+))/gm,o=/\n[ \t]*\n(?!¨0)/.test(e+="¨0");return t.disableForced4SpacesIndentedSublists&&(r=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0|\2([*+-]|\d+[.])[ \t]+))/gm),e=(e=e.replace(r,(function(e,a,r,s,l,c,u){u=u&&""!==u.trim();var m=i.subParser("outdent")(l,t,n),d="";return c&&t.tasklists&&(d=' class="task-list-item" style="list-style-type: none;"',m=m.replace(/^[ \t]*\[(x|X| )?]/m,(function(){var e='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return u&&(e+=" checked"),e+=">"}))),m=m.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,(function(e){return"¨A"+e})),a||m.search(/\n{2,}/)>-1?(m=i.subParser("githubCodeBlocks")(m,t,n),m=i.subParser("blockGamut")(m,t,n)):(m=(m=i.subParser("lists")(m,t,n)).replace(/\n$/,""),m=(m=i.subParser("hashHTMLBlocks")(m,t,n)).replace(/\n\n+/g,"\n\n"),m=o?i.subParser("paragraphs")(m,t,n):i.subParser("spanGamut")(m,t,n)),m="<li"+d+">"+(m=m.replace("¨A",""))+"</li>\n"}))).replace(/¨0/g,""),n.gListLevel--,a&&(e=e.replace(/\s+$/,"")),e}function r(e,t){if("ol"===t){var n=e.match(/^ *(\d+)\./);if(n&&"1"!==n[1])return' start="'+n[1]+'"'}return""}function o(e,n,i){var o=t.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,s=t.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,l="ul"===n?o:s,c="";if(-1!==e.search(l))!function t(u){var m=u.search(l),d=r(e,n);-1!==m?(c+="\n\n<"+n+d+">\n"+a(u.slice(0,m),!!i)+"</"+n+">\n",l="ul"===(n="ul"===n?"ol":"ul")?o:s,t(u.slice(m))):c+="\n\n<"+n+d+">\n"+a(u,!!i)+"</"+n+">\n"}(e);else{var u=r(e,n);c="\n\n<"+n+u+">\n"+a(e,!!i)+"</"+n+">\n"}return c}return e=n.converter._dispatch("lists.before",e,t,n),e+="¨0",e=(e=n.gListLevel?e.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,t,n){return o(t,n.search(/[*+-]/g)>-1?"ul":"ol",!0)})):e.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,t,n,a){return o(n,a.search(/[*+-]/g)>-1?"ul":"ol",!1)}))).replace(/¨0/,""),e=n.converter._dispatch("lists.after",e,t,n)})),i.subParser("metadata",(function(e,t,n){"use strict";if(!t.metadata)return e;function a(e){n.metadata.raw=e,(e=(e=e.replace(/&/g,"&amp;").replace(/"/g,"&quot;")).replace(/\n {4}/g," ")).replace(/^([\S ]+): +([\s\S]+?)$/gm,(function(e,t,a){return n.metadata.parsed[t]=a,""}))}return e=(e=(e=(e=n.converter._dispatch("metadata.before",e,t,n)).replace(/^\s*«««+(\S*?)\n([\s\S]+?)\n»»»+\n/,(function(e,t,n){return a(n),"¨M"}))).replace(/^\s*---+(\S*?)\n([\s\S]+?)\n---+\n/,(function(e,t,r){return t&&(n.metadata.format=t),a(r),"¨M"}))).replace(/¨M/g,""),e=n.converter._dispatch("metadata.after",e,t,n)})),i.subParser("outdent",(function(e,t,n){"use strict";return e=(e=(e=n.converter._dispatch("outdent.before",e,t,n)).replace(/^(\t|[ ]{1,4})/gm,"¨0")).replace(/¨0/g,""),e=n.converter._dispatch("outdent.after",e,t,n)})),i.subParser("paragraphs",(function(e,t,n){"use strict";for(var a=(e=(e=(e=n.converter._dispatch("paragraphs.before",e,t,n)).replace(/^\n+/g,"")).replace(/\n+$/g,"")).split(/\n{2,}/g),r=[],o=a.length,s=0;s<o;s++){var l=a[s];l.search(/¨(K|G)(\d+)\1/g)>=0?r.push(l):l.search(/\S/)>=0&&(l=(l=i.subParser("spanGamut")(l,t,n)).replace(/^([ \t]*)/g,"<p>"),l+="</p>",r.push(l))}for(o=r.length,s=0;s<o;s++){for(var c="",u=r[s],m=!1;/¨(K|G)(\d+)\1/.test(u);){var d=RegExp.$1,p=RegExp.$2;c=(c="K"===d?n.gHtmlBlocks[p]:m?i.subParser("encodeCode")(n.ghCodeBlocks[p].text,t,n):n.ghCodeBlocks[p].codeblock).replace(/\$/g,"$$$$"),u=u.replace(/(\n\n)?¨(K|G)\d+\2(\n\n)?/,c),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(u)&&(m=!0)}r[s]=u}return e=(e=(e=r.join("\n")).replace(/^\n+/g,"")).replace(/\n+$/g,""),n.converter._dispatch("paragraphs.after",e,t,n)})),i.subParser("runExtension",(function(e,t,n,a){"use strict";if(e.filter)t=e.filter(t,a.converter,n);else if(e.regex){var r=e.regex;r instanceof RegExp||(r=new RegExp(r,"g")),t=t.replace(r,e.replace)}return t})),i.subParser("spanGamut",(function(e,t,n){"use strict";return e=n.converter._dispatch("spanGamut.before",e,t,n),e=i.subParser("codeSpans")(e,t,n),e=i.subParser("escapeSpecialCharsWithinTagAttributes")(e,t,n),e=i.subParser("encodeBackslashEscapes")(e,t,n),e=i.subParser("images")(e,t,n),e=i.subParser("anchors")(e,t,n),e=i.subParser("autoLinks")(e,t,n),e=i.subParser("simplifiedAutoLinks")(e,t,n),e=i.subParser("emoji")(e,t,n),e=i.subParser("underline")(e,t,n),e=i.subParser("italicsAndBold")(e,t,n),e=i.subParser("strikethrough")(e,t,n),e=i.subParser("ellipsis")(e,t,n),e=i.subParser("hashHTMLSpans")(e,t,n),e=i.subParser("encodeAmpsAndAngles")(e,t,n),t.simpleLineBreaks?/\n\n¨K/.test(e)||(e=e.replace(/\n+/g,"<br />\n")):e=e.replace(/  +\n/g,"<br />\n"),e=n.converter._dispatch("spanGamut.after",e,t,n)})),i.subParser("strikethrough",(function(e,t,n){"use strict";return t.strikethrough&&(e=(e=n.converter._dispatch("strikethrough.before",e,t,n)).replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,(function(e,a){return function(e){return t.simplifiedAutoLink&&(e=i.subParser("simplifiedAutoLinks")(e,t,n)),"<del>"+e+"</del>"}(a)})),e=n.converter._dispatch("strikethrough.after",e,t,n)),e})),i.subParser("stripLinkDefinitions",(function(e,t,n){"use strict";var a=function(e,a,r,o,s,l,c){return a=a.toLowerCase(),r.match(/^data:.+?\/.+?;base64,/)?n.gUrls[a]=r.replace(/\s/g,""):n.gUrls[a]=i.subParser("encodeAmpsAndAngles")(r,t,n),l?l+c:(c&&(n.gTitles[a]=c.replace(/"|'/g,"&quot;")),t.parseImgDimensions&&o&&s&&(n.gDimensions[a]={width:o,height:s}),"")};return e=(e=(e=(e+="¨0").replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n\n|(?=¨0)|(?=\n\[))/gm,a)).replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=¨0))/gm,a)).replace(/¨0/,"")})),i.subParser("tables",(function(e,t,n){"use strict";if(!t.tables)return e;function a(e,a){return"<td"+a+">"+i.subParser("spanGamut")(e,t,n)+"</td>\n"}function r(e){var r,o=e.split("\n");for(r=0;r<o.length;++r)/^ {0,3}\|/.test(o[r])&&(o[r]=o[r].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(o[r])&&(o[r]=o[r].replace(/\|[ \t]*$/,"")),o[r]=i.subParser("codeSpans")(o[r],t,n);var s,l,c,u,m=o[0].split("|").map((function(e){return e.trim()})),d=o[1].split("|").map((function(e){return e.trim()})),p=[],h=[],g=[],f=[];for(o.shift(),o.shift(),r=0;r<o.length;++r)""!==o[r].trim()&&p.push(o[r].split("|").map((function(e){return e.trim()})));if(m.length<d.length)return e;for(r=0;r<d.length;++r)g.push((s=d[r],/^:[ \t]*--*$/.test(s)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(s)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(s)?' style="text-align:center;"':""));for(r=0;r<m.length;++r)i.helper.isUndefined(g[r])&&(g[r]=""),h.push((l=m[r],c=g[r],u=void 0,u="",l=l.trim(),(t.tablesHeaderId||t.tableHeaderId)&&(u=' id="'+l.replace(/ /g,"_").toLowerCase()+'"'),"<th"+u+c+">"+(l=i.subParser("spanGamut")(l,t,n))+"</th>\n"));for(r=0;r<p.length;++r){for(var _=[],b=0;b<h.length;++b)i.helper.isUndefined(p[r][b]),_.push(a(p[r][b],g[b]));f.push(_)}return function(e,t){for(var n="<table>\n<thead>\n<tr>\n",a=e.length,r=0;r<a;++r)n+=e[r];for(n+="</tr>\n</thead>\n<tbody>\n",r=0;r<t.length;++r){n+="<tr>\n";for(var i=0;i<a;++i)n+=t[r][i];n+="</tr>\n"}return n+"</tbody>\n</table>\n"}(h,f)}return e=(e=(e=(e=n.converter._dispatch("tables.before",e,t,n)).replace(/\\(\|)/g,i.helper.escapeCharactersCallback)).replace(/^ {0,3}\|?.+\|.+\n {0,3}\|?[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:[-=]){2,}[\s\S]+?(?:\n\n|¨0)/gm,r)).replace(/^ {0,3}\|.+\|[ \t]*\n {0,3}\|[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*\n( {0,3}\|.+\|[ \t]*\n)*(?:\n|¨0)/gm,r),e=n.converter._dispatch("tables.after",e,t,n)})),i.subParser("underline",(function(e,t,n){"use strict";return t.underline?(e=n.converter._dispatch("underline.before",e,t,n),e=(e=t.literalMidWordUnderscores?(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,(function(e,t){return"<u>"+t+"</u>"}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(e,t){return"<u>"+t+"</u>"})):(e=e.replace(/___(\S[\s\S]*?)___/g,(function(e,t){return/\S$/.test(t)?"<u>"+t+"</u>":e}))).replace(/__(\S[\s\S]*?)__/g,(function(e,t){return/\S$/.test(t)?"<u>"+t+"</u>":e}))).replace(/(_)/g,i.helper.escapeCharactersCallback),e=n.converter._dispatch("underline.after",e,t,n)):e})),i.subParser("unescapeSpecialChars",(function(e,t,n){"use strict";return e=(e=n.converter._dispatch("unescapeSpecialChars.before",e,t,n)).replace(/¨E(\d+)E/g,(function(e,t){var n=parseInt(t);return String.fromCharCode(n)})),e=n.converter._dispatch("unescapeSpecialChars.after",e,t,n)})),i.subParser("makeMarkdown.blockquote",(function(e,t){"use strict";var n="";if(e.hasChildNodes())for(var a=e.childNodes,r=a.length,o=0;o<r;++o){var s=i.subParser("makeMarkdown.node")(a[o],t);""!==s&&(n+=s)}return n="> "+(n=n.trim()).split("\n").join("\n> ")})),i.subParser("makeMarkdown.codeBlock",(function(e,t){"use strict";var n=e.getAttribute("language"),a=e.getAttribute("precodenum");return"```"+n+"\n"+t.preList[a]+"\n```"})),i.subParser("makeMarkdown.codeSpan",(function(e){"use strict";return"`"+e.innerHTML+"`"})),i.subParser("makeMarkdown.emphasis",(function(e,t){"use strict";var n="";if(e.hasChildNodes()){n+="*";for(var a=e.childNodes,r=a.length,o=0;o<r;++o)n+=i.subParser("makeMarkdown.node")(a[o],t);n+="*"}return n})),i.subParser("makeMarkdown.header",(function(e,t,n){"use strict";var a=new Array(n+1).join("#"),r="";if(e.hasChildNodes()){r=a+" ";for(var o=e.childNodes,s=o.length,l=0;l<s;++l)r+=i.subParser("makeMarkdown.node")(o[l],t)}return r})),i.subParser("makeMarkdown.hr",(function(){"use strict";return"---"})),i.subParser("makeMarkdown.image",(function(e){"use strict";var t="";return e.hasAttribute("src")&&(t+="!["+e.getAttribute("alt")+"](",t+="<"+e.getAttribute("src")+">",e.hasAttribute("width")&&e.hasAttribute("height")&&(t+=" ="+e.getAttribute("width")+"x"+e.getAttribute("height")),e.hasAttribute("title")&&(t+=' "'+e.getAttribute("title")+'"'),t+=")"),t})),i.subParser("makeMarkdown.links",(function(e,t){"use strict";var n="";if(e.hasChildNodes()&&e.hasAttribute("href")){var a=e.childNodes,r=a.length;n="[";for(var o=0;o<r;++o)n+=i.subParser("makeMarkdown.node")(a[o],t);n+="](",n+="<"+e.getAttribute("href")+">",e.hasAttribute("title")&&(n+=' "'+e.getAttribute("title")+'"'),n+=")"}return n})),i.subParser("makeMarkdown.list",(function(e,t,n){"use strict";var a="";if(!e.hasChildNodes())return"";for(var r=e.childNodes,o=r.length,s=e.getAttribute("start")||1,l=0;l<o;++l)if(void 0!==r[l].tagName&&"li"===r[l].tagName.toLowerCase()){a+=("ol"===n?s.toString()+". ":"- ")+i.subParser("makeMarkdown.listItem")(r[l],t),++s}return(a+="\n\x3c!-- --\x3e\n").trim()})),i.subParser("makeMarkdown.listItem",(function(e,t){"use strict";for(var n="",a=e.childNodes,r=a.length,o=0;o<r;++o)n+=i.subParser("makeMarkdown.node")(a[o],t);return/\n$/.test(n)?n=n.split("\n").join("\n    ").replace(/^ {4}$/gm,"").replace(/\n\n+/g,"\n\n"):n+="\n",n})),i.subParser("makeMarkdown.node",(function(e,t,n){"use strict";n=n||!1;var a="";if(3===e.nodeType)return i.subParser("makeMarkdown.txt")(e,t);if(8===e.nodeType)return"\x3c!--"+e.data+"--\x3e\n\n";if(1!==e.nodeType)return"";switch(e.tagName.toLowerCase()){case"h1":n||(a=i.subParser("makeMarkdown.header")(e,t,1)+"\n\n");break;case"h2":n||(a=i.subParser("makeMarkdown.header")(e,t,2)+"\n\n");break;case"h3":n||(a=i.subParser("makeMarkdown.header")(e,t,3)+"\n\n");break;case"h4":n||(a=i.subParser("makeMarkdown.header")(e,t,4)+"\n\n");break;case"h5":n||(a=i.subParser("makeMarkdown.header")(e,t,5)+"\n\n");break;case"h6":n||(a=i.subParser("makeMarkdown.header")(e,t,6)+"\n\n");break;case"p":n||(a=i.subParser("makeMarkdown.paragraph")(e,t)+"\n\n");break;case"blockquote":n||(a=i.subParser("makeMarkdown.blockquote")(e,t)+"\n\n");break;case"hr":n||(a=i.subParser("makeMarkdown.hr")(e,t)+"\n\n");break;case"ol":n||(a=i.subParser("makeMarkdown.list")(e,t,"ol")+"\n\n");break;case"ul":n||(a=i.subParser("makeMarkdown.list")(e,t,"ul")+"\n\n");break;case"precode":n||(a=i.subParser("makeMarkdown.codeBlock")(e,t)+"\n\n");break;case"pre":n||(a=i.subParser("makeMarkdown.pre")(e,t)+"\n\n");break;case"table":n||(a=i.subParser("makeMarkdown.table")(e,t)+"\n\n");break;case"code":a=i.subParser("makeMarkdown.codeSpan")(e,t);break;case"em":case"i":a=i.subParser("makeMarkdown.emphasis")(e,t);break;case"strong":case"b":a=i.subParser("makeMarkdown.strong")(e,t);break;case"del":a=i.subParser("makeMarkdown.strikethrough")(e,t);break;case"a":a=i.subParser("makeMarkdown.links")(e,t);break;case"img":a=i.subParser("makeMarkdown.image")(e,t);break;default:a=e.outerHTML+"\n\n"}return a})),i.subParser("makeMarkdown.paragraph",(function(e,t){"use strict";var n="";if(e.hasChildNodes())for(var a=e.childNodes,r=a.length,o=0;o<r;++o)n+=i.subParser("makeMarkdown.node")(a[o],t);return n=n.trim()})),i.subParser("makeMarkdown.pre",(function(e,t){"use strict";var n=e.getAttribute("prenum");return"<pre>"+t.preList[n]+"</pre>"})),i.subParser("makeMarkdown.strikethrough",(function(e,t){"use strict";var n="";if(e.hasChildNodes()){n+="~~";for(var a=e.childNodes,r=a.length,o=0;o<r;++o)n+=i.subParser("makeMarkdown.node")(a[o],t);n+="~~"}return n})),i.subParser("makeMarkdown.strong",(function(e,t){"use strict";var n="";if(e.hasChildNodes()){n+="**";for(var a=e.childNodes,r=a.length,o=0;o<r;++o)n+=i.subParser("makeMarkdown.node")(a[o],t);n+="**"}return n})),i.subParser("makeMarkdown.table",(function(e,t){"use strict";var n,a,r="",o=[[],[]],s=e.querySelectorAll("thead>tr>th"),l=e.querySelectorAll("tbody>tr");for(n=0;n<s.length;++n){var c=i.subParser("makeMarkdown.tableCell")(s[n],t),u="---";if(s[n].hasAttribute("style"))switch(s[n].getAttribute("style").toLowerCase().replace(/\s/g,"")){case"text-align:left;":u=":---";break;case"text-align:right;":u="---:";break;case"text-align:center;":u=":---:"}o[0][n]=c.trim(),o[1][n]=u}for(n=0;n<l.length;++n){var m=o.push([])-1,d=l[n].getElementsByTagName("td");for(a=0;a<s.length;++a){var p=" ";void 0!==d[a]&&(p=i.subParser("makeMarkdown.tableCell")(d[a],t)),o[m].push(p)}}var h=3;for(n=0;n<o.length;++n)for(a=0;a<o[n].length;++a){var g=o[n][a].length;g>h&&(h=g)}for(n=0;n<o.length;++n){for(a=0;a<o[n].length;++a)1===n?":"===o[n][a].slice(-1)?o[n][a]=i.helper.padEnd(o[n][a].slice(-1),h-1,"-")+":":o[n][a]=i.helper.padEnd(o[n][a],h,"-"):o[n][a]=i.helper.padEnd(o[n][a],h);r+="| "+o[n].join(" | ")+" |\n"}return r.trim()})),i.subParser("makeMarkdown.tableCell",(function(e,t){"use strict";var n="";if(!e.hasChildNodes())return"";for(var a=e.childNodes,r=a.length,o=0;o<r;++o)n+=i.subParser("makeMarkdown.node")(a[o],t,!0);return n.trim()})),i.subParser("makeMarkdown.txt",(function(e){"use strict";var t=e.nodeValue;return t=(t=t.replace(/ +/g," ")).replace(/¨NBSP;/g," "),t=(t=(t=(t=(t=(t=(t=(t=(t=i.helper.unescapeHTMLEntities(t)).replace(/([*_~|`])/g,"\\$1")).replace(/^(\s*)>/g,"\\$1>")).replace(/^#/gm,"\\#")).replace(/^(\s*)([-=]{3,})(\s*)$/,"$1\\$2$3")).replace(/^( {0,3}\d+)\./gm,"$1\\.")).replace(/^( {0,3})([+-])/gm,"$1\\$2")).replace(/]([\s]*)\(/g,"\\]$1\\(")).replace(/^ {0,3}\[([\S \t]*?)]:/gm,"\\[$1]:")}));void 0===(a=function(){"use strict";return i}.call(t,n,t,e))||(e.exports=a)}).call(this)}},t={};function n(a){var r=t[a];if(void 0!==r)return r.exports;var i=t[a]={exports:{}};return e[a].call(i.exports,i,i.exports,n),i.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){"use strict";var e={};n.r(e),n.d(e,{isAutoCompleterOpen:function(){return Ba},updateAIAttributes:function(){return qa},updateData:function(){return za},updatePreviousResults:function(){return Ha}});var t={};n.r(t),n.d(t,{appUi:function(){return Ga}});var a={};n.r(a),n.d(a,{getContentAiAttributes:function(){return Ja},getData:function(){return Za},getPreviousResults:function(){return Qa},getScore:function(){return Ya},isAutoCompleterOpen:function(){return Ka}});var r=jQuery,i=n.n(r),o=lodash,s=wp.i18n,l=wp.hooks,c=wp.components,u=wp.element,m=wp.plugins,d=wp.data,p=wp.editPost,h=wp.editSite,g=function(){return!((0,o.isNil)(window.wp)||(0,o.isNil)(wp.data)||(0,o.isNil)(wp.data.select("core/editor"))||!window.document.body.classList.contains("block-editor-page")||!(0,o.isFunction)(wp.data.select("core/editor").getEditedPostAttribute))},f=function(){return!(0,o.isNull)(document.getElementById("site-editor"))},_=n(184),b=n.n(_),w=wp.compose,v=wp.apiFetch,k=n.n(v);function y(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=rankMath.links[e]||"";if(!n)return"#";if(!t)return n;var a={utm_source:"Plugin",utm_medium:encodeURIComponent(t),utm_campaign:"WP"};return n+"?"+Object.keys(a).map((function(e){return"".concat(e,"=").concat(a[e])})).join("&")}function E(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return C(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return C(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function C(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var S=function(e,t,n){e.objectID=rankMath.objectID,t(!0),k()({method:"POST",path:"/rankmath/v1/ca/researchKeyword",data:e}).catch((function(e){t(!1),alert(e.message)})).then((function(a){t(!1),n("researchedData",a.data),(0,o.isNull)(a.credits)||(0,o.isUndefined)(a.credits)||n("credits",(0,o.isNumber)(a.credits)?a.credits:0),n("keyword",e.keyword),n("country",e.country),(0,l.doAction)("rank_math_content_ai_changed",a.keyword)}))},x=function(e){var t=e.data,n=e.updateData,a=e.showError,r=e.isFree,m=e.hasCredits,d=e.loading,p=e.setLoading,h=E((0,u.useState)(t.keyword),2),g=h[0],f=h[1],_=E((0,u.useState)(t.country),2),b=_[0],w=_[1],v=g===t.keyword&&b===t.country;return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"rank-math-ca-top-section"},(0,o.includes)(["elementor","divi"],rankMath.currentEditor)&&wp.element.createElement(c.Button,{onClick:function(){return i()(".rank-math-general-tab").trigger("click")}},wp.element.createElement("i",{className:"dashicons dashicons-arrow-left-alt"}),(0,s.__)("Back","rank-math")),wp.element.createElement(c.SelectControl,{value:b,onChange:function(e){return w(e)},options:t.countries,disabled:!m||r})),wp.element.createElement("div",{className:"rank-math-ca-keywords-wrapper"},wp.element.createElement("div",{className:"rank-math-ca-credits-wrapper"},wp.element.createElement(c.TextControl,{label:(0,s.__)("Focus Keyword","rank-math"),value:g,disabled:!m||r,onChange:function(e){return f(e)},onKeyDown:function(e){"Enter"===e.key&&e.preventDefault()},help:(0,l.applyFilters)("rank_math_content_ai_help_text",wp.element.createElement(React.Fragment,null,(0,s.__)("Upgrade to buy more credits from ","rank-math"),wp.element.createElement("a",{href:y("content-ai-pricing-tables","Sidebar Upgrade Text"),rel:"noreferrer",target:"_blank",title:(0,s.__)("Content AI Pricing.","rank-math")},(0,s.__)("here.","rank-math")))),placeholder:(0,s.__)("Suggested length 2-3 Words","rank-math")}),wp.element.createElement("div",{className:"help-text"},(0,s.__)("To learn how to use it","rank-math")," ",wp.element.createElement("a",{href:y("content-ai-settings","Content AI Sidebar KB Link"),target:"_blank",rel:"noreferrer"},(0,s.__)("Click here","rank-math"))),v&&!d&&!a&&m&&!r&&!(0,o.isEmpty)(t.researchedData)&&wp.element.createElement(c.Button,{className:"rank-math-ca-force-update",onClick:function(){return S({keyword:g,country:b,forceUpdate:v},p,n)},label:(0,s.__)("Refresh will use 500 Credit.","rank-math"),showTooltip:!0},wp.element.createElement("i",{className:"dashicons dashicons-image-rotate"})),!v&&!d&&!a&&m&&!r&&wp.element.createElement(c.Button,{className:"is-primary",onClick:function(){return S({keyword:g,country:b,forceUpdate:v},p,n)},label:(0,s.__)("500 credits will be used.","rank-math"),disabled:!g,showTooltip:!0},(0,s.__)("Research","rank-math")))))},T=function(e){var t=e.score;return wp.element.createElement("div",{className:"rank-math-ca-score"},wp.element.createElement("div",{className:"score-text"},(0,s.__)("Score:","rank-math")," ",t,wp.element.createElement("span",null," / ",(0,s.__)("100","rank-math"))),wp.element.createElement("div",{className:"score-wrapper"},wp.element.createElement("span",{className:"score-dot",style:{left:((0,o.inRange)(t,0,5)?5:t)+"%"}})))};function O(e){return 100<e?"bad-fk dark":80<e?"good-fk":50<e?"ok-fk":"bad-fk"}function P(e){return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},P(e)}function A(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function N(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?A(Object(n),!0).forEach((function(t){I(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):A(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function I(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==P(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==P(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===P(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function j(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return M(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return M(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var R=(0,w.compose)((0,d.withSelect)((function(e,t){var n=1518,a=12,r=2,i=5,s=3,l=0,c=0,u=0,m=8,d=19;if(!t.showError){var p=t.researcher.getResearch("wordCount"),h=rankMathEditor.assessor.analyzer.defaultAnalyses.contentHasAssets;n=p(t.content),a=(0,o.isUndefined)(h)||(0,o.isUndefined)(t.researcher.paper)?0:h.getImages(t.researcher.paper,t.content),r=(0,o.isUndefined)(h)?0:h.getVideos(t.content),i=(t.content.match(/<h2\b[^>]*>(.*?)<\/h2>/g)||[]).length,s=(t.content.match(/<h3\b[^>]*>(.*?)<\/h3>/g)||[]).length,l=(t.content.match(/<h4\b[^>]*>(.*?)<\/h4>/g)||[]).length,c=(t.content.match(/<h5\b[^>]*>(.*?)<\/h5>/g)||[]).length,u=(t.content.match(/<h6\b[^>]*>(.*?)<\/h6>/g)||[]).length;var g=t.researcher.getResearch("getLinkStats")(t.content);m=g.internalTotal,d=g.externalTotal}return N(N({},t),{},{postStats:{wordCount:n,images:a,videos:r,mediaCount:a+r,h2:i,h3:s,h4:l,h5:c,h6:u,headingCount:i+s+l+c+u,internal:m,external:d,linkCount:m+d}})})))((function(e){var t=j((0,u.useState)(""),2),n=t[0],a=t[1];(0,u.useEffect)((function(){(0,o.forEach)(e.recommendations,(function(e,t){if("total"!==t){var n=r(t),a=(0,o.isUndefined)(e.total)?e:e.total;i(t,n,a,!1,!0)}}))}),[e.postStats]);var r=function(t){return(0,o.has)(e.postStats,t)?e.postStats[t]:0},i=function(t,n,a,r){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(e.showError)return 0;var o=n===a?100:n/a*100;if(0===a&&n<=2&&(o=100),r)return o>100&&o<=125?100:o;var s=o>80?100:n/a*80;return o>125&&"wordCount"!==t&&(s=0),i&&e.updateAiScore(t,s),o>100&&"wordCount"===t||o>100&&o<=125?100:o};return wp.element.createElement("div",{className:"rank-math-ca-recommendations"},function e(t){var l=[],c=null!==(arguments.length>1&&void 0!==arguments[1]?arguments[1]:null);return(0,o.forEach)(t,(function(t,u){if("total"!==u){var m=(0,o.isUndefined)(t.total)?t:t.total,d=r(u),p=b()(u,{"has-children":!(0,o.isUndefined)(t.total),show:u===n}),h=i(u,d,m,c),g=(0,o.ceil)(150*m/100);l.push(wp.element.createElement("div",{key:u,className:p+" "+O(h),onClick:function(){return a(n!==u?u:"")},role:"presentation"},wp.element.createElement("h4",null,c?u:(0,o.startCase)(u)),c&&wp.element.createElement("span",null,d," / ",m),!c&&wp.element.createElement(React.Fragment,null,wp.element.createElement("strong",null,d),wp.element.createElement("span",{className:"desc"},(0,s.__)("Use","rank-math")," ",m," ",(0,s.__)("to","rank-math")," ",g)),(0,o.isObject)(t)&&e(t,u)))}})),c?wp.element.createElement("div",{className:"submenu"},l):l}(e.recommendations))}));function D(e){return D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},D(e)}var L=["tabId","onClick","children","selected"];function B(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,W(a.key),a)}}function q(e,t){return q=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},q(e,t)}function z(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=F(e);if(t){var r=F(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return function(e,t){if(t&&("object"===D(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return H(e)}(this,n)}}function H(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function F(e){return F=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},F(e)}function U(e,t,n){return(t=W(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function W(e){var t=function(e,t){if("object"!==D(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==D(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===D(t)?t:String(t)}function V(){return V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},V.apply(this,arguments)}function $(e,t){if(null==e)return{};var n,a,r=function(e,t){if(null==e)return{};var n,a,r={},i=Object.keys(e);for(a=0;a<i.length;a++)n=i[a],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}var G=function(e){var t=e.tabId,n=e.onClick,a=e.children,r=e.selected,i=$(e,L);return wp.element.createElement(c.Button,V({role:"tab",tabIndex:r?null:-1,"aria-selected":r,id:t,onClick:n},i),a)},K=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&q(e,t)}(i,e);var t,n,a,r=z(i);function i(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),U(H(e=r.apply(this,arguments)),"handleClick",(function(t){var n=e.props.onSelect,a=void 0===n?o.noop:n;e.setState({selected:t}),a(t)})),U(H(e),"onNavigate",(function(e,t){t.click()}));var t=e.props,n=t.tabs,a=t.initialTabName;return e.state={selected:a||(n.length>0?n[0].name:null)},e}return t=i,(n=[{key:"render",value:function(){var e=this,t=this.state.selected,n=this.props,a=n.activeClass,r=void 0===a?"is-active":a,i=n.className,s=n.instanceId,l=n.orientation,u=void 0===l?"horizontal":l,m=n.tabs,d=(0,o.find)(m,{name:t}),p=s+"-"+d.name,h=m.slice(4);return wp.element.createElement("div",{className:i},wp.element.createElement(c.NavigableMenu,{role:"tablist",orientation:u,onNavigate:this.onNavigate,className:"components-tab-panel__tabs "+t},m.slice(0,4).map((function(n){return wp.element.createElement(G,{className:b()("components-tab-panel__tabs-item",n.className,U({},r,n.name===t)),tabId:s+"-"+n.name,"aria-controls":s+"-"+n.name+"-view",selected:n.name===t,key:n.name,onClick:(0,o.partial)(e.handleClick,n.name)},n.title)})),h.map((function(n){return wp.element.createElement(G,{className:b()("components-tab-panel__tabs-item",n.className,U({},r,n.name===t)),tabId:s+"-"+n.name,"aria-controls":s+"-"+n.name+"-view",selected:n.name===t,key:n.name,onClick:(0,o.partial)(e.handleClick,n.name)},n.title)}))),d&&wp.element.createElement("div",{"aria-labelledby":p,role:"tabpanel",id:p+"-view",className:"components-tab-panel__tab-content"},this.props.children(d)))}}])&&B(t.prototype,n),a&&B(t,a),Object.defineProperty(t,"prototype",{writable:!1}),i}(u.Component),J=(0,w.withInstanceId)(K),Z=function(e){return e.replace(/<\/?[a-z][^>]*?>/gi,"\n")},Y=function(e){return e.replace(/<style[^>]*>([\S\s]*?)<\/style>/gim,"")},Q=function(e){return e.replace(/&nbsp;|&#160;/gi," ").replace(/\s{2,}/g," ").replace(/\s\./g,".").replace(/^\s+|\s+$/g,"")},X=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,t)},ee=function(e){return e.replace(/<!--[\s\S]*?-->/g,"")};function te(e){return e.replace(/&nbsp;|&#160;/gi," ").replace(/\s{2,}/g," ").replace(/\s\./g,".").replace(/(\r\n|\n|\r)/gm,"")}var ne={};(0,o.isUndefined)(rankMath.assessor)||(0,o.forEach)(rankMath.assessor.diacritics,(function(e,t){return ne[t]=new RegExp(e,"g")}));var ae=function(e){if((0,o.isUndefined)(e))return e;for(var t in ne)e=e.replace(ne[t],t);return e};function re(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ie(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ie(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ie(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var oe=(0,w.compose)((0,d.withDispatch)((function(e){return{toggleEditor:function(){e("rank-math").toggleSnippetEditor(!0)}}})))((function(e){var t=re((0,u.useState)("content"),2),n=t[0],a=t[1],r=re((0,u.useState)(""),2),i=r[0],l=r[1];(0,u.useEffect)((function(){p()}),[]);var m=function(t,n){var a,r=e.content;if(t=(0,o.isString)(t)?ae(t).toLowerCase():t,"heading"===n){t=t.replace(/[\\^$*+?.()|[\]{}]/g,"\\$&");var i=new RegExp("<h[2-6][^>]*>.*?"+t+".*?</h[2-6]>","g"),s=r.match(i);return(0,o.isNull)(s)?0:s.length}return"title"===n&&(r=e.title),"description"===n&&(r=e.description),(a=r,(0,o.isUndefined)(a)?"":(0,o.flow)([Z,Q])(a)).split(t).length-1},d=function(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e.showError)return i===t.keyword?wp.element.createElement("span",{className:"rank-math-tooltip-data"},(0,s.__)("Copied","rank-math")):t.competition||t.cpc||t.search_volume?wp.element.createElement("span",{className:"rank-math-tooltip-data"},wp.element.createElement("span",null,(0,s.__)("Ad Competition:","rank-math")," ",(0,o.round)(100*t.competition)),wp.element.createElement("span",null,(0,s.__)("CPC:","rank-math")," $",(0,o.round)(t.cpc,2)),wp.element.createElement("span",null,(0,s.__)("Volume:","rank-math")," ",(0,o.round)(t.search_volume))):wp.element.createElement("span",{className:"rank-math-tooltip-data"},n?(0,s.__)("Click to copy keyword","rank-math"):(0,s.__)("No data available","rank-math"))},p=function(){"function"!=typeof ClipboardJS||e.showError||new ClipboardJS(".rank-math-tooltip strong",{text:function(e){return e.getAttribute("data-key")}}).on("success",(function(){setTimeout((function(){l("")}),3e3)}))};return wp.element.createElement(u.Fragment,null,wp.element.createElement(c.PanelBody,{initialOpen:!0},wp.element.createElement(c.SelectControl,{label:(0,s.__)("Use Keyword in","rank-math"),value:n,options:[{value:"content",label:(0,s.__)("Content","rank-math")},{value:"heading",label:(0,s.__)("Headings","rank-math")},{value:"title",label:(0,s.__)("SEO Title","rank-math")},{value:"description",label:(0,s.__)("SEO Description","rank-math")}],onChange:function(e){return a(e)}}),wp.element.createElement(c.Button,{className:"is-link",href:y("content-ai-keywords","Sidebar Keywords KB Icon"),rel:"noreferrer",target:"_blank",id:"rank-math-help-icon",label:(0,s.__)("Know more about Keywords.","rank-math"),showTooltip:!0},"﹖"),wp.element.createElement(React.Fragment,null,wp.element.createElement("span",{className:"components-form-token-field__help"},(0,s.__)("Click on any keyword to copy it.","rank-math")),wp.element.createElement("br",null),(0,o.includes)(["title","description"],n)&&wp.element.createElement("span",{className:"components-form-token-field__help"},(0,s.__)("Please use only one or two keywords from here.","rank-math")),wp.element.createElement("ul",null,function(){if((0,o.isEmpty)(e.researchedData.keywords))return wp.element.createElement("h3",{className:"no-data"},(0,s.__)("There are no recommended Keywords for this researched keyword.","rank-math"));var t=[],a={};return(0,o.forEach)(e.researchedData.keywords,(function(r,i){(0,o.isEmpty)(r)||(a[i]={},(0,o.forEach)(r,(function(r){if(!(0,o.isEmpty)(r.keyword)){var s=e.showError?r.count:m(r.keyword,i),c=O(function(e,t,n,a,r){var i=t/n*100;return r[a][e]=i>100?0:i>80?100:i,i}(r.keyword,s,r.average,i,a)),u=b()("rank-math-tooltip",{show:i===n});t.push(wp.element.createElement("li",{className:u+" "+c,onClick:function(){return l(r.keyword)},role:"presentation"},wp.element.createElement("strong",{"data-key":r.keyword},r.keyword,wp.element.createElement("span",null,s," / ",r.average)),d(r)))}})))})),function(t){var n=0,a=0;(0,o.forEach)(t,(function(e,t){var r=Object.values(e),i=(0,o.sum)(r);"title"!==t&&"description"!==t||100!==(0,o.max)(r)||(i=100*r.length),n+=i,a+=r.length})),n/=a,(0,o.isNaN)(n)||e.updateAiScore("keywords",n)}(a),t}())),function(){if("content"!==n||(0,o.isEmpty)(e.researchedData.related_keywords))return!1;var t=[];return(0,o.forEach)(e.researchedData.related_keywords,(function(e){t.push(wp.element.createElement("li",{className:"rank-math-tooltip show",onClick:function(){return l(e)},role:"presentation"},wp.element.createElement("strong",{"data-key":e},e),d({keyword:e},!0)))})),wp.element.createElement("div",{className:"rank-math-related-keywords"},wp.element.createElement("h3",null,(0,s.__)("Related Keywords","rank-math")),wp.element.createElement("ul",null,t))}()))}));function se(e){return se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},se(e)}function le(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,(r=a.key,i=void 0,i=function(e,t){if("object"!==se(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==se(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===se(i)?i:String(i)),a)}var r,i}function ce(e,t){return ce=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ce(e,t)}function ue(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=de(e);if(t){var r=de(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return function(e,t){if(t&&("object"===se(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return me(e)}(this,n)}}function me(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function de(e){return de=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},de(e)}var pe=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ce(e,t)}(i,e);var t,n,a,r=ue(i);function i(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(e=r.apply(this,arguments)).state={iconClass:"rm-icon-copy",selected:""},e.setState=e.setState.bind(me(e)),e.initializeClipboard(e.setState),e}return t=i,(n=[{key:"initializeClipboard",value:function(e){var t=this;"function"==typeof ClipboardJS&&new ClipboardJS(".rank-math-copy-questions, .rank-math-questions-item h3",{text:function(e){if(!(0,o.isNull)(e.getAttribute("data-key")))return e.getAttribute("data-key");if(!g())return document.getElementById("rank-math-ca-questions-data").innerHTML;var n=[];return(0,o.forEach)(t.props.researchedData.related_questions,(function(e){n.push({id:(0,o.uniqueId)("faq-question-"),title:e,content:"",visible:!0})})),"\x3c!-- wp:rank-math/faq-block "+JSON.stringify({questions:n})+' --\x3e<div class="wp-block-rank-math-faq-block"></div>\x3c!-- /wp:rank-math/faq-block --\x3e'}}).on("success",(function(){setTimeout((function(){e({iconClass:"rm-icon-copy"})}),3e3)}))}},{key:"render",value:function(){var e=this,t=[];return(0,o.isEmpty)(this.props.researchedData.related_questions)?wp.element.createElement("h3",{className:"no-data"},(0,s.__)("There are no recommended Questions for this researched keyword.","rank-math")):((0,o.forEach)(this.props.researchedData.related_questions,(function(n,a){t.push(wp.element.createElement("div",{className:"rank-math-questions-item",key:a},wp.element.createElement("h3",{className:"rank-math-tooltip","data-key":n,onClick:function(){return e.setState({selected:n})},role:"presentation"},n,e.getTooltipContent(n))))})),wp.element.createElement(u.Fragment,null,wp.element.createElement(c.PanelBody,{initialOpen:!0},wp.element.createElement("div",{className:"rank-math-section-heading"},wp.element.createElement("h2",null,(0,s.__)("Related Questions","rank-math"),wp.element.createElement("a",{href:y("content-ai-settings","Sidebar Questions KB Icon"),rel:"noreferrer",target:"_blank",id:"rank-math-help-icon",title:(0,s.__)("Know more about Questions.","rank-math")},"﹖")),wp.element.createElement(c.Button,{onClick:function(){e.setState({iconClass:"rm-icon-tick"})},className:"rank-math-copy-questions button-secondary rank-math-tooltip left"},wp.element.createElement("i",{className:"rm-icon "+this.state.iconClass}),wp.element.createElement("span",null,(0,s.__)("Copy this data as a FAQ Block.","rank-math")))),wp.element.createElement("span",{className:"components-form-token-field__help"},(0,s.__)("Click on any question to copy it.","rank-math")),wp.element.createElement("div",{id:"rank-math-ca-questions-data"},t))))}},{key:"getTooltipContent",value:function(e){return this.state.selected===e&&wp.element.createElement("span",{className:"rank-math-tooltip-data"},(0,s.__)("Copied","rank-math"))}}])&&le(t.prototype,n),a&&le(t,a),Object.defineProperty(t,"prototype",{writable:!1}),i}(u.Component),he=pe,ge=function(e){var t=[];return(0,o.isEmpty)(e.researchedData.links)?wp.element.createElement("h3",{className:"no-data"},(0,s.__)("There are no recommended Links for this researched keyword.","rank-math")):((0,o.forEach)(e.researchedData.links,(function(e,n){return t.push(wp.element.createElement("li",{key:n},wp.element.createElement("a",{href:e,rel:"noreferrer",target:"_blank"},e)))})),wp.element.createElement(u.Fragment,null,wp.element.createElement(c.PanelBody,{initialOpen:!0},wp.element.createElement("div",{className:"rank-math-section-heading"},wp.element.createElement("h2",null,(0,s.__)("Related External Links","rank-math"),wp.element.createElement(c.Button,{className:"is-link",href:y("content-ai-links","Sidebar Links KB Icon"),rel:"noreferrer",target:"_blank",id:"rank-math-help-icon",label:(0,s.__)("Know more about Links.","rank-math"),showTooltip:!0},"﹖")),wp.element.createElement("p",null,(0,s.__)("Use some of these external links in the content area. It is recommended to add","rank-math")," ",wp.element.createElement("a",{href:y("about-and-mentions-schema","Use Some External Links"),rel:"noreferrer",target:"_blank"},(0,s.__)("about or mention Schema.","rank-math")))),wp.element.createElement("ul",null,t))))},fe=function(e){return wp.element.createElement(J,{className:"rank-math-contentai-tabs",activeClass:"is-active",tabs:[{name:"keywords",title:wp.element.createElement(u.Fragment,null,wp.element.createElement("span",null,(0,s.__)("Keywords","rank-math"))),view:oe,className:"rank-math-keywords-tab"},{name:"questions",title:wp.element.createElement(u.Fragment,null,wp.element.createElement("span",null,(0,s.__)("Questions","rank-math"))),view:he,className:"rank-math-questions-tab"},{name:"links",title:wp.element.createElement(u.Fragment,null,wp.element.createElement("span",null,(0,s.__)("Links","rank-math"))),view:ge,className:"rank-math-recommended-links-tab"}],data:e},(function(t,n){return wp.element.createElement("div",{className:"rank-math-contentai-tab-content-"+t.name,key:n},(0,u.createElement)(t.view,e))}))};function _e(e){return _e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_e(e)}function be(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function we(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?be(Object(n),!0).forEach((function(t){ve(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):be(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ve(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==_e(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==_e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===_e(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ke(){return ke=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ke.apply(this,arguments)}var ye=(0,w.compose)((0,d.withSelect)((function(e,t){var n=rankMathEditor.assessor.analyzer.researcher,a=n.paper,r=e("rank-math").getKeywords().split(",")[0];return we(we({},t),{},{score:e("rank-math-content-ai").getScore(),researcher:n,keyword:(0,o.isEmpty)(t.data.keyword)?r:t.data.keyword,content:(0,o.isUndefined)(a)?"":a.getTextLower(),title:(0,o.isUndefined)(a)?"":a.getTitle().toLowerCase(),description:(0,o.isUndefined)(a)?"":a.getDescription().toLowerCase(),hasThumbnail:(0,o.isUndefined)(a)?"":a.hasThumbnail()})})),(0,d.withDispatch)((function(e,t){return{updateAiScore:function(n,a){var r=t.data.score;r[n]=(0,o.round)(a,2),e("rank-math").updateAIScore(r)}}})))((function(e){var t=e.researchedData||{},n=b()("rank-math-content-ai-data",{loading:e.loading,blurred:e.showError}),a=t.recommendations?(0,o.pick)(t.recommendations,e.data.enabledTests):{};return wp.element.createElement("div",{className:n},wp.element.createElement("span",{className:"loader-text"},wp.element.createElement("span",null,(0,s.__)("Fetching Search Results","rank-math")),wp.element.createElement("span",null,(0,s.__)("Analysing Your Competitors","rank-math")),wp.element.createElement("span",null,(0,s.__)("Crunching the Numbers","rank-math")),wp.element.createElement("span",null,(0,s.__)("Cooking a Personalized SEO Plan","rank-math")),wp.element.createElement("span",null,(0,s.__)("Final Touches to the SEO Recommendations","rank-math"))),(0,o.isString)(t)&&wp.element.createElement("h3",{className:"no-data",dangerouslySetInnerHTML:{__html:t}}),!(0,o.isEmpty)(t)&&!(0,o.isString)(t)&&wp.element.createElement("div",null,wp.element.createElement("h3",{className:"rank-math-ca-section-title"},(0,s.__)("Content AI","rank-math"),wp.element.createElement("span",null,(0,s.__)("New!","rank-math")),wp.element.createElement(c.Button,{className:"is-link",href:y("content-ai-settings","Sidebar KB Icon"),rel:"noreferrer",target:"_blank",id:"rank-math-help-icon",label:(0,s.__)("Content AI Knowledge Base.","rank-math"),showTooltip:!0},"﹖")),wp.element.createElement(T,{score:e.score}),wp.element.createElement(R,ke({},e,{recommendations:a})),wp.element.createElement(fe,ke({},e,{researchedData:t}))))})),Ee=React;function Ce(e){return e.startsWith("{{/")?{type:"componentClose",value:e.replace(/\W/g,"")}:e.endsWith("/}}")?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.startsWith("{{")?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}function Se(e,t){let n,a,r=[];for(let i=0;i<e.length;i++){const o=e[i];if("string"!==o.type){if(void 0===t[o.value])throw new Error(`Invalid interpolation, missing component node: \`${o.value}\``);if("object"!=typeof t[o.value])throw new Error(`Invalid interpolation, component node must be a ReactElement or null: \`${o.value}\``);if("componentClose"===o.type)throw new Error(`Missing opening component token: \`${o.value}\``);if("componentOpen"===o.type){n=t[o.value],a=i;break}r.push(t[o.value])}else r.push(o.value)}if(n){const i=function(e,t){const n=t[e];let a=0;for(let r=e+1;r<t.length;r++){const e=t[r];if(e.value===n.value){if("componentOpen"===e.type){a++;continue}if("componentClose"===e.type){if(0===a)return r;a--}}}throw new Error("Missing closing component token `"+n.value+"`")}(a,e),o=Se(e.slice(a+1,i),t),s=(0,Ee.cloneElement)(n,{},o);if(r.push(s),i<e.length-1){const n=Se(e.slice(i+1),t);r=r.concat(n)}}return r=r.filter(Boolean),0===r.length?null:1===r.length?r[0]:(0,Ee.createElement)(Ee.Fragment,null,...r)}function xe(e){const{mixedString:t,components:n,throwErrors:a}=e;if(!n)return t;if("object"!=typeof n){if(a)throw new Error(`Interpolation Error: unable to process \`${t}\` because components is not an object`);return t}const r=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(Ce)}(t);try{return Se(r,n)}catch(e){if(a)throw new Error(`Interpolation Error: unable to process \`${t}\` because of error \`${e.message}\``);return t}}var Te=function(e){var t=e.tags,n=e.components,a=e.children;return n=n||{},!1===(0,o.isUndefined)(t)&&(t=t.split(",")).forEach((function(e){var t=e;n[e]=wp.element.createElement(t,null)})),xe({mixedString:a,components:n})},Oe=function(e,t){arguments.length>2&&void 0!==arguments[2]&&arguments[2]&&(t+="+Free+Plan");var n=new URLSearchParams({utm_source:"Plugin",utm_medium:t,utm_campaign:"WP"});return(0,o.includes)(e,"?")||(e+="?"),e+n.toString()},Pe=function(e){var t=e.width,n=void 0===t?40:t,a=e.showProNotice,r=void 0!==a&&a,i=e.isBulkEdit,l=void 0!==i&&i,u=e.isResearch,m=void 0!==u&&u,d=e.creditsRequired,p=void 0===d?0:d,h=e.isKeywordIntent,g=void 0!==h&&h;if(r)return function(e){return wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks top-20 less-padding "+e},wp.element.createElement("h3",null,(0,s.__)("🔒 This is a PRO-Only Feature","rank-math")),wp.element.createElement("p",null,(0,s.__)("We are sorry but this feature is only available to Rank Math PRO/Business/Agency Users. Unlock this feature and many more by getting a Rank Math plan.","rank-math")),wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,s.__)("Bulk Edit SEO Tags","rank-math")),wp.element.createElement("li",null,(0,s.__)("Advanced Google Analytics 4 Integration","rank-math")),wp.element.createElement("li",null,(0,s.__)("Keyword Rank Tracker","rank-math")),wp.element.createElement("li",null,(0,s.__)("Free Content AI Trial","rank-math")),wp.element.createElement("li",null,(0,s.__)("SEO Performance Email Reports","rank-math"))),wp.element.createElement(c.Button,{href:rankMath.links.pro,target:"_blank",className:"button button-primary is-green"},(0,s.__)("Learn More","rank-math"))))}(n);if(g)return function(e){return wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks top-20 less-padding "+e},wp.element.createElement("h3",null,(0,s.__)("⛔️ Update Required","rank-math")),wp.element.createElement("p",null,(0,s.__)("Your current plugin version does not support this feature. Please update Rank Math PRO to version 3.0.83 or later to unlock full functionality.","rank-math")),wp.element.createElement(c.Button,{href:rankMath.links.pro,target:"_blank",className:"button button-primary is-green"},(0,s.__)("Update Now","rank-math"))))}(n);var f=(0,o.isUndefined)(wp.data.select("rank-math-content-ai"))?rankMath.contentAI:wp.data.select("rank-math-content-ai").getData(),_=f.isUserRegistered,b=f.plan,w="free"===b,v=f.credits>p,k=f.isMigrating;if(v&&m&&!w&&f.credits<500&&(v=!1),_&&b&&v&&!k&&!w)return null;var y,E="width-"+n;return!_||!b||v&&w?wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks top-20 less-padding "+E},wp.element.createElement("h3",null,(0,s.__)("🚀 Supercharge Your Content With AI","rank-math")),wp.element.createElement("p",null,!_&&!l&&(0,s.__)("Start using Content AI by connecting your RankMath.com Account","rank-math"),_&&!b&&!l&&!w&&(0,s.__)("To access this Content AI feature, you need to have an active subscription plan.","rank-math"),_&&!l&&w&&(0,s.__)("To access this Content AI feature, you have to purchase a Content AI Subscription.","rank-math"),l&&(0,s.__)("You are one step away from unlocking this premium feature along with many more.","rank-math")),function(e,t,n){return t?wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,s.__)("1-Click Article Generation","rank-math")),wp.element.createElement("li",null,(0,s.__)("Bulk Update Your SEO Meta using AI","rank-math")),wp.element.createElement("li",null,(0,s.__)("Get Access to 40+ AI SEO Tools","rank-math")),wp.element.createElement("li",null,(0,s.__)("125+ Expert-Written Prompts","rank-math")),wp.element.createElement("li",null,(0,s.__)("1-Click Competitor Content Research","rank-math")),wp.element.createElement("li",null,(0,s.__)("1-Click WooCommerce Product Descriptions","rank-math"))):n?wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,s.__)("1-Click Article Generation","rank-math")),wp.element.createElement("li",null,(0,s.__)("1-Click Competitor Research","rank-math")),wp.element.createElement("li",null,(0,s.__)("On-Page SEO Suggestions","rank-math")),wp.element.createElement("li",null,(0,s.__)("1-Click Bulk SEO Meta","rank-math")),wp.element.createElement("li",null,(0,s.__)("125+ Pre-Built Prompts","rank-math")),wp.element.createElement("li",null,(0,s.__)("Multiple RankBot Sessions","rank-math"))):40===e?wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,s.__)("1-Click Article Generation","rank-math")),wp.element.createElement("li",null,(0,s.__)("1-Click SEO Content","rank-math")),wp.element.createElement("li",null,(0,s.__)("1-Click SEO Meta","rank-math")),wp.element.createElement("li",null,(0,s.__)("40+ Specialized AI Tools","rank-math")),wp.element.createElement("li",null,(0,s.__)("1-Click Competitor Research","rank-math")),wp.element.createElement("li",null,(0,s.__)("125+ Pre-Built Prompts","rank-math"))):wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,s.__)("Gain access to 40+ advanced AI tools, empowering your content strategy.","rank-math")),wp.element.createElement("li",null,(0,s.__)("Experience the revolutionary AI-powered Content Editor for unparalleled efficiency.","rank-math")),wp.element.createElement("li",null,(0,s.__)("Engage with RankBot, your personal AI Chat Assistant, for real-time assistance.","rank-math")))}(n,l,w),!_&&wp.element.createElement(c.Button,{href:rankMath.contentAI.connectSiteUrl,className:"button button-primary is-green"},(0,s.__)("Connect Now","rank-math")),_&&(!b||w)&&wp.element.createElement(c.Button,{href:Oe(rankMath.links["content-ai"]+"?play-video=ioPeVIntJWw&","Buy+Plan+Button",w),className:"button button-primary is-green",target:"_blank"},(0,s.__)("Learn More","rank-math")))):k?wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{style:{textAlign:"center"},className:"rank-math-cta-box less-padding top-20 "+E},wp.element.createElement("h3",null,(0,s.__)("Server Maintenance Underway","rank-math")),wp.element.createElement("p",null,(0,s.__)("We are working on improving your Content AI experience. Please wait for 5 minutes and then refresh to start using the optimized Content AI. If you see this for more than 5 minutes, please ","rank-math"),wp.element.createElement("a",{href:rankMath.links.support,target:"_blank",rel:"noreferrer"},(0,s.__)("reach out to the support team.","rank-math")),(0,s.__)(" We are sorry for the inconvenience.","rank-math")))):wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box less-padding top-20 "+E},wp.element.createElement("h3",null,(0,s.__)("⛔️ Content AI Credit Alert!","rank-math")),wp.element.createElement("p",null,(y=f.resetDate)?wp.element.createElement(Te,{components:{strong:wp.element.createElement("strong",null)}},(0,s.sprintf)((0,s.__)("Your monthly Content AI credits have been fully utilized. You can wait till %s for your credits to refresh or upgrade to continue enjoying seamless content creation","rank-math"),"{{strong}}"+y+"{{/strong}}")):(0,s.__)("Your monthly Content AI credits have been fully utilized. To continue enjoying seamless content creation, simply click the button below to upgrade your plan and access more credits.","rank-math")),wp.element.createElement(c.Button,{href:Oe(rankMath.links["content-ai"]+"?play-video=ioPeVIntJWw&","Buy+Credits+Button",w),className:"button button-primary is-green",target:"_blank"},(0,s.__)("Learn More","rank-math")),wp.element.createElement(c.Button,{variant:"link",href:Oe(rankMath.links["content-ai-restore-credits"],"Buy+Credits+Button",w),className:"button button-secondary",target:"_blank"},(0,s.__)("Missing Credits?","rank-math"))))};function Ae(){return Ae=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ae.apply(this,arguments)}function Ne(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Ie(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ie(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ie(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var je=function(e){var t=Ne((0,u.useState)(!1),2),n=t[0],a=t[1],r=e.data,i=e.updateData,s=r.researchedData,l=(0,o.isEmpty)(r.plan)||"free"===r.plan,m=r.isUserRegistered&&r.credits>=500,d=(0,o.isEmpty)(s)&&(!m||l);return d&&(s={keywords:{content:{"rank math":{keyword:"rank math",average:17,count:12},"rank math vs yoast seo":{keyword:"rank math vs yoast seo",average:1,count:1},"what is rank math":{keyword:"what is rank math",average:1,count:1},"rank math schema":{keyword:"rank math schema",average:1,count:1},"rank math configuration":{keyword:"rank math configuration",average:1,count:1},"rank math pro version":{keyword:"rank math pro version",average:1,count:2},"rank math comparison":{keyword:"rank math comparison",average:1,count:1},"rank math for seo":{keyword:"rank math for seo",average:1,count:1},"seo by rank math":{keyword:"seo by rank math",average:1,count:0}}},related_keywords:["rank math plugin","rank math pricing","rank math vs yoast","rank math review","rank math premium","how to use rank math","rank math training","rank math woocommerce","wordpress seo plugin"],recommendations:{wordCount:1829,linkCount:{total:16},headingCount:{total:9},mediaCount:{total:18}}}),wp.element.createElement(u.Fragment,null,wp.element.createElement(c.PanelBody,{className:"rank-math-content-ai-wrapper research",initialOpen:!0},wp.element.createElement(x,{data:r,updateData:i,hasCredits:m,isFree:l,loading:n,setLoading:a,showError:d}),wp.element.createElement(ye,Ae({},e,{researchedData:s,loading:n,showError:d})),d&&wp.element.createElement(Pe,{isResearch:!0})))},Me=wp.blocks;var Re=(0,s.__)("Sorry, the request has failed. If the issue persists, please contact our Support for assistance.","rank-math"),De=function(e){if(e.input_truncated){var t=(0,s.__)("AI Fix was applied only considering the beginning of the content, as the full content is too large to be processed by the AI.","rank-math");if(g()){(0,d.dispatch)("core/notices").createWarningNotice(t,{id:"aiInputTruncated"})}else{var n=i()(".wp-header-end").length?i()(".wp-header-end"):i()(".rank-math-header");n.length&&function(e,t,n,a){t=t||"error",a=a||!1;var r=i()('<div class="notice notice-'+t+' is-dismissible"><p>'+e+"</p></div>").hide();n.next(".notice").remove(),n.after(r),r.slideDown(),i()(document).trigger("wp-updates-notice-added"),i()("html,body").animate({scrollTop:r.offset().top-50},"slow"),a&&setTimeout((function(){r.fadeOut((function(){r.remove()}))}),a)}(t,"warning",n)}}},Le=function e(t){var n=t.endpoint,a=t.attributes,r=t.callback,l=t.isChat,c=t.setCredits,u=t.repeat,m=void 0===u?0:u,p=t.data,h=void 0===p?{}:p;i().ajax({url:h.url+n,type:"POST",data:a,success:function(t){if((0,o.isEmpty)(t.error))if("default_prompts"!==n){De(t),function(e){var t=e.result,r={endpoint:n,attributes:a,outputs:(0,o.isEmpty)(t.meta)?t.results:t.meta,isChat:l};(0,o.isUndefined)(t.credits)||(r.credits={credits:t.credits,plan:t.plan,refreshDate:t.refreshDate}),k()({method:"POST",path:"/rankmath/v1/ca/saveOutput",data:r}).then((function(e){l||(0,o.isUndefined)(h.history)||(0,d.dispatch)("rank-math-content-ai").updateData("history",e)})).catch((function(e){console.log(e)}))}({result:t});var s=(0,o.isEmpty)(t.meta)?t.results:t.meta;if(!(0,o.isEmpty)(t.warning)){var u=h.errors;s.push({warning:(0,o.isUndefined)(u[t.warning])?Re:u[t.warning]})}r(s),function(e,t){if(!(0,o.isUndefined)(e.credits)){var n=e.credits;(0,o.isEmpty)(n)||(n=(n=n.available-n.taken)<0?0:n,t&&t(n),(0,d.dispatch)("rank-math-content-ai").updateData("credits",n),i()(".credits-remaining").length&&i()(".credits-remaining strong").text(n))}}(t,c)}else r(t);else!function(t){var n=t.error;if(m<2&&"could_not_generate"===n.code)e({repeat:m+1});else{var a=h.errors;r({error:(0,o.isUndefined)(a[n.code])?Re:a[n.code]})}}({error:t.error})},error:function(e){try{var t=JSON.parse(e.responseText);if(!(0,o.isEmpty)(t.err_key)){var n=h.errors,a=(0,o.isUndefined)(t.message)?Re:t.message;return void r({error:(0,o.isUndefined)(n[t.err_key])?a:n[t.err_key]})}}catch(t){413===e.status&&(Re=(0,s.__)("Error: The request payload is too large!","rank-math")),r({error:Re})}r({error:Re})}})},Be=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",i=(0,d.select)("rank-math-content-ai").getData(),s=i.connectData;t=(0,o.merge)(t,{username:s.username,api_key:s.api_key,site_url:s.site_url,plugin_version:rankMath.version}),i.credits?(!(0,o.isUndefined)(t.language)&&t.language||(t.language=i.language),Le({endpoint:e,attributes:t,callback:n,isChat:a,setCredits:r,data:i})):n({error:i.errors.account_limit_reached})},qe=n(634),ze=n.n(qe);function He(e){return He="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},He(e)}function Fe(){return Fe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Fe.apply(this,arguments)}function Ue(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,(r=a.key,i=void 0,i=function(e,t){if("object"!==He(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==He(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===He(i)?i:String(i)),a)}var r,i}function We(e,t){return We=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},We(e,t)}function Ve(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=Ke(e);if(t){var r=Ke(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return function(e,t){if(t&&("object"===He(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ge(e)}(this,n)}}function Ge(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ke(e){return Ke=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ke(e)}var Je=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&We(e,t)}(i,e);var t,n,a,r=Ve(i);function i(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(t=r.call(this,e))._handleRef=t._handleRef.bind(Ge(t)),t}return t=i,(n=[{key:"componentDidMount",value:function(){var e=this;this.tagify=new(ze())(this.component,this.props.settings||{}),this.props.settings.callbacks.setup&&setTimeout((function(){e.props.settings.callbacks.setup.call(e.tagify),e.tagify.DOM.input.setAttribute("contenteditable",!0),e.tagify.DOM.input.addEventListener("blur",e.props.settings.callbacks.blur)}),100),this.props.settings.callbacks.dragEnd&&this.tagify.DOM.scope.addEventListener("dragend",this.props.settings.callbacks.dragEnd),(0,l.doAction)("rank_math_tagify_init",this)}},{key:"shouldComponentUpdate",value:function(e){return this.tagify.settings.whitelist=e.settings.whitelist,e.showDropdown&&this.tagify.dropdown.show.call(this.tagify,e.showDropdown),!1===e.showDropdown&&this.tagify.dropdown.hide.call(this.tagify,!0),!1}},{key:"_handleRef",value:function(e){this.component=e}},{key:"render",value:function(){var e={ref:this._handleRef,id:this.props.id,name:this.props.name,className:this.props.className,placeholder:this.props.placeholder};return"textarea"===this.props.mode?wp.element.createElement("textarea",Fe({},e,{defaultValue:this.props.initialValue})):wp.element.createElement("input",Fe({},e,{defaultValue:this.props.initialValue}))}},{key:"toArray",value:function(){return this.tagify.value.map((function(e){return e.value}))}},{key:"toString",value:function(){return this.toArray().join(",")}},{key:"queryTags",value:function(){return this.tagify.DOM.scope.querySelectorAll("tag")}}])&&Ue(t.prototype,n),a&&Ue(t,a),Object.defineProperty(t,"prototype",{writable:!1}),i}(u.Component),Ze=function(e){if("[object Object]"!==Object.prototype.toString.call(e))return"";var t,n,a,r="",o=Object.keys(e);for(n=o.length;n--;)"class"!==(t=o[n])&&e.hasOwnProperty(t)&&e[t]&&(r+=""+t+(e[t]?'="'.concat((a=e[t],a=i()("<textarea />").html(a).text(),((new DOMParser).parseFromString(a,"text/html").body.textContent||"").replace(/["<>]/g,"")||""),'"'):""));return r},Ye=function(e){return{topic_brief:{label:(0,s.__)("Topic Brief","rank-math"),placeholder:(0,s.__)("Enter a short summary of your topic","rank-math"),type:"textarea",maxlength:"400"},audience:{label:(0,s.__)("Audience","rank-math"),tooltip:(0,s.__)("The target audience for the content.","rank-math"),placeholder:(0,s.__)("Select or Write Custom","rank-math"),options:[{value:"General Audience",icon:"🌏"},{value:"Consumers",icon:"🛍"},{value:"Students",icon:"📚"},{value:"Professionals",icon:"💼"},{value:"Business Owners",icon:"🏭"},{value:"Job Seekers",icon:"🔍"},{value:"Investors",icon:"💰"},{value:"Entrepreneurs",icon:"🚀"},{value:"Social Media Users",icon:"📱"},{value:"Travelers",icon:"🛫"},{value:"Pet Owners",icon:"🐾"},{value:"Seniors",icon:"🧓"},{value:"Gaming Enthusiasts",icon:"🎮"},{value:"Environmentalists",icon:"🌍"},{value:"Sports Fans",icon:"⚽️"},{value:"Health Enthusiasts",icon:"💊"},{value:"Tech Enthusiasts",icon:"💻"},{value:"Parents",icon:"👨‍👧‍👦"},{value:"Artists",icon:"🎨"},{value:"Musicians",icon:"🎸"},{value:"Photographers",icon:"📷"},{value:"Writers",icon:"✍️"},{value:"Retirees",icon:"👴"},{value:"Healthcare Professionals",icon:"👩‍⚕️"},{value:"Educators",icon:"👩‍🏫"},{value:"Activists",icon:"👩‍⚖️"},{value:"Foodies",icon:"🍕"},{value:"Cooks",icon:"👩‍🍳"},{value:"Fitness Enthusiasts",icon:"🏋️‍♀️"},{value:"Bargain Hunters",icon:"🛍"},{value:"Fashionistas",icon:"👗"},{value:"Outdoor Enthusiasts",icon:"🏕"},{value:"Indoor Hobbyists",icon:"🎨"},{value:"Gardeners",icon:"🌱"},{value:"DIYers",icon:"🔧"},{value:"Crafters",icon:"🧶"},{value:"Collectors",icon:"📚"},{value:"Dancers",icon:"💃"},{value:"Gamers",icon:"🎮"},{value:"Movie Buffs",icon:"🎥"},{value:"TV Enthusiasts",icon:"📺"},{value:"Video Creators",icon:"🎥"},{value:"Engineers",icon:"🔧"},{value:"Designers",icon:"🎨"},{value:"Podcast Listeners",icon:"🎧"},{value:"Bloggers",icon:"📝"},{value:"Authors",icon:"📚"}],default:rankMath.contentAI.audience,maxlength:"200"},tone:{label:(0,s.__)("Tone","rank-math"),tooltip:(0,s.__)("The tone of the content.","rank-math"),placeholder:(0,s.__)("Select or Write Custom","rank-math"),options:[{value:"Formal",icon:"🤵"},{value:"Informal",icon:"🤗"},{value:"Friendly",icon:"😊"},{value:"Casual",icon:"💁‍♀️"},{value:"Conversational",icon:"🗣️"},{value:"Descriptive",icon:"📚"},{value:"Persuasive",icon:"🤝"},{value:"Creative",icon:"🎨"},{value:"Technical",icon:"🔧"},{value:"Analytical",icon:"📊"},{value:"Journalese",icon:"📰"},{value:"Poetic",icon:"🌺"},{value:"Factual",icon:"📊"},{value:"Emotional",icon:"💔"},{value:"Satirical",icon:"😅"},{value:"Empathetic",icon:"😔"},{value:"Opinionated",icon:"💬"},{value:"Humorous",icon:"😂"},{value:"Story-telling",icon:"📚"},{value:"Narrative",icon:"📖"},{value:"Expository",icon:"📚"},{value:"Argumentative",icon:"🗣️"},{value:"Objective",icon:"📊"},{value:"Subjective",icon:"💬"}],default:rankMath.contentAI.tone,maxlength:"200"},style:{label:(0,s.__)("Style","rank-math"),tooltip:(0,s.__)("The style of the content.","rank-math"),help_link:"https://rankmath.com/kb/blog-post-idea/?utm_source=Plugin&utm_medium=AI+Tool+Style&utm_campaign=WP#style",placeholder:(0,s.__)("Select or Write Custom","rank-math"),options:[{value:"Listicle",icon:"🔢"},{value:"Tutorial",icon:"📖"},{value:"Review",icon:"⭐️"},{value:"Case Study",icon:"🕵️‍♂️"},{value:"Opinion",icon:"🗣️"},{value:"News",icon:"📰"},{value:"Newsjacking",icon:"🗞"},{value:"Personal",icon:"💬"},{value:"Story-telling",icon:"📚"},{value:"Guide",icon:"🗺️"},{value:"Research-based",icon:"🔬"},{value:"Interview",icon:"🎤"},{value:"Infographic",icon:"📊"},{value:"Debate",icon:"🤔"},{value:"Video Blog",icon:"🎥"},{value:"Vlog",icon:"📹"},{value:"Podcast",icon:"🎧"},{value:"Audio Blog",icon:"🎙"},{value:"Quiz",icon:"🎲"},{value:"Contest",icon:"🎉"},{value:"Poll",icon:"📊"},{value:"Comparison",icon:"🔎"},{value:"How-to",icon:"📖"},{value:"FAQ",icon:"❓"}],maxlength:"200"},language:{label:(0,s.__)("Output Language","rank-math"),placeholder:(0,s.__)("","rank-math"),type:"select",options:[{value:"US English",icon:"🇺🇸"},{value:"UK English",icon:"🇬🇧"},{value:"Arabic",icon:"🇦🇪"},{value:"Bulgarian",icon:"🇧🇬"},{value:"Chinese",icon:"🇨🇳"},{value:"Czech",icon:"🇨🇿"},{value:"Danish",icon:"🇩🇰"},{value:"Dutch",icon:"🇳🇱"},{value:"Estonian",icon:"🇪🇪"},{value:"Finnish",icon:"🇫🇮"},{value:"French",icon:"🇫🇷"},{value:"German",icon:"🇩🇪"},{value:"Greek",icon:"🇬🇷"},{value:"Hebrew",icon:"🇮🇱"},{value:"Hungarian",icon:"🇭🇺"},{value:"Indonesian",icon:"🇮🇩"},{value:"Italian",icon:"🇮🇹"},{value:"Japanese",icon:"🇯🇵"},{value:"Korean",icon:"🇰🇷"},{value:"Latvian",icon:"🇱🇻"},{value:"Lithuanian",icon:"🇱🇹"},{value:"Norwegian",icon:"🇳🇴"},{value:"Polish",icon:"🇵🇱"},{value:"Portuguese",icon:"🇵🇹"},{value:"Romanian",icon:"🇷🇴"},{value:"Russian",icon:"🇷🇺"},{value:"Slovak",icon:"🇸🇰"},{value:"Slovenian",icon:"🇸🇮"},{value:"Spanish",icon:"🇪🇸"},{value:"Swedish",icon:"🇸🇪"},{value:"Turkish",icon:"🇹🇷"}],default:rankMath.contentAI.language,maxTags:1},topic:{label:(0,s.__)("Topic","rank-math"),placeholder:(0,s.__)("Enter a short summary of your topic","rank-math"),maxlength:"200"},main_points:{label:(0,s.__)("Main points &amp; ideas","rank-math"),placeholder:(0,s.__)("Enter the main points you want to cover, separated by comma","rank-math"),type:"textarea",maxlength:"400"},focus_keyword:{label:(0,s.__)("Focus Keyword","rank-math"),placeholder:(0,s.__)("Enter the main keywords to focus on","rank-math"),maxlength:"200",options:[],default:[]},title:{label:(0,s.__)("Post title","rank-math"),placeholder:(0,s.__)("Enter your post title","rank-math"),maxlength:200},main_argument:{label:(0,s.__)("Main Argument","rank-math"),placeholder:(0,s.__)("Enter the main point you want to make","rank-math"),type:"textarea",maxlength:"400"},call_to_action:{label:(0,s.__)("Call to Action","rank-math"),placeholder:(0,s.__)("Select or Write Custom","rank-math"),type:"select",options:["Subscribe to our newsletter","Follow social media accounts","Download a resource or guide","Share the blog post on social media","Comment on the blog post","Check out related resources","Sign up for a webinar or event","Contact for more information","Purchase a product or service"],maxlength:"300"},post_brief:{label:(0,s.__)("Post Brief","rank-math"),placeholder:(0,s.__)("Enter a short summary of your post","rank-math"),type:"textarea",maxlength:"400"},length:{label:(0,s.__)("Length","rank-math"),placeholder:(0,s.__)("","rank-math"),type:"button",options:[{value:"short"},{value:"medium"},{value:"long"}],maxlength:200,default:"medium"},relevance:{label:(0,s.__)("Relevance","rank-math"),placeholder:(0,s.__)("Select or Write Custom","rank-math"),options:[{value:"Recent",icon:"🗓️"},{value:"Historical",icon:"📜"},{value:"Regional",icon:"🗺️"},{value:"Comparative",icon:"⚖️"},{value:"Specific",icon:"🎯"},{value:"Longitudinal",icon:"📈"},{value:"Cross-cultural",icon:"🌍"},{value:"Theoretical",icon:"📚"},{value:"Empirical",icon:"📊"},{value:"Applied",icon:"🛠️"}],maxlength:200},format:{label:(0,s.__)("Format","rank-math"),placeholder:(0,s.__)("Select or Write desired output format","rank-math"),options:["Summary","List","Outline"],maxlength:200},post_title:{label:(0,s.__)("Post Title","rank-math"),placeholder:(0,s.__)("Enter your post title","rank-math"),maxlength:200},seo_title:{label:(0,s.__)("SEO Title","rank-math"),placeholder:(0,s.__)("Enter your SEO title","rank-math"),maxlength:200},supporting_points:{label:(0,s.__)("Supporting Points","rank-math"),placeholder:(0,s.__)("The supporting points you want to include in the paragraph","rank-math"),type:"textarea",maxlength:500},original_paragraph:{label:(0,s.__)("Original Paragraph","rank-math"),placeholder:(0,s.__)("Enter the paragraph you want to rephrase","rank-math"),type:"textarea",maxlength:1e3},sentence:{label:(0,s.__)("Sentence","rank-math"),placeholder:(0,s.__)("Enter a short or incomplete sentence","rank-math"),type:"textarea",maxlength:200},text:{label:(0,s.__)("Original Text","rank-math"),placeholder:(0,s.__)("Enter the text to summarize","rank-math"),type:"textarea",maxlength:2e3},product_name:{label:(0,s.__)("Product Name","rank-math"),placeholder:(0,s.__)("Enter the name of the product","rank-math"),maxlength:"200"},features_and_benefits:{label:(0,s.__)("Features and Benefits","rank-math"),placeholder:(0,s.__)("Enter a list of features and benefits, separated by commas","rank-math"),type:"textarea",maxlength:600},limitations_and_drawbacks:{label:(0,s.__)("Limitations and Drawbacks","rank-math"),placeholder:(0,s.__)("","rank-math"),type:"textarea",maxlength:600},reply_brief:{label:(0,s.__)("Reply Brief","rank-math"),placeholder:(0,s.__)("Enter a short summary of the required response","rank-math"),type:"textarea",maxlength:400},original_comment:{label:(0,s.__)("Original Comment","rank-math"),placeholder:(0,s.__)("The original comment that requires a response","rank-math"),type:"textarea",maxlength:600},personal_information:{label:(0,s.__)("Personal Information","rank-math"),placeholder:(0,s.__)("Enter personal details, such as your name, age, occupation, etc.","rank-math"),type:"textarea",maxlength:400},purpose:{label:(0,s.__)("Purpose","rank-math"),placeholder:(0,s.__)("What is the purpose of this bio?","rank-math"),maxlength:200},personal_achievements:{label:(0,s.__)("Personal Achievements","rank-math"),placeholder:(0,s.__)("Enter a list of your personal achievements, separated by commas","rank-math"),type:"textarea",maxlength:400},company_name:{label:(0,s.__)("Company Name","rank-math"),placeholder:(0,s.__)("Enter the name of your company","rank-math"),maxlength:200},company_information:{label:(0,s.__)("Company Information","rank-math"),placeholder:(0,s.__)("Enter company details, such as the company name, location, and industry","rank-math"),type:"textarea",maxlength:500},company_history:{label:(0,s.__)("Company History","rank-math"),placeholder:(0,s.__)("Enter a brief history of the company","rank-math"),type:"textarea",maxlength:500},team:{label:(0,s.__)("Team","rank-math"),placeholder:(0,s.__)("Enter a brief description of the team","rank-math"),type:"textarea",maxlength:500},job_title:{label:(0,s.__)("Job Title","rank-math"),placeholder:(0,s.__)("Enter the job title.","rank-math"),maxlength:200},requirements:{label:(0,s.__)("Requirements","rank-math"),placeholder:(0,s.__)("Enter the key requirements for the position, separated by commas","rank-math"),type:"textarea",maxlength:400},responsibilities:{label:(0,s.__)("Responsibilities","rank-math"),placeholder:(0,s.__)("Enter a list of responsibilities, separated by commas","rank-math"),type:"textarea",maxlength:400},comment:{label:(0,s.__)("Comment","rank-math"),placeholder:(0,s.__)("The comment you want to reply to.","rank-math"),type:"textarea",maxlength:600},hashtags:{label:(0,s.__)("Hashtags","rank-math"),placeholder:(0,s.__)("Enter one or more hashtags, separated by commas","rank-math"),maxlength:200},tweet:{label:(0,s.__)("Tweet","rank-math"),placeholder:(0,s.__)("Enter the original tweet to reply to.","rank-math"),type:"textarea",maxlength:400},email_brief:{label:(0,s.__)("Email Brief","rank-math"),placeholder:(0,s.__)("Enter a brief description of the email","rank-math"),type:"textarea",maxlength:500},email:{label:(0,s.__)("Email","rank-math"),placeholder:(0,s.__)("Enter the original email","rank-math"),type:"textarea",maxlength:1e3},product_description:{label:(0,s.__)("Product Description","rank-math"),placeholder:(0,s.__)("Introduce your product here. Provide a detailed description of its features and benefits, highlighting what sets it apart from competitors and why it's the perfect solution for your target audience.","rank-math"),type:"textarea",maxlength:200},visual_elements:{label:(0,s.__)("Visual Elements","rank-math"),placeholder:(0,s.__)("Enter the visual elements you want to include in the video","rank-math"),type:"textarea",maxlength:400},key_points:{label:(0,s.__)("Key Points","rank-math"),placeholder:(0,s.__)("Enter the main points you want to cover, separated by commas.","rank-math"),type:"textarea",maxlength:"400"},host:{label:(0,s.__)("Host","rank-math"),placeholder:(0,s.__)("Enter the name of the host of the podcast","rank-math"),type:"textarea",maxlength:200},co_host:{label:(0,s.__)("Guest(s) or co-host","rank-math"),placeholder:(0,s.__)("Enter the name(s) separated by comma","rank-math"),type:"textarea",maxlength:200},cuisine:{label:(0,s.__)("Cuisine","rank-math"),placeholder:(0,s.__)("e.g. Italian, Chinese, Mexican","rank-math"),maxlength:200},type:{label:(0,s.__)("Type of Dish","rank-math"),placeholder:(0,s.__)("e.g. soup, salad, casserole","rank-math"),maxlength:200},ingredients:{label:(0,s.__)("Ingredients","rank-math"),placeholder:(0,s.__)("Enter the ingredients needed for the recipe, separated by commas (e.g. flour, sugar, eggs, milk)","rank-math"),type:"textarea",maxlength:1e3},dietary_restrictions:{label:(0,s.__)("Dietary restrictions","rank-math"),placeholder:(0,s.__)("List any dietary restrictions that the recipe should adhere to (e.g. gluten-free, vegan, low-carb)","rank-math"),type:"textarea",maxlength:400},command:{label:(0,s.__)("Command","rank-math"),placeholder:(0,s.__)("Enter your command","rank-math"),type:"textarea",maxlength:1e3},instructions:{label:(0,s.__)("Instructions","rank-math"),type:"textarea",placeholder:(0,s.__)("Enter instructions","rank-math"),maxlength:600},document_title:{label:(0,s.__)("Document Title","rank-math"),placeholder:(0,s.__)("Enter the document title","rank-math"),maxlength:200}}[e]},Qe={Blog_Post_Wizard:"https://rankmath.com/kb/content-ai-blog-post-wizard-tool/",Blog_Post_Idea:"https://rankmath.com/kb/content-ai-blog-post-idea-tool/",Blog_Post_Outline:"https://rankmath.com/kb/content-ai-blog-post-outline-tool/",Blog_Post_Introduction:"https://rankmath.com/kb/content-ai-blog-post-introduction-tool/",Blog_Post_Conclusion:"https://rankmath.com/kb/content-ai-blog-post-conclusion-tool/",Post_Title:"https://rankmath.com/kb/content-ai-post-title-tool/",Topic_Research:"https://rankmath.com/kb/content-ai-topic-research-tool/?play-video=jbl6YfxdDMA",SEO_Title:"https://rankmath.com/kb/content-ai-seo-title-tool/?play-video=IGzjfbZ0r8g",SEO_Description:"https://rankmath.com/kb/content-ai-seo-description-tool/?play-video=chKiMSDIN14",Paragraph:"https://rankmath.com/kb/content-ai-paragraph-writing-tool/",Sentence_Expander:"https://rankmath.com/kb/content-ai-sentence-expander-tool/",Paragraph_Rewriter:"https://rankmath.com/kb/content-ai-paragraph-rewriter-tool/",Text_Summarizer:"https://rankmath.com/kb/content-ai-text-summarizer-tool/",Fix_Grammar:"https://rankmath.com/kb/content-ai-fix-grammar-tool/",Analogy:"https://rankmath.com/kb/content-ai-analogy-tool/",Product_Description:"https://rankmath.com/kb/content-ai-product-description-tool/",Product_Pros_And_Cons:"https://rankmath.com/kb/content-ai-product-pros-and-cons-tool/",Product_Review:"https://rankmath.com/kb/content-ai-product-review-tool/",Frequently_Asked_Questions:"https://rankmath.com/kb/content-ai-frequently-asked-questions-tool/",Comment_Reply:"https://rankmath.com/kb/content-ai-comment-reply-tool/",Personal_Bio:"https://rankmath.com/kb/content-ai-personal-bio-tool/",Company_Bio:"https://rankmath.com/kb/content-ai-company-bio-tool/",Job_Description:"https://rankmath.com/kb/content-ai-job-description-tool/",Testimonial:"https://rankmath.com/kb/content-ai-testimonial-tool/",Facebook_Post:"https://rankmath.com/kb/content-ai-facebook-post-tool/?play-video=_tBBi26JAiU",Facebook_Comment_Reply:"https://rankmath.com/kb/content-ai-facebook-comment-reply-tool/",Tweet:"https://rankmath.com/kb/content-ai-tweet-tool/",Tweet_Reply:"https://rankmath.com/kb/content-ai-tweet-reply-tool/",Instagram_Caption:"https://rankmath.com/kb/content-ai-instagram-caption-tool/?play-video=GHk4JwcOpRY",Email:"https://rankmath.com/kb/content-ai-email-tool/?play-video=hJSmY0_WTK0",Email_Reply:"https://rankmath.com/kb/content-ai-email-reply-tool/?play-video=j5R8TGVtDLY",AIDA:"https://rankmath.com/kb/content-ai-aida-tool/?play-video=pHH1w_yNy4o",IDCA:"https://rankmath.com/kb/content-ai-idca-tool/",PAS:"https://rankmath.com/kb/content-ai-pas-tool/",HERO:"https://rankmath.com/kb/content-ai-hero-tool/",BAB:"https://rankmath.com/kb/content-ai-bab-tool/",SPIN:"https://rankmath.com/kb/content-ai-spin-tool/",Youtube_Video_Script:"https://rankmath.com/kb/content-ai-youtube-video-script-tool/",Youtube_Video_Description:"https://rankmath.com/kb/content-ai-youtube-video-description-tool/",Podcast_Episode_Outline:"https://rankmath.com/kb/content-ai-podcast-episode-outline-tool/",Recipe:"https://rankmath.com/kb/content-ai-recipe-tool/",Freeform_Writing:"https://rankmath.com/kb/content-ai-freeform-writing-tool/",AI_Command:"https://rankmath.com/kb/content-ai-command-tool/",SEO_Meta:"https://rankmath.com/kb/content-ai-seo-meta-tool/?play-video=fqC81KMX5IY",Opengraph:"https://rankmath.com/kb/content-ai-open-graph-tool/",Write:"https://rankmath.com/kb/content-ai-editor/"},Xe=function(e){var t=Qe[e];return arguments.length>1&&void 0!==arguments[1]&&arguments[1]&&(0,o.includes)(t,"play-video")&&(t=t.substring(0,t.indexOf("?"))),!(0,o.isUndefined)(t)&&"".concat(t,"?utm_source=Plugin&utm_medium=AI+Tool&utm_campaign=WP")},et=function(e){var t=e.id,n=e.data,a=e.value,r=e.endpoint,i=Xe(void 0===r?"":r,!0);return wp.element.createElement("label",{htmlFor:t},(0,o.unescape)(n.label),i&&wp.element.createElement("a",{href:i+"#"+t,rel:"noreferrer",target:"_blank",title:n.tooltip},wp.element.createElement("em",{className:"dashicons-before dashicons-editor-help rank-math-tooltip"})),n.maxlength&&wp.element.createElement("span",{className:"limit"},wp.element.createElement("span",{className:"count"},(0,o.size)((0,o.isArray)(a)?a.join(" "):a)),"/",n.maxlength))},tt=function(e,t,n,a,r){var i=wp.data.select("rank-math-content-ai").getContentAiAttributes();return wp.element.createElement("form",{className:"rank-math-ai-tools"},(0,o.map)(e,(function(e,l){var m=Ye(l);m.placeholder=(0,o.isEmpty)(e.placeholder)?m.placeholder:e.placeholder,m.label=(0,o.isEmpty)(e.label)?m.label:e.label;var d=e.isRequired,p=(0,o.isArray)(t[l])?t[l].join(" "):t[l],h=b()("form-field",{"is-required":d,"limit-reached":!(0,o.isUndefined)(m.maxlength)&&!(0,o.isUndefined)(p)&&p.length>m.maxlength}),g=(0,o.isUndefined)(i[l])?function(e,t){return(0,o.isUndefined)(e.default)?t:e.default}(e,m.default):i[l];if(!(0,o.isEmpty)(m.options)&&"button"===m.type)return wp.element.createElement("div",{className:h,key:l},wp.element.createElement(et,{id:l,data:m,value:"",endpoint:n}),wp.element.createElement(c.__experimentalToggleGroupControl,{value:g},(0,o.map)(m.options,(function(e,t){return wp.element.createElement(c.ToolbarButton,{key:t,value:e.value,isPressed:e.value===g,onClick:function(){return a(l,e.value)}},(0,o.isEmpty)(e.label)?e.value:e.label)}))));if(!(0,o.isUndefined)(m.options)){var f=(0,u.createRef)(),_={add:function(e){var n=(0,o.isArray)(t[l])?[e.detail.data.value]:e.detail.data.value;!(0,o.isUndefined)(t[l])&&(0,o.isArray)(t[l])&&(n=t[l]).push(e.detail.data.value),a(l,n)},remove:function(e){if(!(0,o.isArray)(t[l]))return a(l,""),!1;var n=(0,o.remove)(t[l],(function(t){return t!==e.detail.data.value}));return a(l,n),!1}},w={addTagOnBlur:!0,maxTags:m.maxTags?m.maxTags:"100",whitelist:m.options,focusableTags:!0,transformTag:function(e){e.value=e.value.replaceAll(",","")},templates:{tag:function(e){var t=(0,o.isUndefined)(e.icon)?"":e.icon;try{return"<tag ".concat(Ze(e)," title='").concat(e.value,"' contenteditable='false' spellcheck=\"false\" class='tagify__tag'>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<x title='remove tag' class='tagify__tag__removeBtn'></x>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t").concat(t,"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class='tagify__tag-text'>").concat(e.value,"</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t</tag>")}catch(e){}},dropdownItem:function(e){var t=(0,o.isUndefined)(e.icon)?"":e.icon;try{return"<div ".concat(Ze(e)," class='tagify__dropdown__item'>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t").concat(t,"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span>").concat(e.value,"</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>")}catch(e){console.error(e)}}},dropdown:{enabled:0,maxItems:100,closeOnSelect:!0},callbacks:_};return wp.element.createElement("div",{className:h+" content-ai-tagify rank-math-focus-keyword",key:l},wp.element.createElement(et,{id:l,data:m,value:t[l],endpoint:n}),wp.element.createElement(Je,{id:l,ref:f,mode:"input",settings:w,placeholder:m.placeholder,initialValue:g}))}return(0,o.isEmpty)(m.type)||"textarea"!==m.type?wp.element.createElement("div",{className:h,key:l},wp.element.createElement(et,{id:l,data:m,value:g,endpoint:n}),wp.element.createElement(c.TextControl,{id:l,onChange:function(e){return a(l,e)},placeholder:m.placeholder,className:d?"is-required":"",required:d?"required":"",value:g})):wp.element.createElement("div",{className:h,key:l},wp.element.createElement(et,{id:l,data:m,value:t[l],endpoint:n}),wp.element.createElement(c.TextareaControl,{id:l,onChange:function(e){return a(l,e)},placeholder:m.placeholder,className:d?"is-required":"",rows:m.rows?m.rows:"5",required:d?"required":"",value:(0,o.isUndefined)(g)?"":g}),r&&"main_points"===l&&!(0,o.isEmpty)(t.topic)&&wp.element.createElement(c.Button,{variant:"link",className:"generate",onClick:function(){t.title=t.topic,a(l,(0,s.__)("Generating…","rank-math")),Be("Main_Points",t,(function(e){a(l,e[0]),a("topic",t.topic),document.getElementById("main_points").value=e[0]}))}},(0,s.__)("Generate with AI","rank-math")))})))},nt=function(){var e=(0,d.select)("core/block-editor").getSelectedBlock(),t=(0,d.select)("core/block-editor").getBlocks();if(!(0,o.isEmpty)(e)){var n=t.map((function(t){return t.clientId===e.clientId})).indexOf(!0)+1;return{block:e,position:n,clientId:e.clientId}}if((0,o.isEmpty)(t))return{block:[],position:0};var a=t[t.length-1];return{block:a,position:t.length,clientId:a.clientId}},at=function(e){return!(arguments.length>1&&void 0!==arguments[1])||arguments[1]?(0,o.isUndefined)(e.attributes.content)||(0,o.isEmpty)(e.attributes.content.text)?e.attributes.content:e.attributes.content.text:(0,o.isEmpty)(e.attributes.content)&&(0,o.isEmpty)(e.innerBlocks)},rt=n(787),it=new(n.n(rt)().Converter)({noHeaderId:!0,tables:!0,literalMidWordUnderscores:!0,omitExtraWLInCodeBlocks:!0,simpleLineBreaks:!0,strikethrough:!0}),ot=function(e){return e.replace(/((?:^|\n)```)([^\n`]+)(```(?:$|\n))/,(function(e,t,n,a){return"".concat(t,"\n").concat(n,"\n").concat(a)}))},st=function(e){return e.replace(/(^|\n)•( +)/g,"$1*$2")},lt=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return(0,o.isString)(e)?t?it.makeMarkdown(e):rankMath.contentAI.isContentAIPage||"gutenberg"===rankMath.currentEditor?(0,Me.serialize)((0,Me.rawHandler)({HTML:it.makeHtml(ot(st(e))),mode:"BLOCKS"})):it.makeHtml(ot(st(e))):e},ct=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=800-e,n=[];if("elementor"===rankMath.currentEditor){var a,r=elementor.$preview[0].contentWindow.document;r.getElementsByClassName("elementor-element-editable");a=r.getElementsByClassName("elementor-widget-container");var s=Array.from(a),l=!1;return(0,o.forEach)(s.reverse(),(function(a){if(e>=800)return!1;if(a.innerText){i()(a).parents(".elementor-element-editable").length&&(i()(r).find(".rank-math-active").removeClass("rank-math-active"),i()(a).addClass("rank-math-active"),l=!0),l||(i()(a).addClass("rank-math-active"),l=!0);var s=a.querySelectorAll("*"),c=Array.from(s);(0,o.forEach)(c.reverse(),(function(a){if(e>=600)return!1;var r=(0,o.includes)(["h1","h2","h3","h4","h5","h6"],a.localName)?"Heading: "+a.innerText:a.innerText;if(r&&!(0,o.includes)(["div","b","i","u","em"],a.localName))if(r.length<=t)n.push(r),e+=r.length,t-=r.length;else{var i=r.match(/[^\.!\?]+[\.!\?]+/g);(0,o.forEach)(i.reverse(),(function(a){e<600&&(n.push(a),e+=a.length,t-=a.length)}))}}))}})),lt(n.reverse().join("\n\n"),!0)}if("undefined"!=typeof tinymce&&null!==tinymce.activeEditor&&!0!==tinymce.activeEditor.isHidden()&&"content"===tinymce.activeEditor.id){var c=tinyMCE.activeEditor.selection.getSelectedBlocks();return(0,o.forEach)(c.reverse(),(function(a){if(e>=800)return!1;var r=(0,o.includes)(["h1","h2","h3","h4","h5","h6"],a.localName)?"<"+a.localName+">"+a.innerText+"</"+a.localName+">":a.innerHTML;if(r.length<=t)n.push(r),e+=r.length,t-=r.length;else{var i=r.match(/[^\.!\?]+[\.!\?]+/g);(0,o.forEach)(i.reverse(),(function(a){e<800&&(n.push(a),e+=a.length,t-=a.length)}))}})),lt(n.reverse().join("\n\n"),!0)}var u=nt();if((0,o.isEmpty)(u.block))return"";var m=u.clientId,p=[];return(0,o.forEach)((0,d.select)("core/block-editor").getBlocks(),(function(e){if("rank-math/command"!==e.name&&p.push(e),e.clientId===m)return!1})),(0,o.forEach)(p.reverse(),(function(a){if(e>=800)return!1;var r=at(a);if(!(0,o.isEmpty)(r)){var i="core/heading"===a.name?"<h"+a.attributes.level+">"+r+"</h"+a.attributes.level+">":"<p>"+r+"</p>";if(i.length<=t)n.push(i),e+=i.length,t-=i.length;else{var s=i.match(/[^\.!\?]+[\.!\?]+/g);(0,o.isEmpty)(s)||(0,o.forEach)(s.reverse(),(function(a){e<800&&(n.push(a),e+=a.length,t-=a.length)}))}}})),lt(n.reverse().join("\n\n"),!0)},ut=function(){return wp.element.createElement(Te,{components:{strong:wp.element.createElement("strong",null)}},(0,s.__)("Thank you for choosing Rank Math! {{strong}}Enjoy 750 Credits monthly for life{{/strong}} as a token of our appreciation! 🎁","rank-math"))},mt=function(e){var t=e.isContentAIPage,n=void 0!==t&&t,a=e.addNotice,r=void 0===a||a;if(rankMath.contentAI.plan&&"free"===rankMath.contentAI.plan){if(!r)return wp.element.createElement(React.Fragment,null,ut(),wp.element.createElement("br",null));var i=b()("rank-math-content-ai-notice",{"is-page":n});return wp.element.createElement(c.Notice,{status:n?"success":"warning",className:i,isDismissible:!1},ut())}},dt=function(){return"\n\tlet words = [];\n\tlet currentWordIndex = 0;\n\tlet currentLetterIndex = 0;\n\tlet typingSpeed = ".concat(arguments.length>0&&void 0!==arguments[0]?arguments[0]:25,"\n\t\n\tfunction typeWords() {\n\t\tif ( currentWordIndex >= words.length ) {\n\t\t\tpostMessage('rank_math_process_complete')\n\t\t\treturn\n\t\t}\n\t\n\t\tconst currentWord = words[ currentWordIndex ];\n\t\tif ( currentLetterIndex < currentWord.length ) {\n\t\t\tpostMessage( currentWord );\n\t\t\tcurrentLetterIndex = currentLetterIndex + currentWord.length;\n\t\t} else {\n\t\t\tcurrentWordIndex++;\n\t\t\tcurrentLetterIndex = 0;\n\t\t\tpostMessage(' '); // Add space between words\n\t\t}\n\t\n\t\tsetTimeout( typeWords, typingSpeed );\n\t}\n\t\n\tself.onmessage = function (event) {\n\t\twords = event.data.split(' ');\n\t\ttypeWords();\n\t};\n\t")},pt=function(){var e="Blog_Post_Outline"!==(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"")?25:10;return new Worker(URL.createObjectURL(new Blob([dt(e)],{type:"application/javascript"})))},ht=function(e){var t=elementor.$preview[0].contentWindow.document,n=t.getElementsByClassName("rank-math-active");n.length||(n=(n=t.getElementsByClassName("elementor-widget-container"))[n.length-1]),i()(n).trigger("click");var a=i()(n).find("[data-elementor-setting-key]"),r=a.data();setTimeout((function(){if("title"===r.elementorSettingKey){var t=i()('[data-setting="title"]');t.val(t.val()+" "+e),a.text(a.text()+" "+e)}else{var n=tinymce.activeEditor;n.selection.select(n.getBody(),!0),n.selection.collapse(!1),n.insertContent(" "+e)}}),300),i()(n).removeClass("rank-math-active")},gt=function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];!function(){var e=elementor.settings.page.getEditedView().getContainer();if(!e.children.length){var t={model:{custom:"",elType:"widget",widgetType:"text-editor"},options:{at:void 0,side:"top",default:"",value:"",text:"",html:""},container:e};$e.run("preview/drop",t)}}(),setTimeout((function(){!function(e,t){var n=elementor.$preview[0].contentWindow.document,a=Array.from(n.getElementsByClassName("elementor-widget-container"));if((0,o.forEach)(a.reverse(),(function(e){e.innerText&&i()(e).addClass("rank-math-active")})),e=lt(e),t){var r=pt();r.onmessage=function(e){var t=e.data;t&&"rank_math_process_complete"!==t&&ht(t)},r.postMessage(e)}else ht(e)}(e,t)}),500)},ft=wp.blockEditor,_t=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";a&&(0,d.dispatch)("core/block-editor").updateBlockAttributes(t,{content:a});var r=pt();r.onmessage=function(e){var a=e.data;if(a)if("classic"!==rankMath.currentEditor){if(t){var r,o,s,l=(0,d.select)("core/block-editor").getBlock(t),c="<br>"===a,u=at(l);if("rank_math_process_complete"!==a)u?u+="<br>"===a||c?a:" "+a:u=a,(0,d.dispatch)("core/block-editor").updateBlockAttributes(l.clientId,{content:u}),r=document.getElementById("block-"+l.clientId),o=getSelection(),s=document.createRange(),o.removeAllRanges(),s.selectNodeContents(r),s.collapse(!1),o.addRange(s);else n&&((0,d.dispatch)("core/block-editor").updateBlockAttributes(l.clientId,{content:u+n,className:""}),setTimeout((function(){i()(".rank-math-content-ai-command-buttons .rank-math-content-ai-use").trigger("focus")}),100))}}else tinymce.activeEditor.insertContent("rank_math_process_complete"!==a?" "+a:"")},r.postMessage(e)},bt=(0,s.__)("Generating…","rank-math"),wt=(0,d.dispatch)(ft.store),vt=wt.updateBlockAttributes,kt=wt.removeBlock,yt='<div class="rank-math-content-ai-command-buttons">'+('<button class="button button-small rank-math-content-ai-use" tabindex="0"><span contenteditable="false">'+(0,s.__)("Use","rank-math")+"</span></button>")+('<button class="button button-small rank-math-content-ai-regenerate" tabindex="0"><span contenteditable="false">'+(0,s.__)("Regenerate","rank-math")+"</span></button>")+('<button class="button button-small rank-math-content-ai-write-more" tabindex="0"><span contenteditable="false">'+(0,s.__)("Write More","rank-math")+"</span></button>")+"</div>",Et=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";Be(e,t,(function(e){if(e.error){var t='<div class="rank-math-content-ai-command-buttons"><button class="button button-small rank-math-content-ai-dismiss" contenteditable="false" contenteditable="true">'+(0,s.__)("Dismiss","rank-math")+"</button></div>";vt(n,{content:e.error+t,className:"rank-math-content-ai-command",isAiGenerated:!0})}else e=(0,o.isArray)(e)?e[0]:e,(0,o.isNull)(n)?_t(e):(vt(n,{content:"",className:"rank-math-content-ai-command typing",isAiGenerated:!0}),_t(e,n,yt,a))}))},Ct=function(){var e=(0,d.select)("core/block-editor").getSelectedBlock(),t=at(e).replace(/<div .*<\/div>/g,"").replaceAll("  ",""),n=(0,Me.rawHandler)({HTML:t,mode:"BLOCKS"}).map((function(e){return(0,Me.createBlock)(e.name,e.attributes,e.innerBlocks)}));if(e.attributes.replaceBlock)return(0,d.dispatch)("core/block-editor").replaceBlock(e.attributes.selectedId,n),void kt(e.clientId);(0,d.dispatch)("core/block-editor").replaceBlock(e.clientId,n)},St=function(){var e=(0,d.select)("core/block-editor").getSelectedBlock(),t=e.clientId,n=e.attributes.endpoint,a=e.attributes.params;vt(t,{content:bt}),Et(n,a,t)},xt=function(e){if(800===e.length)return e;if(e.length>800){var t=0,n=[],a=e.match(/[^\.!\?]+[\.!\?]+/g);return(0,o.isEmpty)(a)||(0,o.forEach)(a.reverse(),(function(e){t<800&&(n.push(e),t+=e.length)})),n.reverse().join(" ")}return ct(e.length)+"\n"+e},Tt=function(){var e=(0,d.select)("core/block-editor").getSelectedBlock(),t=e.clientId,n=at(e).replace(/<div .*<\/div>/g,"").replace("<br>","").replaceAll("  ","");vt(t,{content:n+""+bt}),Et("Continue_Writing",{sentence:xt(n),choices:1},t,n)};i()(document).on("click",".rank-math-content-ai-dismiss",(function(){var e=(0,d.select)("core/block-editor").getSelectedBlock();kt(e.clientId)})),i()(document).on("keydown",".rank-math-content-ai-use",(function(e){"Enter"===e.code&&Ct()})),i()(document).on("click",".rank-math-content-ai-use",(function(){Ct()})),i()(document).on("keydown",".rank-math-content-ai-regenerate",(function(e){"Enter"===e.code&&St()})),i()(document).on("click",".rank-math-content-ai-regenerate",(function(){St()})),i()(document).on("keydown",".rank-math-content-ai-write-more",(function(e){"Enter"===e.code&&Tt()})),i()(document).on("click",".rank-math-content-ai-write-more",(function(){Tt()}));var Ot=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3?arguments[3]:void 0,r=arguments.length>4&&void 0!==arguments[4]&&arguments[4];(0,o.isNull)(n)||vt(n,{content:(0,s.__)("Generating…","rank-math"),className:"typing rank-math-content-ai-command",endpoint:e,params:t,replaceBlock:r,selectedId:a}),Et(e,t,n)};function Pt(e){return Pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Pt(e)}function At(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function Nt(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Pt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==Pt(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Pt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function It(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return jt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return jt(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function jt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var Mt=function(e){var t=It((0,u.useState)({document_title:"undefined"!=typeof rankMathEditor?rankMathEditor.assessor.dataCollector.getData().title:"",text:"",instructions:"",tone:rankMath.contentAI.tone,focus_keyword:[],length:"medium",choices:1}),2),n=t[0],a=t[1],r=It((0,u.useState)(!1),2),i=r[0],l=r[1],m=g()||e.data.isContentAIPage;return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:e.hasError?"rank-math-ui module-listing blurred":"rank-math-ui module-listing"},wp.element.createElement("div",{className:"rank-math-focus-keyword"},wp.element.createElement(c.Notice,{status:"warning",isDismissible:!1},wp.element.createElement(mt,{addNotice:!1}),wp.element.createElement(Te,{components:{link:wp.element.createElement("a",{href:"https://rankmath.com/kb/content-ai-editor/?utm_source=Plugin&utm_medium=Write+Tab+Notice&utm_campaign=WP#write-tab",target:"_blank",rel:"noopener noreferrer"})}},(0,s.__)("{{link}}Click here{{/link}} to learn how to use it.","rank-math")))),tt({instructions:{isRequired:!1},tone:{isRequired:!1},focus_keyword:{isRequired:!1},length:{isRequired:!0}},n,"Write",(function(e,t){n[e]=t,a(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?At(Object(n),!0).forEach((function(t){Nt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):At(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},n)),(0,d.dispatch)("rank-math-content-ai").updateAIAttributes(e,t)})),wp.element.createElement(c.Button,{className:"write-button is-primary",onClick:function(){if(n.text=ct(),a(n),!m)return l(!0),void Be("Write",n,(function(e){var t=(0,o.isArray)(e)?e[0]:e;"elementor"===rankMath.currentEditor?gt(t):_t(t),l(!1)}));var e=nt();if((0,o.isEmpty)(e)||(0,o.isEmpty)(e.block)||!at(e.block,!1)){var t=(0,Me.createBlock)("rank-math/command",{content:""});(0,d.dispatch)("core/block-editor").insertBlocks(t,(0,o.isEmpty)(e)?1:e.position),e=t}else{var r=(0,Me.createBlock)("rank-math/command",{content:"",className:"rank-math-content-ai-command"});(0,d.dispatch)("core/block-editor").replaceBlock(e.clientId,r),e=r}Ot("Write",n,e.clientId)}},wp.element.createElement(React.Fragment,null,i?(0,s.__)("Generating…","rank-math"):(0,s.__)("Generate","rank-math"),m&&wp.element.createElement("span",null,"CTRL + /"))),wp.element.createElement("p",{style:{marginTop:"10px",opacity:"0.7"}},wp.element.createElement("em",null,(0,s.__)("1 Word Output = 1 Credit","rank-math")))),e.hasError&&!e.isContentAIPage&&wp.element.createElement(Pe,null))},Rt=function(){return[{endpoint:"Blog_Post_Wizard",title:(0,s.__)("Blog Post Wizard","rank-math"),description:(0,s.__)("Create a complete blog post in one go. Just fill in some details and Content AI will create a complete blog post for you.","rank-math"),category:"blog",icon:"rm-icon rm-icon-pencil",helpLink:Xe("Blog_Post_Wizard"),output:{default:5,max:20}},{endpoint:"Blog_Post_Idea",title:(0,s.__)("Blog Post Idea","rank-math"),description:(0,s.__)("Get fresh ideas for engaging blog posts that resonate with your niche and audience, ensuring captivating content.","rank-math"),category:"blog",icon:"rm-icon rm-icon-edit",helpLink:Xe("Blog_Post_Idea"),params:{topic_brief:{isRequired:!0,label:(0,s.__)("Describe Your Industry/Niche","rank-math"),placeholder:(0,s.__)("e.g. Technology blog that covers latest gadgets, tech news, and reviews","rank-math")},audience:{isRequired:!1},tone:{isRequired:!1},style:{isRequired:!1},language:{isRequired:!1}},output:{default:5,max:20}},{endpoint:"Blog_Post_Outline",title:(0,s.__)("Blog Post Outline","rank-math"),description:(0,s.__)("Structure blog posts with a clear flow, guiding readers effortlessly for better understanding and engagement.","rank-math"),category:"blog",icon:"rm-icon rm-icon-howto",helpLink:Xe("Blog_Post_Outline"),params:{topic:{isRequired:!0},main_points:{isRequired:!1},audience:{isRequired:!1},focus_keyword:{isRequired:!1},tone:{isRequired:!1},style:{isRequired:!1},language:{isRequired:!1}},output:{default:1,max:3}},{endpoint:"Blog_Post_Introduction",title:(0,s.__)("Blog Post Introduction","rank-math"),description:(0,s.__)("Craft attractive intros that captivate readers' interest, compelling them to explore further into your blog.","rank-math"),category:"blog",icon:"rm-icon rm-icon-acf",helpLink:Xe("Blog_Post_Introduction"),params:{title:{isRequired:!0},audience:{isRequired:!1},focus_keyword:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:2,max:5}},{endpoint:"Blog_Post_Conclusion",title:(0,s.__)("Blog Post Conclusion","rank-math"),description:(0,s.__)("End your blog posts with impactful summaries, reinforcing key takeaways and leaving a lasting impression.","rank-math"),category:"blog",icon:"rm-icon rm-icon-support",helpLink:Xe("Blog_Post_Conclusion"),params:{topic:{isRequired:!0},main_argument:{isRequired:!0},call_to_action:{isRequired:!1},tone:{isRequired:!1},audience:{isRequired:!1},language:{isRequired:!1}},output:{default:2,max:5}},{endpoint:"Post_Title",title:(0,s.__)("Post Title","rank-math"),description:(0,s.__)("Create eye-catching headlines for articles and blogs, grabbing readers' attention and boosting engagement.","rank-math"),category:"blog",icon:"rm-icon rm-icon-heading",helpLink:Xe("Post_Title"),params:{post_brief:{isRequired:!0},audience:{isRequired:!1},tone:{isRequired:!1},style:{isRequired:!1},length:{isRequired:!0},language:{isRequired:!1}},output:{default:5,max:25}},{endpoint:"Topic_Research",title:(0,s.__)("Topic Research","rank-math"),description:(0,s.__)("Dive deep into comprehensive reports on specific topics, uncovering trends, history, and industry players.","rank-math"),category:"seo",icon:"rm-icon rm-icon-analyzer",helpLink:Xe("Topic_Research"),params:{topic:{isRequired:!0},relevance:{isRequired:!1},format:{isRequired:!0},focus_keyword:{isRequired:!1},audience:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"SEO_Title",title:(0,s.__)("SEO Title","rank-math"),description:(0,s.__)("Optimize headlines for enhanced visibility, organic traffic, and a stronger online presence.","rank-math"),category:"seo",icon:"rm-icon rm-icon-seo-title",helpLink:Xe("SEO_Title"),params:{post_title:{isRequired:!0},focus_keyword:{isRequired:!1},post_brief:{isRequired:!1},tone:{isRequired:!1},audience:{isRequired:!1},language:{isRequired:!1}},output:{default:5,max:25}},{endpoint:"SEO_Description",title:(0,s.__)("SEO Description","rank-math"),description:(0,s.__)("Craft concise and persuasive summaries that captivate readers and search engines, improving click-through rates.","rank-math"),category:"seo",icon:"rm-icon rm-icon-seo-description",helpLink:Xe("SEO_Description"),params:{seo_title:{isRequired:!0},focus_keyword:{isRequired:!1},post_brief:{isRequired:!1},tone:{isRequired:!1},audience:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:25}},{endpoint:"Paragraph",title:(0,s.__)("Paragraph","rank-math"),description:(0,s.__)("Generate well-structured and informative paragraphs, seamlessly blending into your content for better readability.","rank-math"),category:"blog",icon:"rm-icon rm-icon-text-align-left",helpLink:Xe("Paragraph_Writing"),params:{topic:{isRequired:!0},main_argument:{isRequired:!0},tone:{isRequired:!1},audience:{isRequired:!1},supporting_points:{isRequired:!1},length:{isRequired:!0},language:{isRequired:!1}},output:{default:2,max:5}},{endpoint:"Paragraph_Rewriter",title:(0,s.__)("Paragraph Rewriter","rank-math"),description:(0,s.__)("Refine paragraphs while preserving meaning, ensuring originality, and enhancing clarity.","rank-math"),category:"blog",icon:"rm-icon rm-icon-book",helpLink:Xe("Paragraph_Rewritter"),params:{original_paragraph:{isRequired:!0},audience:{isRequired:!1},focus_keyword:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:1,max:3}},{endpoint:"Sentence_Expander",title:(0,s.__)("Sentence Expander","rank-math"),description:(0,s.__)("Transform incomplete sentences into polished expressions, adding depth and clarity to your writing.","rank-math"),category:"misc",icon:"rm-icon rm-icon-misc",helpLink:Xe("Sentence_Expander"),params:{sentence:{isRequired:!0},topic:{isRequired:!1},audience:{isRequired:!1},focus_keyword:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:1,max:5}},{endpoint:"Text_Summarizer",title:(0,s.__)("Text Summarizer","rank-math"),description:(0,s.__)("Condense complex texts into concise summaries, highlighting crucial points and essential information.","rank-math"),category:"misc",icon:"rm-icon rm-icon-page",helpLink:Xe("Text_Summarizer"),params:{text:{isRequired:!0},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:8}},{endpoint:"Fix_Grammar",title:(0,s.__)("Fix Grammar","rank-math"),description:(0,s.__)("Utilize AI-powered grammar correction to polish your written content, eliminating errors and improving clarity.","rank-math"),category:"misc",icon:"rm-icon rm-icon-help",helpLink:Xe("Fix_Grammar"),params:{text:{isRequired:!0,label:(0,s.__)("Text","rank-math"),placeholder:(0,s.__)("Enter the text to fix grammar","rank-math")}},output:{default:1,max:1}},{endpoint:"Analogy",title:(0,s.__)("Analogy","rank-math"),description:(0,s.__)("Enhance clarity by rephrasing text using alternative words, providing a fresh perspective without altering meaning.","rank-math"),category:"misc",icon:"rm-icon rm-icon-sitemap",helpLink:Xe("Analogy"),params:{text:{isRequired:!0},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:8}},{endpoint:"Product_Description",title:(0,s.__)("Product Description","rank-math"),description:(0,s.__)("Craft compelling descriptions that effectively showcase the unique benefits and features of your product.","rank-math"),category:"ecommerce",icon:"rm-icon rm-icon-mobile",helpLink:Xe("Product_Description"),params:{product_name:{isRequired:!0},features_and_benefits:{isRequired:!0},audience:{isRequired:!1},focus_keyword:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:2,max:5}},{endpoint:"Product_Pros_And_Cons",title:(0,s.__)("Product Pros & Cons","rank-math"),description:(0,s.__)("Present balanced overviews outlining the advantages and limitations, aiding informed decisions.","rank-math"),category:"ecommerce",icon:"rm-icon rm-icon-thumbs-up",helpLink:Xe("Product_Pros_and_Cons"),params:{product_name:{isRequired:!0},features_and_benefits:{isRequired:!0},limitations_and_drawbacks:{isRequired:!0},audience:{isRequired:!1},focus_keyword:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:2,max:5}},{endpoint:"Product_Review",title:(0,s.__)("Product Review","rank-math"),description:(0,s.__)("Provide detailed evaluations covering strengths, weaknesses, and practical recommendations.","rank-math"),category:"ecommerce",icon:"rm-icon rm-icon-star",helpLink:Xe("Product_Review"),params:{features_and_benefits:{isRequired:!0},product_name:{isRequired:!0},limitations_and_drawbacks:{isRequired:!1},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"Frequently_Asked_Questions",title:(0,s.__)("Frequently Asked Questions","rank-math"),description:(0,s.__)("Address common queries with comprehensive answers, offering valuable information and guidance.","rank-math"),category:"ecommerce",icon:"rm-icon rm-icon-faq",helpLink:Xe("Frequently_Asked_Questions"),params:{topic:{isRequired:!0},features_and_benefits:{isRequired:!1},audience:{isRequired:!1},focus_keyword:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"Comment_Reply",title:(0,s.__)("Comment Reply","rank-math"),description:(0,s.__)("Engage your audience with thoughtful and engaging responses, fostering meaningful interactions.","rank-math"),category:"blog",icon:"rm-icon rm-icon-comments",helpLink:Xe("Comment_Reply"),params:{reply_brief:{isRequired:!0},original_comment:{isRequired:!1},audience:{isRequired:!1},focus_keyword:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:8}},{endpoint:"Personal_Bio",title:(0,s.__)("Personal Bio","rank-math"),description:(0,s.__)("Create professional and captivating biographies highlighting accomplishments, expertise, and personality.","rank-math"),category:"misc",icon:"rm-icon rm-icon-user",helpLink:Xe("Personal_Bio"),params:{personal_information:{isRequired:!0},purpose:{isRequired:!0},personal_achievements:{isRequired:!0},focus_keyword:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:1,max:1}},{endpoint:"Company_Bio",title:(0,s.__)("Company Bio","rank-math"),description:(0,s.__)("Craft informative overviews of your company's history, values, mission, and team, building credibility.","rank-math"),category:"misc",icon:"rm-icon rm-icon-restaurant",helpLink:Xe("Company_Bio"),params:{company_name:{isRequired:!0},purpose:{isRequired:!0},company_information:{isRequired:!0},company_history:{isRequired:!1},team:{isRequired:!1},focus_keyword:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:1,max:1}},{endpoint:"Job_Description",title:(0,s.__)("Job Description","rank-math"),description:(0,s.__)("Create enticing and comprehensive descriptions outlining requirements, responsibilities, and opportunities.","rank-math"),category:"misc",icon:"rm-icon rm-icon-job",helpLink:Xe("Job_Description"),params:{company_name:{isRequired:!0},job_title:{isRequired:!0},requirements:{isRequired:!0},responsibilities:{isRequired:!0},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:1,max:3}},{endpoint:"Testimonial",title:(0,s.__)("Testimonial","rank-math"),description:(0,s.__)("Develop persuasive testimonials sharing positive experiences, endorsing your product, service, or brand.","rank-math"),category:"ecommerce",icon:"rm-icon rm-icon-schema",helpLink:Xe("Testimonial"),params:{topic:{isRequired:!0,label:(0,s.__)("Product or Service","rank-math")},features_and_benefits:{isRequired:!0},limitations_and_drawbacks:{isRequired:!1},focus_keyword:{isRequired:!1},audience:{isRequired:!1},tone:{isRequired:!1},length:{isRequired:!0},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"Facebook_Post",title:(0,s.__)("Facebook Post","rank-math"),description:(0,s.__)("Create intriguing and shareable content for Facebook, captivating your audience and boosting engagement.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-facebook",helpLink:Xe("Facebook_Post"),params:{topic_brief:{isRequired:!0},audience:{isRequired:!1},focus_keyword:{isRequired:!1},tone:{isRequired:!1},length:{isRequired:!0},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"Facebook_Comment_Reply",title:(0,s.__)("Facebook Comment Reply","rank-math"),description:(0,s.__)("Generate relevant responses to Facebook comments, build relationships & encourage interaction.","rank-math"),category:"marketing-comments-reply",icon:"rm-icon rm-icon-comments-reply",helpLink:Xe("Facebook_Comment_Reply"),params:{reply_brief:{isRequired:!0,label:(0,s.__)("Reply brief","rank-math")},comment:{isRequired:!0},post_brief:{isRequired:!1,label:(0,s.__)("Post brief","rank-math")},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"Tweet",title:(0,s.__)("Tweet","rank-math"),description:(0,s.__)("Create engaging tweets, boost interaction, and foster connections with your followers.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-twitter",helpLink:Xe("Tweet"),params:{topic_brief:{isRequired:!0},hashtags:{isRequired:!1},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"Tweet_Reply",title:(0,s.__)("Tweet Reply","rank-math"),description:(0,s.__)("Generate optimized replies for tweets to promote engagement and strengthen connections.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-comments-reply",helpLink:Xe("Tweet_Reply"),params:{reply_brief:{isRequired:!0,label:(0,s.__)("Reply brief","rank-math")},tweet:{isRequired:!0},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"Instagram_Caption",title:(0,s.__)("Instagram Caption","rank-math"),description:(0,s.__)("Craft catchy captions for Instagram posts to increase engagement and grab attention.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-instagram",helpLink:Xe("Instagram_Caption"),params:{post_brief:{isRequired:!0},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"Email",title:(0,s.__)("Email","rank-math"),description:(0,s.__)("Create effective emails for promotions, announcements, and follow-ups to achieve marketing goals.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-mail",helpLink:Xe("Email"),params:{email_brief:{isRequired:!0},call_to_action:{isRequired:!1},audience:{isRequired:!1},tone:{isRequired:!1},length:{isRequired:!0},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"Email_Reply",title:(0,s.__)("Email Reply","rank-math"),description:(0,s.__)("Craft courteous email replies to promote interaction and strengthen relationships.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-mail-reply",helpLink:Xe("Email_Reply"),params:{email:{isRequired:!0},reply_brief:{isRequired:!0},audience:{isRequired:!1},tone:{isRequired:!1},length:{isRequired:!0},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"AIDA",title:(0,s.__)("AIDA","rank-math"),description:(0,s.__)("Write persuasive text using the Attention-Interest-Desire-Action formula to drive action.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-light-bulb",helpLink:Xe("AIDA"),params:{product_name:{isRequired:!0},product_description:{isRequired:!1},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:1,max:2}},{endpoint:"IDCA",title:(0,s.__)("IDCA","rank-math"),description:(0,s.__)("Create compelling messages using the Identify-Develop-Communicate-Ask strategy to resonate.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-help",helpLink:Xe("IDCA"),params:{product_name:{isRequired:!0},product_description:{isRequired:!1},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:1,max:2}},{endpoint:"PAS",title:(0,s.__)("PAS","rank-math"),description:(0,s.__)("Address customer problems with the Problem-Agitate-Solution technique to fulfill needs.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-tick",helpLink:Xe("PAS"),params:{product_name:{isRequired:!0},product_description:{isRequired:!1},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:1,max:2}},{endpoint:"HERO",title:(0,s.__)("HERO","rank-math"),description:(0,s.__)("Craft captivating headlines using the HERO formula to engage, reveal, and offer value.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-text",helpLink:Xe("HERO"),params:{product_name:{isRequired:!0},product_description:{isRequired:!1},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:1,max:2}},{endpoint:"SPIN",title:(0,s.__)("SPIN","rank-math"),description:(0,s.__)("Describe customer problems, highlight implications, and offer solutions using the SPIN method.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-social",helpLink:Xe("SPIN"),params:{product_name:{isRequired:!0},product_description:{isRequired:!1},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:1,max:2}},{endpoint:"BAB",title:(0,s.__)("BAB","rank-math"),description:(0,s.__)("Create a compelling Before-After-Bridge narrative to demonstrate product or service value.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-dataset",helpLink:Xe("BAB"),params:{product_name:{isRequired:!0},product_description:{isRequired:!1},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:1,max:2}},{endpoint:"Youtube_Video_Script",title:(0,s.__)("YouTube Video Script","rank-math"),description:(0,s.__)("Develop engaging video scripts for YouTube to inform, entertain, and align.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-youtube",helpLink:Xe("Youtube_Video_Script"),params:{topic:{isRequired:!0},visual_elements:{isRequired:!1},key_points:{isRequired:!0},call_to_action:{isRequired:!1},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"Youtube_Video_Description",title:(0,s.__)("YouTube Video Description","rank-math"),description:(0,s.__)("Generate informative and engaging video descriptions for YouTube.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-video",helpLink:Xe("Youtube_Video_Description"),params:{topic:{isRequired:!0},audience:{isRequired:!1},focus_keyword:{isRequired:!0},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"Podcast_Episode_Outline",title:(0,s.__)("Podcast Episode Outline","rank-math"),description:(0,s.__)("Create detailed outlines for podcast episodes, including topics and takeaways.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-podcast",helpLink:Xe("Podcast_Episode_Outline"),params:{topic:{isRequired:!0},host:{isRequired:!1},co_host:{isRequired:!1},key_points:{isRequired:!0},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"Recipe",title:(0,s.__)("Recipe","rank-math"),description:(0,s.__)("Create detailed and easy-to-follow recipes with ingredients, instructions, and nutrition.","rank-math"),category:"food-cooking",icon:"rm-icon rm-icon-recipe",helpLink:Xe("Recipe"),params:{cuisine:{isRequired:!0},type:{isRequired:!0},ingredients:{isRequired:!0},dietary_restrictions:{isRequired:!1},audience:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:10}},{endpoint:"Freeform_Writing",title:(0,s.__)("Freeform Writing","rank-math"),description:(0,s.__)("Generate text based on prompts or topics, allowing for imaginative or technical writing.","rank-math"),category:"misc",icon:"rm-icon rm-icon-page",helpLink:Xe("Freeform_Writing"),params:{text:{isRequired:!0,label:(0,s.__)("What do you want to write?","rank-math")},main_points:{isRequired:!1},audience:{isRequired:!1},tone:{isRequired:!1},length:{isRequired:!0},language:{isRequired:!1}},output:{default:1,max:1}},{endpoint:"AI_Command",title:(0,s.__)("AI Command","rank-math"),description:(0,s.__)("Ask AI anything and receive relevant and informative responses for questions or requests.","rank-math"),category:"misc",icon:"rm-icon rm-icon-code",helpLink:Xe("AI_Command"),params:{command:{isRequired:!0},language:{isRequired:!1}},output:{default:1,max:1}},{endpoint:"SEO_Meta",title:(0,s.__)("SEO Meta","rank-math"),description:(0,s.__)("Optimize headlines and descriptions to improve visibility on search engines.","rank-math"),category:"seo",icon:"rm-icon rm-icon-seo",helpLink:Xe("SEO_Meta"),params:{topic:{isRequired:!0},post_brief:{isRequired:!1},audience:{isRequired:!1},focus_keyword:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:3,max:25}},{endpoint:"Opengraph",title:(0,s.__)("Open Graph","rank-math"),description:(0,s.__)("Boost content visibility on social media with topic-specific meta tags for easy discovery.","rank-math"),category:"marketing-sales",icon:"rm-icon rm-icon-social",helpLink:Xe("Open_Graph"),params:{topic:{isRequired:!0},post_brief:{isRequired:!1},audience:{isRequired:!1},focus_keyword:{isRequired:!1},tone:{isRequired:!1},language:{isRequired:!1}},output:{default:1,max:5}}]},Dt=function(e,t){var n={choices:t.default},a=wp.data.select("rank-math-content-ai").getContentAiAttributes();return(0,o.map)(e,(function(e,t){var r=(0,o.isUndefined)(a[t])?function(e,t){return(0,o.isUndefined)(t.default)?Ye(e).default:t.default}(t,e):a[t];(0,o.isUndefined)(r)||(n[t]=r)})),n};function Lt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Bt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Bt(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Bt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var qt=function(e){var t=e.value,n=e.showWordCount,a=void 0===n||n,r=e.addTypingEffect,i=void 0===r||r,o=e.endpoint,l=void 0===o?"":o,c=t.split(" "),m=Lt((0,u.useState)(i?"":t),2),d=m[0],p=m[1],h=Lt((0,u.useState)(null),2),g=h[0],f=h[1];i&&((0,u.useEffect)((function(){var e=pt(l);return f(e),e.onmessage=function(e){"rank_math_process_complete"!==e.data&&p((function(t){return t+e.data}))},function(){e.terminate()}}),[]),(0,u.useEffect)((function(){g&&(p(""),g.postMessage(t))}),[t,g]));var _=d.length<c.length?"content typing":"content";return wp.element.createElement(React.Fragment,null,a&&wp.element.createElement("div",{className:"word-count"},(0,s.sprintf)((0,s.__)("Words: %d","rank-math"),d.split(" ").length)),wp.element.createElement("div",{className:_,dangerouslySetInnerHTML:{__html:lt(d)}}))},zt=function(e){return"".concat(e,"-").concat((new Date).getTime())};function Ht(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Ft(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ft(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ft(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var Ut=function(e){var t=e.value,n=e.label,a=void 0===n?"":n,r=e.disabled,i=void 0!==r&&r,o=e.onClick,l=void 0===o?"":o,m=Ht((0,u.useState)(),2),d=m[0],p=m[1];l&&(t=(0,Me.serialize)(wp.data.select("core/block-editor").getBlocks())),a=a||(0,s.__)("Copy","rank-math");var h=(0,w.useCopyToClipboard)(t);return wp.element.createElement(c.Button,{variant:"secondary",className:"button structured-data-copy is-small",ref:h,disabled:i,onClick:function(){p(!0),setTimeout((function(){p(!1)}),700)}},wp.element.createElement("i",{className:"rm-icon rm-icon-copy"}),d?(0,s.__)("Copied!","rank-math"):a)},Wt=function(e){var t=e.value,n=e.index,a=void 0===n?0:n,r=e.isPage,l=void 0!==r&&r,u=e.endpoint,m=e.typingEffect,p=void 0===m||m,h=e.isSerpPreview,g=void 0!==h&&h,f=function(e,t){if("Frequently_Asked_Questions"!==t)return e;var n='<div class="wp-block-rank-math-faq-block">',a=[];return a.questions=(0,o.map)(e,(function(e){return n+='<div class="rank-math-faq-item"><h3 class="rank-math-question">'+e.question+'</h3><div class="rank-math-answer">'+e.answer+"</div></div>",{id:zt("faq-question"),title:e.question,content:e.answer,visible:!0}})),n+="</div>",'\x3c!-- wp:rank-math/faq-block {"questions":'+JSON.stringify(a.questions)+"} --\x3e"+n+"\x3c!-- /wp:rank-math/faq-block --\x3e"}(t,u),_=t;return(0,o.isArray)(t)&&(_="",(0,o.map)(t,(function(e){_+="<h2>"+e.question+"</h2><span>"+e.answer+"</span>"}))),(0,o.isObject)(t)&&!(0,o.isArray)(t)&&(_="",(0,o.map)(t,(function(e,t){_+="<h4>"+(0,o.startCase)(t)+"</h4><span>"+e+"</span>"}))),wp.element.createElement("div",{className:"output-item",key:a},wp.element.createElement("div",{className:"output-actions"},wp.element.createElement(Ut,{value:(0,o.isString)(f)?f:_}),((0,o.isUndefined)(rankMath.currentEditor)||(0,o.includes)(["gutenberg","classic","elementor"],rankMath.currentEditor)||g)&&!l&&wp.element.createElement(c.Button,{variant:"secondary",className:"button structured-data-test is-small",onClick:function(){var e=!1;if(!l){if("SEO_Title"===u||"SEO_Meta"===u&&!(0,o.isEmpty)(t.title)){var n=(0,o.isUndefined)(t.title)?t:t.title;(0,d.dispatch)("rank-math").updateSerpTitle(n),(0,d.dispatch)("rank-math").updateTitle(n),e=!0}if("SEO_Description"===u||"SEO_Meta"===u&&!(0,o.isEmpty)(t.description)){var a=(0,o.isUndefined)(t.description)?t:t.description;(0,d.dispatch)("rank-math").updateSerpDescription(a),(0,d.dispatch)("rank-math").updateDescription(a),e=!0}if("Opengraph"===u){var r="twitter"===wp.data.select("rank-math").getSocialTab();(0,o.isEmpty)(t.title)||(r?(0,d.dispatch)("rank-math").updateTwitterTitle(t.title):(0,d.dispatch)("rank-math").updateFacebookTitle(t.title),e=!0),(0,o.isEmpty)(t.description)||(r?(0,d.dispatch)("rank-math").updateTwitterDescription(t.description):(0,d.dispatch)("rank-math").updateFacebookDescription(t.description),e=!0)}}e?i()(".rank-math-contentai-modal-overlay .components-modal__header button").trigger("click"):function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(e=lt(e),"elementor"===rankMath.currentEditor)return gt(e,!1),void i()(".rank-math-contentai-modal-overlay .components-modal__header button").trigger("click");if("classic"===rankMath.currentEditor)return tinymce.activeEditor.insertContent(" "+e),void i()(".rank-math-contentai-modal-overlay .components-modal__header button").trigger("click");var n=(0,d.select)("core/block-editor").getSelectedBlock(),a=(0,d.select)("core/block-editor").getBlocks(),r=0;(0,o.isNull)(n)?r=a.length:(r=a.map((function(e){return e.clientId===n.clientId})).indexOf(!0),r=at(n)?r+1:r);var s="";s="Frequently_Asked_Questions"===t?(0,Me.createBlock)("rank-math/faq-block",{questions:(0,o.map)(e,(function(e){return{id:zt("faq-question"),title:e.question,content:e.answer.replaceAll(/(?:\r\n|\r|\n)/g,"<br>").trim(),visible:!0}}))}):(0,Me.rawHandler)({HTML:e,mode:"BLOCKS"}).map((function(e){return(0,Me.createBlock)(e.name,e.attributes,e.innerBlocks)})),(0,d.dispatch)("core/block-editor").insertBlocks(s,r),i()(".rank-math-contentai-modal-overlay .components-modal__header button").trigger("click")}(t,u)}},wp.element.createElement("i",{className:"rm-icon rm-icon-plus"}),wp.element.createElement("span",null,(0,s.__)("Insert","rank-math")))),wp.element.createElement(qt,{value:_,addTypingEffect:p,endpoint:u}))},Vt=function(e){var t=e.value;return wp.element.createElement("div",{className:"content-ai-error",dangerouslySetInnerHTML:{__html:t}})},$t=function(e,t,n){var a=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],r=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(!(0,o.isEmpty)(e)){if((0,o.isObject)(e)&&!(0,o.isArray)(e)){var i="";return(0,o.forEach)(Object.keys(e),(function(t){var n="";(0,o.forEach)(e[t],(function(e){n+=e+"<br /><br />"})),i+="<div><h4>"+(0,o.startCase)(t)+"</h4>"+n+"</div>"})),wp.element.createElement("div",{className:"inner-wrapper"},wp.element.createElement(Wt,{value:i,isPage:t,endpoint:n,typingEffect:a,isSerpPreview:r}))}return(0,o.isArray)(e)?wp.element.createElement("div",{className:"inner-wrapper"},"Frequently_Asked_Questions"!==n&&(0,o.map)(e,(function(e,i){return(0,o.isEmpty)(e.warning)?wp.element.createElement(Wt,{value:e,key:i,index:i,isPage:t,endpoint:n,typingEffect:a,isSerpPreview:r}):wp.element.createElement(Vt,{value:'<div class="notice notice-error">'+e.warning+"</div>"})})),"Frequently_Asked_Questions"===n&&!(0,o.isArray)(e[0])&&wp.element.createElement(Wt,{value:e,isPage:t,endpoint:n,typingEffect:a,isSerpPreview:r}),"Frequently_Asked_Questions"===n&&(0,o.isArray)(e[0])&&(0,o.map)(e,(function(e){return wp.element.createElement(Wt,{value:e,isPage:t,endpoint:n,typingEffect:a,isSerpPreview:r})}))):(0,o.isArray)(e)||(0,o.isObject)(e)?void 0:wp.element.createElement(Vt,{value:e})}},Gt=function(e){return e&&e(""),(0,o.isNull)(document.getElementById("rank-math-content-ai-modal-wrapper"))||(document.getElementById("rank-math-content-ai-modal-wrapper").remove(),document.querySelector(".rank-math-contentai-modal-overlay").remove(),document.body.classList.remove("modal-open")),!0},Kt=function(e){var t=e.title,n=e.helpLink,a=(0,s.sprintf)((0,s.__)("Learn how to use this %s Tool effectively.","rank-math"),"<strong>".concat(t,"</strong>")),r=(0,o.includes)(n,"play-video");return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{key:"title",className:"rank-math-video-tutorial"},wp.element.createElement("div",{className:"info"},wp.element.createElement("p",null,wp.element.createElement("span",{dangerouslySetInnerHTML:{__html:a}})),wp.element.createElement("a",{className:r?"":"button button-primary is-red",href:n,target:"_blank",rel:"noreferrer"},r&&wp.element.createElement("span",{className:"rm-icon-youtube"}),!r&&(0,s.__)("Click Here","rank-math")))),wp.element.createElement("p",null,wp.element.createElement("em",null,(0,s.__)("1 Word Output = 1 Credit","rank-math"))))},Jt=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";k()({method:"POST",path:"/rankmath/v1/ca/createPost",data:{content:e,title:t}}).catch((function(e){console.log(e)})).then((function(e){window.location.href=e}))},Zt=function(e){var t,n=e.endpoint,a=e.originalEndpoint,r=e.apiContent,l=e.output,u=e.attributes,m=e.generating,d=e.results,p=e.steps,h=e.setSteps,g=e.isDisabled,f=e.onChange,_=e.setGenerating,b=e.setDisabled,w=e.setHistory,v=e.setData,k=e.setResults,y="Blog_Post_Wizard"===a;return wp.element.createElement("div",{className:"footer"},!y&&wp.element.createElement(React.Fragment,null,wp.element.createElement(c.__experimentalNumberControl,{min:"1",max:l.max,value:null!==(t=u.choices)&&void 0!==t?t:l.default,onChange:function(e){return f("choices",e)}}),wp.element.createElement("span",{className:"output-label"},(0,s.__)("Outputs","rank-math"))),wp.element.createElement(c.Button,{variant:y?(0,o.isEmpty)(d)?"primary":"secondary":"primary",className:"button",disabled:g,onClick:function(){var e=i()("form.rank-math-ai-tools").get(0);e.checkValidity()?(_(!0),b(!0),w(!1),y&&"Long_Form_Content"===p.endpoint?Be("Long_Form_Content",p.attributes,r,!1):Be(n,u,r,!1)):e.reportValidity()}},wp.element.createElement("span",{className:"text"},function(e,t,n){return e?(0,s.__)("Generating…","rank-math"):(0,o.isEmpty)(t)||"Blog_Post_Wizard"!==n?(0,o.isEmpty)(t)?(0,s.__)("Generate","rank-math"):(0,s.__)("Generate More","rank-math"):(0,s.__)("Regenerate","rank-math")}(m,d,a))),y&&"Long_Form_Content"!==p.endpoint&&wp.element.createElement(c.Button,{variant:(0,o.isEmpty)(d)?"secondary":"primary",className:"button",disabled:g||(0,o.isEmpty)(d)&&"Blog_Post_Idea"!==p.endpoint,onClick:function(){if("Blog_Post_Outline"===n){var e=u.topic;return u={outline:lt(d[0],!0),language:(0,o.isEmpty)(u.language)?"":u.language,tone:(0,o.isEmpty)(u.tone)?"":u.tone,audience:(0,o.isEmpty)(u.audience)?"":u.audience,style:(0,o.isEmpty)(u.style)?"":u.style,choices:1},v(""),k([]),_(!0),Be("Long_Form_Content",u,r,!1),void h({endpoint:"Long_Form_Content",content:"",topic:e,attributes:u})}var t=(0,o.isEmpty)(d)?"":d[0];h({endpoint:"Blog_Post_Outline",content:t}),v(""),k([])}},wp.element.createElement("span",{className:"text"},function(e,t){return"Blog_Post_Idea"===e&&(0,o.isEmpty)(t)?(0,s.__)("Skip","rank-math"):"Blog_Post_Outline"===e?(0,s.__)("Write Post","rank-math"):(0,s.__)("Next Step","rank-math")}(n,d))),y&&"Long_Form_Content"===p.endpoint&&!(0,o.isEmpty)(d)&&rankMath.contentAI.isContentAIPage&&wp.element.createElement(c.Button,{variant:(0,o.isEmpty)(d)?"secondary":"primary",className:"button",onClick:function(){return Jt(lt(d[0]),p.topic)}},wp.element.createElement("span",{className:"text"},rankMath.contentAI.isContentAIPage?(0,s.__)("Create New Post","rank-math"):(0,s.__)("Insert","rank-math"))))};function Yt(e){return Yt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Yt(e)}function Qt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function Xt(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Yt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==Yt(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Yt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function en(e){return function(e){if(Array.isArray(e))return an(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||nn(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||nn(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nn(e,t){if(e){if("string"==typeof e)return an(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?an(e,t):void 0}}function an(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var rn=function(e){var t=[(0,s.__)("Post Title","rank-math"),(0,s.__)("Post Outline","rank-math"),(0,s.__)("Write Post","rank-math")],n=e.endpoint;return wp.element.createElement("div",{className:"wizard-navigation"},wp.element.createElement("label",{className:"dot-label"},(0,s.__)("Steps","rank-math")),(0,o.map)(t,(function(e,t){var a="";return 0===t&&(a="active"),1===t&&(a="Blog_Post_Outline"===n||"Long_Form_Content"===n?"active":""),"Long_Form_Content"===n&&2===t&&(a="active"),wp.element.createElement(c.Button,{variant:"link",className:a,href:"#",label:e,showTooltip:!0},wp.element.createElement("span",null))})))},on=function(e){var t=e.tool,n=e.setTool,a=void 0!==n&&n,r=e.isContentAIPage,l=void 0!==r&&r,m=e.callApi,p=void 0!==m&&m,h=e.plan,g=t.title,f=t.icon,_=t.output,b=t.helpLink,w=t.endpoint,v=t.params,k=e.hasError,y=tn((0,u.useState)(Dt(v,_)),2),E=y[0],C=y[1],S=tn((0,u.useState)(),2),x=S[0],T=S[1],O=tn((0,u.useState)(),2),P=O[0],A=O[1],N=tn((0,u.useState)(!1),2),I=N[0],j=N[1],M=tn((0,u.useState)([]),2),R=M[0],D=M[1],L=tn((0,u.useState)([]),2),B=L[0],q=L[1],z=tn((0,u.useState)({endpoint:"Blog_Post_Idea"}),2),H=z[0],F=z[1],U=function(e){return(0,o.compact)((0,o.map)(rankMath.contentAI.history,(function(t){if(t.key===e)return t.output})))}(w),W=w,V="Blog_Post_Wizard"===w;if(V){k="free"===h||k,w="Long_Form_Content"===H.endpoint?"Blog_Post_Outline":H.endpoint;var $=(0,o.find)(Rt(),["endpoint",w]);"Blog_Post_Outline"!==w||(0,o.isEmpty)(H.content)||($.params.topic.default=H.content),v=$.params,_.default=1,(0,o.isEmpty)(E.main_points)&&(E=Dt(v,_))}(0,u.useEffect)((function(){if((0,o.isArray)(R)&&"Frequently_Asked_Questions"!==w){(0,o.isString)(B)&&q("");for(var e=0,t=function(t){if(t>0){var n=(0,o.isObject)(R[t-1])?Object.values(R[t-1]).join(" "):R[t-1];e+=110*n.split(" ").length}setTimeout((function(){return q((function(e){return[].concat(en(e),[R[t]])}))}),e)},n=0;n<=R.length-1;n++)t(n)}else q(R)}),[R]),(0,u.useEffect)((function(){p&&(T(!0),A(!0),j(!1),Be(w,E,G))}),[]);var G=function(e){(0,o.isEmpty)(e.error)?("Blog_Post_Wizard"===W&&q(""),D(e.faqs?e.faqs:e)):D('<div class="notice notice-error">'+e.error+"</div>"),T(!1),A(!1)},K=(0,u.useCallback)((function(e,t){E[e]=t,C(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Qt(Object(n),!0).forEach((function(t){Xt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Qt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},E)),(0,d.dispatch)("rank-math-content-ai").updateAIAttributes(e,t),setTimeout((function(){A(i()("form.rank-math-ai-tools").find(".limit-reached").length)}),500)}),[]);return wp.element.createElement(c.Modal,{className:"rank-math-contentai-modal rank-math-modal",overlayClassName:"rank-math-modal-overlay rank-math-contentai-modal-overlay",title:wp.element.createElement(React.Fragment,null,wp.element.createElement("i",{className:f})," ",g," ","Blog_Post_Wizard"===W?rn(H):""),shouldCloseOnClickOutside:!0,onRequestClose:function(e){return function(e,t,n,a){if((0,o.isNull)((0,d.dispatch)("rank-math"))||(0,d.dispatch)("rank-math-content-ai").isAutoCompleterOpen(!1),!(0,o.isUndefined)(e)){if("blur"===e.type){if(!(0,o.includes)(e.target.classList,"rank-math-contentai-modal"))return!1;var r=!0;return(0,o.forEach)(t,(function(e,t){e.isRequired&&!(0,o.isEmpty)(n[t])&&(r=!1)})),!!r&&Gt(a)}return"Escape"!==e.key||(0,o.isNull)(document.querySelector(".tagify__dropdown"))?Gt(a):(document.querySelector(".tagify__dropdown").remove(),!1)}}(e,v,E,a)}},wp.element.createElement("div",{className:k?"columns column-body blurred":"columns column-body"},wp.element.createElement("div",{className:"column column-input"},wp.element.createElement("div",{className:"column-inner"},tt(v,E,w,K,"Blog_Post_Wizard"===W),wp.element.createElement("p",{className:"required-fields"},wp.element.createElement("i",null,wp.element.createElement("span",null,"*")," ",(0,s.__)("Required fields.","rank-math")))),wp.element.createElement(Zt,{output:_,attributes:E,generating:x,results:R,endpoint:w,originalEndpoint:W,isDisabled:P,steps:H,apiContent:G,setSteps:F,onChange:K,setData:q,setResults:D,setGenerating:T,setDisabled:A,setHistory:j})),wp.element.createElement("div",{className:"column column-output"},wp.element.createElement("div",{className:"column-output-heading"},wp.element.createElement("h3",null,wp.element.createElement("span",null,(0,s.__)("Output","rank-math")),V&&wp.element.createElement("span",null," - ",(0,o.startCase)(H.endpoint))),!(0,o.isEmpty)(U)&&wp.element.createElement(c.Button,{className:"button button-secondary button-small output-history",onClick:function(){j(!I)}},(0,s.__)("History","rank-math"))),x&&wp.element.createElement("div",{className:"inner-wrapper"},wp.element.createElement("div",{className:"output-item loading"},wp.element.createElement("div",{className:"rank-math-loader"}))),!x&&!I&&(0,o.isEmpty)(R)&&wp.element.createElement(React.Fragment,null,wp.element.createElement("p",{style:{fontSize:"1rem",marginTop:0}},(0,s.__)("Suggestions will appear here.","rank-math")),b&&wp.element.createElement(Kt,{helpLink:b,title:g})),!I&&$t(B,l,w,!0,p),!x&&I&&$t((0,o.reverse)(U),l,w,!1,p),!I&&p&&!x&&wp.element.createElement("div",{className:"notice notice-info",dangerouslySetInnerHTML:{__html:(0,s.sprintf)((0,s.__)("%s to access all the Content AI tools","rank-math"),'<a href="'+rankMath.adminurl+'?page=rank-math-content-ai-page#ai-tools" target="_blank">'+(0,s.__)("Click here","rank-math")+"</a>")}}))),k&&wp.element.createElement(Pe,{width:60}))},sn=function(e){var t=e.search,n=e.setSearch,a=(0,u.useCallback)((function(e){var t=i()(".rank-math-content-ai-search-field input"),n=document.activeElement;if("/"===e.key&&t.length&&n!==t[0]&&(0,o.includes)(["BODY","DIV","BUTTON","SPAN"],n.tagName))return e.preventDefault(),t.trigger("focus"),!1}),[]);return(0,u.useEffect)((function(){return window.addEventListener("keydown",a),function(){window.removeEventListener("keydown",a)}}),[a]),wp.element.createElement("div",{className:"search-field"},wp.element.createElement(c.SearchControl,{value:t,className:"rank-math-content-ai-search-field",onChange:function(e){n(e)}}))};function ln(){return ln=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ln.apply(this,arguments)}function cn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return un(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return un(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function un(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var mn=function(e){var t=e.showMinimal,n=void 0!==t&&t,a=e.isContentAIPage,r=void 0!==a&&a,i=cn((0,u.useState)("all"),2),l=i[0],m=i[1],d=cn((0,u.useState)(),2),p=d[0],h=d[1],g=cn((0,u.useState)(),2),f=g[0],_=g[1],b={all:(0,s.__)("All","rank-math"),seo:(0,s.__)("SEO","rank-math"),blog:(0,s.__)("Blog","rank-math"),"marketing-sales":(0,s.__)("Marketing & Sales","rank-math"),ecommerce:(0,s.__)("eCommerce","rank-math"),misc:(0,s.__)("Misc","rank-math")};return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:e.hasError?"rank-math-ui module-listing blurred":"rank-math-ui module-listing"},!r&&wp.element.createElement(mt,null),wp.element.createElement("div",{className:"content-ai-header"},wp.element.createElement("div",{className:"content-ai-filter"},!r&&wp.element.createElement(c.SelectControl,{options:(0,o.map)(b,(function(e,t){return{value:t,label:e}})),onChange:function(e){return m(e)}}),!n&&r&&wp.element.createElement("div",null,(0,o.map)(b,(function(e,t){return wp.element.createElement(c.Button,{className:l===t?"active":"",key:t,onClick:function(){return m(t)}},e)}))),wp.element.createElement(sn,{search:p,setSearch:h}))),wp.element.createElement("div",{className:"grid"},(0,o.map)(Rt(),(function(e,t){if(("all"===l||l===e.category)&&(!p||(0,o.lowerCase)(e.title).includes((0,o.lowerCase)(p))||(0,o.lowerCase)(e.endpoint).includes((0,o.lowerCase)(p))))return wp.element.createElement(c.Button,{key:t,className:"rank-math-box",onClick:function(){_(e)}},wp.element.createElement("i",{className:e.endpoint+" ai-icon "+e.icon}),"Blog_Post_Wizard"===e.endpoint&&wp.element.createElement("div",{className:"dot-container",title:(0,s.__)("Multistep Wizard","rank-math")},wp.element.createElement("div",{className:"dot"}),wp.element.createElement("div",{className:"dot"}),wp.element.createElement("div",{className:"dot"})),wp.element.createElement("header",null,wp.element.createElement("h3",null,e.title),!n&&wp.element.createElement("p",null,e.description)))}))),f&&wp.element.createElement(on,ln({},e,{tool:f,setTool:_}))),e.hasError&&!r&&wp.element.createElement(Pe,null))};function dn(e){return dn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},dn(e)}function pn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function hn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?pn(Object(n),!0).forEach((function(t){gn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function gn(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==dn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==dn(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===dn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function fn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return _n(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _n(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var bn={all:(0,s.__)("All","rank-math"),recent:(0,s.__)("Recent","rank-math"),custom:(0,s.__)("Custom +","rank-math"),seo:(0,s.__)("SEO","rank-math"),blog:(0,s.__)("Blog","rank-math"),"marketing-sales":(0,s.__)("Marketing & Sales","rank-math"),ecommerce:(0,s.__)("eCommerce","rank-math"),misc:(0,s.__)("Misc","rank-math")},wn=function(e){var t=wp.data.select("rank-math-content-ai").getData(),n=e.isOpen,a=e.toggleModal,r=e.setMessage,i=e.updateData,l=fn((0,u.useState)(),2),m=l[0],d=l[1],p=fn((0,u.useState)("custom"),2),h=p[0],g=p[1],f=fn((0,u.useState)("all"),2),_=f[0],w=f[1],v=fn((0,u.useState)(t.prompts),2),y=v[0],E=v[1],C=fn((0,u.useState)(!1),2),S=C[0],x=C[1],T=fn((0,u.useState)(!1),2),O=T[0],P=T[1],A=fn((0,u.useState)(!1),2),N=A[0],I=A[1],j=fn((0,u.useState)(!1),2),M=j[0],R=j[1],D=fn((0,u.useState)({prompt_name:"",prompt:"",prompt_category:"custom"}),2),L=D[0],B=D[1];(0,u.useEffect)((function(){if("recent"!==_||(0,o.isEmpty)(t.recentPrompts))if("all"!==_){var e=(0,o.compact)((0,o.map)(t.prompts,(function(e){return e.PromptCategory===_&&!(0,o.isUndefined)(e.Prompt)&&e})));E("custom"===_?(0,o.reverse)(e):e)}else E(t.prompts);else{var n=(0,o.map)(t.recentPrompts,(function(e){return(0,o.find)(t.prompts,(function(t){return t.PromptName===e}))}));E(n)}}),[_,S]),(0,u.useEffect)((function(){setTimeout((function(){R(!1)}),5e3)}),[M]);var q="free"===t.plan?"blurred":"";return n&&wp.element.createElement(c.Modal,{title:(0,s.__)("Prompts Library","rank-math"),closeButtonLabel:(0,s.__)("Close","rank-math"),shouldCloseOnClickOutside:!1,shouldCloseOnEsc:!0,onRequestClose:function(){return a(!1)},className:"rank-math-contentai-modal rank-math-prompt-modal rank-math-modal",overlayClassName:"rank-math-modal-overlay rank-math-contentai-modal-overlay"},wp.element.createElement("div",{className:q},wp.element.createElement("div",{className:"content-ai-filter"},wp.element.createElement("div",null,(0,o.map)(bn,(function(e,t){var n=b()(t,{active:t===_});return wp.element.createElement(c.Button,{className:n,key:t,onClick:function(){w(t),g(0)}},e)}))),wp.element.createElement(c.Button,{className:"update-library is-secondary is-small",key:"update-library",onClick:function(){I(!0),Be("default_prompts",{},(function(e){k()({method:"POST",path:"/rankmath/v1/ca/savePrompts",data:{prompts:e}}).then((function(e){i("prompts",e),E(e),g(0),I(!1)})).catch((function(e){console.log(e),I(!1)}))}))}},N?(0,s.__)("Updating…","rank-math"):(0,s.__)("Update Library","rank-math"))),wp.element.createElement("div",{className:"grid"},wp.element.createElement("div",{className:"column column-first"},wp.element.createElement("h3",null,(0,s.__)("Prompt List","rank-math")),!(0,o.isEmpty)(y)&&wp.element.createElement(React.Fragment,null,wp.element.createElement(sn,{search:m,setSearch:d}),wp.element.createElement("div",{className:"prompt-list"},"all"===_&&wp.element.createElement(c.Button,{className:"custom"===h?"prompt-item active":"prompt-item",key:"add-custom",onClick:function(){return g("custom")}},wp.element.createElement("i",null,"🧪"),wp.element.createElement("span",null,(0,s.__)("Add Custom Prompt +","rank-math"))),(0,o.map)(y,(function(e,t){if(!((0,o.isUndefined)(e)||(0,o.isUndefined)(e.Prompt)||m&&!e.PromptName.toLowerCase().includes(m.toLowerCase()))){var n=b()("prompt-item "+t,{active:t===h,"custom-prompt":"custom"===e.PromptCategory}),a=b()("delete-prompt",{"rank-math-loader":S===t});return wp.element.createElement(c.Button,{className:n,key:t,onClick:function(){return g(t)}},wp.element.createElement("i",null,e.PromptIcon?e.PromptIcon:"🖌️"),wp.element.createElement("span",null,e.PromptName),"custom"===e.PromptCategory&&wp.element.createElement("span",{role:"button",tabIndex:"0",onKeyDown:void 0,className:a,onClick:function(){x(t),k()({method:"POST",path:"/rankmath/v1/ca/updatePrompt",data:{prompt:e.PromptName}}).then((function(e){i("prompts",e),g(0),x(!1)})).catch((function(e){x(!1),console.log(e)}))}},S!==t&&wp.element.createElement("i",{className:"dashicons dashicons-no-alt"})))}}))))),wp.element.createElement("div",{className:"column column-second"},"custom"===h&&"all"===_&&wp.element.createElement("div",{className:"custom-prompt-form"},wp.element.createElement("div",{className:"form-field"},wp.element.createElement(c.TextControl,{label:(0,s.__)("Prompt Name","rank-math"),onChange:function(e){L.prompt_name=e,B(hn({},L))},className:M&&!L.prompt_name?"is-required":""})),wp.element.createElement("div",{className:"form-field"},wp.element.createElement("div",{className:L.prompt.length>=2e3?"limit limit-reached":"limit"},wp.element.createElement("span",{className:"count"},L.prompt.length),"/2000"),wp.element.createElement(c.TextareaControl,{label:(0,s.__)("Prompt Text","rank-math"),help:(0,s.__)("Use [brackets] to insert placeholders.","rank-math"),onChange:function(e){L.prompt=e,B(hn({},L))},maxLength:"2000",className:M&&!L.prompt?"is-required":""})),wp.element.createElement("div",{className:"form-field"},wp.element.createElement(c.BaseControl,{className:"save-prompt"},wp.element.createElement(c.Button,{variant:"primary",className:"is-large",onClick:function(){if(L.prompt_name&&L.prompt){var e=(0,o.find)(y,(function(e){return e.PromptName===L.prompt_name}));(0,o.isUndefined)(e)?(P(!0),k()({method:"POST",path:"/rankmath/v1/ca/updatePrompt",data:{prompt:{PromptName:L.prompt_name,Prompt:L.prompt,PromptCategory:L.prompt_category}}}).then((function(e){t.prompts=e,i("prompts",e),w("custom"),setTimeout((function(){g(0),P(!1)}),300)})).catch((function(e){console.log(e),P(!1)}))):alert((0,s.__)("Prompt with this name already exists","rank-math"))}else R(!0)}},O&&wp.element.createElement("span",{className:"rank-math-loader"}),!O&&(0,s.__)("Save Prompt","rank-math"))))),("custom"!==h||"all"!==_)&&!(0,o.isEmpty)(y)&&!(0,o.isEmpty)(y[h])&&wp.element.createElement("div",{className:"prompt-details"},wp.element.createElement("div",{className:"prompt-preview"},wp.element.createElement("h3",null,(0,s.__)("Preview","rank-math")),wp.element.createElement("div",{className:"prompt-preview-content"},wp.element.createElement("p",{dangerouslySetInnerHTML:{__html:(0,o.isUndefined)(y[h].Prompt)?"":y[h].Prompt.replaceAll("[","<span>[").replaceAll("]","]</span>")}}))),wp.element.createElement("div",{className:"form-field"},wp.element.createElement(c.BaseControl,{className:"use-prompt"},wp.element.createElement(c.Button,{variant:"primary",className:"is-large",onClick:function(){k()({method:"POST",path:"/rankmath/v1/ca/updateRecentPrompt",data:{prompt:y[h].PromptName}}).then((function(){t.recentPrompts.unshift(y[h].PromptName),i("recentPrompts",t.recentPrompts)})).catch((function(e){console.log(e)})),r(y[h].Prompt.replaceAll("[","<span>[").replaceAll("]","]</span>")),a(!1)}},(0,s.__)("Use Prompt","rank-math")))))))),q&&wp.element.createElement(Pe,{width:"60",isResearch:!0}))},vn=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;k()({method:"POST",path:"/rankmath/v1/ca/deleteOutput",data:{isChat:e,index:t}}).catch((function(e){console.log(e)}))};function kn(){return kn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},kn.apply(this,arguments)}function yn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return En(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return En(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function En(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var Cn=function(e){var t=e.data,n=e.isContentAIPage,a=void 0!==n&&n,r="free"===t.plan,l=yn((0,u.useState)(!1),2),m=l[0],d=l[1],p=yn((0,u.useState)(!1),2),h=p[0],g=p[1],f=yn((0,u.useState)(""),2),_=f[0],w=f[1],v=yn((0,u.useState)(t.chats),2),k=v[0],y=v[1],E=yn((0,u.useState)(r&&k.length?0:""),2),C=E[0],S=E[1];r&&k.length&&(0,o.isUndefined)(wp.blocks.getBlockType("core/paragraph"))&&wp.blockLibrary.registerCoreBlocks(),(0,u.useEffect)((function(){i()(".chat-input span").on("click",(function(e){if(window.getSelection&&document.createRange){var t=window.getSelection();""===t.toString()&&window.setTimeout((function(){var n=e.target,a=document.createRange();a.selectNodeContents(n),t.removeAllRanges(),t.addRange(a)}),1)}}))}),[_]);var x,T=["How do backlinks affect SEO?","Why is keyword research important for SEO?","What are some effective SEO strategies for small businesses?","Can you explain the difference between on-page and off-page SEO?","List trending topics in [Industry] that I should write about.","How can I optimize my website for local search queries?","What are some effective strategies for managing [product/service description] reputation on social media?","Develop a content strategy for [product/service description] to increase organic search traffic."],O=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];w(""),g(!0),""===C&&S(0);var a=""===C,r=""!==C?C:t.chats.length,i=(0,o.isEmpty)(k[r])?[]:k[r];n||i.unshift({role:"user",content:e||_}),a?k.unshift(i):k[r]=i,y(k),setTimeout((function(){Be("Chat",{messages:(0,o.reverse)(i),session:r,isNew:a,regenerate:n,site_type:"ecommerce",site_name:rankMath.blogName,language:t.language,choices:1},(function(e){(0,o.reverse)(i),e&&i.unshift({role:"assistant",content:e[0],isNew:!0}),g(!1)}),!0)}),100)};return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:e.hasError?"blurred":""},wp.element.createElement("div",{className:"tab-header"},wp.element.createElement("span",{className:"tab-header-title"},wp.element.createElement("i",{className:"rm-icon rm-icon-bot"})," RankBot ",wp.element.createElement("span",null,"- ",(0,s.__)("Your Personal Assistant","rank-math"))),wp.element.createElement("a",{href:"https://rankmath.com/kb/how-to-use-rankbot-ai-tool/?play-video=OBxuy8u0eCY&utm_source=Plugin&utm_medium=RankBot+Tab&utm_campaign=WP",rel:"noreferrer",target:"_blank",title:(0,s.__)("Know more about RankBot","rank-math")},wp.element.createElement("em",{className:"dashicons-before dashicons-editor-help rank-math-tooltip"})),!a&&""!==C&&wp.element.createElement(c.Button,{className:"clear-history is-small button-link-delete",onClick:function(){k.splice(C,1),y(k),vn(!0,C),setTimeout((function(){S("")}),10)}},(0,s.__)("Delete Session","rank-math"))),!a&&wp.element.createElement(mt,null),wp.element.createElement("div",{className:"rank-math-content-chat-page"},!(0,o.isEmpty)(k)&&wp.element.createElement("div",{className:"chat-sidebar"},wp.element.createElement("div",{className:"chat-sidebar-content"},!a&&wp.element.createElement(c.SelectControl,{value:C,options:(x=[],r&&k.length||x.push({label:(0,s.__)("New Chat","rank-math"),value:""}),(0,o.map)(k,(function(e,t){x.push({label:e[0].content.replace(/(<([^>]+)>)/gi,"").split(/\s+/).slice(0,8).join(" ")+"...",value:t})})),x),onChange:function(e){S(e)}}),a&&!r&&wp.element.createElement(c.Button,{className:b()("history-button button new-chat",{active:""===C}),onClick:function(){S("")}},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"})," ",(0,s.__)("New Chat","rank-math")),a&&(0,o.map)(k,(function(e,t){var n=e.length>2?e[e.length-2].content:e[0].content;return wp.element.createElement("div",{role:"button",tabIndex:"0",className:b()("history-button button",{active:C===t}),key:t,onClick:function(){S(t)},onKeyDown:void 0},wp.element.createElement("i",{className:"rm-icon rm-icon-comments"}),n.split(/\s+/).slice(0,8).join(" ")+"...",wp.element.createElement(c.Button,{className:"delete-session",onClick:function(){k.splice(t,1),y(k),vn(!0,t),setTimeout((function(){S("")}),10)},title:(0,s.__)("Delete Session","rank-math")},wp.element.createElement("i",{className:"dashicons dashicons-no-alt"})))})),a&&r&&wp.element.createElement(c.Button,{className:"button is-green",href:"https://rankmath.com/content-ai/#pricing",target:"_blank"},(0,s.__)("Buy PRO plan for Multiple Sessions","rank-math")))),wp.element.createElement("div",{className:"chat-container"},wp.element.createElement("div",{className:"chat-messages"},h&&wp.element.createElement("div",{className:"chat-message loading"},wp.element.createElement("div",{className:"rank-math-loader"})),""===C&&wp.element.createElement((function(){return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"prompt-examples"},wp.element.createElement("h2",null,(0,s.__)("Examples","rank-math")),wp.element.createElement("p",null,(0,s.__)("Here are some examples of questions you can ask RankBot","rank-math")),wp.element.createElement("div",{className:"grid"},(0,o.map)(T,(function(e,t){return wp.element.createElement("div",{role:"button",tabIndex:"0",onClick:function(){w(e.replaceAll("[","<span>[").replaceAll("]","]</span>"))},key:t,dangerouslySetInnerHTML:{__html:e.replaceAll("[","<span>[").replaceAll("]","]</span>")}})})))))}),null),!(0,o.isEmpty)(k)&&""!==C&&(0,o.map)(k[C],(function(e,t){if(!(0,o.isEmpty)(e.content)){var n="user"===e.role,a=n?(0,s.__)("You:","rank-math"):(0,s.__)("RankBot:","rank-math"),r=(0,o.uniqueId)(),i=e.isNew;return e.isNew=!1,wp.element.createElement("div",{className:n?"chat-message user":"chat-message",key:t},wp.element.createElement("div",{className:"message-actions"},wp.element.createElement("span",null,a),!n&&wp.element.createElement(Ut,{value:e.content})),wp.element.createElement("div",{className:"message",id:"block-"+r},i&&wp.element.createElement(qt,{value:e.content,showWordCount:!1}),!i&&wp.element.createElement("div",{dangerouslySetInnerHTML:{__html:lt(e.content)}})))}}))),(!r||!C)&&wp.element.createElement("div",{className:"chat-input"},wp.element.createElement("div",{className:"chat-input-actions"},wp.element.createElement(ft.RichText,{tagName:"div",className:"chat-input-textarea",value:_.slice(0,2e3),allowedFormats:[],disableLineBreaks:!0,onChange:function(e){var t=document.getElementsByClassName("chat-input-textarea")[0];if(e.length>2e3){e=e.slice(0,2e3),t.innerHTML=_;var n=document.createRange(),a=window.getSelection(),r=t.childNodes[t.childNodes.length-1];n.setStart(r,r.textContent.length),n.collapse(!0),a.removeAllRanges(),a.addRange(n)}w(e)},onKeyUp:function(e){if("Enter"===e.key){if(e.shiftKey){w(_+"\n");var t=window.getSelection(),n=t.getRangeAt(0),a=document.createElement("br");n.insertNode(a);var r=a.nextSibling,i=document.createRange();return i.setStart(r,0),i.collapse(!0),t.removeAllRanges(),void t.addRange(i)}(0,o.isEmpty)((0,o.trim)(_))||h||O()}},preserveWhiteSpace:"true",placeholder:(0,s.__)("Type your message here…","rank-math")}),wp.element.createElement("div",{className:"chat-input-buttons"},wp.element.createElement(c.Button,{className:"prompts-button button",onClick:function(){return d(!0)}},wp.element.createElement("i",{className:"rm-icon rm-icon-htaccess"})," ",a?(0,s.__)("Prompts Library","rank-math"):""),wp.element.createElement(wn,kn({},e,{isOpen:m,toggleModal:d,setMessage:w})),""!==C&&!h&&wp.element.createElement(c.Tooltip,{text:(0,s.__)("Regenerate Response","rank-math")},wp.element.createElement(c.Button,{className:"regenerate-response button button-small",onClick:function(){var e=k[C],t=e[1].content;e.shift(),k[C]=e,y(k),O(t,!0)},showTooltip:!0},wp.element.createElement(c.Dashicon,{icon:"controls-repeat"}))),wp.element.createElement("div",{className:_.length>=2e3?"limit limit-reached":"limit"},wp.element.createElement("span",{className:"count"},_.length),"/",(0,s.__)("2000","rank-math")),wp.element.createElement(c.Button,{className:"button is-primary is-large","aria-label":(0,s.__)("Send","rank-math"),disabled:(0,o.isEmpty)((0,o.trim)(_))||h,onClick:function(){return O()}},wp.element.createElement("span",{className:"rm-icon rm-icon-send"})))))))),e.hasError&&!a&&wp.element.createElement(Pe,null))};function Sn(e){return Sn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Sn(e)}function xn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function Tn(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Sn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==Sn(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Sn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function On(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Pn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Pn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Pn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var An=(0,w.compose)((0,d.withSelect)((function(e){var t=e("rank-math-content-ai").getData();return{data:t,hasError:!t.isUserRegistered||!t.plan||!t.credits||t.isMigrating}})),(0,d.withDispatch)((function(e){return{updateData:function(t,n){e("rank-math-content-ai").updateData(t,n)}}})))((function(e){var t,n,a=On((0,u.useState)(""),2),r=a[0],i=a[1],l=e.data,m=b()("rank-math-tooltip update-credits",{loading:r});return wp.element.createElement(c.PanelBody,{className:"rank-math-content-ai-wrapper",initialOpen:!0},rankMath.contentAI.isUserRegistered&&wp.element.createElement("div",{className:"rank-math-ca-credits"},wp.element.createElement(c.Button,{className:m,onClick:function(){i(!0),k()({method:"POST",path:"/rankmath/v1/ca/getCredits"}).catch((function(e){i(""),alert(e.message)})).then((function(t){t.error?alert(t.error):e.updateData("credits",t),i("")}))}},wp.element.createElement("i",{className:"dashicons dashicons-image-rotate"}),wp.element.createElement("span",null,(0,s.__)("Click to refresh the available credits.","rank-math"))),wp.element.createElement("span",null,(0,s.__)("Credits","rank-math"))," ",wp.element.createElement("strong",{title:l.credits},(n=l.credits,Math.abs(n)>999?Math.sign(n)*(Math.abs(n)/1e3).toFixed(1)+"k":Math.sign(n)*Math.abs(n))),wp.element.createElement("a",{href:y("content-ai-credits-usage","Sidebar Credits Tooltip Icon"),rel:"noreferrer",target:"_blank",id:"rank-math-help-icon",title:(0,s.__)("Know more about credits.","rank-math")},"﹖")),wp.element.createElement(J,{className:"rank-math-tabs",activeClass:"is-active",tabs:(t=[{name:"content-ai",title:wp.element.createElement(u.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-analyzer",title:(0,s.__)("Research","rank-math")}),wp.element.createElement("span",null,(0,s.__)("Research","rank-math"))),view:je,className:"rank-math-general-tab"}],"divi"!==rankMath.currentEditor&&t.push({name:"write",title:wp.element.createElement(u.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-edit",title:(0,s.__)("Write","rank-math")}),wp.element.createElement("span",null,(0,s.__)("Write","rank-math"))),view:Mt,className:"rank-math-write-tab"}),t.push({name:"ai-tools",title:wp.element.createElement(u.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-page",title:(0,s.__)("AI Tools","rank-math")}),wp.element.createElement("span",null,(0,s.__)("AI Tools","rank-math"))),view:mn,className:"rank-math-ai-tools-tab"},{name:"chat",title:wp.element.createElement(u.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-comments",title:(0,s.__)("Chat","rank-math")}),wp.element.createElement("span",null,(0,s.__)("Chat","rank-math"))),view:Cn,className:"rank-math-chat-tab"}),"free"===rankMath.contentAI.plan&&(t.push(t[0]),t=(0,o.tail)(t)),t)},(function(t){return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"rank-math-tab-content-"+t.name},(0,u.createElement)(t.view,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?xn(Object(n),!0).forEach((function(t){Tn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):xn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},e))))})))})),Nn=wp.keyboardShortcuts;function In(){return In=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},In.apply(this,arguments)}function jn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Mn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Mn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Mn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var Rn=function e(t){var n="";return t.forEach((function(t){"rank-math/command"!==t.name&&t.attributes&&at(t)&&(n+=at(t)+" "),t.innerBlocks&&t.innerBlocks.length>0&&(n+=e(t.innerBlocks))})),n},Dn=function(){var e=(0,d.useSelect)((function(){return(0,d.select)("core/editor").getEditedPostContent()})),t=Rn((0,d.select)("core/block-editor").getBlocks());return wp.element.createElement("div",{className:"actions-wrapper"},wp.element.createElement(Ut,{value:t,disabled:!t,label:(0,s.__)("Copy as Text","rank-math")}),wp.element.createElement(Ut,{disabled:!t,value:e,label:(0,s.__)("Copy as Blocks","rank-math"),onClick:!0}),wp.element.createElement(c.Button,{disabled:!t,onClick:function(){var e=(0,d.select)("core/block-editor").getBlocks(),t=wp.blocks.createBlock("rank-math/command",{content:"",className:"rank-math-content-ai-command"});e.push(t),Jt((0,Me.serialize)(e))},className:"button is-secondary is-small"},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"})," ",(0,s.__)("Create New Post","rank-math")))},Ln=function(e){var t=jn((0,u.useState)(!1),2),n=t[0],a=t[1],r=jn((0,u.useState)(!0),2),i=r[0],o=r[1];return wp.element.createElement("div",{className:n?"wp-block-column interface-interface-skeleton__sidebar has-collapsed":"wp-block-column interface-interface-skeleton__sidebar"},n&&wp.element.createElement(c.Button,{className:"collapsed",onClick:function(){return a(!1)},icon:"align-pull-right",title:(0,s.__)("Expand Sidebar","rank-math")}),!n&&wp.element.createElement("div",{className:"interface-complementary-area edit-post-sidebar rank-math-tabs"},wp.element.createElement("div",{role:"tablist","aria-orientation":"horizontal",className:"components-panel__header interface-complementary-area-header"},wp.element.createElement(c.Button,{className:i?"is-active":"",onClick:function(){return o(!0)}},wp.element.createElement("i",{className:"rm-icon rm-icon-edit",title:(0,s.__)("Write","rank-math")}),wp.element.createElement("span",null,(0,s.__)("Write","rank-math"))),wp.element.createElement(c.Button,{className:i?"":"is-active",onClick:function(){return o(!1)}},wp.element.createElement("i",{className:"rm-icon rm-icon-page",title:(0,s.__)("AI Tools","rank-math")}),wp.element.createElement("span",null,(0,s.__)("AI Tools","rank-math"))),wp.element.createElement(c.Button,{onClick:function(){return a(!0)},icon:"no-alt",title:(0,s.__)("Collapse Sidebar","rank-math")})),wp.element.createElement("div",{className:i?"rank-math-content-ai-wrapper rank-math-tab-content-write":"rank-math-content-ai-wrapper rank-math-tab-content-write rank-math-tab-content-tools"},wp.element.createElement("div",{className:"rank-math-tab-content-ai-tools"},i&&wp.element.createElement(Mt,e),!i&&wp.element.createElement(mn,In({},e,{showMinimal:!0,isContentAIPage:!1}))))))},Bn=function(e){return setTimeout((function(){var e=document.getElementById("editor2").dataset;wp.editPost.initializeEditor("editor","rm_content_editor",e.postId,JSON.parse(e.settings),{}),(0,d.select)("core/edit-post").isFeatureActive("fullscreenMode")&&(0,d.dispatch)("core/edit-post").toggleFeature("fullscreenMode")}),200),wp.element.createElement(Nn.ShortcutProvider,null,wp.element.createElement("div",{className:"wp-block-columns rank-math-content-ai-wrapper",id:"rank-math-content-editor-page"},wp.element.createElement("div",{className:"wp-block-column"},wp.element.createElement(Dn,null),wp.element.createElement("div",{id:"editor"})),wp.element.createElement(Ln,e)))};function qn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return zn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return zn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var Hn=function(e){var t=qn((0,u.useState)(e.data.history),2),n=t[0],a=t[1];return wp.element.createElement("div",{className:"history-container"},wp.element.createElement("div",{className:"tab-header"},wp.element.createElement("span",{className:"tab-header-title"},wp.element.createElement("span",{className:"rm-icon rm-icon-dataset"})," ",(0,s.__)("AI History","rank-math")),!(0,o.isEmpty)(n)&&wp.element.createElement(c.Button,{className:"button clear-history is-small is-link button-link-delete",onClick:function(){vn(!1),a([])}},(0,s.__)("Clear History","rank-math"))),wp.element.createElement("div",{className:"inner-wrapper"},!(0,o.isEmpty)(n)&&(0,o.map)(n,(function(e,t){var n=function(e,t){if(!(0,o.isObject)(e))return lt(e);var n="";return"Frequently_Asked_Questions"===t?((0,o.map)(e,(function(e){n+="<h4>"+e.question+"</h4><span>"+e.answer+"</span>"})),n):((0,o.map)(e,(function(e,t){n+="<h4>"+(0,o.startCase)(t)+"</h4><span>"+e+"</span>"})),lt(n))}(e.output,e.key);if(!(0,o.isUndefined)(n))return wp.element.createElement("div",{className:"output-item",key:t},wp.element.createElement("div",{className:"tool-name"},e.key),wp.element.createElement("div",{className:"output-actions"},wp.element.createElement(Ut,{value:n})),wp.element.createElement("div",{className:"word-count"},(0,s.__)("Words:","rank-math")," ",n.split(" ").length),wp.element.createElement("div",{className:"content",dangerouslySetInnerHTML:{__html:n}}))})),(0,o.isEmpty)(n)&&wp.element.createElement("div",{className:"no-output"},(0,s.__)("No AI History found.","rank-math"))))};function Fn(e){return Fn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Fn(e)}function Un(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function Wn(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Fn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==Fn(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Fn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Vn=(0,w.compose)((0,d.withSelect)((function(e,t){return{data:t,hasError:!t.isUserRegistered||!t.plan||!t.credits||t.isMigrating}})),(0,d.withDispatch)((function(e){return{updateData:function(t,n){e("rank-math-content-ai").updateData(t,n)}}})))((function(e){var t=window.location.hash?window.location.hash.replace("#",""):"ai-tools";return(0,u.useEffect)((function(){!e.hasError&&"content-editor"!==t&&(0,o.isUndefined)(wp.blocks.getBlockType("core/paragraph"))&&wp.blockLibrary.registerCoreBlocks()}),[]),wp.element.createElement(React.Fragment,null,wp.element.createElement(c.TabPanel,{className:"rank-math-tabs",activeClass:"is-active",tabs:[{name:"ai-tools",id:"ai-tools",title:(0,s.__)("AI Tools","rank-math"),view:mn,className:"rank-math-ai-tools-tab rank-math-tab"},{name:"content-editor",id:"content-editor",title:(0,s.__)("Content Editor","rank-math"),view:Bn,className:"rank-math-content-editor-tab rank-math-tab"},{name:"chat",id:"chat",title:(0,s.__)("Chat","rank-math"),view:Cn,className:"rank-math-chat-tab rank-math-tab"},{name:"history",id:"history",title:(0,s.__)("History","rank-math"),view:Hn,className:"rank-math-history-tab rank-math-tab"}],initialTabName:t,onSelect:function(e){window.location.hash=e}},(function(t){var n=e.hasError&&"history"!==t.id?" blurred":"";return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"rank-math-tab-content dashboard-wrapper rank-math-tab-content-"+t.name+n},wp.element.createElement(mt,{isContentAIPage:"true"}),(0,u.createElement)(t.view,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Un(Object(n),!0).forEach((function(t){Wn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Un(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},e))),n&&wp.element.createElement(Pe,{width:"40"}))})))}));function $n(e){return $n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$n(e)}function Gn(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,(r=a.key,i=void 0,i=function(e,t){if("object"!==$n(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==$n(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===$n(i)?i:String(i)),a)}var r,i}var Kn=function(){function e(){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),rankMath.contentAI.isUserRegistered){var t=(0,d.select)("rank-math-content-ai").getData();this.ca_keyword=t.keyword,this.data=t.researchedData,this.init=this.init.bind(this),this.contentLength=this.contentLength.bind(this),this.contentLengthBoundary=this.contentLengthBoundary.bind(this),this.keywordDensity=this.keywordDensity.bind(this),this.removeTests=this.removeTests.bind(this),this.researchesTests=rankMath.assessor.researchesTests,this.ca_keyword=wp.data.select("rank-math-content-ai").getData().keyword,(0,l.addAction)("rank_math_loaded","rank-math",this.init),(0,l.addAction)("rank_math_keyword_refresh","rank-math",this.init),(0,l.addAction)("rank_math_content_ai_changed","rank-math",this.init)}}var t,n,a;return t=e,(n=[{key:"init",value:function(e){e&&(this.ca_keyword=e),this.keyword=rankMathEditor.assessor.getPrimaryKeyword(),(0,o.isEmpty)(this.data)||(this.filters(),this.removeTests(),e&&rankMathEditor.refresh("content"))}},{key:"removeTests",value:function(){if(this.ca_keyword===this.keyword){var e=this.data.recommendations;rankMath.assessor.researchesTests=(0,o.filter)(rankMath.assessor.researchesTests,(function(t){return!(!e.mediaCount&&"contentHasAssets"===t||!e.linkCount.internal&&"linksHasInternal"===t||!(e.linkCount.external||"linksHasExternals"!==t&&"linksNotAllExternals"!==t))}))}else rankMath.assessor.researchesTests=this.researchesTests}},{key:"filters",value:function(){(0,l.addFilter)("rankMath_analysis_contentLength","rank-math",this.contentLength),(0,l.addFilter)("rankMath_analysis_contentLength_boundaries","rank-math",this.contentLengthBoundary),(0,l.addFilter)("rankMath_analysis_keywordDensity","rank-math",this.keywordDensity)}},{key:"contentLength",value:function(e){if(this.ca_keyword!==this.keyword)return e;var t=this.data.recommendations.wordCount,n=(0,o.ceil)(150*t/100);return{hasScore:e.hasScore,failed:(0,s.sprintf)((0,s.__)("Content is %1$s words long. Consider using at least %2$s words.","rank-math"),"%1$d",t),tooltipText:(0,s.sprintf)((0,s.__)("Minimum recommended content length should be %1$d words.","rank-math"),t),emptyContent:(0,s.sprintf)((0,s.__)("Content should be %1$s long.","rank-math"),'<a href="https://rankmath.com/kb/score-100-in-tests/?utm_source=Plugin&utm_campaign=WP#overall-content-length" target="_blank">'+t+" - "+n+(0,s.__)(" words","rank-math")+"</a>")}}},{key:"contentLengthBoundary",value:function(e){if(this.ca_keyword!==this.keyword)return e;var t=this.data.recommendations.wordCount,n=(0,o.ceil)(150*t/100);return{recommended:{boundary:n,score:8},belowRecommended:{boundary:(0,o.ceil)(n/2),score:5},low:{boundary:t,score:2}}}},{key:"keywordDensity",value:function(e,t){if((0,o.isEmpty)(this.data.keywords.content)||(0,o.isEmpty)(this.data.keywords.content[this.keyword]))return e;var n=t/this.data.keywords.content[this.keyword].average*100;return n>80?1.1:100<n?2.5:80<n?1.1:50<n?.8:.5}}])&&Gn(t.prototype,n),a&&Gn(t,a),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Jn(e){return Jn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Jn(e)}function Zn(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Jn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==Jn(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Jn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Yn=(0,d.withSelect)((function(e){return{isLoaded:e("rank-math").isLoaded(),score:e("rank-math-content-ai").getScore()}}))((function(e){var t=e.isLoaded,n=e.score,a="free"===rankMath.contentAI.plan,r=b()("rank-math-toolbar-score content-ai-score",Zn(Zn(Zn({},O(n),!0),"loading",!t),"is-free",a));return wp.element.createElement("div",{className:r},a&&wp.element.createElement("span",{className:"rank-math-free-badge"},(0,s.__)("Free","rank-math")),wp.element.createElement("i",{className:"rm-icon rm-icon-content-ai"}),wp.element.createElement("span",{className:"content-ai-score"},n," / 100"))}));const Qn=(0,u.forwardRef)((({className:e,isPressed:t,...n},a)=>{const r={...n,className:b()(e,{"is-pressed":t})||void 0,"aria-hidden":!0,focusable:!1};return(0,u.createElement)("svg",{...r,ref:a})}));Qn.displayName="SVG";var Xn=(0,Ee.createElement)(Qn,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,Ee.createElement)((e=>(0,u.createElement)("path",e)),{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"}));function ea(e){var t;return e=e.replace(/,/g,""),e=ae(e),t=e,e=(0,o.isUndefined)(t)?"":(0,o.flow)([Y,X,Z,ee,te])(t)}var ta=function(e){var t=(0,d.select)("core/block-editor").getBlocks().map((function(e){return e.clientId}));(0,d.dispatch)("core/block-editor").replaceBlocks(t,e)},na=function(e,t){e=e.replaceAll("\n","");var n=ra(e),a=ra(t),r=[];(0,o.forEach)(a,(function(e){(0,o.includes)(n,e)||r.push(e)}));var i=t;return(0,o.forEach)(r,(function(e){var n=aa(t,e);i=i.replace(n,'<mark class="rank-math-highlight" style="background-color: #fee894">'.concat(n,"</mark>"))})),i},aa=function(e,t){var n,a=(null===(n=e.replace(/<!\x2D\x2D[\s\S]*?\x2D\x2D>/g,"").match(/<([a-z0-9]+)([^>]*)>([\s\S]*?)<\/\1>/i))||void 0===n?void 0:n[3])||"",r=(0,o.trim)(a.replace(/<[^>]*>/g,"").toLowerCase().replace(/\s+/g," ")),i=(0,o.trim)(t.toLowerCase().replace(/\s+/g," ")),s=r.indexOf(i);if(-1===s)return null;for(var l=0,c="",u=0;u<a.length;u++){var m=a[u];if("<"===m){for(;u<a.length&&">"!==a[u];)c+=a[u++];c+=a[u]}else if(l>=s&&l<s+i.length&&(c+=m),++l>=s+i.length)break}return(0,o.trim)(c)},ra=function(e){var t=(e=(0,o.toLower)(e.replace(/<[^>]*>/g,""))).split(/(?<!\d)\.(?!\d)|[!?]|\n/)||[e];return(0,o.compact)((0,o.map)(t,(function(e){return(0,o.trim)(e)})))},ia=wp.element.createElement("svg",{width:"16",height:"17",viewBox:"0 0 16 17",fill:"none",xmlns:"http://www.w3.org/2000/svg"},wp.element.createElement("g",{clipPath:"url(#clip0_3091_13042)"},wp.element.createElement("path",{d:"M12 14.5L13 16.5L14 14.5L16 13.5L14 12.5L13 10.5L12 12.5L10 13.5L12 14.5ZM12.8887 3.61133L13.6667 5.16667L14.4447 3.61133L16 2.83333L14.4447 2.05533L13.6667 0.5L12.8887 2.05533L11.3333 2.83333L12.8887 3.61133Z",fill:"url(#paint0_linear_3091_13042)"}),wp.element.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.50336 10.33L6.66669 14.6574L8.83003 10.33L13.1574 8.16669L8.83003 6.00336L6.66669 1.67603L4.50336 6.00336L0.176025 8.16669L4.50336 10.33ZM3.30691 8.16669L5.54688 9.2865L6.66669 11.5265L7.78651 9.2865L10.0265 8.16669L7.78651 7.04688L6.66669 4.80691L5.54688 7.04688L3.30691 8.16669Z",fill:"url(#paint1_linear_3091_13042)"})),wp.element.createElement("defs",null,wp.element.createElement("linearGradient",{id:"paint0_linear_3091_13042",x1:"14.684",y1:"1.60572",x2:"6.35101",y2:"6.48577",gradientUnits:"userSpaceOnUse"},wp.element.createElement("stop",{stopColor:"#DA4892"}),wp.element.createElement("stop",{offset:"1",stopColor:"#6C4D90"})),wp.element.createElement("linearGradient",{id:"paint1_linear_3091_13042",x1:"10.3101",y1:"2.57313",x2:"3.26935",y2:"13.5686",gradientUnits:"userSpaceOnUse"},wp.element.createElement("stop",{stopColor:"#DA4892"}),wp.element.createElement("stop",{offset:"1",stopColor:"#6C4D90"})),wp.element.createElement("clipPath",{id:"clip0_3091_13042"},wp.element.createElement("rect",{width:"16",height:"16",fill:"white",transform:"translate(0 0.5)"})))),oa=wp.element.createElement("svg",{width:"16",height:"10",viewBox:"0 0 16 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},wp.element.createElement("path",{d:"M15.1499 0.350024C14.9249 0.125024 14.5749 0.125024 14.3499 0.350024L5.84994 8.60003L1.64994 4.47502C1.42494 4.25002 1.07494 4.27502 0.849942 4.47502C0.624942 4.70002 0.649942 5.05002 0.849942 5.27502L5.27494 9.57502C5.42494 9.72502 5.62494 9.80002 5.84994 9.80002C6.07494 9.80002 6.24994 9.72502 6.42494 9.57502L15.1499 1.10002C15.3749 0.925024 15.3749 0.575024 15.1499 0.350024Z",fill:"white"})),sa=wp.element.createElement("svg",{width:"14",height:"16",viewBox:"0 0 14 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},wp.element.createElement("path",{d:"M13.55 6.69923C13.45 6.39923 13.15 6.24923 12.85 6.32423C12.55 6.42423 12.4 6.72423 12.475 7.02423C12.65 7.57423 12.75 8.14923 12.75 8.72423C12.75 9.59923 12.55 10.4242 12.175 11.1992C11.2 13.1992 9.225 14.4492 7 14.4492C3.825 14.4492 1.25 11.8742 1.25 8.74923C1.25 5.62423 3.825 3.04923 7 3.04923C7.575 3.04923 8.15 3.12423 8.675 3.29923L7.825 3.69923C7.55 3.82423 7.425 4.17423 7.55 4.44923C7.65 4.64923 7.85 4.77423 8.05 4.77423C8.125 4.77423 8.2 4.74923 8.275 4.72423L10.475 3.69923C10.6 3.62423 10.725 3.52423 10.775 3.37423C10.825 3.22423 10.825 3.07423 10.75 2.94923L9.75 0.749227C9.625 0.474227 9.275 0.349227 9 0.474227C8.725 0.599227 8.6 0.949227 8.725 1.22423L9.2 2.27423C8.5 2.02423 7.75 1.89923 6.975 1.89923C3.225 1.92423 0.125 4.97423 0.125 8.74923C0.125 12.5242 3.225 15.5742 7 15.5742C9.65 15.5742 12.025 14.0992 13.175 11.7242C13.625 10.7992 13.875 9.79923 13.875 8.74923C13.875 8.04923 13.75 7.34923 13.55 6.69923Z",fill:"#017CBA"})),la=wp.element.createElement("svg",{width:"14",height:"12",viewBox:"0 0 14 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},wp.element.createElement("path",{d:"M7.8687 6.06869L12.9591 0.978287C13.1841 0.753287 13.1841 0.403287 12.9591 0.178287C12.7341 -0.0467133 12.3841 -0.0467133 12.1591 0.178287L7.0687 5.26869L1.97829 0.178271C1.75329 -0.0467285 1.40329 -0.0467285 1.17829 0.178271C0.953288 0.403271 0.953288 0.753271 1.17829 0.978271L6.2687 6.06869L1.1098 11.0276C0.884797 11.2526 0.884797 11.6026 1.1098 11.8276C1.2098 11.9276 1.3598 12.0026 1.5098 12.0026C1.6598 12.0026 1.8098 11.9526 1.9098 11.8276L7.0687 6.86869L12.0277 11.8276C12.1277 11.9276 12.2777 12.0026 12.4277 12.0026C12.5777 12.0026 12.7277 11.9526 12.8277 11.8276C13.0527 11.6026 13.0527 11.2526 12.8277 11.0276L7.8687 6.06869Z",fill:"#757575"})),ca=wp.element.createElement("svg",{width:"16",height:"14",viewBox:"0 0 16 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},wp.element.createElement("path",{d:"M14.1999 2.09994C13.3499 1.24994 12.2499 0.774944 11.0749 0.749944C9.89991 0.724944 8.79991 1.14994 7.97491 1.97494L3.82491 6.12494L2.59991 4.89994C2.32491 4.62494 1.89991 4.52494 1.52491 4.64994C1.14991 4.77494 0.874909 5.12494 0.824908 5.52494L0.449908 9.14994C0.399908 9.47494 0.524909 9.77494 0.749909 9.99994C0.949908 10.1999 1.19991 10.2999 1.47491 10.2999C1.49991 10.2999 1.54991 10.2999 1.59991 10.2999L5.22491 9.89994C5.62491 9.84994 5.94991 9.59994 6.09991 9.19994C6.24991 8.79994 6.14991 8.39994 5.84991 8.12494L4.62491 6.89994L8.77491 2.74994C9.37491 2.14994 10.1749 1.84994 11.0499 1.87494C11.9249 1.89994 12.7749 2.27494 13.3999 2.89994C14.7249 4.22494 14.7749 6.29994 13.5499 7.52494L8.77491 12.2999C8.54991 12.5249 8.54991 12.8749 8.77491 13.0999C8.87491 13.1999 9.02491 13.2749 9.17491 13.2749C9.32491 13.2749 9.47491 13.2249 9.57491 13.0999L14.3499 8.32494C16.0249 6.64994 15.9499 3.84994 14.1999 2.09994ZM1.57491 9.17494L1.92491 5.82494L4.89991 8.79994L1.57491 9.17494Z",fill:"#017CBA"})),ua=wp.element.createElement("svg",{width:"12",height:"12",viewBox:"0 0 11 11",fill:"none",xmlns:"http://www.w3.org/2000/svg"},wp.element.createElement("path",{d:"M10.4423 7.38225C10.3238 7.32181 10.1863 7.31037 10.0594 7.35038C9.93248 7.39039 9.82639 7.47865 9.76397 7.59614C9.3934 8.30474 8.84109 8.90208 8.16364 9.32693C7.48619 9.75179 6.70796 9.98889 5.90871 10.0139C5.10945 10.039 4.3179 9.85107 3.61518 9.46946C2.91247 9.08785 2.32384 8.52627 1.90963 7.84226C1.49543 7.15824 1.27053 6.3764 1.25799 5.57685C1.24545 4.7773 1.44572 3.98879 1.83828 3.29213C2.23083 2.59546 2.80156 2.0157 3.49197 1.61224C4.18237 1.20878 4.96764 0.996145 5.76729 0.996115C5.95529 0.996161 6.14309 1.00841 6.32951 1.03278C6.43098 0.663682 6.66627 0.345602 6.98951 0.140556C6.58876 0.0474722 6.17871 0.000316554 5.76729 0C4.79357 0.00377237 3.83831 0.265979 2.99912 0.759827C2.15992 1.25368 1.46688 1.96146 0.990796 2.81086C0.514716 3.66026 0.272668 4.62083 0.289385 5.59441C0.306102 6.56799 0.580986 7.51967 1.08595 8.35223C1.59091 9.18478 2.30785 9.86836 3.1635 10.3331C4.01916 10.7979 4.98285 11.0271 5.95612 10.9974C6.9294 10.9678 7.87735 10.6803 8.70311 10.1643C9.52888 9.6483 10.2029 8.92234 10.6562 8.06059C10.7166 7.94206 10.7281 7.80453 10.6881 7.67765C10.6481 7.55076 10.5598 7.44467 10.4423 7.38225Z",fill:"#949494"}),wp.element.createElement("path",{d:"M10.4423 7.38225C10.3238 7.32181 10.1863 7.31037 10.0594 7.35038C9.93248 7.39039 9.82639 7.47865 9.76397 7.59614C9.3934 8.30474 8.84109 8.90208 8.16364 9.32693C7.48619 9.75179 6.70796 9.98889 5.90871 10.0139C5.10945 10.039 4.3179 9.85107 3.61518 9.46946C2.91247 9.08785 2.32384 8.52627 1.90963 7.84226C1.49543 7.15824 1.27053 6.3764 1.25799 5.57685C1.24545 4.7773 1.44572 3.98879 1.83828 3.29213C2.23083 2.59546 2.80156 2.0157 3.49197 1.61224C4.18237 1.20878 4.96764 0.996145 5.76729 0.996115C5.95529 0.996161 6.14309 1.00841 6.32951 1.03278C6.43098 0.663682 6.66627 0.345602 6.98951 0.140556C6.58876 0.0474722 6.17871 0.000316554 5.76729 0C4.79357 0.00377237 3.83831 0.265979 2.99912 0.759827C2.15992 1.25368 1.46688 1.96146 0.990796 2.81086C0.514716 3.66026 0.272668 4.62083 0.289385 5.59441C0.306102 6.56799 0.580986 7.51967 1.08595 8.35223C1.59091 9.18478 2.30785 9.86836 3.1635 10.3331C4.01916 10.7979 4.98285 11.0271 5.95612 10.9974C6.9294 10.9678 7.87735 10.6803 8.70311 10.1643C9.52888 9.6483 10.2029 8.92234 10.6562 8.06059C10.7166 7.94206 10.7281 7.80453 10.6881 7.67765C10.6481 7.55076 10.5598 7.44467 10.4423 7.38225Z",fill:"url(#paint0_linear_3105_12223)"}),wp.element.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.96062 4.45502C4.7077 4.47078 4.47112 4.58534 4.30192 4.77398C4.13272 4.96262 4.04445 5.21022 4.05617 5.46335C4.0445 5.71587 4.13295 5.96278 4.30231 6.15045C4.47166 6.33811 4.70823 6.45136 4.96062 6.46558C5.08587 6.45932 5.20865 6.42841 5.32193 6.37463C5.43522 6.32084 5.53678 6.24523 5.6208 6.15213C5.70481 6.05903 5.76963 5.95027 5.81155 5.83208C5.85347 5.71389 5.87165 5.58858 5.86506 5.46335C5.8784 5.20981 5.7907 4.96132 5.62117 4.77232C5.45164 4.58332 5.21411 4.46922 4.96062 4.45502Z",fill:"#949494"}),wp.element.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.96062 4.45502C4.7077 4.47078 4.47112 4.58534 4.30192 4.77398C4.13272 4.96262 4.04445 5.21022 4.05617 5.46335C4.0445 5.71587 4.13295 5.96278 4.30231 6.15045C4.47166 6.33811 4.70823 6.45136 4.96062 6.46558C5.08587 6.45932 5.20865 6.42841 5.32193 6.37463C5.43522 6.32084 5.53678 6.24523 5.6208 6.15213C5.70481 6.05903 5.76963 5.95027 5.81155 5.83208C5.85347 5.71389 5.87165 5.58858 5.86506 5.46335C5.8784 5.20981 5.7907 4.96132 5.62117 4.77232C5.45164 4.58332 5.21411 4.46922 4.96062 4.45502Z",fill:"url(#paint1_linear_3105_12223)"}),wp.element.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.41952 2.87834C8.22664 2.9615 8.01843 3.00314 7.80841 3.00057C7.46879 2.9948 7.14053 2.87736 6.87433 2.66639C6.60813 2.45542 6.41881 2.16266 6.33562 1.83334C6.17075 1.82403 6.00549 1.82403 5.84062 1.83334C4.99143 1.83403 4.1688 2.12946 3.51318 2.66918C2.85757 3.2089 2.40961 3.95946 2.2458 4.7927C2.08199 5.62595 2.21247 6.49022 2.61498 7.23797C3.01748 7.98571 3.66705 8.57057 4.45278 8.89268C5.23851 9.21479 6.11169 9.25419 6.92324 9.00415C7.73479 8.75411 8.43439 8.23013 8.9026 7.52168C9.37082 6.81322 9.57861 5.96421 9.4905 5.1196C9.4024 4.27499 9.02386 3.48714 8.41952 2.89057V2.87834ZM6.70229 6.90558C6.70229 7.02714 6.654 7.14372 6.56805 7.22967C6.48209 7.31563 6.36551 7.36392 6.24396 7.36392C6.1224 7.36392 6.00582 7.31563 5.91986 7.22967C5.83391 7.14372 5.78562 7.02714 5.78562 6.90558V6.86891C5.51487 7.13559 5.15118 7.28676 4.77117 7.29058C4.31972 7.25465 3.90074 7.04183 3.60545 6.69847C3.31015 6.3551 3.16245 5.90899 3.1945 5.45724C3.16407 5.00587 3.31234 4.56063 3.60733 4.21763C3.90231 3.87463 4.32033 3.66138 4.77117 3.6239C5.14665 3.63505 5.5034 3.79045 5.76729 4.05779C5.77296 3.93623 5.82669 3.82191 5.91665 3.73997C6.00662 3.65802 6.12545 3.61517 6.24701 3.62085C6.36857 3.62652 6.48289 3.68025 6.56484 3.77021C6.64678 3.86018 6.68963 3.97901 6.68396 4.10057L6.70229 6.90558ZM8.26674 6.90558C8.26674 7.02714 8.21845 7.14372 8.1325 7.22967C8.04654 7.31563 7.92996 7.36392 7.80841 7.36392C7.68685 7.36392 7.57027 7.31563 7.48431 7.22967C7.39836 7.14372 7.35007 7.02714 7.35007 6.90558V4.10057C7.35007 3.97901 7.39836 3.86243 7.48431 3.77648C7.57027 3.69052 7.68685 3.64224 7.80841 3.64224C7.92996 3.64224 8.04654 3.69052 8.1325 3.77648C8.21845 3.86243 8.26674 3.97901 8.26674 4.10057V6.90558Z",fill:"#949494"}),wp.element.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.41952 2.87834C8.22664 2.9615 8.01843 3.00314 7.80841 3.00057C7.46879 2.9948 7.14053 2.87736 6.87433 2.66639C6.60813 2.45542 6.41881 2.16266 6.33562 1.83334C6.17075 1.82403 6.00549 1.82403 5.84062 1.83334C4.99143 1.83403 4.1688 2.12946 3.51318 2.66918C2.85757 3.2089 2.40961 3.95946 2.2458 4.7927C2.08199 5.62595 2.21247 6.49022 2.61498 7.23797C3.01748 7.98571 3.66705 8.57057 4.45278 8.89268C5.23851 9.21479 6.11169 9.25419 6.92324 9.00415C7.73479 8.75411 8.43439 8.23013 8.9026 7.52168C9.37082 6.81322 9.57861 5.96421 9.4905 5.1196C9.4024 4.27499 9.02386 3.48714 8.41952 2.89057V2.87834ZM6.70229 6.90558C6.70229 7.02714 6.654 7.14372 6.56805 7.22967C6.48209 7.31563 6.36551 7.36392 6.24396 7.36392C6.1224 7.36392 6.00582 7.31563 5.91986 7.22967C5.83391 7.14372 5.78562 7.02714 5.78562 6.90558V6.86891C5.51487 7.13559 5.15118 7.28676 4.77117 7.29058C4.31972 7.25465 3.90074 7.04183 3.60545 6.69847C3.31015 6.3551 3.16245 5.90899 3.1945 5.45724C3.16407 5.00587 3.31234 4.56063 3.60733 4.21763C3.90231 3.87463 4.32033 3.66138 4.77117 3.6239C5.14665 3.63505 5.5034 3.79045 5.76729 4.05779C5.77296 3.93623 5.82669 3.82191 5.91665 3.73997C6.00662 3.65802 6.12545 3.61517 6.24701 3.62085C6.36857 3.62652 6.48289 3.68025 6.56484 3.77021C6.64678 3.86018 6.68963 3.97901 6.68396 4.10057L6.70229 6.90558ZM8.26674 6.90558C8.26674 7.02714 8.21845 7.14372 8.1325 7.22967C8.04654 7.31563 7.92996 7.36392 7.80841 7.36392C7.68685 7.36392 7.57027 7.31563 7.48431 7.22967C7.39836 7.14372 7.35007 7.02714 7.35007 6.90558V4.10057C7.35007 3.97901 7.39836 3.86243 7.48431 3.77648C7.57027 3.69052 7.68685 3.64224 7.80841 3.64224C7.92996 3.64224 8.04654 3.69052 8.1325 3.77648C8.21845 3.86243 8.26674 3.97901 8.26674 4.10057V6.90558Z",fill:"url(#paint2_linear_3105_12223)"}),wp.element.createElement("path",{d:"M7.85729 2.33445C8.34668 2.33445 8.74341 1.93773 8.74341 1.44834C8.74341 0.958951 8.34668 0.562224 7.85729 0.562224C7.36791 0.562224 6.97118 0.958951 6.97118 1.44834C6.97118 1.93773 7.36791 2.33445 7.85729 2.33445Z",fill:"#949494"}),wp.element.createElement("path",{d:"M7.85729 2.33445C8.34668 2.33445 8.74341 1.93773 8.74341 1.44834C8.74341 0.958951 8.34668 0.562224 7.85729 0.562224C7.36791 0.562224 6.97118 0.958951 6.97118 1.44834C6.97118 1.93773 7.36791 2.33445 7.85729 2.33445Z",fill:"url(#paint3_linear_3105_12223)"}),wp.element.createElement("defs",null,wp.element.createElement("linearGradient",{id:"paint0_linear_3105_12223",x1:"8.42545",y1:"0.760183",x2:"2.33068",y2:"9.77897",gradientUnits:"userSpaceOnUse"},wp.element.createElement("stop",{stopColor:"#DA4892"}),wp.element.createElement("stop",{offset:"1",stopColor:"#6C4D90"})),wp.element.createElement("linearGradient",{id:"paint1_linear_3105_12223",x1:"8.42545",y1:"0.760183",x2:"2.33068",y2:"9.77897",gradientUnits:"userSpaceOnUse"},wp.element.createElement("stop",{stopColor:"#DA4892"}),wp.element.createElement("stop",{offset:"1",stopColor:"#6C4D90"})),wp.element.createElement("linearGradient",{id:"paint2_linear_3105_12223",x1:"8.42545",y1:"0.760183",x2:"2.33068",y2:"9.77897",gradientUnits:"userSpaceOnUse"},wp.element.createElement("stop",{stopColor:"#DA4892"}),wp.element.createElement("stop",{offset:"1",stopColor:"#6C4D90"})),wp.element.createElement("linearGradient",{id:"paint3_linear_3105_12223",x1:"8.42545",y1:"0.760183",x2:"2.33068",y2:"9.77897",gradientUnits:"userSpaceOnUse"},wp.element.createElement("stop",{stopColor:"#DA4892"}),wp.element.createElement("stop",{offset:"1",stopColor:"#6C4D90"}))));function ma(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return da(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return da(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function da(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var pa=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=t.id,r=t.previousResults;if((0,o.includes)(["keywordInTitle","titleStartWithKeyword","titleSentiment","titleHasNumber","titleHasPowerWords"],a)&&(e=!e&&n?t.seo_title:e,(0,d.dispatch)("rank-math").updateSerpTitle(e),(0,d.dispatch)("rank-math").updateTitle(e),(0,d.dispatch)("rank-math").toggleSnippetEditor(!0)),"keywordInMetaDescription"===a&&(e=!e&&n?t.description:e,(0,d.dispatch)("rank-math").updateSerpDescription(e),(0,d.dispatch)("rank-math").updateDescription(e),(0,d.dispatch)("rank-math").toggleSnippetEditor(!0)),"keywordInPermalink"===a&&(e=!e&&n?t.url:e,(0,d.dispatch)("rank-math").updatePermalink(e),rankMathEditor.updatePermalink(ea(e),!0),rankMathEditor.updatePermalinkSanitize(ea(e)),(0,d.dispatch)("rank-math").toggleSnippetEditor(!0)),t.isContentTest){if(!e&&n)return void ta(t.blocks);ta(t.blocks),function(e,t){var n=(0,d.dispatch)("core/block-editor"),a=n.replaceBlock,r=n.removeBlock;(0,o.forEach)(e,(function(e){var n=e.action,i=e.refBlockId,s=e.content,l=e.position,c=(0,o.find)(t,(function(e){return e.clientId===i}));if(c)switch(n){case"replace":s=na((0,Me.serialize)(c),s),a(i,(0,Me.rawHandler)({HTML:s}));break;case"delete":r(i);break;case"insert":var u=t.indexOf(c);if(-1===u)return;u="before"===l?u:u+1,s=na("",s);var m=(0,Me.rawHandler)({HTML:s});(0,o.forEach)(m,(function(e){(0,d.dispatch)("core/block-editor").insertBlock(e,u),u++}));break;default:console.warn("Unknown action: ".concat(n))}else console.warn("Block with ID ".concat(i," not found."))}))}(JSON.parse(e),t.blocks)}n||(r.push(e),(0,d.dispatch)("rank-math-content-ai").updatePreviousResults(r))},ha=function(e){return function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,o.forEach)(t,(function(t){if(t.innerBlocks&&t.innerBlocks.length>0)e(t.innerBlocks,n);else{var a=(0,Me.serialize)(t);(0,o.trim)(a.replace(/<!\x2D\x2D[\s\S]*?\x2D\x2D>/g,"").replace(/<[^>]*>/g,""))&&(n[t.clientId]=a.replace(/<!\x2D\x2D[\s\S]*?\x2D\x2D>/g,""))}})),n}(e)},ga=(0,w.compose)((0,d.withSelect)((function(e,t){var n=t.id,a=t.data,r=t.seoTitle,i=t.seoDescription,s=t.keyword,l=t.blocks,c=e("rank-math-content-ai").getPreviousResults();return{id:n,keyword:s,title:a.title,seo_title:r,url:a.slug,description:i,excerpt:(0,o.isEmpty)(a.excerpt)?"":a.excerpt,content:g()?e("core/editor").getEditedPostContent():a.content,blocks:l,content_blocks:g()?ha(l):"",previousResults:c,isSnippetEditorOpen:e("rank-math").isSnippetEditorOpen(),isContentTest:(0,o.includes)(["keywordIn10Percent","keywordInContent","lengthContent","keywordInSubheadings","keywordInImageAlt","keywordDensity","contentHasShortParagraphs"],n)}})),(0,d.withDispatch)((function(e,t){return{reject:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=t.previousResults;pa(e?n[0]:"",t,!0)},removeSnackBar:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&pa("",t,!0),e("rank-math-content-ai").updatePreviousResults([]),document.getElementById("rank-math-content-ai-snackbar").remove()}}})))((function(e){var t=e.id,n=e.previousResults,a=e.isSnippetEditorOpen,r=e.isContentTest,i=e.reject,l=e.removeSnackBar,m=ma((0,u.useState)(n.length),2),d=m[0],p=m[1],h=ma((0,u.useState)(!1),2),g=h[0],f=h[1],_=ma((0,u.useState)(!1),2),b=_[0],w=_[1],v=!(0,o.isBoolean)(b),k=function(){var a=(0,o.includes)(["keywordInTitle","titleStartWithKeyword","titleSentiment","titleHasNumber","titleHasPowerWords","keywordInPermalink","lengthContent","keywordDensity"],t),r=(0,o.includes)(["keywordIn10Percent","keywordInContent","lengthContent","keywordInSubheadings","keywordDensity","contentHasShortParagraphs"],t),i="\x3c!-- SCRIPT REMOVED --\x3e";w(!0),Be("FIX_SEO_TESTS",{test_type:t,focus_keyword:e.keyword,post_title:e.title,post_seo_title:e.seo_title,post_seo_description:e.description,post_permalink:e.url,post_content:a?X(e.content,i):null,content_blocks:r?(0,o.mapValues)(e.content_blocks,(function(e){return X(e,i)})):null,post_excerpt:X(e.excerpt,i),previous_results:n},(function(t){(0,o.isEmpty)(t.error)?(w(!1),t=t[0],pa(t,e,!1),p(n.length)):w(t.error)}))},y=function(t){pa(n[t-1],e,!0),p(t),f(!1)};return(0,u.useEffect)((function(){k()}),[]),wp.element.createElement("div",null,!0===b&&wp.element.createElement("div",{className:"snackbar-loader"},wp.element.createElement("span",null),wp.element.createElement("span",null),wp.element.createElement("span",null),wp.element.createElement("span",null)),(!b||v)&&wp.element.createElement("div",{className:"snackbar-content"},wp.element.createElement("div",{className:"snackbar-header"},wp.element.createElement("h2",{className:"main-title"},(0,s.__)("Content AI","rank-math")," ",wp.element.createElement("span",null,"•")," ",a?(0,s.__)("Snippet Editor","rank-math"):(0,s.__)("Post Editor","rank-math")),wp.element.createElement(c.Button,{size:"small",className:"close-snackbar",onClick:function(){return l(!0)},icon:Xn,label:(0,s.__)("Close","rank-math")})),wp.element.createElement("div",{className:"snackbar-body"},wp.element.createElement("div",{className:"pagination-container"},wp.element.createElement("h4",null,ia,(0,s.__)("Generated Content","rank-math")),n.length>1&&wp.element.createElement("div",{className:"snackbar-pagination"},d>1&&wp.element.createElement(c.Button,{size:"small",className:"previous",onClick:function(){return y(d-1)},label:(0,s.__)("Previous","rank-math")}),wp.element.createElement("span",null,d," / ",n.length),d<n.length&&wp.element.createElement(c.Button,{size:"small",className:"next",onClick:function(){return y(d+1)},label:(0,s.__)("Next","rank-math")}))),wp.element.createElement("p",{className:"desc"},v&&b,!v&&(0,s.__)("The highlighted content has been generated by AI for your review. Carefully evaluate the suggested changes and use the options below to accept or reject them.","rank-math")),!v&&wp.element.createElement("div",{className:"snackbar-actions"},wp.element.createElement(c.Button,{variant:"secondary",className:"approve-content",onClick:function(){r&&function(e){if(e){e=e.replace(/<mark class="rank-math-highlight" style="background-color: #fee894">(.+?)<\/mark>/g,"$1");var t=(0,Me.rawHandler)({HTML:e});ta(t)}}(e.content),l()}},oa,(0,s.__)("Approve","rank-math")),wp.element.createElement(c.Button,{variant:"secondary",className:"regenerate-content",onClick:function(){r&&pa("",e,!0),k()}},sa,(0,s.__)("Regenerate","rank-math")),!g&&wp.element.createElement(c.Button,{variant:"secondary",className:"reject-content",onClick:function(){i(),f(!0)}},la,(0,s.__)("Reject","rank-math")),g&&wp.element.createElement(c.Button,{variant:"secondary",className:"undo-content",onClick:function(){i(!0),f(!1)}},ca,(0,s.__)("Undo","rank-math"))))))})),fa=function(){return!rankMath.contentAI.isUserRegistered||!rankMath.contentAI.plan||!rankMath.contentAI.credits||rankMath.contentAI.isMigrating},_a=function(e){var t=e.showProNotice,n=void 0!==t&&t,a=e.isBulkEdit,r=void 0!==a&&a,s=e.creditsRequired,l=void 0===s?0:s,m=e.isKeywordIntent,d=void 0!==m&&m;(0,o.isNull)(document.getElementById("rank-math-content-ai-modal-wrapper"))&&i()("#wpwrap").append('<div id="rank-math-content-ai-modal-wrapper"></div>'),setTimeout((function(){(0,u.render)(wp.element.createElement(c.Modal,{className:"rank-math-contentai-modal rank-math-modal rank-math-error-modal",shouldCloseOnClickOutside:!1,onRequestClose:function(e){(0,o.isUndefined)(e)||(i()(".components-modal__screen-overlay").remove(),document.getElementById("rank-math-content-ai-modal-wrapper").remove(),document.body.classList.remove("modal-open"))}},wp.element.createElement(Pe,{width:100,showProNotice:n,isBulkEdit:r,creditsRequired:l,isKeywordIntent:d})),document.getElementById("rank-math-content-ai-modal-wrapper"))}),100)};function ba(e){return function(e){if(Array.isArray(e))return wa(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return wa(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return wa(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wa(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var va=function e(t){return(0,o.isEmpty)(t)?t:t.flatMap((function(t){return[t].concat(ba(e(t.innerBlocks||[])))}))},ka=function(e){var t=e.id,n=e.result;if(!function(e,t){if(t.hasScore()||"post"!==rankMath.objectType)return!1;var n=["keywordIn10Percent","keywordInContent","lengthContent","keywordInSubheadings","keywordDensity","contentHasShortParagraphs"];return(!(0,o.includes)(n,e)||"gutenberg"===rankMath.currentEditor)&&((0,o.includes)(["keywordInTitle","keywordInMetaDescription","keywordInPermalink","titleStartWithKeyword","titleSentiment","titleHasNumber","titleHasPowerWords"],e)||(0,o.includes)(n,e))}(t,n))return null;var a=rankMathEditor.assessor.analyzer.researcher.paper,r=a.getKeyword();return(0,o.isEmpty)(r)&&(0,o.includes)((0,o.toLower)(t),"keyword")?null:wp.element.createElement(c.Button,{variant:"link",className:"rank-math-ai-button",showTooltip:!0,tooltipPosition:"top",label:(0,s.__)("Fix with AI","rank-math"),onClick:function(){fa()?_a({showProNotice:!1}):function(e){var t=document.createElement("div");t.id="rank-math-content-ai-snackbar",document.body.appendChild(t);var n=rankMathEditor.assessor.dataCollector.getData(),a=wp.data.select("rank-math"),r=a.getKeywords().split(",");(0,u.createRoot)(document.getElementById(t.id)).render(wp.element.createElement(ga,{id:e,seoTitle:a.getSerpTitle(),seoDescription:a.getSerpDescription(),keyword:r[0],data:n,blocks:va(wp.data.select("core/block-editor").getBlocks())}))}(t)}},ua,(0,s.__)("Fix with AI","rank-math"))},ya=function(e){return new Promise((function(t,n){var a=wp.data.select("rank-math-content-ai").getData();fetch(a.url+"generate_image_alt",{method:"POST",body:JSON.stringify({images:[e],username:a.connectData.username,api_key:a.connectData.api_key,site_url:a.connectData.site_url,plugin_version:rankMath.version}),headers:{"Content-Type":"application/json"}}).then((function(e){return e.json()})).then((function(a){if(a.altTexts&&a.altTexts[e])return t(a.altTexts[e]),void function(e){if(!(0,o.isUndefined)(e.credits)){var t=e.credits;(0,o.isEmpty)(t)||(t=(t=t.available-t.taken)<0?0:t,k()({method:"POST",path:"/rankmath/v1/ca/updateCredits",data:{credits:t}}).then((function(){wp.data.dispatch("rank-math-content-ai").updateData("credits",t),i()(".credits-remaining").length&&i()(".credits-remaining strong").text(t)})))}}(a);n((0,s.__)("Failed to generate alt text.","rank-math"))})).catch((function(e){n(e)}))}))};function Ea(e){return Ea="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ea(e)}function Ca(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function Sa(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ca(Object(n),!0).forEach((function(t){xa(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ca(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function xa(e,t,n){return(t=Oa(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ta(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,Oa(a.key),a)}}function Oa(e){var t=function(e,t){if("object"!==Ea(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==Ea(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ea(t)?t:String(t)}var Pa=function(){function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.injectGenerateAltTextButton(),i()(window).on("elementor:init",(function(){t.injectGenerateAltTextButton()}))}var t,n,a;return t=e,n=[{key:"injectGenerateAltTextButton",value:function(){var e=this;void 0!==wp.media.view.Attachment.Details.TwoColumn&&(wp.media.view.Attachment.Details.TwoColumn=wp.media.view.Attachment.Details.TwoColumn.extend({template:function(t){return e.getTemplate("attachment-details-two-column",t,!0)},events:Sa(Sa({},wp.media.view.Attachment.Details.TwoColumn.prototype.events),{},{"click .rank-math-generate-alt-button":this.generateAltTextForImage})})),"classic"===rankMath.currentEditor&&(wp.media.view.ImageDetails=wp.media.view.ImageDetails.extend({template:function(t){return e.getTemplate("image-details",t)},events:Sa(Sa({},wp.media.view.ImageDetails.prototype.events),{},{"click .rank-math-generate-alt-button":this.generateAltTextForImage})})),wp.media.view.Attachment.Details=wp.media.view.Attachment.Details.extend({template:function(t){return e.getTemplate("attachment-details",t)},events:Sa(Sa({},wp.media.view.Attachment.Details.prototype.events),{},{"click .rank-math-generate-alt-button":this.generateAltTextForImage})})}},{key:"getTemplate",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=wp.media.template(e)(t),r=document.createElement("div");if(r.innerHTML=a,!r.querySelector("#alt-text-description"))return a;var i='<button class="rank-math-generate-alt-button" data-two-column="'+n+'"><i class="rm-icon rm-icon-content-ai"></i>'+(0,s.__)("Generate Alt","rank-math")+"</button><br />",o=r.querySelector("#alt-text-description");return o.innerHTML=i+o.innerHTML,r.innerHTML}},{key:"generateAltTextForImage",value:function(e){var t=this;if(e.preventDefault(),e.stopPropagation(),fa()||wp.data.select("rank-math-content-ai").getData().credits<50)return i()(".media-modal-close").trigger("click"),void _a({creditsRequired:50});var n=this.model.attributes.url;if(n){var a=e.currentTarget,r=a.innerHTML;a.disabled=!0,a.innerHTML=(0,s.__)("Generating…","rank-math");var o="true"===a.getAttribute("data-two-column");ya(n).then((function(e){t.model.set("alt",e),"classic"!==rankMath.currentEditor&&t.model.save();var n=o?"#attachment-details-two-column-alt-text":"#attachment-details-alt-text",i=document.querySelector(n);i&&(i.value=e),a.innerHTML=r})).catch((function(e){console.error(e),a.classList.add("failed"),a.innerHTML=(0,s.__)("Failed","rank-math"),setTimeout((function(){a.classList.remove("failed"),a.innerHTML=r}),3e3)})).finally((function(){a.disabled=!1}))}}}],n&&Ta(t.prototype,n),a&&Ta(t,a),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Aa=Pa,Na=function(e){var t=wp.data.select("rank-math-content-ai").getContentAiAttributes();return e||(e=(0,o.isEmpty)(t.instructions)?"":t.instructions),{document_title:"undefined"!=typeof rankMathEditor?rankMathEditor.assessor.dataCollector.getData().title:"",text:ct(),instructions:e,tone:(0,o.isEmpty)(t.tone)?rankMath.contentAI.tone:t.tone,focus_keyword:(0,o.isEmpty)(t.focus_keyword)?"":t.focus_keyword,length:(0,o.isEmpty)(t.length)?"medium":t.length,choices:1}},Ia=function(){(0,Nn.useShortcut)("rank-math-contentai-write",(0,u.useCallback)((function(){var e=nt(),t=(0,Me.createBlock)("rank-math/command");(0,o.isEmpty)(e.block)||at(e.block)?(0,d.dispatch)("core/block-editor").insertBlock(t,e.position):(0,d.dispatch)("core/block-editor").replaceBlock(e.clientId,t),Ot("Write",Na(),t.clientId)}),[]));var e=(0,d.useSelect)((function(e){return e(Nn.store).getShortcutKeyCombination("rank-math-contentai-write")}),[]);if(!(0,o.isNull)(e))return null;(0,(0,d.useDispatch)(Nn.store).registerShortcut)({name:"rank-math-contentai-write",category:"global",keyCombination:{modifier:"ctrl",character:"/"}})},ja=function(e){i()(e.target).parent().attr("data-mce-selected",!1);var t=i()(e.target).parents("p"),n=document.createElement("p");n.textContent=(0,s.__)("Generating…","rank-math");var a=t.html();t.before(n),Be("Text_Summarizer",{text:a,language:rankMath.contentAI.language,format:"paragraph",choices:1},(function(e){n.textContent=e[0]}))};function Ma(e,t){return{type:"RANK_MATH_APP_UI",key:e,value:t}}function Ra(e){return Ra="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ra(e)}function Da(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function La(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Ra(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==Ra(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ra(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ba(e){if(!e)return Ma("isAutoCompleterOpen",e);var t=(0,d.select)("core/block-editor").getSelectedBlock();(0,o.isNull)(t)||(0,(0,d.dispatch)(ft.store).removeBlock)(t.clientId);return Ma("isAutoCompleterOpen",e)}function qa(e,t){var n=(0,d.select)("rank-math-content-ai").getContentAiAttributes();return n[e]="language"!==e||t?t:rankMath.contentAI.language,Ma("contentAiAttributes",n)}function za(e,t){var n=(0,d.select)("rank-math-content-ai").getData();return n[e]=t,Ma("data",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Da(Object(n),!0).forEach((function(t){La(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Da(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},n))}function Ha(e){return Ma("previousResults",e)}function Fa(e){return Fa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Fa(e)}function Ua(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function Wa(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ua(Object(n),!0).forEach((function(t){Va(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ua(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Va(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Fa(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==Fa(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Fa(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var $a={isAutoCompleterOpen:!1,contentAiAttributes:{},data:rankMath.contentAI,previousResults:[]};function Ga(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:$a,t=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===t.type?Wa(Wa({},e),{},Va({},t.key,t.value)):e}function Ka(e){return e.appUi.isAutoCompleterOpen}function Ja(e){return e.appUi.contentAiAttributes}function Za(e){return e.appUi.data}function Ya(e){var t=e.appUi.data.score;return(0,o.isEmpty)(Object.values(t))?0:(t=(0,o.map)(Object.values(t),o.toNumber),(0,o.round)((0,o.sum)(t)/t.length))}function Qa(e){return e.appUi.previousResults}(0,d.register)((0,d.createReduxStore)("rank-math-content-ai",{reducer:(0,d.combineReducers)(t),selectors:a,actions:e}));var Xa=wp.richText,er=function(e){var t=e.width,n=void 0===t?40:t,a=rankMath.contentAI.isUserRegistered,r=rankMath.contentAI.plan,i=rankMath.contentAI.credits>0,o=rankMath.contentAI.isMigrating;if(a&&r&&i&&!o)return null;var l="width-"+n;return a&&r?o?wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{style:{textAlign:"center"},className:"rank-math-cta-box less-padding top-20 "+l},wp.element.createElement("h3",null,(0,s.__)("Server Maintenance Underway","rank-math")),wp.element.createElement("p",null,(0,s.__)("We are working on improving your Content AI experience. Please wait for 5 minutes and then refresh to start using the optimized Content AI. If you see this for more than 5 minutes, please ","rank-math"),wp.element.createElement("a",{href:"https://rankmath.com/support/",target:"_blank",rel:"noreferrer"},(0,s.__)("reach out to the support team.","rank-math")),(0,s.__)(" We are sorry for the inconvenience.","rank-math")))):wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box less-padding top-20 "+l},wp.element.createElement("h3",null,(0,s.__)("⛔️ Content AI Credit Alert!","rank-math")),wp.element.createElement("p",null,(0,s.__)("Your monthly Content AI credits have been fully utilized. To continue enjoying seamless content creation, simply click the button below to upgrade your plan and access more credits.","rank-math")),wp.element.createElement(c.Button,{href:"https://rankmath.com/kb/how-to-use-content-ai/?play-video=ioPeVIntJWw&utm_source=Plugin&utm_medium=Buy+Credits+Button&utm_campaign=WP",className:"button button-primary is-green",target:"_blank"},(0,s.__)("Learn More","rank-math")))):wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center rank-math-content-ai-warning-wrapper"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks top-20 less-padding "+l},wp.element.createElement("h3",null,(0,s.__)("🚀 Supercharge Your Content With AI","rank-math")),wp.element.createElement("p",null,!a&&(0,s.__)("Start using Content AI by connecting your RankMath.com Account","rank-math"),a&&!r&&(0,s.__)("To access this Content AI feature, you need to have an active subscription plan.","rank-math")),function(e){return 40===e?wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,s.__)("1-Click SEO Content","rank-math")),wp.element.createElement("li",null,(0,s.__)("1-Click SEO Meta","rank-math")),wp.element.createElement("li",null,(0,s.__)("40+ Specialized AI Tools","rank-math")),wp.element.createElement("li",null,(0,s.__)("1-Click Competitor Research","rank-math")),wp.element.createElement("li",null,(0,s.__)("125+ Pre-Built Prompts","rank-math"))):wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,s.__)("Gain access to 40+ advanced AI tools, empowering your content strategy.","rank-math")),wp.element.createElement("li",null,(0,s.__)("Experience the revolutionary AI-powered Content Editor for unparalleled efficiency.","rank-math")),wp.element.createElement("li",null,(0,s.__)("Engage with RankBot, your personal AI Chat Assistant, for real-time assistance.","rank-math")))}(n),!a&&wp.element.createElement(c.Button,{href:rankMath.contentAI.connectSiteUrl,className:"button button-primary is-green"},(0,s.__)("Connect Now","rank-math")),a&&!r&&wp.element.createElement(c.Button,{href:"https://rankmath.com/kb/how-to-use-content-ai/?play-video=ioPeVIntJWw&utm_source=Plugin&utm_medium=Buy+Plan+Button&utm_campaign=WP",className:"button button-primary is-green",target:"_blank"},(0,s.__)("Learn More","rank-math"))))},tr=(0,s.__)("Generating…","rank-math"),nr=function(e,t,n){var a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(fa()){var r=(0,o.find)(Rt(),["endpoint",e]);return(0,o.isUndefined)(r)&&((r=(0,o.find)(Rt(),["endpoint","Blog_Post_Idea"])).title=(0,o.startCase)(e)),(0,o.isNull)(document.getElementById("rank-math-content-ai-modal-wrapper"))&&i()("#wpwrap").append('<div id="rank-math-content-ai-modal-wrapper"></div>'),void setTimeout((function(){(0,u.createRoot)(document.getElementById("rank-math-content-ai-modal-wrapper")).render(wp.element.createElement(c.Modal,{className:"rank-math-contentai-modal rank-math-modal rank-math-error-modal",onRequestClose:function(){i()(".components-modal__screen-overlay").remove(),document.getElementById("rank-math-content-ai-modal-wrapper").remove()},shouldCloseOnClickOutside:!0},wp.element.createElement(er,{width:100})))}),100)}var s=(0,Me.createBlock)("rank-math/command",{content:tr,className:"rank-math-content-ai-command"}),l=(0,d.select)("core/block-editor").getBlocks().map((function(e){return e.clientId===n.clientId})).indexOf(!0);(0,d.dispatch)("core/block-editor").insertBlocks(s,l+1),Ot(e,t,s.clientId,n.clientId,a)},ar=(0,w.createHigherOrderComponent)((function(e){return function(t){if(t&&!(0,o.includes)(["core/paragraph","core/heading"],t.name))return wp.element.createElement(e,t);var n=at(t);if((0,o.isEmpty)(n))return wp.element.createElement(e,t);var a=wp.data.select("rank-math-content-ai").getData().language,r=[{title:"💻  "+(0,s.__)("Run as Command","rank-math"),onClick:function(){return nr("AI_Command",{command:n,language:a,choices:1},t)}},{title:"📖  "+(0,s.__)("Write More","rank-math"),onClick:function(){return nr("Continue_Writing",{sentence:ct(),choices:1},t)}},{title:"📝  "+(0,s.__)("Improve","rank-math"),onClick:function(){return nr("Paragraph_Rewriter",{original_paragraph:n,language:a,choices:1},t)}}];return(0,o.isNull)(t)||"core/paragraph"!==t.name||r.push({title:"📙  "+(0,s.__)("Summarize","rank-math"),onClick:function(){return nr("Text_Summarizer",{text:n,language:a,choices:1},t)}},{title:"💭  "+(0,s.__)("Write Analogy","rank-math"),onClick:function(){return nr("Analogy",{text:n,language:a,choices:1},t)}}),r.push({title:"✨  "+(0,s.__)("Fix Grammar","rank-math"),onClick:function(){return nr("Fix_Grammar",{text:n,choices:1},t)}}),wp.element.createElement(React.Fragment,null,wp.element.createElement(e,t),wp.element.createElement(ft.BlockControls,null,wp.element.createElement(c.ToolbarGroup,null,wp.element.createElement(c.ToolbarDropdownMenu,{icon:"rm-icon rm-icon-content-ai",label:(0,s.__)("Content AI Commands","rank-math"),controls:r}))))}}));function rr(e){return function(e){if(Array.isArray(e))return ir(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return ir(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ir(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ir(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var or=function(e){return{name:"content-ai-tools",className:"content-ai-autocompleters",triggerPrefix:e,isDebounced:!0,allowContext:function(e,t){return!(/\S/.test(e)||/\S/.test(t))},options:function(){return Rt()},getOptionKeywords:function(e){var t=e.endpoint,n=e.title,a=e.searchTerms,r=n.split(/\s+/);return r.push(r.join(" ")),(0,o.isUndefined)(a)?[t].concat(rr(r)):a},getOptionLabel:function(e){return wp.element.createElement("span",null,wp.element.createElement("i",{className:"ai-icon "+e.icon})," ",e.title)},getOptionCompletion:function(e){if(!e.endpoint)return!1;(0,o.isNull)(document.getElementById("rank-math-content-ai-modal-wrapper"))&&i()("#wpwrap").append('<div id="rank-math-content-ai-modal-wrapper"></div>'),wp.data.dispatch("rank-math-content-ai").isAutoCompleterOpen(!0),setTimeout((function(){(0,u.createRoot)(document.getElementById("rank-math-content-ai-modal-wrapper")).render(wp.element.createElement(on,{tool:e}))}),100)}}},sr=function(){var e=(0,o.isUndefined)((0,d.select)("rank-math"))?[]:(0,d.select)("rank-math").getHighlightedParagraphs();if((0,o.isEmpty)(e))return"";var t=(0,d.select)("core/block-editor").getSelectedBlock();return(0,o.isEmpty)(t)||!(0,o.includes)(e,t.clientId)?(i()(".block-editor-block-popover").show(),""):(i()(".block-editor-block-popover").hide(),wp.element.createElement(c.Popover,{placement:"top-start",focusOnMount:"firstElement",key:"rank-math-popover",expandOnMobile:!0,noArrow:!1,anchor:document.getElementById("block-"+t.clientId)},wp.element.createElement(c.Button,{variant:"primary",onClick:function(){var e=at(t);nr("Text_Summarizer",{text:e,language:wp.data.select("rank-math-content-ai").getData().language,format:"paragraph",choices:1},t,!0)}},(0,s.__)("Shorten with AI","rank-math"))))};function lr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=i.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return cr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return cr(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var ur=(0,w.createHigherOrderComponent)((function(e){return function(t){if("core/image"!==t.name||(0,o.isEmpty)(t.attributes.url))return wp.element.createElement(e,t);var n=lr((0,u.useState)(!1),2),a=n[0],r=n[1],i=lr((0,u.useState)(""),2),l=i[0],m=i[1],d=lr((0,u.useState)(""),2),p=d[0],h=d[1],g=t.attributes,f=t.setAttributes,_=g.url,b=g.alt;l&&(f({alt:l}),m(""),h("error"),setTimeout((function(){f({alt:b}),r(!1),h("")}),2e3));return wp.element.createElement(u.Fragment,null,wp.element.createElement(e,t),wp.element.createElement(ft.InspectorControls,null,wp.element.createElement(c.Button,{variant:"tertiary",size:"compact",className:p+" rank-math-generate-alt",onClick:function(){fa()||rankMath.contentAI.credits<50?_a({creditsRequired:50}):_?(r(!0),m(""),ya(_).then((function(e){e?(f({alt:e}),r(!1)):m((0,s.__)("Failed to generate alt text.","rank-math"))})).catch((function(e){m(e)}))):m((0,s.__)("Image URL is missing.","rank-math"))},disabled:a},p&&(0,s.__)("Failed","rank-math"),!p&&a&&(0,s.__)("Generating…","rank-math"),!p&&!a&&wp.element.createElement(React.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-content-ai"}),(0,s.__)("Generate Alt","rank-math")),!a&&wp.element.createElement("span",{className:"rank-math-tooltip"},wp.element.createElement("em",{className:"dashicons-before dashicons-editor-help"}),wp.element.createElement("span",null,(0,s.__)("50 Content AI credits will be used to generate the Alt","rank-math"))))))}}),"withInspectorControl"),mr=function(){var e=(0,d.select)("rank-math-content-ai").getData();(0,l.addFilter)("rankMath.analytics.contentAI","rank-math",(function(){return function(){var t=b()("button-secondary rank-math-content-ai",{"is-new":!e.viewed});return wp.element.createElement(c.Button,{className:t,onClick:function(){i()(".rank-math-toolbar-score.content-ai-score").length?i()(".rank-math-toolbar-score.content-ai-score").parent().trigger("click"):i()(".rank-math-content-ai-tab").trigger("click")}},wp.element.createElement("i",{className:"rm-icon rm-icon-content-ai"}),(0,s.__)("Content AI","rank-math"))}})),(0,l.addFilter)("rank_math_before_serp_devices","rank-math",(function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"SEO_Meta",a=b()("rank-math-content-ai-meta-button button button-small button-primary",{"is-new":!e.viewed,"field-group":"SEO_Meta"!==n});return wp.element.createElement(c.Button,{className:a,disabled:fa(),onClick:function(){(0,o.isNull)(document.getElementById("rank-math-content-ai-modal-wrapper"))&&i()("body").append('<div id="rank-math-content-ai-modal-wrapper"></div>');var e=(0,d.select)("rank-math"),t=(0,o.find)(Rt(),["endpoint",n]),a=t.params;a.topic.default=e.getSerpTitle(),a.post_brief.default=e.getSerpDescription(),a.focus_keyword.default=e.getKeywords().split(","),t.output.default=1,t.params=a,(0,u.createRoot)(document.getElementById("rank-math-content-ai-modal-wrapper")).render(wp.element.createElement(on,{tool:t,callApi:!0}))}},wp.element.createElement("i",{className:"rm-icon rm-icon-content-ai"}),(0,s.__)("Generate With AI","rank-math"))})),(g()||e.isContentAIPage)&&((0,l.addFilter)("rank_math_block_faq_actions","rank-math",(function(e,t,n){return wp.element.createElement(React.Fragment,null,e,wp.element.createElement(c.Button,{icon:"rm-icon rm-icon-content-ai",className:"rank-math-faq-content-ai",label:(0,s.__)("Generate Answer with Content AI","rank-math"),disabled:fa(),showTooltip:!0,onClick:function(){n.setQuestionProp("content",(0,s.__)("Generating…","rank-math")),Be("AI_Command",{command:t.title,choices:1},(function(e){var t="";setTimeout((function(){var a=e[0].replaceAll(/(?:\r\n|\r|\n)/g,"<br>").split(" "),r=0,i=!1,o=setInterval((function(){t?t+="<br>"===a[r]||i?a[r]:" "+a[r]:t=a[r],i="<br>"===a[r],n.setQuestionProp("content",t),++r>=a.length&&clearInterval(o)}),50)}),100)}))}}))})),(0,Xa.registerFormatType)("rank-math/content-ai",{title:(0,s.__)("Content AI","rank-math"),tagName:"p",className:null,edit:sr}),(0,l.addFilter)("editor.BlockEdit","rank-math",ar),fa()||((0,l.addFilter)("editor.Autocomplete.completers","rank-math/content-ai/tools",(function(e,t){return"core/paragraph"===t||"rank-math/command"===t?[].concat(rr(e),[or("//")]):e})),(0,l.addFilter)("editor.Autocomplete.completers","rank-math/content-ai/tools2",(function(e,t){return"core/paragraph"===t||"rank-math/command"===t?[].concat(rr(e),[or("// ")]):e}))),(0,l.addFilter)("editor.BlockEdit","rank-math/add-alt-generator",ur))};function dr(e){return dr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},dr(e)}function pr(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,(r=a.key,i=void 0,i=function(e,t){if("object"!==dr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==dr(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===dr(i)?i:String(i)),a)}var r,i}var hr=function(){var e=(0,d.useSelect)((function(){return f()&&(0,o.endsWith)(wp.data.select("core/edit-site").getEditedPostId(),"//home")||!(0,o.isUndefined)((0,d.select)("core/edit-site"))&&(0,o.endsWith)((0,d.select)("core/edit-site").getEditedPostId(),"//page")}));return f()&&e?wp.element.createElement(u.Fragment,null,wp.element.createElement(h.PluginSidebarMoreMenuItem,{target:"seo-by-rank-math-content-ai-sidebar",icon:wp.element.createElement(Yn,null)},(0,s.__)("Content AI","rank-math")),wp.element.createElement(h.PluginSidebar,{name:"seo-by-rank-math-content-ai-sidebar",title:"Content AI",className:"rank-math-sidebar-panel rank-math-sidebar-content-ai-panel"},wp.element.createElement(An,null))):wp.element.createElement(u.Fragment,null,wp.element.createElement(p.PluginSidebarMoreMenuItem,{target:"seo-by-rank-math-content-ai-sidebar",icon:wp.element.createElement(Yn,null)},(0,s.__)("Content AI","rank-math")),wp.element.createElement(p.PluginSidebar,{name:"seo-by-rank-math-content-ai-sidebar",title:"Content AI",className:"rank-math-sidebar-panel rank-math-sidebar-content-ai-panel"},wp.element.createElement(An,null)))},gr=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.init(),this.events(),this.addPrePublishPanel(this.data),this.registerWriteShortcut(this.data),this.loadContentAIPage(this.data),this.changePlaceholder=this.changePlaceholder.bind(this),this.setup=this.setup.bind(this),(0,l.addAction)("rank_math_loaded","rank-math",this.setup),(0,l.addFilter)("blocks.registerBlockType","rank-math",this.changePlaceholder),(0,l.addFilter)("rankMath.checklists.FixWithAI","rank-math-pro",(function(){return ka}))}var t,n,a;return t=e,n=[{key:"init",value:function(){mr(),function(){var e=(0,d.dispatch)(ft.store).updateBlockAttributes,t=function(){var e=(0,d.select)("core/block-editor").getSelectedBlock();(0,o.isNull)(e)||Ot("Write",Na(at(e)),e.clientId)},n=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=document.activeElement.childNodes[1];if(!(0,o.isUndefined)(t)){var n=window.getSelection(),a=document.createRange();a.setStart(t,e?0:t.length),a.collapse(!0),n.removeAllRanges(),n.addRange(a)}};i()(document).on("click",".rank-math-content-ai-command-button",(function(){t()})),i()(document).on("keydown",".rank-math-content-ai-command-button",(function(e){e.target.classList.contains("rank-math-content-ai-command-button")&&("Enter"!==e.code?"ArrowLeft"===e.code&&(e.preventDefault(),n()):t())}));var a=!1,r='<button class="rank-math-content-ai-command-button" title="'+(0,s.__)("Click or Press Enter","rank-math")+'" contenteditable="false"><i class="rm-icon rm-icon-enter-key"></i></button>';document.addEventListener("keyup",(function(t){var i=(0,d.select)("core/block-editor").getSelectedBlock();if(!(0,o.isNull)(i)){var s=i.clientId,l=at(i);if((0,o.startsWith)(l,"//")&&"core/paragraph"===i.name){var c=(0,Me.createBlock)("rank-math/command",{content:at(i).replace("//","<span>//</span>"),className:"rank-math-content-ai-command"});(0,d.dispatch)("core/block-editor").replaceBlock(s,c),s=c.clientId}if("rank-math/command"!==i.name)return!1;if(a&&(0,o.includes)(["ArrowLeft","ArrowRight"],t.code))return n("ArrowLeft"===t.code),void(a=!1);if("Backspace"!==t.code&&(a="KeyA"===t.code),"Backspace"!==t.code||!a&&l.replace(/(<div[^>]*>[\s\S]*<\/div>)/,"").replace(/(<span[^>]*>[\s\S]*<\/span>)/,"")&&"/"!==l.replace(/(<([^>]+)>)/gi,"")){var u=(0,o.isUndefined)(i.attributes.className)?[]:i.attributes.className.split(" ");""===l.replace("//","").replace(" ","").replace(new RegExp(r,"i"),"").replace(/(<([^>]+)>)/gi,"")?e(s,{content:l.replace(new RegExp(r,"i"),"")}):(0,o.includes)(u,"typing")||l.includes("rank-math-content-ai-command-button")||e(s,{content:l+=r,className:"rank-math-content-ai-command"})}else{var m=(0,Me.createBlock)("core/paragraph");(0,d.dispatch)("core/block-editor").replaceBlock(s,m)}}})),document.addEventListener("keydown",(function(t){if(!("Enter"!==t.code||t.shiftKey||t.metaKey||"button"===t.target.localName||fa())){var n=(0,d.select)("core/block-editor").getSelectedBlock();if(!(0,o.isNull)(n)&&"rank-math/command"===n.name){var a=at(n);if(a.replace("//","").replace(" ","").replace(/(<([^>]+)>)/gi,"")){var r=n.clientId;e(r,{content:"",className:""}),(0,d.select)("rank-math-content-ai").isAutoCompleterOpen()||Ot("Write",Na(a),r)}}}}))}(),function(){if("classic"===rankMath.currentEditor){var e=[],t=function(){(0,o.isEmpty)(e)||(0,o.forEach)(e,(function(e){return e.remove()}))};(0,l.addAction)("rank_math_annotations_removed","rank-math",(function(){return t()})),(0,l.addAction)("rank_math_data_changed","rank-math",(function(e,n){"dirtyMetadata"===e&&(0,o.isEmpty)(n)&&t()})),(0,l.addAction)("rank_math_content_refresh","rank-math",(function(){(0,o.isUndefined)(window.tinymce)||setTimeout((function(){var n=window.tinymce.get(window.wpActiveEditor);if(!(0,o.isNull)(n)){var a=n.annotator.getAll("rank-math-annotations");(0,o.isEmpty)(a)?t():(0,o.forEach)(a["rank-math-annotation"],(function(t){var n=t.getElementsByClassName("rank-math-content-ai-tooltip");n.length||((n=document.createElement("button")).className="rank-math-content-ai-tooltip",n.textContent=(0,s.__)("Shorten with AI","rank-math"),n.addEventListener("click",ja),e.push(n),t.appendChild(n))}))}}),1e3)}))}}(),this.data=(0,d.select)("rank-math-content-ai").getData(),this.data.isContentAIPage||(new Aa,new Kn)}},{key:"setup",value:function(){this.addHeaderButton(),this.addSidebarTab();var e=document.getElementById("cmb2-metabox-rank_math_metabox_content_ai");e&&setTimeout((function(){(0,u.createRoot)(e).render((0,u.createElement)(An))}),1e3)}},{key:"events",value:function(){if("classic"!==rankMath.currentEditor&&(i()(document).on("click",".rank-math-open-contentai",(function(e){e.preventDefault();var t=(0,d.dispatch)("core/edit-post");return(0,o.isNull)(t)?(i()(".rank-math-content-ai-tab").trigger("click"),!1):(t.openGeneralSidebar("rank-math-content-ai/seo-by-rank-math-content-ai-sidebar"),!1)})),g()&&(0,o.includes)(window.location.search,"tab=content-ai"))){var e="rank-math-content-ai/seo-by-rank-math-content-ai-sidebar";(0,d.select)("core/edit-post").getActiveGeneralSidebarName()!==e&&(0,d.dispatch)("core/edit-post").openGeneralSidebar(e)}}},{key:"addSidebarTab",value:function(){(0,l.addFilter)("rank_math_sidebar_tabs","rank-math",(function(e){return e.push({name:"contentAI",title:wp.element.createElement(u.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-content-ai"}),wp.element.createElement("span",null,(0,s.__)("Content AI","rank-math"))),view:An,className:"rank-math-content-ai-tab hidden is-active"}),e}))}},{key:"changePlaceholder",value:function(e,t){return"core/paragraph"!==t||(e.attributes.placeholder={type:"string",default:(0,s.__)("Type / to choose a block or // to use Content AI","rank-math")}),e}},{key:"addHeaderButton",value:function(){g()&&(0,m.registerPlugin)("rank-math-content-ai",{icon:wp.element.createElement(Yn,null),render:hr})}},{key:"addPrePublishPanel",value:function(e){g()&&(0,m.registerPlugin)("rank-math-content-ai-box",{render:function(){return!(!(0,o.isEmpty)(e.score)||!(0,o.isEmpty)(e.keyword))&&wp.element.createElement(p.PluginPrePublishPanel,{title:(0,s.__)("Content AI","rank-math"),icon:"rm-icon rm-icon-content-ai",initialOpen:"true",className:"rank-math-content-ai-box"},wp.element.createElement("p",null,(0,s.__)("Improve your content with a personal Content AI.","rank-math")),wp.element.createElement(c.Button,{className:"button-primary",onClick:function(){i()(".editor-post-publish-panel__header-cancel-button button").trigger("click"),i()(".rank-math-toolbar-score").parent().hasClass("is-pressed")||i()(".rank-math-toolbar-score").trigger("click"),setTimeout((function(){i()(".rank-math-content-ai-tab").trigger("click")}),100)}},(0,s.__)("Improve Now","rank-math")))}})}},{key:"registerWriteShortcut",value:function(e){e.registerWriteShortcut&&(g()||e.isContentAIPage)&&(0,m.registerPlugin)("rank-math-content-ai-write-shortcut",{render:function(){return wp.element.createElement(Ia,null)}})}},{key:"loadContentAIPage",value:function(e){var t=document.getElementById("rank-math-content-ai-page");if(!(0,o.isNull)(t)){(0,u.createRoot)(t).render(wp.element.createElement(Vn,e)),i()("#wp-admin-bar-rank-math-content-ai-page").on("click","a",(function(){setTimeout((function(){var e=window.location.hash.replace("#","");i()("#tab-panel-0-"+e).length&&i()("#tab-panel-0-"+e).trigger("click")}),100)}));var n=window.location,a=n.pathname;(0,d.subscribe)((function(){var e=(0,d.select)("core/editor").isAutosavingPost(),t=(0,d.select)("core/block-editor").getBlocks();1===t.length&&"core/paragraph"===t[0].name&&(0,o.isEmpty)(at(t[0]))&&"rank-math-command"!==t[0].attributes.className&&(0,d.dispatch)("core/block-editor").updateBlockAttributes(t[0].clientId,{className:"rank-math-command"}),e&&(setTimeout((function(){(0,d.dispatch)("core/editor").savePost()}),500),!(0,o.isNull)(window.history.state)&&window.history.state.id&&window.history.replaceState("","Content AI",n.origin+a+"?page=rank-math-content-ai-page#content-editor"))}))}}}],n&&pr(t.prototype,n),a&&pr(t,a),Object.defineProperty(t,"prototype",{writable:!1}),e}();new gr}()}();