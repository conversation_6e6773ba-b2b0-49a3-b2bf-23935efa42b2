!function(){"use strict";var e={n:function(n){var t=n&&n.__esModule?function(){return n.default}:function(){return n};return e.d(t,{a:t}),t},d:function(n,t){for(var r in t)e.o(t,r)&&!e.o(n,r)&&Object.defineProperty(n,r,{enumerable:!0,get:t[r]})},o:function(e,n){return Object.prototype.hasOwnProperty.call(e,n)}},n=jQuery,t=e.n(n);function r(e,n,r,o){n=n||"error",o=o||!1;var i=t()('<div class="notice notice-'+n+' is-dismissible"><p>'+e+"</p></div>").hide();r.next(".notice").remove(),r.after(i),i.slideDown(),t()(document).trigger("wp-updates-notice-added"),o&&setTimeout((function(){i.fadeOut((function(){i.remove()}))}),o)}var o,i=lodash;(o=jQuery)((function(){var e=o("#indexnow_urls"),n=o("#indexnow_submit"),t=o("#indexnow_spinner"),a=o("div.cmb2-id-indexnow-urls"),s=!1,d="all",c=function(){s||(s=!0,o.ajax({url:rankMath.indexNow.restUrl+"/getLog",data:{filter:d},type:"GET",beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",rankMath.api.nonce)},success:function(e){if(o("#indexnow_clear_history, #indexnow_history_filters").removeClass("hidden"),0===e.total&&o("#indexnow_clear_history, #indexnow_history_filters").addClass("hidden"),o('#indexnow_history_filters a[data-filter="'+d+'"]').addClass("current").siblings().removeClass("current"),!e.data.length)return n=o("#indexnow_history").find("thead th").length,void o("#indexnow_history").find("tbody").html('<tr><td colspan="'+n+'">'+rankMath.indexNow.i18n.noHistory+"</td></tr>");var n,t="";e.data.forEach((function(e){t+="<tr>",t+='<td title="'+e.timeFormatted+'">'+e.timeHumanReadable+"</td>",t+='<td style="width: 60%;word-break: break-all;">'+e.url+"</td>",t+="<td>"+e.status+"</td>",t+="</tr>"})),o("#indexnow_history tbody").html(t)},error:function(e){r(rankMath.indexNow.i18n.getHistoryError,"error",o("#indexnow_history"),5e3)},complete:function(){s=!1}}))};n.on("click",(function(i){i.preventDefault();var s=e.val();n.addClass("disabled"),t.addClass("is-active"),o.ajax({url:rankMath.indexNow.restUrl+"/submitUrls",type:"POST",beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",rankMath.api.nonce)},data:{urls:s},success:function(n){n.success?(r(n.message,"success",a,5e3),e.val("")):r(n.message,"error",a,5e3)},error:function(e){r(void 0!==e.responseJSON.message?e.responseJSON.message:rankMath.indexNow.i18n.submitError,"error",a,5e3)},complete:function(){n.removeClass("disabled"),t.removeClass("is-active"),d="all",c()}})})),o("#indexnow_show_response_codes").on("click",(function(e){e.preventDefault(),o(this).toggleClass("active"),o("#indexnow_response_codes").toggleClass("hidden")})),function(){o("#indexnow_clear_history").on("click",(function(e){e.preventDefault(),o.ajax({url:rankMath.indexNow.restUrl+"/clearLog",type:"POST",beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",rankMath.api.nonce)},success:function(e){c()},error:function(e){r(rankMath.indexNow.i18n.clearHistoryError,"error",a,5e3)}})}));var e=(0,i.toInteger)(rankMath.indexNow.refreshHistoryInterval);e>0&&(e=Math.max((0,i.toInteger)(rankMath.indexNow.refreshHistoryInterval),1e3),setInterval(c,e)),o("#indexnow_history_filters a").on("click",(function(e){e.preventDefault();var n=o(this).data("filter");n!==d&&(d=n,c())})),c()}(),o("#indexnow_reset_key").on("click",(function(e){e.preventDefault();var n=o("#indexnow_api_key").val();o("#indexnow_api_key").val("..."),o.ajax({url:rankMath.indexNow.restUrl+"/resetKey",type:"POST",beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",rankMath.api.nonce)},success:function(e){o("#indexnow_api_key").val(e.key),o("#indexnow_api_key_location").text(e.location),o("#indexnow_check_key").attr("href",e.location)},error:function(e){o("#indexnow_api_key").val(n)}})}))}))}();