/*!
 * Plugin:	Rank Math - Redirections
 * URL:		https://rankmath.com/wordpress/plugin/seo-suite/
 * Name:	redirections.css
 */@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(-360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(-360deg)}}@keyframes bounce{from{-webkit-transform:translateY(0px);transform:translateY(0px)}to{-webkit-transform:translateY(-5px);transform:translateY(-5px)}}@-webkit-keyframes bounce{from{-webkit-transform:translateY(0px);transform:translateY(0px)}to{-webkit-transform:translateY(-5px);transform:translateY(-5px)}}@-webkit-keyframes loading{0%{background-size:20% 50%,20% 50%,20% 50%}20%{background-size:20% 20%,20% 50%,20% 50%}40%{background-size:20% 100%,20% 20%,20% 50%}60%{background-size:20% 50%,20% 100%,20% 20%}80%{background-size:20% 50%,20% 50%,20% 100%}100%{background-size:20% 50%,20% 50%,20% 50%}}@keyframes loading{0%{background-size:20% 50%,20% 50%,20% 50%}20%{background-size:20% 20%,20% 50%,20% 50%}40%{background-size:20% 100%,20% 20%,20% 50%}60%{background-size:20% 50%,20% 100%,20% 20%}80%{background-size:20% 50%,20% 50%,20% 100%}100%{background-size:20% 50%,20% 50%,20% 50%}}:root{--rankmath-wp-adminbar-height: 0}.column-url_from,.column-url_to{width:28%}tr.rank-math-redirection-deactivated .deactivate,tr.rank-math-redirection-activated .activate,.rank-math-more,.rank-math-redirections-form,.metabox-prefs legend+label{display:none}.rank-math-redirections-form{width:808px;max-width:100%;margin-top:1.25rem;-webkit-box-sizing:border-box;box-sizing:border-box}.rank-math-redirections-form.is-open{display:block}.rank-math-redirections-form>h2{margin-top:0;margin-bottom:2rem}#rank-math-redirections-form .field-row{border-top:0 !important}#rank-math-redirections-form .rank-math-editcreate-form{overflow:visible}#rank-math-redirections-form .rank-math-editcreate-form .field-repeatable-grouping{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-flow:row wrap;flex-flow:row wrap;margin-bottom:10px;line-height:1.3}#rank-math-redirections-form .rank-math-editcreate-form .field-repeatable-grouping>.group-item{-webkit-box-flex:0;-ms-flex:0 0 41%;flex:0 0 41%;display:inline-block;width:calc(43% - 10px)}#rank-math-redirections-form .rank-math-editcreate-form .field-repeatable-grouping>.group-item~.group-item:not(.group-item-checkbox){margin-left:10px}#rank-math-redirections-form .rank-math-editcreate-form .field-repeatable-grouping>.group-item.group-item-checkbox{margin-left:0;margin-top:10px;-ms-flex-order:4;order:4;-webkit-box-ordinal-group:5}#rank-math-redirections-form .rank-math-editcreate-form .field-repeatable-grouping>.group-item.group-item-checkbox .components-checkbox-control__label{color:#7f868d;letter-spacing:.01em;line-height:1.3}#rank-math-redirections-form .rank-math-editcreate-form .field-repeatable-grouping .button-remove-group{height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;margin-left:10px}.form-footer:not(.rank-math-custom){margin:1.875rem -1.875rem -1.875rem;padding:20px 1.875rem;width:auto;overflow:hidden;border:0;border-top:1px solid #b5bfc9}.form-footer:not(.rank-math-custom) .rank-math-button.components-button{padding:0 20px}tr.rank-math-redirection-deactivated{opacity:.5}.rank-math-redirections-export-options{padding:20px}