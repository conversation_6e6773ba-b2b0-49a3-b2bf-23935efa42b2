!function(){var t={184:function(t,e){var r;!function(){"use strict";var n={}.hasOwnProperty;function i(){for(var t=[],e=0;e<arguments.length;e++){var r=arguments[e];if(r){var o=typeof r;if("string"===o||"number"===o)t.push(r);else if(Array.isArray(r)){if(r.length){var a=i.apply(null,r);a&&t.push(a)}}else if("object"===o){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){t.push(r.toString());continue}for(var l in r)n.call(r,l)&&r[l]&&t.push(l)}}}return t.join(" ")}t.exports?(i.default=i,t.exports=i):void 0===(r=function(){return i}.apply(e,[]))||(t.exports=r)}()},317:function(t,e,r){"use strict";var n=r(81),i=r.n(n),o=r(645),a=r.n(o)()(i());a.push([t.id,':root{--rankmath-wp-adminbar-height: 0}p.field-description{padding-top:.5em;margin:0;color:#666;letter-spacing:.01em}.form-table{width:100%;margin-top:.5em;border-collapse:collapse;clear:both}.form-table,.form-table td,.form-table td p,.form-table th{font-size:14px}.field-metabox{margin:0;clear:both}.field-metabox>.field-row:first-of-type>.field-td,.field-metabox>.field-row:first-of-type>.field-th,.field-metabox.field-list>.field-row:first-of-type>.field-td,.field-metabox.field-list>.field-row:first-of-type>.field-th{border:0}.field-metabox .note{margin-right:5px;padding:2px 6px;border-radius:3px;color:#794800;background:rgba(255,190,95,.5)}.field-th{float:left;width:200px;padding:20px 10px 20px 0;color:#222;font-weight:600;vertical-align:top}.field-th label{display:block;padding:5px 0}.field-th+.field-td{float:left}.field-td{max-width:100%;padding:15px 10px;line-height:1.3;vertical-align:middle}[id^=field-metabox-rank] .field-row{margin:0;padding:25px 0}[id^=field-metabox-rank] .field-row:not(.field-type-title):first-of-type{padding-top:0}[id^=field-metabox-rank] .field-row:not(.field-type-title):last-of-type{padding-bottom:0}[id^=field-metabox-rank] .field-row.rank-math-advanced-option~.rank-math-advanced-option,[id^=field-metabox-rank] .field-row:not(.rank-math-advanced-option):not(.tab-header):not(.field-type-notice)~.field-row:not(.rank-math-advanced-option):not(.rank-math-notice){border-top:1px solid #dadfe4;border-bottom:0}[id^=field-metabox-rank] .field-row .field-td,[id^=field-metabox-rank] .field-row .field-th{padding:0;box-sizing:border-box}[id^=field-metabox-rank] .field-row .field-th{width:25%;max-width:200px;color:#242628}[id^=field-metabox-rank] .field-row .field-th label{padding:0 15px 0 0}[id^=field-metabox-rank] .field-row .field-td{float:left;width:75%}[id^=field-metabox-rank] .field-description{padding-top:10px;color:#7f868d;font-size:14px;font-style:normal}[id^=field-metabox-rank] .media-status .img-status img{box-shadow:0 0 0 1px #e9e9e9;outline:none}.field-list>.field-row{vertical-align:top}.field-wrap{margin:0}.field-wrap .field-row{position:relative;margin:0}.field-wrap .field-row::after{content:"";display:block;width:100%;clear:both}.field-wrap .field-row:first-of-type>.field-td .rank-math-button.toggle-all-capabilities{top:32px}.field-wrap+footer.form-footer{display:flex;justify-content:space-between;flex-wrap:wrap;padding:1.875rem;margin:30px -1.875rem -1.875rem;border:0;border-top:1px solid #c3c4c7;border-radius:0 0 6px 6px;box-sizing:border-box;width:auto;background:#f8f9fa;text-align:center;overflow:hidden}.field-wrap+footer.form-footer .rank-math-button{align-items:center;justify-content:center;padding:0}.field-wrap ul{margin:0}.field-wrap li{margin:1px 0 5px 0;font-size:14px;line-height:16px}.field-disabled{opacity:.4;pointer-events:none}@media(min-width: 641px){.field-td .rank-math-button.toggle-all-capabilities{position:absolute;top:56px;left:0;font-weight:600}}@media screen and (max-width: 782px){.form-table label{font-size:14px}}@media screen and (max-width: 640px){.field-td .rank-math-button.toggle-all-capabilities{margin:1em 0 1.7em}[id^=field-metabox-rank] .field-row .field-td,[id^=field-metabox-rank] .field-row .field-th label{padding:0 0 15px 2px}[id^=field-metabox-rank] .field-row .field-td,[id^=field-metabox-rank] .field-row .field-th{width:100%;padding:0}.field-wrap .field-row{padding:15px 0}}@media screen and (max-width: 450px){.field-th{display:block;float:none;width:100%;padding-bottom:1em;font-size:1.2em;text-align:left}.field-th label{display:block;margin-top:0;margin-bottom:.5em}.field-td,.field-th+.field-td{display:block;float:none;width:100%}}.field-disabled{opacity:.4;pointer-events:none}',""]),e.Z=a},653:function(t,e,r){"use strict";var n=r(81),i=r.n(n),o=r(645),a=r.n(o)()(i());a.push([t.id,".rank-math-tab-header{margin:-1.875rem -1.875rem 30px;padding:1.875rem 1.875rem 0;text-align:center;border-bottom:1px solid #b5bfc9;border-radius:6px 6px 0 0;background-color:#f8f9fa}.rank-math-tab-header h2{margin:0;font-size:30px;font-weight:500}.rank-math-tab-header p{font-size:1rem;max-width:715px;margin:0 auto 2rem}",""]),e.Z=a},645:function(t){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var r="",n=void 0!==e[5];return e[4]&&(r+="@supports (".concat(e[4],") {")),e[2]&&(r+="@media ".concat(e[2]," {")),n&&(r+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),r+=t(e),n&&(r+="}"),e[2]&&(r+="}"),e[4]&&(r+="}"),r})).join("")},e.i=function(t,r,n,i,o){"string"==typeof t&&(t=[[null,t,void 0]]);var a={};if(n)for(var l=0;l<this.length;l++){var c=this[l][0];null!=c&&(a[c]=!0)}for(var u=0;u<t.length;u++){var s=[].concat(t[u]);n&&a[s[0]]||(void 0!==o&&(void 0===s[5]||(s[1]="@layer".concat(s[5].length>0?" ".concat(s[5]):""," {").concat(s[1],"}")),s[5]=o),r&&(s[2]?(s[1]="@media ".concat(s[2]," {").concat(s[1],"}"),s[2]=r):s[2]=r),i&&(s[4]?(s[1]="@supports (".concat(s[4],") {").concat(s[1],"}"),s[4]=i):s[4]="".concat(i)),e.push(s))}},e}},81:function(t){"use strict";t.exports=function(t){return t[1]}},379:function(t){"use strict";var e=[];function r(t){for(var r=-1,n=0;n<e.length;n++)if(e[n].identifier===t){r=n;break}return r}function n(t,n){for(var o={},a=[],l=0;l<t.length;l++){var c=t[l],u=n.base?c[0]+n.base:c[0],s=o[u]||0,f="".concat(u," ").concat(s);o[u]=s+1;var d=r(f),p={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==d)e[d].references++,e[d].updater(p);else{var m=i(p,n);n.byIndex=l,e.splice(l,0,{identifier:f,updater:m,references:1})}a.push(f)}return a}function i(t,e){var r=e.domAPI(e);r.update(t);return function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;r.update(t=e)}else r.remove()}}t.exports=function(t,i){var o=n(t=t||[],i=i||{});return function(t){t=t||[];for(var a=0;a<o.length;a++){var l=r(o[a]);e[l].references--}for(var c=n(t,i),u=0;u<o.length;u++){var s=r(o[u]);0===e[s].references&&(e[s].updater(),e.splice(s,1))}o=c}}},569:function(t){"use strict";var e={};t.exports=function(t,r){var n=function(t){if(void 0===e[t]){var r=document.querySelector(t);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(t){r=null}e[t]=r}return e[t]}(t);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(r)}},216:function(t){"use strict";t.exports=function(t){var e=document.createElement("style");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},565:function(t,e,r){"use strict";t.exports=function(t){var e=r.nc;e&&t.setAttribute("nonce",e)}},795:function(t){"use strict";t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var e=t.insertStyleElement(t);return{update:function(r){!function(t,e,r){var n="";r.supports&&(n+="@supports (".concat(r.supports,") {")),r.media&&(n+="@media ".concat(r.media," {"));var i=void 0!==r.layer;i&&(n+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),n+=r.css,i&&(n+="}"),r.media&&(n+="}"),r.supports&&(n+="}");var o=r.sourceMap;o&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),e.styleTagTransform(n,t,e.options)}(e,t,r)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(e)}}}},589:function(t){"use strict";t.exports=function(t,e){if(e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}}},e={};function r(n){var i=e[n];if(void 0!==i)return i.exports;var o=e[n]={id:n,exports:{}};return t[n](o,o.exports,r),o.exports}r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,{a:e}),e},r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nc=void 0,function(){"use strict";var t={};r.r(t),r.d(t,{resetStore:function(){return bt},resetdirtySettings:function(){return ht},setStep:function(){return gt},toggleLoaded:function(){return yt},updateModules:function(){return mt},updateSettings:function(){return pt},updateView:function(){return vt}});var e={};r.r(e),r.d(e,{appData:function(){return Et},appUi:function(){return Mt}});var n={};r.r(n),r.d(n,{getAppData:function(){return Nt},getCurrentStep:function(){return zt},getModules:function(){return Lt},getSettings:function(){return Rt},getView:function(){return Ft},getdirtySettings:function(){return It},isLoaded:function(){return Ut}});var i=jQuery,o=r.n(i),a=wp.element,l=lodash,c=wp.i18n,u=wp.hooks,s=function(){var t=document.querySelector(".rank-math-editcreate-form").querySelectorAll('input[type="text"]:not(#add_category):not(#exclude)');(0,l.forEach)(t,(function(t){t.classList.remove("invalid");var e=t.nextElementSibling;e&&e.classList.contains("validation-message")&&e.remove()}));var e=(0,l.filter)(t,(function(t){return!t.value.trim()}));return!e.length>0||((0,l.forEach)(e,(function(t,e){var r=document.createElement("span");r.className="validation-message",r.innerText=(0,c.__)("This field must not be empty.","rank-math"),t.classList.add("invalid"),t.after(r),0===e&&t.scrollIntoView({behavior:"smooth",block:"nearest"})})),!1)},f=function(){var t=document.querySelector(".field-id-header_code");if(t){var e=t.querySelector(".rank-math-toggle-group-control");if(e){var r=function(t){var e=document.createElement("div");return e.className=t,e},n=r("field-th"),i=document.createElement("label");i.textContent=(0,c.__)("Maintenance Code","rank-math"),n.appendChild(i);var o=r("field-td"),a=r("components-base-control"),l=r("components-base-control__field"),u=r("components-toggle-group-control rank-math-toggle-group-control css-ml4wxx e19lxcc00");u.append(e.children[3],e.children[4]),l.appendChild(u),a.appendChild(l),o.appendChild(a);var s=r("field-row field-type-radio_inline field-id-maintenance");s.append(n,o),t.after(s)}}},d=r(379),p=r.n(d),m=r(795),h=r.n(m),b=r(569),y=r.n(b),v=r(565),g=r.n(v),w=r(216),k=r.n(w),S=r(589),O=r.n(S),x=r(653),j={};j.styleTagTransform=O(),j.setAttributes=g(),j.insert=y().bind(null,"head"),j.domAPI=h(),j.insertStyleElement=k();p()(x.Z,j),x.Z&&x.Z.locals&&x.Z.locals;var _=function(t){var e=t.title,r=t.description,n=t.link;return wp.element.createElement("header",{className:"rank-math-tab-header"},wp.element.createElement("h2",null,e),wp.element.createElement("p",null,r,n&&wp.element.createElement(React.Fragment,null," ",wp.element.createElement("a",{href:n,target:"_blank",rel:"noreferrer"},(0,c.__)("Learn more","rank-math")),".")))},E=wp.compose,P=wp.data,C=wp.apiFetch,A=r.n(C),T=window.rankMathComponents;var D=["validate","afterSave"];function M(){return M=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},M.apply(this,arguments)}function N(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function I(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,l=[],c=!0,u=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(l.push(n.value),l.length!==e);c=!0);}catch(t){u=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return R(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return R(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var L=function(t,e){return"resetting"===e?(0,c.__)("Resetting…","rank-math"):"resetted"===e?(0,c.__)("Resetted","rank-math"):t.children},U=function(t,e){return"updating"===e?(0,c.__)("Updating…","rank-math"):"updated"===e?(0,c.__)("Updated","rank-math"):t.children},F=(0,E.compose)((0,P.withSelect)((function(t){return{settings:t("rank-math-settings").getdirtySettings()}})),(0,P.withDispatch)((function(t,e){var r=e.type,n=e.settings,i=e.footer.applyButton;return{saveSettings:function(e){e("updating"),A()({method:"POST",path:"/rankmath/v1/updateSettings",data:{type:r,settings:n[r]}}).then((function(r){return e("updated"),r.error?(function(t,e,r,n){e=e||"error",n=n||!1;var i=o()('<div class="notice notice-'+e+' is-dismissible"><p>'+t+"</p></div>").hide();r.next(".notice").remove(),r.after(i),i.slideDown(),o()(document).trigger("wp-updates-notice-added"),o()("html,body").animate({scrollTop:i.offset().top-50},"slow"),n&&setTimeout((function(){i.fadeOut((function(){i.remove()}))}),n)}(r.error,"error",o()(".wp-heading-inline")),void o()("html, body").animate({scrollTop:0},"fast")):r?(r&&!r.error&&i.afterSave&&i.afterSave(),e("updated"),void t("rank-math-settings").resetdirtySettings()):(e(""),void window.alert((0,c.__)("Something went wrong! Please try again.","rank-math")))}))},resetSettings:function(t){t("resetting"),A()({method:"POST",path:"/rankmath/v1/resetSettings",data:{type:r}}).then((function(e){if(!e)return t(""),void window.alert((0,c.__)("Something went wrong! Please try again.","rank-math"));t("resetted"),window.location.reload()}))}}})))((function(t){var e=I((0,a.useState)(""),2),r=e[0],n=e[1],i=t.settings,o=t.resetSettings,c=t.saveSettings,u=t.footer,s=u.applyButton,f=s.validate,d=(s.afterSave,N(s,D));return(0,a.useEffect)((function(){(0,l.includes)(["updated","resetted"],r)&&setTimeout((function(){return n("")}),1e3)}),[r]),wp.element.createElement("footer",{className:"form-footer rank-math-ui"},wp.element.createElement(T.Button,M({onClick:function(){o(n)}},u.discardButton,{children:L(u.discardButton,r)})),wp.element.createElement(T.Button,M({variant:"primary",onClick:function(){(!f||f())&&c(n)},disabled:(0,l.isEmpty)(i)},d,{children:U(d,r)})))})),z=r(184),B=r.n(z);function Z(t){return Z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Z(t)}var q=["id","type","content","Component","isDisabled"];function H(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function G(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?H(Object(r),!0).forEach((function(e){K(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function K(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Z(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Z(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Z(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function V(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var $=(0,E.compose)((0,P.withSelect)((function(t,e){var r=t("rank-math-settings").getAppData();return{field:e.field,settingType:e.settingType,settings:r}})),(0,P.withDispatch)((function(t,e){var r=e.settings,n=e.settingType;return{updateSetting:function(e,i){r[n][e]=i,t("rank-math-settings").updateSettings(r)}}})))((function(t){var e,r=t.field,n=t.settingType,i=t.settings,o=r.id,c=r.type,u=r.content,s=r.Component,f=r.isDisabled,d=V(r,q),p=(null===(e=i[n])||void 0===e?void 0:e[o])||"",m=function(e){return t.updateSetting(o,e)},h=function(){var t={toggle:"checked",checkbox:"checked"}[c]||"value",e=(0,l.includes)(["component","group"],c);return G(G({},d),{},K(K({id:o},t,d.value||p),"onChange",d.onChange||!f&&m),e&&{settingType:n})},b={file:window.rankMathComponents.UploadFile,text:window.rankMathComponents.TextControl,select:window.rankMathComponents.SelectControl,toggle:window.rankMathComponents.ToggleControl,select_search:window.rankMathComponents.SelectWithSearch,multicheck:window.rankMathComponents.CheckboxList,multicheck_inline:window.rankMathComponents.CheckboxList,radio_inline:window.rankMathComponents.ToggleGroupControl,repeatable_group:window.rankMathComponents.RepeatableGroup,group:window.rankMathComponents.Group,checkbox:window.rankMathComponents.CheckboxControl}[c];return b?wp.element.createElement(b,h()):"component"===c?wp.element.createElement(s,h()):"raw"===c?(0,a.createElement)(u):null})),W=["relation"];function J(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,l=[],c=!0,u=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(l.push(n.value),l.length!==e);c=!0);}catch(t){u=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Q(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Q(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function X(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function Y(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,l=[],c=!0,u=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(l.push(n.value),l.length!==e);c=!0);}catch(t){u=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return tt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var et=r(317),rt={};rt.styleTagTransform=O(),rt.setAttributes=g(),rt.insert=y().bind(null,"head"),rt.domAPI=h(),rt.insertStyleElement=k();p()(et.Z,rt),et.Z&&et.Z.locals&&et.Z.locals;function nt(t){return nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},nt(t)}function it(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ot(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?it(Object(r),!0).forEach((function(e){at(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):it(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function at(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==nt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==nt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===nt(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var lt=function(t){var e=t.settingType,r=t.fields,n=t.settings,i=void 0===n?null:n;return wp.element.createElement("div",{className:"field-wrap form-table wp-core-ui rank-math-ui"},wp.element.createElement("div",{id:"field-metabox-rank-math-".concat(e),className:"field-metabox field-list"},(0,l.map)(r,(function(t){var r=t.id,n=t.type,o=t.name,a=t.desc,c=t.classes,u=t.content,s=t.dep,f=function(t,e){var r=(0,P.useSelect)((function(t){return t("rank-math-settings").getAppData()}));return(0,l.some)(t,(function(t){var n=Y(t,1)[0];return r[e][n]}))}(t.disableDep,e);if(!s||function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=t.relation,i=X(t,W);r=(0,l.isNull)(r)?(0,P.useSelect)((function(t){return t("rank-math-settings").getAppData()})):r;var o=function(t){var n,i=J(t,2),o=i[0],a=i[1],c=(0,l.isUndefined)(e)?r[o]:null===(n=r[e])||void 0===n?void 0:n[o];return(0,l.isArray)(a)?(0,l.includes)(a,c):a===c};return"and"===n?(0,l.every)((0,l.entries)(i),o):(0,l.some)((0,l.entries)(i),o)}(s,e,i)){if("raw"===n)return u;var d=B()("field-row",c,at(at({"field-disabled":f},"field-id-"+r,r),"field-type-"+n,n));return wp.element.createElement("div",{key:r,className:d},o&&wp.element.createElement("div",{className:"field-th"},wp.element.createElement("label",{htmlFor:r},o)),wp.element.createElement("div",{className:"field-td"},wp.element.createElement($,{settingType:e,field:ot(ot({},t),{},{isDisabled:f})}),a&&wp.element.createElement("p",{className:"field-description",dangerouslySetInnerHTML:{__html:a}})))}}))))};function ct(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return e=(0,u.applyFilters)("rank_math_sanitize_settings",e,t,r),null!==n&&(n=(0,u.applyFilters)("rank_math_sanitize_settings_value",n,t,r)),n=null===n?e:n,(0,u.doAction)("rank_math_settings_changed",t,e,r),{type:"RANK_MATH_SETTINGS_DATA",key:t,value:e,settingsKey:r,settingsValue:n}}function ut(t,e){return(0,u.doAction)("rank_math_update_app_ui",t,e),{type:"RANK_MATH_APP_UI",key:t,value:e}}function st(t){return st="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},st(t)}function ft(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function dt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==st(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==st(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===st(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pt(t){return ct("settings",t,"settings")}function mt(t,e){var r=wp.data.select("rank-math-settings").getModules();return r[t].isActive=e,(0,l.forEach)(r,(function(e,n){if((0,l.includes)(e.dep_modules,t)){var i=!1;(0,l.forEach)(e.dep_modules,(function(t){r[t].isActive||(i=!0)})),r[n].isDisabled=i,r[n].disabled=i}})),ct("modules",function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ft(Object(r),!0).forEach((function(e){dt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ft(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},r))}function ht(){return ct("dirtySettings",{})}function bt(){return{type:"RESET_STORE"}}function yt(t){return ut("isLoaded",t)}function vt(t){return ut("view",t)}function gt(t){return ut("currentStep",t)}function wt(t){return wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wt(t)}function kt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function St(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?kt(Object(r),!0).forEach((function(e){Ot(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):kt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ot(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==wt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==wt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===wt(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var xt={header_code:"301",status:"active",sources:[{comparison:"exact"}]},jt={roleCapabilities:(0,l.get)(rankMath,"roleCapabilities",{}),redirections:rankMath.redirections||xt,modules:(0,l.get)(rankMath,"modulesList",{}),dirtySettings:{}},_t=St(St({},jt),{},{redirections:xt});function Et(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:jt,e=arguments.length>1?arguments[1]:void 0,r=St({},t.dirtySettings);return!1!==e.settingsKey&&(r=e.settingsValue),"RANK_MATH_SETTINGS_DATA"===e.type?"dirtySettings"===e.key?St(St({},t),{},{dirtySettings:e.value}):St(St({},t),{},Ot(Ot({},e.key,e.value),"dirtySettings",r)):"RESET_STORE"===e.type?_t:t}function Pt(t){return Pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pt(t)}function Ct(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function At(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ct(Object(r),!0).forEach((function(e){Tt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ct(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Tt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Pt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Pt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Pt(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Dt={currentStep:"getting-started"};function Mt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Dt,e=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===e.type?At(At({},t),{},Tt({},e.key,e.value)):t}function Nt(t){return t.appData}function It(t){return t.appData.dirtySettings}function Rt(t){return t.appData.settings}function Lt(t){return t.appData.modules}function Ut(t){return t.appUi.isLoaded}function Ft(t){return t.appUi.view}function zt(t){return t.appUi.currentStep}(0,P.registerStore)("rank-math-settings",{reducer:(0,P.combineReducers)(e),selectors:n,actions:t});var Bt=function(t){var e=t.type,r=t.header,n=t.footer,i=t.fields,o=void 0===i?[]:i,a=t.settings,l=void 0===a?null:a;return wp.element.createElement(React.Fragment,null,r&&wp.element.createElement(_,r),wp.element.createElement(lt,{settingType:e,fields:o,settings:l}),n&&wp.element.createElement(F,{type:e,footer:n}))};function Zt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,l=[],c=!0,u=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(l.push(n.value),l.length!==e);c=!0);}catch(t){u=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return qt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return qt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Ht=function(){var t=rankMath,e=t.isNew,r=t.redirections,n=Zt((0,a.useState)(!(0,l.isEmpty)(r)),2),i=n[0],o=n[1];return(0,a.useEffect)(f,[]),(0,a.useEffect)((function(){return function(t){var e=document.querySelector(".rank-math-add-new-redirection");if(!(0,l.isNull)(e)){var r=document.querySelectorAll(".value-url_from, .rank-math-redirection-edit"),n=function(){return t(!0)},i=function(){return t(!1)};return(0,l.forEach)(r,(function(t){return t.addEventListener("click",n)})),e.addEventListener("click",i),function(){(0,l.forEach)(r,(function(t){return t.removeEventListener("click",n)})),e.removeEventListener("click",i)}}}(o)}),[]),wp.element.createElement("div",{className:"rank-math-redirections-form rank-math-editcreate-form rank-math-page rank-math-box ".concat(e||i?"is-open":"")},wp.element.createElement("h2",null,wp.element.createElement("strong",null,i?(0,c.__)("Update Redirection","rank-math"):(0,c.__)("Add Redirection","rank-math"))),wp.element.createElement(Bt,{type:"redirections",fields:(0,u.applyFilters)("rank_math_redirection_fields",[{id:"sources",type:"repeatable_group",name:(0,c.__)("Source URLs","rank-math"),default:{comparison:"exact"},options:{addButton:{children:(0,c.__)("Add another","rank-math")},removeButton:{children:(0,c.__)("Remove","rank-math")}},classes:"field-group-text-only",fields:[{id:"pattern",type:"text"},{id:"comparison",type:"select",options:{exact:(0,c.__)("Exact","rank-math"),contains:(0,c.__)("Contains","rank-math"),start:(0,c.__)("Starts With","rank-math"),end:(0,c.__)("End With","rank-math"),regex:(0,c.__)("Regex","rank-math")}},{id:"ignore",type:"checkbox",label:(0,c.__)("Ignore Case","rank-math"),variant:"metabox"}]},{id:"url_to",type:"text",name:(0,c.__)("Destination URL","rank-math"),dep:{header_code:["301","302","307"]}},{id:"header_code",type:"radio_inline",name:(0,c.__)("Redirection Type","rank-math"),options:{301:(0,c.__)("301 Permanent Move","rank-math"),302:(0,c.__)("302 Temporary Move","rank-math"),307:(0,c.__)("307 Temporary Redirect","rank-math"),410:(0,c.__)("410 Content Deleted","rank-math"),451:(0,c.__)("451 Content Unavailable for Legal Reasons","rank-math")}},{id:"status",type:"radio_inline",name:(0,c.__)("Status","rank-math"),options:{active:(0,c.__)("Activate","rank-math"),inactive:(0,c.__)("Deactivate","rank-math")},disableDep:[["start-date"],["end-date"]]}]),footer:{discardButton:{onClick:void 0,isDestructive:!0,children:(0,c.__)("Cancel","rank-math")},applyButton:{type:"submit",validate:s,children:i?(0,c.__)("Update Redirection","rank-math"):(0,c.__)("Add Redirection","rank-math"),afterSave:function(){return window.location.href="".concat(rankMath.adminurl,"?page=rank-math-redirections")}}}}))};function Gt(t){return Gt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gt(t)}function Kt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Vt(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Gt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Gt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Gt(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var $t,Wt="".concat(rankMath.adminurl,"?page=rank-math-redirections");($t=o())((function(){var t={init:function(){this.wrap=$t(".rank-math-redirections-wrap"),this.addForm(),this.addNew(),this.edit(),this.importExport(),this.showMore(),this.columnActions(),this.explodePastedContent()},addForm:function(){(0,a.createRoot)(document.getElementById("rank-math-redirections-form")).render(wp.element.createElement(Ht,null))},addNew:function(){var t=this,e=$t("html, body");this.wrap.on("click",".rank-math-add-new-redirection",(function(r){r.preventDefault();var n=t.wrap.find(".rank-math-editcreate-form");if(t.wrap.find(".rank-math-importexport-form").hide(),n.is(":visible"))return n.hide(),void window.history.pushState(null,{},Wt);wp.data.select("rank-math-settings").getAppData().redirections.id&&wp.data.dispatch("rank-math-settings").resetStore(),n.show(),window.history.pushState(null,{},r.currentTarget.href),e.on("scroll mousedown wheel DOMMouseScroll mousewheel keyup touchmove",(function(){e.stop()})),e.animate({scrollTop:n.position().top},"slow",(function(){e.off("scroll mousedown wheel DOMMouseScroll mousewheel keyup touchmove")}))})),this.wrap.on("click",".rank-math-button.is-destructive",(function(t){t.preventDefault(),$t(this).closest(".rank-math-editcreate-form").hide(),window.history.pushState(null,{},Wt)}))},edit:function(){var t=this,e=$t("html, body");this.wrap.on("click",".value-url_from",(function(t){t.preventDefault();var e=$t(t.currentTarget);return e.closest(".rank-math-more").length?e.closest(".rank-math-more").parent().find(".rank-math-redirection-edit").trigger("click"):e.parent().find(".rank-math-redirection-edit").trigger("click"),!1})),this.wrap.on("click",".rank-math-redirection-edit",(function(r){r.preventDefault();var n=t.wrap.find(".rank-math-editcreate-form");t.wrap.find(".rank-math-importexport-form").hide();var i=wp.data.select("rank-math-settings").getAppData();i.redirections=o()(this).data("redirection"),wp.data.dispatch("rank-math-settings").updateSettings(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Kt(Object(r),!0).forEach((function(e){Vt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Kt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},i)),n.show(),window.history.pushState(null,{},r.currentTarget.href),e.on("scroll mousedown wheel DOMMouseScroll mousewheel keyup touchmove",(function(){e.stop()})),e.animate({scrollTop:n.position().top},"slow",(function(){e.off("scroll mousedown wheel DOMMouseScroll mousewheel keyup touchmove")}))}))},importExport:function(){var t=this;this.wrap.on("click",".rank-math-redirections-import_export",(function(e){e.preventDefault();var r=t.wrap.find(".rank-math-importexport-form");if(r.is(":visible"))return r.hide(),void window.history.pushState(null,{},Wt);t.wrap.find(".rank-math-editcreate-form").hide(),r.slideToggle(200),window.history.pushState(null,{},e.currentTarget.href)}))},explodePastedContent:function(){var t=$t("#sources_repeat");t.on("paste","input",(function(e){var r=e.originalEvent.clipboardData.getData("text");if(!/\r|\n/.exec(r))return!0;var n=$t(this),i=$t(".cmb-add-group-row",t),o=r.split(/\r?\n/).filter(String),a=n.closest(".cmb-field-list").find("select").val();return $t.each(o,(function(e,r){if(n.val(r),n.closest(".cmb-field-list").find("select").val(a),e<o.length-1){if(rankMath.redirectionPastedContent-1<=e)return!1;i.click(),n=$t(".cmb-repeatable-grouping",t).last().find("input")}else n.focus()})),!1}))},showMore:function(){this.wrap.on("click",".rank-math-showmore",(function(t){t.preventDefault();var e=$t(this);e.hide(),e.next(".rank-math-more").slideDown()})),this.wrap.on("click",".rank-math-hidemore",(function(t){t.preventDefault();var e=$t(this).parent();e.hide(),e.prev(".rank-math-showmore").show()}))},columnActions:function(){var t=this;this.wrap.on("click",".rank-math-redirection-action",(function(e){e.preventDefault();var r=$t(this),n=r.data("action"),i=this.href.replace("admin.php","admin-ajax.php").replace("page=rank-math-redirections&","")+"&action=rank_math_redirection_"+n;$t.ajax({url:i,type:"GET",success:function(e){e&&e.success&&(["delete","trash","restore"].includes(n)?r.closest("tr").fadeOut(800,(function(){$t(this).remove()})):r.closest("tr").toggleClass("rank-math-redirection-activated rank-math-redirection-deactivated"),"activate"===n?(t.filterCountAdd("active"),t.filterCountSubstract("inactive")):"deactivate"===n?(t.filterCountAdd("inactive"),t.filterCountSubstract("active")):"trash"===n?(t.filterCountAdd("trashed"),t.filterCountSubstract("all"),r.closest(".rank-math-redirection-deactivated").length?t.filterCountSubstract("inactive"):t.filterCountSubstract("active")):"delete"===n?t.filterCountSubstract("trashed"):"restore"===n&&(t.filterCountAdd("active"),t.filterCountAdd("all"),t.filterCountSubstract("trashed")))}})}))},filterCountAdd:function(t){this.filterCount(t,"add")},filterCountSubstract:function(t){this.filterCount(t,"sub")},filterCount:function(t,e){var r=this.wrap.find("form > ul.subsubsub > ."+t+" .count"),n=parseInt(r.text().substr(1));n="add"===e?n+1:n-1,r.text("("+n+")")}};t.init()}))}()}();