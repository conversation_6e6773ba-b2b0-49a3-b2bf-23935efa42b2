!function(){var e={184:function(e,t){var r;!function(){"use strict";var n={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var i=typeof r;if("string"===i||"number"===i)e.push(r);else if(Array.isArray(r)){if(r.length){var a=o.apply(null,r);a&&e.push(a)}}else if("object"===i){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){e.push(r.toString());continue}for(var l in r)n.call(r,l)&&r[l]&&e.push(l)}}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(r=function(){return o}.apply(t,[]))||(e.exports=r)}()},317:function(e,t,r){"use strict";var n=r(81),o=r.n(n),i=r(645),a=r.n(i)()(o());a.push([e.id,':root{--rankmath-wp-adminbar-height: 0}p.field-description{padding-top:.5em;margin:0;color:#666;letter-spacing:.01em}.form-table{width:100%;margin-top:.5em;border-collapse:collapse;clear:both}.form-table,.form-table td,.form-table td p,.form-table th{font-size:14px}.field-metabox{margin:0;clear:both}.field-metabox>.field-row:first-of-type>.field-td,.field-metabox>.field-row:first-of-type>.field-th,.field-metabox.field-list>.field-row:first-of-type>.field-td,.field-metabox.field-list>.field-row:first-of-type>.field-th{border:0}.field-metabox .note{margin-right:5px;padding:2px 6px;border-radius:3px;color:#794800;background:rgba(255,190,95,.5)}.field-th{float:left;width:200px;padding:20px 10px 20px 0;color:#222;font-weight:600;vertical-align:top}.field-th label{display:block;padding:5px 0}.field-th+.field-td{float:left}.field-td{max-width:100%;padding:15px 10px;line-height:1.3;vertical-align:middle}[id^=field-metabox-rank] .field-row{margin:0;padding:25px 0}[id^=field-metabox-rank] .field-row:not(.field-type-title):first-of-type{padding-top:0}[id^=field-metabox-rank] .field-row:not(.field-type-title):last-of-type{padding-bottom:0}[id^=field-metabox-rank] .field-row.rank-math-advanced-option~.rank-math-advanced-option,[id^=field-metabox-rank] .field-row:not(.rank-math-advanced-option):not(.tab-header):not(.field-type-notice)~.field-row:not(.rank-math-advanced-option):not(.rank-math-notice){border-top:1px solid #dadfe4;border-bottom:0}[id^=field-metabox-rank] .field-row .field-td,[id^=field-metabox-rank] .field-row .field-th{padding:0;box-sizing:border-box}[id^=field-metabox-rank] .field-row .field-th{width:25%;max-width:200px;color:#242628}[id^=field-metabox-rank] .field-row .field-th label{padding:0 15px 0 0}[id^=field-metabox-rank] .field-row .field-td{float:left;width:75%}[id^=field-metabox-rank] .field-description{padding-top:10px;color:#7f868d;font-size:14px;font-style:normal}[id^=field-metabox-rank] .media-status .img-status img{box-shadow:0 0 0 1px #e9e9e9;outline:none}.field-list>.field-row{vertical-align:top}.field-wrap{margin:0}.field-wrap .field-row{position:relative;margin:0}.field-wrap .field-row::after{content:"";display:block;width:100%;clear:both}.field-wrap .field-row:first-of-type>.field-td .rank-math-button.toggle-all-capabilities{top:32px}.field-wrap+footer.form-footer{display:flex;justify-content:space-between;flex-wrap:wrap;padding:1.875rem;margin:30px -1.875rem -1.875rem;border:0;border-top:1px solid #c3c4c7;border-radius:0 0 6px 6px;box-sizing:border-box;width:auto;background:#f8f9fa;text-align:center;overflow:hidden}.field-wrap+footer.form-footer .rank-math-button{align-items:center;justify-content:center;padding:0}.field-wrap ul{margin:0}.field-wrap li{margin:1px 0 5px 0;font-size:14px;line-height:16px}.field-disabled{opacity:.4;pointer-events:none}@media(min-width: 641px){.field-td .rank-math-button.toggle-all-capabilities{position:absolute;top:56px;left:0;font-weight:600}}@media screen and (max-width: 782px){.form-table label{font-size:14px}}@media screen and (max-width: 640px){.field-td .rank-math-button.toggle-all-capabilities{margin:1em 0 1.7em}[id^=field-metabox-rank] .field-row .field-td,[id^=field-metabox-rank] .field-row .field-th label{padding:0 0 15px 2px}[id^=field-metabox-rank] .field-row .field-td,[id^=field-metabox-rank] .field-row .field-th{width:100%;padding:0}.field-wrap .field-row{padding:15px 0}}@media screen and (max-width: 450px){.field-th{display:block;float:none;width:100%;padding-bottom:1em;font-size:1.2em;text-align:left}.field-th label{display:block;margin-top:0;margin-bottom:.5em}.field-td,.field-th+.field-td{display:block;float:none;width:100%}}.field-disabled{opacity:.4;pointer-events:none}',""]),t.Z=a},653:function(e,t,r){"use strict";var n=r(81),o=r.n(n),i=r(645),a=r.n(i)()(o());a.push([e.id,".rank-math-tab-header{margin:-1.875rem -1.875rem 30px;padding:1.875rem 1.875rem 0;text-align:center;border-bottom:1px solid #b5bfc9;border-radius:6px 6px 0 0;background-color:#f8f9fa}.rank-math-tab-header h2{margin:0;font-size:30px;font-weight:500}.rank-math-tab-header p{font-size:1rem;max-width:715px;margin:0 auto 2rem}",""]),t.Z=a},896:function(e,t,r){"use strict";var n=r(81),o=r.n(n),i=r(645),a=r.n(i)()(o());a.push([e.id,"/*!\n * Plugin:\tRank Math - Role Manager\n * URL:\t\thttps://rankmath.com/wordpress/plugin/seo-suite/\n * Name:\trole-manager.css\n */@media(min-width: 1200px){.field-wrap .field-big-labels .rank-math-checkbox-list li{width:33.33%}}footer.form-footer .rank-math-button:first-of-type{width:90px}footer.form-footer .rank-math-button:last-of-type{width:150px}",""]),t.Z=a},645:function(e){"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var r="",n=void 0!==t[5];return t[4]&&(r+="@supports (".concat(t[4],") {")),t[2]&&(r+="@media ".concat(t[2]," {")),n&&(r+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),r+=e(t),n&&(r+="}"),t[2]&&(r+="}"),t[4]&&(r+="}"),r})).join("")},t.i=function(e,r,n,o,i){"string"==typeof e&&(e=[[null,e,void 0]]);var a={};if(n)for(var l=0;l<this.length;l++){var u=this[l][0];null!=u&&(a[u]=!0)}for(var c=0;c<e.length;c++){var f=[].concat(e[c]);n&&a[f[0]]||(void 0!==i&&(void 0===f[5]||(f[1]="@layer".concat(f[5].length>0?" ".concat(f[5]):""," {").concat(f[1],"}")),f[5]=i),r&&(f[2]?(f[1]="@media ".concat(f[2]," {").concat(f[1],"}"),f[2]=r):f[2]=r),o&&(f[4]?(f[1]="@supports (".concat(f[4],") {").concat(f[1],"}"),f[4]=o):f[4]="".concat(o)),t.push(f))}},t}},81:function(e){"use strict";e.exports=function(e){return e[1]}},379:function(e){"use strict";var t=[];function r(e){for(var r=-1,n=0;n<t.length;n++)if(t[n].identifier===e){r=n;break}return r}function n(e,n){for(var i={},a=[],l=0;l<e.length;l++){var u=e[l],c=n.base?u[0]+n.base:u[0],f=i[c]||0,s="".concat(c," ").concat(f);i[c]=f+1;var p=r(s),d={css:u[1],media:u[2],sourceMap:u[3],supports:u[4],layer:u[5]};if(-1!==p)t[p].references++,t[p].updater(d);else{var m=o(d,n);n.byIndex=l,t.splice(l,0,{identifier:s,updater:m,references:1})}a.push(s)}return a}function o(e,t){var r=t.domAPI(t);r.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;r.update(e=t)}else r.remove()}}e.exports=function(e,o){var i=n(e=e||[],o=o||{});return function(e){e=e||[];for(var a=0;a<i.length;a++){var l=r(i[a]);t[l].references--}for(var u=n(e,o),c=0;c<i.length;c++){var f=r(i[c]);0===t[f].references&&(t[f].updater(),t.splice(f,1))}i=u}}},569:function(e){"use strict";var t={};e.exports=function(e,r){var n=function(e){if(void 0===t[e]){var r=document.querySelector(e);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}t[e]=r}return t[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(r)}},216:function(e){"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},565:function(e,t,r){"use strict";e.exports=function(e){var t=r.nc;t&&e.setAttribute("nonce",t)}},795:function(e){"use strict";e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(r){!function(e,t,r){var n="";r.supports&&(n+="@supports (".concat(r.supports,") {")),r.media&&(n+="@media ".concat(r.media," {"));var o=void 0!==r.layer;o&&(n+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),n+=r.css,o&&(n+="}"),r.media&&(n+="}"),r.supports&&(n+="}");var i=r.sourceMap;i&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(n,e,t.options)}(t,e,r)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},589:function(e){"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={id:n,exports:{}};return e[n](i,i.exports,r),i.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.nc=void 0,function(){"use strict";var e={};r.r(e),r.d(e,{resetStore:function(){return be},resetdirtySettings:function(){return me},setStep:function(){return he},toggleLoaded:function(){return ye},updateModules:function(){return de},updateSettings:function(){return pe},updateView:function(){return ge}});var t={};r.r(t),r.d(t,{appData:function(){return Pe},appUi:function(){return De}});var n={};r.r(n),r.d(n,{getAppData:function(){return Me},getCurrentStep:function(){return Le},getModules:function(){return Re},getSettings:function(){return Ie},getView:function(){return Ze},getdirtySettings:function(){return Ne},isLoaded:function(){return Ue}});var o=jQuery,i=r.n(o),a=lodash,l=wp.element,u=wp.i18n;function c(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=rankMath.links[e]||"";if(!r)return"#";if(!t)return r;var n={utm_source:"Plugin",utm_medium:encodeURIComponent(t),utm_campaign:"WP"};return r+"?"+Object.keys(n).map((function(e){return"".concat(e,"=").concat(n[e])})).join("&")}var f=r(379),s=r.n(f),p=r(795),d=r.n(p),m=r(569),b=r.n(m),y=r(565),g=r.n(y),h=r(216),v=r.n(h),w=r(589),O=r.n(w),S=r(653),k={};k.styleTagTransform=O(),k.setAttributes=g(),k.insert=b().bind(null,"head"),k.domAPI=d(),k.insertStyleElement=v();s()(S.Z,k),S.Z&&S.Z.locals&&S.Z.locals;var j=function(e){var t=e.title,r=e.description,n=e.link;return wp.element.createElement("header",{className:"rank-math-tab-header"},wp.element.createElement("h2",null,t),wp.element.createElement("p",null,r,n&&wp.element.createElement(React.Fragment,null," ",wp.element.createElement("a",{href:n,target:"_blank",rel:"noreferrer"},(0,u.__)("Learn more","rank-math")),".")))},x=wp.compose,P=wp.data,E=wp.apiFetch,_=r.n(E),T=window.rankMathComponents;var A=["validate","afterSave"];function C(){return C=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},C.apply(this,arguments)}function D(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function M(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],u=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);u=!0);}catch(e){c=!0,o=e}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return N(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return N(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var I=function(e,t){return"resetting"===t?(0,u.__)("Resetting…","rank-math"):"resetted"===t?(0,u.__)("Resetted","rank-math"):e.children},R=function(e,t){return"updating"===t?(0,u.__)("Updating…","rank-math"):"updated"===t?(0,u.__)("Updated","rank-math"):e.children},U=(0,x.compose)((0,P.withSelect)((function(e){return{settings:e("rank-math-settings").getdirtySettings()}})),(0,P.withDispatch)((function(e,t){var r=t.type,n=t.settings,o=t.footer.applyButton;return{saveSettings:function(t){t("updating"),_()({method:"POST",path:"/rankmath/v1/updateSettings",data:{type:r,settings:n[r]}}).then((function(r){return t("updated"),r.error?(function(e,t,r,n){t=t||"error",n=n||!1;var o=i()('<div class="notice notice-'+t+' is-dismissible"><p>'+e+"</p></div>").hide();r.next(".notice").remove(),r.after(o),o.slideDown(),i()(document).trigger("wp-updates-notice-added"),i()("html,body").animate({scrollTop:o.offset().top-50},"slow"),n&&setTimeout((function(){o.fadeOut((function(){o.remove()}))}),n)}(r.error,"error",i()(".wp-heading-inline")),void i()("html, body").animate({scrollTop:0},"fast")):r?(r&&!r.error&&o.afterSave&&o.afterSave(),t("updated"),void e("rank-math-settings").resetdirtySettings()):(t(""),void window.alert((0,u.__)("Something went wrong! Please try again.","rank-math")))}))},resetSettings:function(e){e("resetting"),_()({method:"POST",path:"/rankmath/v1/resetSettings",data:{type:r}}).then((function(t){if(!t)return e(""),void window.alert((0,u.__)("Something went wrong! Please try again.","rank-math"));e("resetted"),window.location.reload()}))}}})))((function(e){var t=M((0,l.useState)(""),2),r=t[0],n=t[1],o=e.settings,i=e.resetSettings,u=e.saveSettings,c=e.footer,f=c.applyButton,s=f.validate,p=(f.afterSave,D(f,A));return(0,l.useEffect)((function(){(0,a.includes)(["updated","resetted"],r)&&setTimeout((function(){return n("")}),1e3)}),[r]),wp.element.createElement("footer",{className:"form-footer rank-math-ui"},wp.element.createElement(T.Button,C({onClick:function(){i(n)}},c.discardButton,{children:I(c.discardButton,r)})),wp.element.createElement(T.Button,C({variant:"primary",onClick:function(){(!s||s())&&u(n)},disabled:(0,a.isEmpty)(o)},p,{children:R(p,r)})))})),Z=r(184),L=r.n(Z);function z(e){return z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},z(e)}var B=["id","type","content","Component","isDisabled"];function F(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function H(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?F(Object(r),!0).forEach((function(t){K(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):F(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function K(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==z(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==z(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===z(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function G(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var V=(0,x.compose)((0,P.withSelect)((function(e,t){var r=e("rank-math-settings").getAppData();return{field:t.field,settingType:t.settingType,settings:r}})),(0,P.withDispatch)((function(e,t){var r=t.settings,n=t.settingType;return{updateSetting:function(t,o){r[n][t]=o,e("rank-math-settings").updateSettings(r)}}})))((function(e){var t,r=e.field,n=e.settingType,o=e.settings,i=r.id,u=r.type,c=r.content,f=r.Component,s=r.isDisabled,p=G(r,B),d=(null===(t=o[n])||void 0===t?void 0:t[i])||"",m=function(t){return e.updateSetting(i,t)},b=function(){var e={toggle:"checked",checkbox:"checked"}[u]||"value",t=(0,a.includes)(["component","group"],u);return H(H({},p),{},K(K({id:i},e,p.value||d),"onChange",p.onChange||!s&&m),t&&{settingType:n})},y={file:window.rankMathComponents.UploadFile,text:window.rankMathComponents.TextControl,select:window.rankMathComponents.SelectControl,toggle:window.rankMathComponents.ToggleControl,select_search:window.rankMathComponents.SelectWithSearch,multicheck:window.rankMathComponents.CheckboxList,multicheck_inline:window.rankMathComponents.CheckboxList,radio_inline:window.rankMathComponents.ToggleGroupControl,repeatable_group:window.rankMathComponents.RepeatableGroup,group:window.rankMathComponents.Group,checkbox:window.rankMathComponents.CheckboxControl}[u];return y?wp.element.createElement(y,b()):"component"===u?wp.element.createElement(f,b()):"raw"===u?(0,l.createElement)(c):null})),$=["relation"];function W(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],u=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);u=!0);}catch(e){c=!0,o=e}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return q(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return q(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function J(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function Q(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],u=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);u=!0);}catch(e){c=!0,o=e}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return X(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return X(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function X(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Y=r(317),ee={};ee.styleTagTransform=O(),ee.setAttributes=g(),ee.insert=b().bind(null,"head"),ee.domAPI=d(),ee.insertStyleElement=v();s()(Y.Z,ee),Y.Z&&Y.Z.locals&&Y.Z.locals;function te(e){return te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},te(e)}function re(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ne(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?re(Object(r),!0).forEach((function(t){oe(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):re(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function oe(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==te(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==te(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===te(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ie=function(e){var t=e.settingType,r=e.fields,n=e.settings,o=void 0===n?null:n;return wp.element.createElement("div",{className:"field-wrap form-table wp-core-ui rank-math-ui"},wp.element.createElement("div",{id:"field-metabox-rank-math-".concat(t),className:"field-metabox field-list"},(0,a.map)(r,(function(e){var r=e.id,n=e.type,i=e.name,l=e.desc,u=e.classes,c=e.content,f=e.dep,s=function(e,t){var r=(0,P.useSelect)((function(e){return e("rank-math-settings").getAppData()}));return(0,a.some)(e,(function(e){var n=Q(e,1)[0];return r[t][n]}))}(e.disableDep,t);if(!f||function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=e.relation,o=J(e,$);r=(0,a.isNull)(r)?(0,P.useSelect)((function(e){return e("rank-math-settings").getAppData()})):r;var i=function(e){var n,o=W(e,2),i=o[0],l=o[1],u=(0,a.isUndefined)(t)?r[i]:null===(n=r[t])||void 0===n?void 0:n[i];return(0,a.isArray)(l)?(0,a.includes)(l,u):l===u};return"and"===n?(0,a.every)((0,a.entries)(o),i):(0,a.some)((0,a.entries)(o),i)}(f,t,o)){if("raw"===n)return c;var p=L()("field-row",u,oe(oe({"field-disabled":s},"field-id-"+r,r),"field-type-"+n,n));return wp.element.createElement("div",{key:r,className:p},i&&wp.element.createElement("div",{className:"field-th"},wp.element.createElement("label",{htmlFor:r},i)),wp.element.createElement("div",{className:"field-td"},wp.element.createElement(V,{settingType:t,field:ne(ne({},e),{},{isDisabled:s})}),l&&wp.element.createElement("p",{className:"field-description",dangerouslySetInnerHTML:{__html:l}})))}}))))},ae=wp.hooks;function le(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return t=(0,ae.applyFilters)("rank_math_sanitize_settings",t,e,r),null!==n&&(n=(0,ae.applyFilters)("rank_math_sanitize_settings_value",n,e,r)),n=null===n?t:n,(0,ae.doAction)("rank_math_settings_changed",e,t,r),{type:"RANK_MATH_SETTINGS_DATA",key:e,value:t,settingsKey:r,settingsValue:n}}function ue(e,t){return(0,ae.doAction)("rank_math_update_app_ui",e,t),{type:"RANK_MATH_APP_UI",key:e,value:t}}function ce(e){return ce="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ce(e)}function fe(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function se(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==ce(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==ce(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ce(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pe(e){return le("settings",e,"settings")}function de(e,t){var r=wp.data.select("rank-math-settings").getModules();return r[e].isActive=t,(0,a.forEach)(r,(function(t,n){if((0,a.includes)(t.dep_modules,e)){var o=!1;(0,a.forEach)(t.dep_modules,(function(e){r[e].isActive||(o=!0)})),r[n].isDisabled=o,r[n].disabled=o}})),le("modules",function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fe(Object(r),!0).forEach((function(t){se(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fe(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},r))}function me(){return le("dirtySettings",{})}function be(){return{type:"RESET_STORE"}}function ye(e){return ue("isLoaded",e)}function ge(e){return ue("view",e)}function he(e){return ue("currentStep",e)}function ve(e){return ve="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ve(e)}function we(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Oe(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?we(Object(r),!0).forEach((function(t){Se(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):we(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Se(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==ve(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==ve(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ve(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ke={header_code:"301",status:"active",sources:[{comparison:"exact"}]},je={roleCapabilities:(0,a.get)(rankMath,"roleCapabilities",{}),redirections:rankMath.redirections||ke,modules:(0,a.get)(rankMath,"modulesList",{}),dirtySettings:{}},xe=Oe(Oe({},je),{},{redirections:ke});function Pe(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:je,t=arguments.length>1?arguments[1]:void 0,r=Oe({},e.dirtySettings);return!1!==t.settingsKey&&(r=t.settingsValue),"RANK_MATH_SETTINGS_DATA"===t.type?"dirtySettings"===t.key?Oe(Oe({},e),{},{dirtySettings:t.value}):Oe(Oe({},e),{},Se(Se({},t.key,t.value),"dirtySettings",r)):"RESET_STORE"===t.type?xe:e}function Ee(e){return Ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ee(e)}function _e(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Te(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_e(Object(r),!0).forEach((function(t){Ae(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_e(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ae(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Ee(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Ee(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ee(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ce={currentStep:"getting-started"};function De(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Ce,t=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===t.type?Te(Te({},e),{},Ae({},t.key,t.value)):e}function Me(e){return e.appData}function Ne(e){return e.appData.dirtySettings}function Ie(e){return e.appData.settings}function Re(e){return e.appData.modules}function Ue(e){return e.appUi.isLoaded}function Ze(e){return e.appUi.view}function Le(e){return e.appUi.currentStep}(0,P.registerStore)("rank-math-settings",{reducer:(0,P.combineReducers)(t),selectors:n,actions:e});var ze=function(e){var t=e.type,r=e.header,n=e.footer,o=e.fields,i=void 0===o?[]:o,a=e.settings,l=void 0===a?null:a;return wp.element.createElement(React.Fragment,null,r&&wp.element.createElement(j,r),wp.element.createElement(ie,{settingType:t,fields:i,settings:l}),n&&wp.element.createElement(U,{type:t,footer:n}))},Be=r(896),Fe={};Fe.styleTagTransform=O(),Fe.setAttributes=g(),Fe.insert=b().bind(null,"head"),Fe.domAPI=d(),Fe.insertStyleElement=v();s()(Be.Z,Fe),Be.Z&&Be.Z.locals&&Be.Z.locals;function He(e){return He="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},He(e)}function Ke(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ge(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ke(Object(r),!0).forEach((function(t){Ve(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ke(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ve(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==He(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==He(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===He(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var $e=function(){return wp.element.createElement("div",{className:"wrap rank-math-wrap"},wp.element.createElement("div",{className:"rank-math-box container"},wp.element.createElement("span",{className:"wp-header-end"}),wp.element.createElement(ze,{type:"roleCapabilities",fields:(e=rankMath,t=e.roles,r=e.capabilities,n=(0,a.map)((0,a.keys)(r),(function(e){return{id:e,label:r[e]}})),(0,a.map)((0,a.keys)(t),(function(e){var r=(0,a.map)(n,(function(t){return Ge(Ge({},t),"administrator"===e&&"rank_math_role_manager"===t.id?{disabled:!0}:{})}));return{id:e,name:t[e],options:r,type:"multicheck_inline",classes:"field-big-labels",toggleAll:{size:"small",className:"toggle-all-capabilities",children:(0,u.__)("Toggle All","rank-math")}}}))),header:{title:(0,u.__)("Role Manager","rank-math"),link:c("role-manager","Role Manager Page"),description:(0,u.__)("Control which user has access to which options of Rank Math.","rank-math")},footer:{discardButton:{children:(0,u.__)("Reset","rank-math")},applyButton:{children:(0,u.__)("Update Capabilities","rank-math")}}})));var e,t,r,n};i()((function(){var e=document.getElementById("rank-math-settings");(0,a.isNull)(e)||(0,l.createRoot)(e).render(wp.element.createElement($e,null))}))}()}();