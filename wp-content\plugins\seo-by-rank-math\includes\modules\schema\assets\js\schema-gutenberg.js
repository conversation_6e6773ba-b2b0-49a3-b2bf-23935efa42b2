!function(){var e={184:function(e,r){var a;!function(){"use strict";var t={}.hasOwnProperty;function i(){for(var e=[],r=0;r<arguments.length;r++){var a=arguments[r];if(a){var n=typeof a;if("string"===n||"number"===n)e.push(a);else if(Array.isArray(a)){if(a.length){var o=i.apply(null,a);o&&e.push(o)}}else if("object"===n){if(a.toString!==Object.prototype.toString&&!a.toString.toString().includes("[native code]")){e.push(a.toString());continue}for(var s in a)t.call(a,s)&&a[s]&&e.push(s)}}}return e.join(" ")}e.exports?(i.default=i,e.exports=i):void 0===(a=function(){return i}.apply(r,[]))||(e.exports=a)}()},703:function(e,r,a){"use strict";var t=a(414);function i(){}function n(){}n.resetWarningCache=i,e.exports=function(){function e(e,r,a,i,n,o){if(o!==t){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function r(){return e}e.isRequired=e;var a={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:r,element:e,elementType:e,instanceOf:r,node:e,objectOf:r,oneOf:r,oneOfType:r,shape:r,exact:r,checkPropTypes:n,resetWarningCache:i};return a.PropTypes=a,a}},697:function(e,r,a){e.exports=a(703)()},414:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}},r={};function a(t){var i=r[t];if(void 0!==i)return i.exports;var n=r[t]={exports:{}};return e[t](n,n.exports,a),n.exports}a.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(r,{a:r}),r},a.d=function(e,r){for(var t in r)a.o(r,t)&&!a.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},a.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},function(){"use strict";var e=lodash,r=wp.hooks,t=wp.i18n,i=wp.data,n=wp.components,o=wp.apiFetch,s=a.n(o),l={version:"1.0.0",properties:{author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Author Name","rank-math"),placeholder:"%name%"}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Image URL","rank-math")}}}},rating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating","rank-math"),help:(0,t.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Minimum","rank-math"),help:(0,t.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Maximum","rank-math"),help:(0,t.__)("Rating maximum score","rank-math"),placeholder:5}}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating","rank-math"),help:(0,t.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Minimum","rank-math"),help:(0,t.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Maximum","rank-math"),help:(0,t.__)("Rating maximum score","rank-math"),placeholder:5}}}}},bookEditions:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Edition","rank-math"),help:(0,t.__)("Either a specific edition of the written work, or the volume of the work","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Book"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Title","rank-math"),help:(0,t.__)("The title of the tome. Use for the title of the tome if it differs from the book. *Optional when tome has the same title as the book","rank-math")}}},bookEdition:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Edition","rank-math"),help:(0,t.__)("The edition of the book","rank-math")}}},isbn:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("ISBN","rank-math"),help:(0,t.__)("The ISBN of the print book","rank-math")}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("URL","rank-math"),help:(0,t.__)("URL specific to this edition if one exists","rank-math")}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Author Name","rank-math"),placeholder:"%name%"}}}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,t.__)("Date Published","rank-math"),help:(0,t.__)("Date of first publication of this tome","rank-math")}}},bookFormat:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"radio",label:(0,t.__)("Book Format","rank-math"),desc:"The format of the book.",options:{"https://schema.org/EBook":"eBook","https://schema.org/Hardcover":"Hardcover","https://schema.org/Paperback":"Paperback","https://schema.org/AudioBook":"Audio Book"},default:"https://schema.org/Hardcover"}}}},provider:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{type:"radio",label:(0,t.__)("Course Provider","rank-math"),classes:"show-property",options:{Organization:"Organization",Person:"Person"},default:"Organization"}}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Course Provider Name","rank-math")}}},sameAs:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Course Provider URL","rank-math")}}}},courseSchedule:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Course Schedule","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,value:"Schedule"}},duration:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Duration","rank-math"),help:(0,t.__)("Suggested pacing in repeatFrequency units (8601 duration format). For example, PT5H monthly means 5 hours per month. Use courseWorkload for total length.","rank-math")}}},repeatCount:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Repeat Count","rank-math"),help:(0,t.__)("The numerical value for how long the course lasts for, in repeatFrequency units. For example, if the repeatFrequency is monthly and the repeatCount is 4, the course lasts for 4 months.","rank-math")}}},repeatFrequency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Repeat Frequency","rank-math"),help:(0,t.__)("The duration and repeatCount properties are relative to this field. ","rank-math"),options:{"":(0,t.__)("Select Repeat Frequency","rank-math"),Daily:(0,t.__)("Daily","rank-math"),Weekly:(0,t.__)("Weekly","rank-math"),Monthly:(0,t.__)("Monthly","rank-math"),Yearly:(0,t.__)("Yearly","rank-math")},default:""}}},startDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,t.__)("Start Date","rank-math"),help:(0,t.__)("The start date for the course, in 8601 date format (YYYY-MM-DD), if applicable.","rank-math"),classes:"col-4"}}},endDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,t.__)("End Date","rank-math"),help:(0,t.__)("The end date for the course, in 8601 date format (YYYY-MM-DD), if applicable.","rank-math"),classes:"col-4"}}}},courseInstance:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Course Instance","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"CourseInstance"}},courseMode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Course Mode","rank-math"),help:(0,t.__)("The medium through which the course will be delivered.","rank-math"),options:{Online:"Online",Onsite:"Onsite",Blended:"Blended"},default:"Online"}}},courseWorkload:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Course Workload","rank-math"),help:(0,t.__)("Total time to watch all videos and complete all assignments and exams for the course. Use the 8601 format. Example: PT22H","rank-math")}}},courseSchedule:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Course Schedule","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,value:"Schedule"}},duration:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Duration","rank-math"),help:(0,t.__)("Suggested pacing in repeatFrequency units (8601 duration format). For example, PT5H monthly means 5 hours per month. Use courseWorkload for total length.","rank-math")}}},repeatCount:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Repeat Count","rank-math"),help:(0,t.__)("The numerical value for how long the course lasts for, in repeatFrequency units. For example, if the repeatFrequency is monthly and the repeatCount is 4, the course lasts for 4 months.","rank-math")}}},repeatFrequency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Repeat Frequency","rank-math"),help:(0,t.__)("The duration and repeatCount properties are relative to this field. ","rank-math"),options:{"":"Select Repeat Frequency",Daily:"Daily",Weekly:"Weekly",Monthly:"Monthly",Yearly:"Yearly"},default:""}}},startDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,t.__)("Start Date","rank-math"),help:(0,t.__)("The start date for the course, in 8601 date format (YYYY-MM-DD), if applicable.","rank-math"),classes:"col-4"}}},endDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,t.__)("End Date","rank-math"),help:(0,t.__)("The end date for the course, in 8601 date format (YYYY-MM-DD), if applicable.","rank-math"),classes:"col-4"}}}}},offers:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Offers","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Offer"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Name","rank-math")}}},category:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Category","rank-math")}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("URL","rank-math")}}},price:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Price","rank-math")}}},priceCurrency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Currency","rank-math")}}},availability:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Availability","rank-math"),help:(0,t.__)("Offer availability","rank-math"),classes:"col-4",options:{InStock:"In Stock",SoldOut:"Sold Out",PreOrder:"Preorder"},default:"InStock"}}},validFrom:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,t.__)("Price Valid From","rank-math"),help:(0,t.__)("The date when the item becomes valid.","rank-math")}}},priceValidUntil:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,t.__)("Price Valid Until","rank-math"),help:(0,t.__)("The date after which the price will no longer be available","rank-math")}}},inventoryLevel:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Inventory Level","rank-math")}}}},"virtual-location":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{value:"VirtualLocation"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Online Event URL","rank-math"),help:(0,t.__)("The URL of the online event, where people can join. This property is required if your event is happening online","rank-math")}}}},address:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Address","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"PostalAddress"}},streetAddress:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Street Address","rank-math")}}},addressLocality:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Locality","rank-math")}}},addressRegion:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Region","rank-math")}}},postalCode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Postal Code","rank-math")}}},addressCountry:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Country","rank-math")}}}},"physical-location":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Place"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Venue Name","rank-math"),help:(0,t.__)("The venue name.","rank-math")}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Venue URL","rank-math"),help:(0,t.__)("Website URL of the venue","rank-math")}}},address:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Address","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"PostalAddress"}},streetAddress:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Street Address","rank-math")}}},addressLocality:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Locality","rank-math")}}},addressRegion:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Region","rank-math")}}},postalCode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Postal Code","rank-math")}}},addressCountry:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Country","rank-math")}}}}},"event-performer":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Performer Information","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"radio",label:(0,t.__)("Performer","rank-math"),classes:"show-property",options:{Organization:"Organization",Person:"Person"},default:"Person"}}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Name","rank-math")}}},sameAs:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Website or Social Link","rank-math")}}}},"monetary-amount-unit":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Salary","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"QuantitativeValue"}},value:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Salary (Recommended)","rank-math"),help:(0,t.__)("Insert amount, e.g. 50.00, or a salary range, e.g. 40.00-50.00","rank-math"),classes:"col-4"}}},unitText:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Payroll (Recommended)","rank-math"),help:(0,t.__)("Salary amount is for","rank-math"),options:{"":"None",YEAR:"Yearly",MONTH:"Monthly",WEEK:"Weekly",DAY:"Daily",HOUR:"Hourly"},classes:"col-4"}}}},"monetary-amount":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"MonetaryAmount"}},currency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Salary Currency","rank-math"),help:(0,t.__)("ISO 4217 Currency code. Example: EUR","rank-math"),classes:"col-4"}}},value:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Salary","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"QuantitativeValue"}},value:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Salary (Recommended)","rank-math"),help:(0,t.__)("Insert amount, e.g. 50.00, or a salary range, e.g. 40.00-50.00","rank-math"),classes:"col-4"}}},unitText:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Payroll (Recommended)","rank-math"),help:(0,t.__)("Salary amount is for","rank-math"),options:{"":"None",YEAR:"Yearly",MONTH:"Monthly",WEEK:"Weekly",DAY:"Daily",HOUR:"Hourly"},classes:"col-4"}}}}},"hiring-organization":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Organization"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Hiring Organization","rank-math"),placeholder:"%org_name%",help:(0,t.__)("The name of the company. Leave empty to use your own company information.","rank-math"),classes:"col-4"}}},sameAs:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Organization URL (Recommended)","rank-math"),placeholder:"%org_url%",help:(0,t.__)("The URL of the organization offering the job position. Leave empty to use your own company information","rank-math"),classes:"col-6"}}},logo:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Organization Logo (Recommended)","rank-math"),placeholder:"%org_logo%",help:(0,t.__)("Logo URL of the organization offering the job position. Leave empty to use your own company information","rank-math"),classes:"col-6"}}}},brand:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Brand"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Brand Name","rank-math")}}}},calories:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"NutritionInformation"}},calories:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Calories","rank-math"),help:(0,t.__)("The number of calories in the recipe. Optional.","rank-math")}}}},"video-object":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Video","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"VideoObject"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Name","rank-math"),help:(0,t.__)("A recipe video Name","rank-math"),classes:"col-6"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),help:(0,t.__)("A recipe video Description","rank-math")}}},embedUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Video URL","rank-math"),help:(0,t.__)("A video URL. Optional.","rank-math"),classes:"col-6"}}},contentUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Content URL","rank-math"),help:(0,t.__)("A URL pointing to the actual video media file","rank-math"),classes:"col-6"}}},thumbnailUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Recipe Video Thumbnail","rank-math"),help:(0,t.__)("A recipe video thumbnail URL","rank-math"),classes:"col-6"}}},duration:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Duration","rank-math"),help:(0,t.__)("ISO 8601 duration format. Example: PT1H30M","rank-math"),classes:"col-6"}}},uploadDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,t.__)("Video Upload Date","rank-math"),classes:"col-6"}}}},instructionText:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"HowtoStep"}},text:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"textarea"}}}},instructions:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Recipe Instructions","rank-math"),help:(0,t.__)("Either a specific edition of the written work, or the volume of the work","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"HowToSection"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Name","rank-math"),help:(0,t.__)("Instruction name of the recipe.","rank-math")}}},itemListElement:{map:{isArray:!0,isGroup:!0,isRequired:!1,isRecommended:!1,arrayMap:"instructionText",arrayProps:{map:{classes:"show-delete-property-group"}},classes:"show-add-property-group",field:{label:(0,t.__)("Instruction Texts","rank-math")}}}},"geo-coordinates":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Geo Coordinates","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"GeoCoordinates"}},latitude:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Latitude","rank-math")}}},longitude:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Longitude","rank-math")}}}},"opening-hours":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Timings","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"OpeningHoursSpecification"}},dayOfWeek:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"checkbox",label:(0,t.__)("Open Days","rank-math"),options:{monday:"Monday",tuesday:"Tuesday",wednesday:"Wednesday",thursday:"Thursday",friday:"Friday",saturday:"Saturday",sunday:"Sunday"},default:[]}}},opens:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"timepicker",label:(0,t.__)("Opening Time","rank-math"),classes:"col-6",placeholder:"09:00 AM"}}},closes:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"timepicker",label:(0,t.__)("Closing Time","rank-math"),classes:"col-6",placeholder:"05:00 PM"}}}},cuisine:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1},cuisine:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Cuisine","rank-math")}}}}},schemas:{Article:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("Article","rank-math"),defaultEn:"Article"},headline:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),placeholder:"%seo_description%"}}},keywords:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Keywords","rank-math"),placeholder:"%keywords%"}}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{type:"radio",label:(0,t.__)("Article Type","rank-math"),classes:"show-property",options:{Article:"Article",BlogPosting:"Blog Post",NewsArticle:"News Article"},notice:{status:"warning",className:"article-notice",content:(0,t.__)("Google does not allow Person as the Publisher for articles. Organization will be used instead.","rank-math")}}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Author Name","rank-math"),placeholder:"%name%"}}}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Published Date","rank-math"),classes:"hide-group",default:"%date(Y-m-d\\TH:i:sP)%"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Modified Date","rank-math"),classes:"hide-group",default:"%modified(Y-m-d\\TH:i:sP)%"}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}},articleSection:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Article Section","rank-math"),classes:"hide-group",default:"%primary_taxonomy_terms%"}}}},Book:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("Book","rank-math"),defaultEn:"Book"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},reviewLocation:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,t.__)("Review Location","rank-math"),help:(0,t.__)("The review or rating must be displayed on the page to comply with Google's Schema guidelines.","rank-math"),options:{bottom:"Below Content",top:"Above Content",both:"Above and Below Content",custom:"Custom (use shortcode)"},default:"custom"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,t.__)("Shortcode","rank-math"),help:(0,t.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]",dependency:[{field:"reviewLocation",value:"custom"}]}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("URL","rank-math")}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Author Name","rank-math"),placeholder:"%name%"}}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating","rank-math"),help:(0,t.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Minimum","rank-math"),help:(0,t.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Maximum","rank-math"),help:(0,t.__)("Rating maximum score","rank-math"),placeholder:5}}}}},hasPart:{map:{isArray:!0,isGroup:!0,isRequired:!1,isRecommended:!1,arrayMap:"bookEditions",arrayProps:{map:{classes:"show-delete-property-group"}},field:{label:(0,t.__)("Editions","rank-math")}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},Course:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("Course","rank-math"),defaultEn:"Course"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},reviewLocation:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,t.__)("Review Location","rank-math"),help:(0,t.__)("The review or rating must be displayed on the page to comply with Google's Schema guidelines.","rank-math"),options:{bottom:"Below Content",top:"Above Content",both:"Above and Below Content",custom:"Custom (use shortcode)"},default:"custom"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,t.__)("Shortcode","rank-math"),help:(0,t.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]",dependency:[{field:"reviewLocation",value:"custom"}]}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),placeholder:"%seo_description%"}}},provider:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{type:"radio",label:(0,t.__)("Course Provider","rank-math"),classes:"show-property",options:{Organization:"Organization",Person:"Person"},default:"Organization"}}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Course Provider Name","rank-math")}}},sameAs:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Course Provider URL","rank-math")}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}},hasCourseInstance:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Course Instance","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"CourseInstance"}},courseMode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Course Mode","rank-math"),help:(0,t.__)("The medium through which the course will be delivered.","rank-math"),options:{Online:"Online",Onsite:"Onsite",Blended:"Blended"},default:"Online"}}},courseWorkload:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Course Workload","rank-math"),help:(0,t.__)("Total time to watch all videos and complete all assignments and exams for the course. Use the 8601 format. Example: PT22H","rank-math")}}},courseSchedule:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Course Schedule","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,value:"Schedule"}},duration:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Duration","rank-math"),help:(0,t.__)("Suggested pacing in repeatFrequency units (8601 duration format). For example, PT5H monthly means 5 hours per month. Use courseWorkload for total length.","rank-math")}}},repeatCount:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Repeat Count","rank-math"),help:(0,t.__)("The numerical value for how long the course lasts for, in repeatFrequency units. For example, if the repeatFrequency is monthly and the repeatCount is 4, the course lasts for 4 months.","rank-math")}}},repeatFrequency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Repeat Frequency","rank-math"),help:(0,t.__)("The duration and repeatCount properties are relative to this field. ","rank-math"),options:{"":"Select Repeat Frequency",Daily:"Daily",Weekly:"Weekly",Monthly:"Monthly",Yearly:"Yearly"},default:""}}},startDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,t.__)("Start Date","rank-math"),help:(0,t.__)("The start date for the course, in 8601 date format (YYYY-MM-DD), if applicable.","rank-math"),classes:"col-4"}}},endDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,t.__)("End Date","rank-math"),help:(0,t.__)("The end date for the course, in 8601 date format (YYYY-MM-DD), if applicable.","rank-math"),classes:"col-4"}}}}},offers:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Offers","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Offer"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Name","rank-math"),classes:"hide-group"}}},category:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Category","rank-math"),help:(0,t.__)("The pricing category of the course. Example: Free, Partially Free, Subscription, Paid","rank-math")}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("URL","rank-math"),classes:"hide-group"}}},price:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Price","rank-math"),help:(0,t.__)("The numerical price of the course, if applicable.","rank-math")}}},priceCurrency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Currency","rank-math"),help:(0,t.__)("The currency of the price of the course, in ISO 4217 currency format (3 letter code), if applicable.","rank-math")}}},availability:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Availability","rank-math"),help:(0,t.__)("Offer availability","rank-math"),classes:["col-4","hide-group"],options:{InStock:"In Stock",SoldOut:"Sold Out",PreOrder:"Preorder"},default:"InStock"}}},validFrom:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,t.__)("Price Valid From","rank-math"),help:(0,t.__)("The date when the item becomes valid.","rank-math"),classes:"hide-group"}}},priceValidUntil:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,t.__)("Price Valid Until","rank-math"),help:(0,t.__)("The date after which the price will no longer be available","rank-math"),classes:"hide-group"}}},inventoryLevel:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Inventory Level","rank-math"),classes:"hide-group"}}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating","rank-math"),help:(0,t.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Minimum","rank-math"),help:(0,t.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Maximum","rank-math"),help:(0,t.__)("Rating maximum score","rank-math"),placeholder:5}}}}}},Event:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("Event","rank-math"),defaultEn:"Event"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},reviewLocation:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,t.__)("Review Location","rank-math"),help:(0,t.__)("The review or rating must be displayed on the page to comply with Google's Schema guidelines.","rank-math"),options:{bottom:"Below Content",top:"Above Content",both:"Above and Below Content",custom:"Custom (use shortcode)"},default:"custom"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,t.__)("Shortcode","rank-math"),help:(0,t.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]",dependency:[{field:"reviewLocation",value:"custom"}]}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),placeholder:"%seo_description%"}}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"select",label:(0,t.__)("Event Type","rank-math"),help:(0,t.__)("Type of the event","rank-math"),classes:"show-property col-4",options:{Event:"Event",BusinessEvent:"Business Event",ChildrensEvent:"Childrens Event",ComedyEvent:"Comedy Event",DanceEvent:"Dance Event",DeliveryEvent:"Delivery Event",EducationEvent:"Education Event",ExhibitionEvent:"Exhibition Event",Festival:"Festival",FoodEvent:"Food Event",LiteraryEvent:"Literary Event",MusicEvent:"Music Event",PublicationEvent:"Publication Event",SaleEvent:"Sale Event",ScreeningEvent:"Screening Event",SocialEvent:"Social Event",SportsEvent:"Sports Event",TheaterEvent:"Theater Event",VisualArtsEvent:"Visual Arts Event"}}}},eventStatus:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Event Status","rank-math"),help:(0,t.__)("Current status of the event (optional)","rank-math"),options:{"":"None",EventScheduled:"Scheduled",EventCancelled:"Cancelled",EventPostponed:"Postponed",EventRescheduled:"Rescheduled",EventMovedOnline:"Moved Online"},classes:"col-4",default:"EventScheduled"}}},eventAttendanceMode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Event Attendance Mode","rank-math"),help:(0,t.__)("Indicates whether the event occurs online, offline at a physical location, or a mix of both online and offline.","rank-math"),options:{OfflineEventAttendanceMode:"Offline",OnlineEventAttendanceMode:"Online",MixedEventAttendanceMode:"Online + Offline"},default:"OfflineEventAttendanceMode",classes:"col-4"}}},VirtualLocation:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header",dependency:[{field:"eventAttendanceMode",value:["OnlineEventAttendanceMode","MixedEventAttendanceMode"]}]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{value:"VirtualLocation"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Online Event URL","rank-math"),help:(0,t.__)("The URL of the online event, where people can join. This property is required if your event is happening online","rank-math")}}}},location:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header",dependency:[{field:"eventAttendanceMode",value:["OfflineEventAttendanceMode","MixedEventAttendanceMode"]}]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Place"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Venue Name","rank-math"),help:(0,t.__)("The venue name.","rank-math")}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Venue URL","rank-math"),help:(0,t.__)("Website URL of the venue","rank-math")}}},address:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Address","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"PostalAddress"}},streetAddress:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Street Address","rank-math")}}},addressLocality:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Locality","rank-math")}}},addressRegion:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Region","rank-math")}}},postalCode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Postal Code","rank-math")}}},addressCountry:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Country","rank-math")}}}}},performer:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Performer Information","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"radio",label:(0,t.__)("Performer","rank-math"),classes:"show-property",options:{Organization:"Organization",Person:"Person"},default:"Person"}}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Name","rank-math")}}},sameAs:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Website or Social Link","rank-math")}}}},startDate:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Start Date","rank-math"),help:(0,t.__)("Date and time of the event","rank-math"),classes:"col-4"}}},endDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("End Date","rank-math"),help:(0,t.__)("End date and time of the event","rank-math"),classes:"col-4"}}},offers:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Offers","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Offer"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Name","rank-math"),classes:"hide-group",placeholder:"General Admission"}}},category:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Category","rank-math"),classes:"hide-group",placeholder:"primary"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("URL","rank-math")}}},price:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Price","rank-math")}}},priceCurrency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Currency","rank-math")}}},availability:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Availability","rank-math"),help:(0,t.__)("Offer availability","rank-math"),classes:"col-4",options:{InStock:"In Stock",SoldOut:"Sold Out",PreOrder:"Preorder"},default:"InStock"}}},validFrom:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,t.__)("Price Valid From","rank-math"),help:(0,t.__)("The date when the item becomes valid.","rank-math")}}},priceValidUntil:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,t.__)("Price Valid Until","rank-math"),help:(0,t.__)("The date after which the price will no longer be available","rank-math"),classes:"hide-group"}}},inventoryLevel:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Inventory Level","rank-math")}}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating","rank-math"),help:(0,t.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Minimum","rank-math"),help:(0,t.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Maximum","rank-math"),help:(0,t.__)("Rating maximum score","rank-math"),placeholder:5}}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},JobPosting:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("Job Posting","rank-math"),defaultEn:"Job Posting"},title:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),placeholder:"%seo_description%"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,t.__)("Shortcode","rank-math"),help:(0,t.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]"}},baseSalary:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"MonetaryAmount"}},currency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Salary Currency","rank-math"),help:(0,t.__)("ISO 4217 Currency code. Example: EUR","rank-math"),classes:"col-4"}}},value:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Salary","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"QuantitativeValue"}},value:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Salary (Recommended)","rank-math"),help:(0,t.__)("Insert amount, e.g. 50.00, or a salary range, e.g. 40.00-50.00","rank-math"),classes:"col-4"}}},unitText:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Payroll (Recommended)","rank-math"),help:(0,t.__)("Salary amount is for","rank-math"),options:{"":"None",YEAR:"Yearly",MONTH:"Monthly",WEEK:"Weekly",DAY:"Daily",HOUR:"Hourly"},classes:"col-4"}}}}},datePosted:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,t.__)("Date Posted","rank-math"),placeholder:"%date(Y-m-d)%",help:(0,t.__)("The original date on which employer posted the job. You can leave it empty to use the post publication date as job posted date","rank-math"),classes:"col-4"}}},validThrough:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,t.__)("Expiry Posted","rank-math"),help:(0,t.__)("The date when the job posting will expire. If a job posting never expires, or you do not know when the job will expire, do not include this property","rank-math"),classes:"col-4"}}},unpublish:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,t.__)("Unpublish when expired","rank-math"),options:{on:"Yes",off:"No"},help:(0,t.__)("If checked, post status will be changed to Draft and its URL will return a 404 error, as required by the Rich Result guidelines","rank-math"),classes:"col-4",default:"on"}}},employmentType:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"checkbox",multiple:!0,label:(0,t.__)("Employment Type (Recommended)","rank-math"),help:(0,t.__)("Type of employment. You can choose more than one value","rank-math"),options:{"":"None",FULL_TIME:"Full Time",PART_TIME:"Part Time",CONTRACTOR:"Contractor",TEMPORARY:"Temporary",INTERN:"Intern",VOLUNTEER:"Volunteer",PER_DIEM:"Per Diem",OTHER:"Other"},default:[]}}},hiringOrganization:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Organization"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Hiring Organization","rank-math"),placeholder:"%org_name%",help:(0,t.__)("The name of the company. Leave empty to use your own company information.","rank-math"),classes:"col-4"}}},sameAs:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Organization URL (Recommended)","rank-math"),placeholder:"%org_url%",help:(0,t.__)("The URL of the organization offering the job position. Leave empty to use your own company information","rank-math"),classes:"col-6"}}},logo:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Organization Logo (Recommended)","rank-math"),placeholder:"%org_logo%",help:(0,t.__)("Logo URL of the organization offering the job position. Leave empty to use your own company information","rank-math"),classes:"col-6"}}}},id:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Posting ID (Recommended)","rank-math"),help:(0,t.__)("The hiring organization's unique identifier for the job.","rank-math"),classes:"col-6"}}},jobLocation:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Place"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Venue Name","rank-math"),help:(0,t.__)("The venue name.","rank-math"),classes:"hide-group"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Venue URL","rank-math"),help:(0,t.__)("Website URL of the venue","rank-math"),classes:"hide-group"}}},address:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Address","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"PostalAddress"}},streetAddress:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Street Address","rank-math")}}},addressLocality:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Locality","rank-math")}}},addressRegion:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Region","rank-math")}}},postalCode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Postal Code","rank-math")}}},addressCountry:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Country","rank-math")}}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},Music:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("Music","rank-math"),defaultEn:"Music"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),placeholder:"%seo_description%"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,t.__)("Shortcode","rank-math"),help:(0,t.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("URL","rank-math"),placeholder:"%url%"}}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"radio",label:(0,t.__)("Music Type","rank-math"),classes:"show-property",options:{MusicGroup:"MusicGroup",MusicAlbum:"MusicAlbum"},default:"MusicGroup"}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},Person:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("Person","rank-math"),defaultEn:"Person"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),placeholder:"%seo_description%"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,t.__)("Shortcode","rank-math"),help:(0,t.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]"}},email:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Email","rank-math")}}},address:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Address","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"PostalAddress"}},streetAddress:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Street Address","rank-math")}}},addressLocality:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Locality","rank-math")}}},addressRegion:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Region","rank-math")}}},postalCode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Postal Code","rank-math")}}},addressCountry:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Country","rank-math")}}}},gender:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Gender","rank-math"),classes:"col-6"}}},jobTitle:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Job title","rank-math"),help:(0,t.__)("The job title of the person (for example, Financial Manager).","rank-math"),classes:"col-6"}}}},Product:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("Product","rank-math"),defaultEn:"Product"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Product name","rank-math"),placeholder:"%seo_title%"}}},reviewLocation:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,t.__)("Review Location","rank-math"),help:(0,t.__)("The review or rating must be displayed on the page to comply with Google's Schema guidelines.","rank-math"),options:{bottom:"Below Content",top:"Above Content",both:"Above and Below Content",custom:"Custom (use shortcode)"},default:"custom"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,t.__)("Shortcode","rank-math"),help:(0,t.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]",dependency:[{field:"reviewLocation",value:"custom"}]}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),placeholder:"%seo_description%"}}},sku:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Product SKU","rank-math")}}},brand:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Brand"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Brand Name","rank-math")}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}},gtin8:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Gtin","rank-math"),classes:"hide-group"}}},mpn:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("MPN","rank-math"),classes:"hide-group"}}},isbn:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("ISBN","rank-math"),classes:"hide-group"}}},offers:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Offers","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Offer"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Name","rank-math"),classes:"hide-group"}}},category:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Category","rank-math"),classes:"hide-group"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("URL","rank-math"),classes:"hide-group",placeholder:"%url%"}}},price:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Price","rank-math")}}},priceCurrency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Currency","rank-math")}}},availability:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Availability","rank-math"),help:(0,t.__)("Offer availability","rank-math"),classes:"col-4",options:{InStock:"In Stock",SoldOut:"Sold Out",PreOrder:"Preorder"},default:"InStock"}}},validFrom:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,t.__)("Price Valid From","rank-math"),help:(0,t.__)("The date when the item becomes valid.","rank-math"),classes:"hide-group"}}},priceValidUntil:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,t.__)("Price Valid Until","rank-math"),help:(0,t.__)("The date after which the price will no longer be available","rank-math")}}},inventoryLevel:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Inventory Level","rank-math"),classes:"hide-group"}}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating","rank-math"),help:(0,t.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Minimum","rank-math"),help:(0,t.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Maximum","rank-math"),help:(0,t.__)("Rating maximum score","rank-math"),placeholder:5}}}}}},Recipe:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("Recipe","rank-math"),defaultEn:"Recipe"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},reviewLocation:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,t.__)("Review Location","rank-math"),help:(0,t.__)("The review or rating must be displayed on the page to comply with Google's Schema guidelines.","rank-math"),options:{bottom:"Below Content",top:"Above Content",both:"Above and Below Content",custom:"Custom (use shortcode)"},default:"custom"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,t.__)("Shortcode","rank-math"),help:(0,t.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]",dependency:[{field:"reviewLocation",value:"custom"}]}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Published Date","rank-math"),classes:"hide-group",default:"%date(Y-m-d\\TH:i:sP)%"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Author Name","rank-math"),placeholder:"%name%"}}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),placeholder:"%seo_description%"}}},prepTime:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Preparation Time","rank-math"),help:(0,t.__)("ISO 8601 duration format. Example: PT1H30M","rank-math"),classes:"col-4"}}},cookTime:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Cooking Time","rank-math"),help:(0,t.__)("ISO 8601 duration format. Example: PT1H30M","rank-math"),classes:"col-4"}}},totalTime:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Total Time","rank-math"),help:(0,t.__)("ISO 8601 duration format. Example: PT1H30M","rank-math"),classes:"col-4"}}},recipeCategory:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Type","rank-math"),help:(0,t.__)("Type of dish, for example appetizer, or dessert.","rank-math"),classes:"col-4"}}},recipeCuisine:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Cuisine","rank-math"),help:(0,t.__)("The cuisine of the recipe (for example, French or Ethiopian).","rank-math"),classes:"col-4"}}},keywords:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Keywords","rank-math"),help:(0,t.__)("Other terms for your recipe such as the season, the holiday, or other descriptors. Separate multiple entries with commas.","rank-math"),classes:"col-4"}}},recipeYield:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Recipe Yield","rank-math"),help:(0,t.__)("Quantity produced by the recipe, for example 4 servings","rank-math"),classes:"col-4"}}},nutrition:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"NutritionInformation"}},calories:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Calories","rank-math"),help:(0,t.__)("The number of calories in the recipe. Optional.","rank-math")}}}},recipeIngredient:{map:{isArray:!0,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"show-add-property show-delete-property",field:{label:(0,t.__)("Recipe Ingredients","rank-math"),help:(0,t.__)("Recipe ingredients, add one item per line","rank-math")}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating","rank-math"),help:(0,t.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Minimum","rank-math"),help:(0,t.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Maximum","rank-math"),help:(0,t.__)("Rating maximum score","rank-math"),placeholder:5}}}}},video:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Video","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"VideoObject"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Name","rank-math"),help:(0,t.__)("A recipe video Name","rank-math"),classes:"col-6"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),help:(0,t.__)("A recipe video Description","rank-math")}}},embedUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Video URL","rank-math"),help:(0,t.__)("A video URL. Optional.","rank-math"),classes:"col-6"}}},contentUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Content URL","rank-math"),help:(0,t.__)("A URL pointing to the actual video media file","rank-math"),classes:"col-6"}}},thumbnailUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Recipe Video Thumbnail","rank-math"),help:(0,t.__)("A recipe video thumbnail URL","rank-math"),classes:"col-6"}}},duration:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Duration","rank-math"),help:(0,t.__)("ISO 8601 duration format. Example: PT1H30M","rank-math"),classes:"col-6"}}},uploadDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,t.__)("Video Upload Date","rank-math"),classes:"col-6"}}}},instructionType:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"radio",label:(0,t.__)("Instruction Type","rank-math"),options:{SingleField:"Single Field",HowToStep:"How To Step"},default:"SingleField"}}},instructionsSingleField:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,t.__)("Recipe Instructions","rank-math")},dependency:[{field:"instructionType",value:"SingleField"}]}},instructionsHowToStep:{map:{isArray:!0,isGroup:!0,isRequired:!1,isRecommended:!1,arrayMap:"instructions",arrayProps:{map:{classes:"show-delete-property-group"}},field:{label:(0,t.__)("Recipe Instructions","rank-math")},dependency:[{field:"instructionType",value:["HowToStep"]}]}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},Restaurant:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("Restaurant","rank-math"),defaultEn:"Restaurant"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),placeholder:"%seo_description%"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,t.__)("Shortcode","rank-math"),help:(0,t.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]"}},telephone:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Phone Number","rank-math")}}},priceRange:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Price Range","rank-math"),classes:"col-4"}}},address:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Address","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"PostalAddress"}},streetAddress:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Street Address","rank-math")}}},addressLocality:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Locality","rank-math")}}},addressRegion:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Region","rank-math")}}},postalCode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Postal Code","rank-math")}}},addressCountry:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,t.__)("Country","rank-math")}}}},geo:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Geo Coordinates","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"GeoCoordinates"}},latitude:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Latitude","rank-math")}}},longitude:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Longitude","rank-math")}}}},openingHoursSpecification:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Timings","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"OpeningHoursSpecification"}},dayOfWeek:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"checkbox",label:(0,t.__)("Open Days","rank-math"),options:{monday:"Monday",tuesday:"Tuesday",wednesday:"Wednesday",thursday:"Thursday",friday:"Friday",saturday:"Saturday",sunday:"Sunday"},default:[]}}},opens:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"timepicker",label:(0,t.__)("Opening Time","rank-math"),classes:"col-6",placeholder:"09:00 AM"}}},closes:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"timepicker",label:(0,t.__)("Closing Time","rank-math"),classes:"col-6",placeholder:"05:00 PM"}}}},servesCuisine:{map:{isArray:!0,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"show-add-property show-delete-property",field:{label:(0,t.__)("Serves Cuisine","rank-math")}}},hasMenu:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Menu URL","rank-math"),help:(0,t.__)("URL pointing to the menu of the restaurant.","rank-math"),classes:"col-6"}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},Service:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("Service","rank-math"),defaultEn:"Service"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),placeholder:"%seo_description%"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,t.__)("Shortcode","rank-math"),help:(0,t.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]"}},serviceType:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Service Type","rank-math"),help:(0,t.__)("The type of service being offered, e.g. veterans' benefits, emergency relief, etc.","rank-math"),classes:"col-4"}}},offers:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Offers","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Offer"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Name","rank-math"),classes:"hide-group"}}},category:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Category","rank-math"),classes:"hide-group"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("URL","rank-math"),classes:"hide-group"}}},price:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Price","rank-math")}}},priceCurrency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Currency","rank-math")}}},availability:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Availability","rank-math"),help:(0,t.__)("Offer availability","rank-math"),classes:["col-4","hide-group"],options:{InStock:"In Stock",SoldOut:"Sold Out",PreOrder:"Preorder"},default:"InStock"}}},validFrom:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,t.__)("Price Valid From","rank-math"),help:(0,t.__)("The date when the item becomes valid.","rank-math"),classes:"hide-group"}}},priceValidUntil:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,t.__)("Price Valid Until","rank-math"),help:(0,t.__)("The date after which the price will no longer be available","rank-math"),classes:"hide-group"}}},inventoryLevel:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Inventory Level","rank-math"),classes:"hide-group"}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},SoftwareApplication:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("Software","rank-math"),defaultEn:"Software"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},reviewLocation:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,t.__)("Review Location","rank-math"),help:(0,t.__)("The review or rating must be displayed on the page to comply with Google's Schema guidelines.","rank-math"),options:{bottom:"Below Content",top:"Above Content",both:"Above and Below Content",custom:"Custom (use shortcode)"},default:"custom"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,t.__)("Shortcode","rank-math"),help:(0,t.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]",dependency:[{field:"reviewLocation",value:"custom"}]}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),placeholder:"%seo_description%"}}},operatingSystem:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Operating System","rank-math"),help:(0,t.__)("For example, Windows 7, OSX 10.6, Android 1.6","rank-math"),classes:"col-6"}}},applicationCategory:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Application Category","rank-math"),help:(0,t.__)("For example, Game, Multimedia","rank-math"),classes:"col-6"}}},offers:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Offers","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Offer"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Name","rank-math"),classes:"hide-group"}}},category:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Category","rank-math"),classes:"hide-group"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("URL","rank-math"),classes:"hide-group"}}},price:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Price","rank-math")}}},priceCurrency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,t.__)("Currency","rank-math")}}},availability:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,t.__)("Availability","rank-math"),help:(0,t.__)("Offer availability","rank-math"),classes:["col-4","hide-group"],options:{InStock:"In Stock",SoldOut:"Sold Out",PreOrder:"Preorder"},default:"InStock"}}},validFrom:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,t.__)("Price Valid From","rank-math"),help:(0,t.__)("The date when the item becomes valid.","rank-math"),classes:"hide-group"}}},priceValidUntil:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,t.__)("Price Valid Until","rank-math"),help:(0,t.__)("The date after which the price will no longer be available","rank-math"),classes:"hide-group"}}},inventoryLevel:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Inventory Level","rank-math"),classes:"hide-group"}}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,t.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating","rank-math"),help:(0,t.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Minimum","rank-math"),help:(0,t.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,t.__)("Rating Maximum","rank-math"),help:(0,t.__)("Rating maximum score","rank-math"),placeholder:5}}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},VideoObject:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("Video","rank-math"),defaultEn:"Video"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,t.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,t.__)("Description","rank-math"),placeholder:"%seo_description%"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,t.__)("Shortcode","rank-math"),help:(0,t.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]"}},uploadDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Upload Date","rank-math"),classes:"hide-group",placeholder:"%date(Y-m-d\\TH:i:sP)%"}}},embedUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Embed URL","rank-math"),help:(0,t.__)("A URL pointing to the embeddable player for the video. Example: <code>https://www.youtube.com/embed/VIDEOID</code>","rank-math"),classes:"col-6"}}},contentUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Content URL","rank-math"),help:(0,t.__)("A URL pointing to the actual video media file like MP4, MOV, etc. Please leave it empty if you don't know the URL.","rank-math"),classes:"col-6"}}},duration:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Duration","rank-math"),help:(0,t.__)("ISO 8601 duration format. Example: 1H30M","rank-math"),classes:"col-6"}}},thumbnailUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,t.__)("Video Thumbnail","rank-math"),help:(0,t.__)("A video thumbnail URL","rank-math"),classes:"hide-group",placeholder:"%post_thumbnail%"}}}},WooCommerceProduct:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("WooCommerce Product","rank-math"),defaultEn:"WooCommerce Product"}},EDDProduct:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,t.__)("EDD Product","rank-math"),defaultEn:"EDD Product"}}}},m={set:function(e,r,a){return!!e&&(localStorage.setItem(e,JSON.stringify({value:r,expires:this.expiry(a)})),!0)},get:function(e){if(!e)return!1;var r=localStorage.getItem(e);return!!r&&((r=JSON.parse(r)).expires&&Date.now()>r.expires?(localStorage.removeItem(e),!1):r.value)},remove:function(e){return!!e&&(localStorage.removeItem(e),!0)},expiry:function(e){if(!e)return!1;if(-1===e){var r=new Date;return r.setYear(1970),r.getTime()}var a=parseInt(e),t=e.replace(a,"");return"d"===t&&(a=24*a*60*60*1e3),"h"===t&&(a=60*a*60*1e3),"m"===t&&(a=60*a*1e3),"s"===t&&(a*=1e3),Date.now()+a}};function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function p(e,r){for(var a=0;a<r.length;a++){var t=r[a];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,u(t.key),t)}}function c(e,r,a){return(r=u(r))in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}function u(e){var r=function(e,r){if("object"!==d(e)||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!==d(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"===d(r)?r:String(r)}var h="rank_math_schema_templates_store",y=function(){function a(){!function(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}(this,a),c(this,"cache",null),c(this,"templates",null),c(this,"verion","1.1.0"),"product"!==rankMath.postType&&delete l.schemas.WooCommerceProduct,"download"!==rankMath.postType&&delete l.schemas.EDDProduct,this.cache=(0,r.applyFilters)("rank_math_schema_maps",l),(0,r.doAction)("rank_math_schema_template_loaded")}var i,n,o;return i=a,n=[{key:"verifyCache",value:function(){var e=m.get(h);return!1!==e&&this.version===e.version&&(this.cache=e,(0,r.doAction)("rank_math_schema_template_loaded"),!0)}},{key:"fetchStore",value:function(){var e=this;s()({method:"GET",url:"//"+window.location.host+"/wp-json/rankmath/v1/getSchemas"}).then((function(a){m.set(h,a,"30d"),e.cache=a,(0,r.doAction)("rank_math_schema_template_loaded")}))}},{key:"getMap",value:function(r){var a=(0,e.get)(this.cache.properties,r,!1);return a||(0,e.get)(this.cache.schemas,r,!1)}},{key:"getTemplates",value:function(){var r=this,a=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return null===this.templates&&(this.templates=[],(0,e.forEach)(this.cache.schemas,(function(a,i){var n=(0,e.get)(a,"map.title",i);r.templates.push({type:i,title:"WooCommerceProduct"!==i?n:(0,t.__)("WooCommerce Product","rank-math")})})),a||this.templates.push({type:"PodcastEpisode",title:(0,t.__)("Podcast Episode","rank-math"),isPro:!0},{type:"Dataset",title:(0,t.__)("Dataset","rank-math"),isPro:!0},{type:"FactCheck",title:(0,t.__)("Fact Check","rank-math"),isPro:!0},{type:"Movie",title:(0,t.__)("Movie","rank-math"),isPro:!0},{type:"FAQ",title:(0,t.__)("FAQ","rank-math"),isPro:!0},{type:"HowTo",title:(0,t.__)("HowTo","rank-math"),isPro:!0}),this.templates=(0,e.orderBy)(this.templates,"type")),this.templates}}],n&&p(i.prototype,n),o&&p(i,o),Object.defineProperty(i,"prototype",{writable:!1}),a}(),f=new y;function _(e){return f.getMap(e)}function R(e,r){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!a){if(Array.isArray(e)||(a=function(e,r){if(!e)return;if("string"==typeof e)return b(e,r);var a=Object.prototype.toString.call(e).slice(8,-1);"Object"===a&&e.constructor&&(a=e.constructor.name);if("Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return b(e,r)}(e))||r&&e&&"number"==typeof e.length){a&&(e=a);var t=0,i=function(){};return{s:i,n:function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,o=!0,s=!1;return{s:function(){a=a.call(e)},n:function(){var e=a.next();return o=e.done,e},e:function(e){s=!0,n=e},f:function(){try{o||null==a.return||a.return()}finally{if(s)throw n}}}}function b(e,r){(null==r||r>e.length)&&(r=e.length);for(var a=0,t=new Array(r);a<r;a++)t[a]=e[a];return t}function k(r,a){if((0,e.isEmpty)(r)||a.id===r)return a;var t,i=R(a.properties);try{for(i.s();!(t=i.n()).done;){var n=t.value;if(n.id===r)return n;if(n.map.isGroup){var o=k(r,n);if(o)return o}}}catch(e){i.e(e)}finally{i.f()}}function g(r,a){if((0,e.isEmpty)(r))return a;var t,i=R(a.properties);try{for(i.s();!(t=i.n()).done;){var n=t.value;if(n.property===r)return n.value=(0,e.has)(a.metadata,r)?a.metadata[r]:n.value,n}}catch(e){i.e(e)}finally{i.f()}return!1}var v={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let A;const w=new Uint8Array(16);function G(){if(!A&&(A="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!A))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return A(w)}const q=[];for(let e=0;e<256;++e)q.push((e+256).toString(16).slice(1));function E(e,r=0){return q[e[r+0]]+q[e[r+1]]+q[e[r+2]]+q[e[r+3]]+"-"+q[e[r+4]]+q[e[r+5]]+"-"+q[e[r+6]]+q[e[r+7]]+"-"+q[e[r+8]]+q[e[r+9]]+"-"+q[e[r+10]]+q[e[r+11]]+q[e[r+12]]+q[e[r+13]]+q[e[r+14]]+q[e[r+15]]}var S=function(e,r,a){if(v.randomUUID&&!r&&!e)return v.randomUUID();const t=(e=e||{}).random||(e.rng||G)();if(t[6]=15&t[6]|64,t[8]=63&t[8]|128,r){a=a||0;for(let e=0;e<16;++e)r[a+e]=t[e];return r}return E(t)};function P(){return{id:"g-".concat(S()),property:"",properties:[],map:{isGroup:!0,isArray:!1,isRequired:!1,isRecommended:!1}}}function O(){return{id:"p-".concat(S()),property:"",value:"",map:{isGroup:!1,isArray:!1,isRequired:!1,isRecommended:!1}}}function C(e){return e.id="g-".concat(S()),e.properties.forEach((function(e){e.map.isGroup?C(e):e.id="p-".concat(S())})),e}var T=function r(a){if(!a)return P();var t=a.map.isGroup?P():O();return(0,e.forEach)(a,(function(a,i){if("map"!==i){var n=O();a.map.isGroup&&(n=r(a)),n.map=a.map,n.property=i,n.value=(0,e.get)(a.map,"value",(0,e.get)(a.map,"field.default","")),t.properties.push(n)}else(0,e.has)(a,"title")&&(t.map.title=a.title,t.map.defaultEn=a.defaultEn)})),t};function j(r){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=!1;arguments.length>2&&void 0!==arguments[2]&&arguments[2]||(t=(0,e.isString)(r)?_(r):r);var i=T(t);return(i=(0,e.merge)(i,a)).property=r,i}function N(e){return N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},N(e)}function L(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),a.push.apply(a,t)}return a}function M(e,r,a){return(r=function(e){var r=function(e,r){if("object"!==N(e)||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!==N(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"===N(r)?r:String(r)}(r))in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}function x(r,a){var t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return(0,e.isEmpty)(a)||(0,e.forEach)(a,(function(a,i){if("@context"!==i){var n=g(i,r);if(n)I(n,i,a,t);else{var o=!t&&_(i);(0,e.isArray)(a)?(n=P()).map.isArray=!0:n=(0,e.isObject)(a)||o?x(o?j(o):P(),a,t):O(),I(n,i,a,t),n.property=i,r.properties.push(n)}}})),r}var I=function(e,a,t){var i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!a)return e;var n=(0,r.applyFilters)("rank_math_schema_convert_value",!1,e,a,t,i);return!1!==n||!1!==(n=(0,r.applyFilters)("rank_math_schema_convert_"+a,!1,e,t))?n:(e.value=t,e)};function D(e,r){var a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return x(j(r,arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a),e,a)}function U(a){var t=function(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?L(Object(a),!0).forEach((function(r){M(e,r,a[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):L(Object(a)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))}))}return e}({},a),i=(0,r.applyFilters)("rank_math_schema_type",(0,e.get)(t,"@type","")),n=(0,e.get)(t,"metadata",{type:"template"});delete t.metadata;var o="custom"===n.type?P():j(i);(0,e.has)(o.map,"title")&&!(0,e.has)(n,"title")&&(n.title=o.map.title),(0,e.has)(n,"title")&&n.title===o.map.defaultEn&&(n.title=o.map.title),o.property=(0,e.get)(n,"title",i),o.metadata=n;var s=g("@type",o);if(!1!==s&&""!==s.value&&(t["@type"]=s.value),o=x(o,t,"custom"===n.type),"custom"!==n.type){var l=o.properties.pop();o.properties.unshift(l)}return o}function F(e){return F="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},F(e)}function H(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),a.push.apply(a,t)}return a}function B(e,r,a){return(r=function(e){var r=function(e,r){if("object"!==F(e)||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!==F(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"===F(r)?r:String(r)}(r))in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}var V=function(r){if(!(0,e.isEmpty)(r.value)||!(0,e.isUndefined)(r.map.field)&&"toggle"===r.map.field.type)return r.value;var a=(0,e.get)(r,"map.field.placeholder");return(0,e.isEmpty)(a)?(0,e.get)(r,"map.field.default",!1):a},Y=function(a){if((0,e.isEmpty)(a)||(0,e.isUndefined)(a.properties))return a;var t={};return"metadata"in a&&(t.metadata=function(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?H(Object(a),!0).forEach((function(r){B(e,r,a[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):H(Object(a)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))}))}return e}({},a.metadata),t.metadata.title=t.metadata.title?t.metadata.title:a.property),(0,e.map)(a.properties,(function(a){var i=(0,e.get)(a,"map.save",!0),n=(0,e.get)(a,"map.isHidden",!1);if((0,e.isEmpty)(a.properties)||(0,e.isEmpty)(a.properties[0])||(0,e.isEmpty)(a.properties[0].property)||"0"!==a.properties[0].property||(a.map.isArray=!0),!1!==i&&!n)if("metadata"!==i){var o=function(e){var a=!1;return a=(0,r.applyFilters)("rank_math_schema_process_value",a,e),(0,r.applyFilters)("rank_math_schema_process_"+e.property,a,e)}(a);if(!1===o){if(a.map.isArray){var s=[];return(0,e.map)(a.properties,(function(r){s.push((0,e.isUndefined)(r.properties)?r.value:W(r))})),void(t[a.property]=s)}if(a.map.isGroup){var l=W(a),m=(0,e.get)(l,"@type",(0,e.isUndefined)(l["@id"])?a.property:"");return m&&(l["@type"]=m),void(t[a.property]=l)}var d=V(a);!(0,e.isBoolean)(d)&&(0,e.isEmpty)(d)||!d||(t[a.property]=d)}else t[a.property]=o}else{var p=V(a);if((0,e.isEmpty)(p)&&"toggle"!==a.map.field.type)return;t.metadata[a.property]=p}})),t};function W(e){var a=Y(e);return a=(0,r.applyFilters)("rank_math_processed_schema_"+a["@type"],a),(0,r.applyFilters)("rank_math_processed_schema",a)}function z(e){return"string"!=typeof e&&(e=JSON.stringify(e,null,2)),(e=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")).replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,(function(e){var r="number";return/^"/.test(e)?r=/:$/.test(e)?"key":"string":/true|false/.test(e)?r="boolean":/null/.test(e)&&(r="null"),'<span class="'+r+'">'+e+"</span>"}))}function J(r,a){if(!(0,e.has)(r,"map.dependency"))return!0;var t=null,i=r.map.dependency,n=(0,e.get)(i,"relation","or");return(0,e.forEach)(i,(function(r){var i,o,s,l=g(r.field,a),m=(i=l.value,o=(0,e.get)(r,"value",!1),s=(0,e.get)(r,"comparison","="),(0,e.isArray)(o)&&"="===s?o.includes(i):(0,e.isArray)(o)&&"!="===s?!o.includes(i):"="===s&&i===o||"=="===s&&i===o||">="===s&&i>=o||"<="===s&&i<=o||">"===s&&i>o||"<"===s&&i<o||"!="===s&&i!==o);if("or"===n&&m)return t=!0,!1;"and"===n&&(t=(null===t||t)&&m)})),!!t}window.rankMath=window.rankMath||{},window.rankMath.Helpers=window.rankMath.Helpers||{},window.rankMath.Helpers={generateValidSchema:U,prettyJSON:z,processSchema:W};var Q=wp.compose;function K(r){return(0,e.get)({off:"rm-icon rm-icon-schema",Article:"rm-icon rm-icon-post",Book:"rm-icon rm-icon-book",Course:"rm-icon rm-icon-course",Dataset:"rm-icon rm-icon-dataset",Event:"rm-icon rm-icon-calendar",FactCheck:"rm-icon rm-icon-fact-check",JobPosting:"rm-icon rm-icon-job",Local:"rm-icon rm-icon-local-seo",Movie:"rm-icon rm-icon-movie",Music:"rm-icon rm-icon-music",Product:"rm-icon rm-icon-cart",Products:"rm-icon rm-icon-cart",WooCommerceProduct:"rm-icon rm-icon-cart",Recipe:"rm-icon rm-icon-recipe",Restaurant:"rm-icon rm-icon-restaurant",Video:"rm-icon rm-icon-video",Videos:"rm-icon rm-icon-video",VideoObject:"rm-icon rm-icon-video",Person:"rm-icon rm-icon-users",Review:"rm-icon rm-icon-star","Review snippets":"rm-icon rm-icon-star",Service:"rm-icon rm-icon-service",Software:"rm-icon rm-icon-software",SoftwareApplication:"rm-icon rm-icon-software","Sitelinks searchbox":"rm-icon rm-icon-search",FAQ:"rm-icon rm-icon-faq",FAQPage:"rm-icon rm-icon-faq",HowTo:"rm-icon rm-icon-howto",Breadcrumbs:"rm-icon rm-icon-redirection",PodcastEpisode:"rm-icon rm-icon-podcast"},r,"rm-icon rm-icon-schema")}function $(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=rankMath.links[e]||"";if(!a)return"#";if(!r)return a;var t={utm_source:"Plugin",utm_medium:encodeURIComponent(r),utm_campaign:"WP"};return a+"?"+Object.keys(t).map((function(e){return"".concat(e,"=").concat(t[e])})).join("&")}var X=React;function Z(e,r){return function(e){if(Array.isArray(e))return e}(e)||function(e,r){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var t,i,n,o,s=[],l=!0,m=!1;try{if(n=(a=a.call(e)).next,0===r){if(Object(a)!==a)return;l=!1}else for(;!(l=(t=n.call(a)).done)&&(s.push(t.value),s.length!==r);l=!0);}catch(e){m=!0,i=e}finally{try{if(!l&&null!=a.return&&(o=a.return(),Object(o)!==o))return}finally{if(m)throw i}}return s}}(e,r)||function(e,r){if(!e)return;if("string"==typeof e)return ee(e,r);var a=Object.prototype.toString.call(e).slice(8,-1);"Object"===a&&e.constructor&&(a=e.constructor.name);if("Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return ee(e,r)}(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ee(e,r){(null==r||r>e.length)&&(r=e.length);for(var a=0,t=new Array(r);a<r;a++)t[a]=e[a];return t}var re=function(e){var r=e.onClick,a=e.children,i=(e.className,(0,X.useRef)()),o=Z((0,X.useState)(!1),2),s=o[0],l=o[1],m=function(e){i.current.contains(e.target)||l(!1)};return(0,X.useEffect)((function(){return s?document.addEventListener("mousedown",m):document.removeEventListener("mousedown",m),function(){document.removeEventListener("mousedown",m)}}),[s]),wp.element.createElement("div",{ref:i,className:"rank-math-inline-confirmation"},!s&&a(l),s&&wp.element.createElement("div",{className:"rank-math-confirm-delete"},wp.element.createElement("span",null,(0,t.__)("Delete?","rank-math")),wp.element.createElement(n.Button,{isLink:!0,onClick:function(){l(!1),r()}},wp.element.createElement("span",null,(0,t.__)("Yes","rank-math"))),wp.element.createElement(n.Button,{isLink:!0,onClick:function(){return l(!1)}},wp.element.createElement("span",null,(0,t.__)("No","rank-math")))))},ae=(0,n.withFilters)("rankMath.schema.SchemaList")((function(r){var a=r.schemas,i=r.edit,o=r.trash,s=r.preview,l=r.showProNotice;return(0,e.isEmpty)(a)?null:wp.element.createElement("div",{className:"rank-math-schema-in-use"},wp.element.createElement("h4",{className:"rank-math-schema-section-title"},(0,t.__)("Schema in Use","rank-math")),l&&wp.element.createElement("div",{className:"components-notice rank-math-notice is-warning"},wp.element.createElement("div",{className:"components-notice__content"},(0,t.__)("Multiple Schemas are allowed in the","rank-math")," ",wp.element.createElement("a",{href:$("pro","Schema Tab Notice"),rel:"noreferrer noopener",target:"_blank"},wp.element.createElement("strong",null,(0,t.__)("PRO Version","rank-math"))))),(0,e.map)(a,(function(r,a){return wp.element.createElement("div",{key:a,id:"rank-math-schema-item",className:"rank-math-schema-item row"},wp.element.createElement("strong",{className:"rank-math-schema-name"},wp.element.createElement("i",{className:K(r["@type"])}),(0,e.get)(r,"metadata.title",r["@type"])),wp.element.createElement("span",{className:"rank-math-schema-item-actions"},wp.element.createElement(n.Button,{className:"button rank-math-edit-schema",isLink:!0,onClick:function(){return i(a)}},wp.element.createElement("i",{className:"rm-icon rm-icon-edit"}),wp.element.createElement("span",null,(0,t.__)("Edit","rank-math"))),wp.element.createElement(n.Button,{className:"button rank-math-preview-schema",isLink:!0,onClick:function(){return s(a)}},wp.element.createElement("i",{className:"rm-icon rm-icon-eye"}),wp.element.createElement("span",null,(0,t.__)("Preview","rank-math"))),wp.element.createElement(re,{key:a,onClick:function(){return o(a)}},(function(e){return wp.element.createElement(n.Button,{isLink:!0,className:"button rank-math-delete-schema",onClick:function(){return e(!0)}},wp.element.createElement("i",{className:"rm-icon rm-icon-trash"}),wp.element.createElement("span",null,(0,t.__)("Delete","rank-math")))}))))})))})),te=(0,Q.compose)((0,i.withSelect)((function(e){var r=e("rank-math").getSchemas();return{schemas:r,showProNotice:1<=Object.keys(r).length}})),(0,i.withDispatch)((function(e){return{trash:function(r){e("rank-math").deleteSchema(r)},edit:function(r){e("rank-math").setEditingSchemaId(r),e("rank-math").toggleSchemaEditor(!0)},preview:function(r){e("rank-math").setEditingSchemaId(r),e("rank-math").setEditorTab("codeValidation"),e("rank-math").toggleSchemaEditor(!0)}}})))(ae),ie=jQuery,ne=a.n(ie);function oe(e){return oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},oe(e)}function se(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),a.push.apply(a,t)}return a}function le(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?se(Object(a),!0).forEach((function(r){me(e,r,a[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):se(Object(a)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))}))}return e}function me(e,r,a){return(r=function(e){var r=function(e,r){if("object"!==oe(e)||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!==oe(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"===oe(r)?r:String(r)}(r))in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}function de(e){return function(e){if(Array.isArray(e))return pe(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,r){if(!e)return;if("string"==typeof e)return pe(e,r);var a=Object.prototype.toString.call(e).slice(8,-1);"Object"===a&&e.constructor&&(a=e.constructor.name);if("Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return pe(e,r)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pe(e,r){(null==r||r>e.length)&&(r=e.length);for(var a=0,t=new Array(r);a<r;a++)t[a]=e[a];return t}var ce=function(){ne()((function(e){new ClipboardJS(".structured-data-copy").on("success",(function(r){var a=e(r.trigger);a.addClass("copied"),setTimeout((function(){a.removeClass("copied")}),2e3)}))})),(0,r.addAction)("rank_math_loaded","rank-math",(function(){var a={},t=(0,e.get)(rankMath,"schemas",{});(0,e.map)(t,(function(t,i){var n=(0,e.get)(t,"@type");t=(0,r.applyFilters)("rank_math_pre_schema_"+n,t),t=U((0,r.applyFilters)("rank_math_pre_schema",t)),a[i]=(0,r.applyFilters)("rank_math_pre_edited_schema",t,i)})),(0,i.dispatch)("rank-math").updateEditSchemas(a)})),(0,r.addFilter)("rank_math_schema_type","rank-math",(function(r){return(0,e.isUndefined)(r)||(0,e.isEmpty)(r)?r:"NewsArticle"===r||"BlogPosting"===r?"Article":"MusicGroup"===r||"MusicAlbum"===r?"Music":r.includes("Event")||"Festival"===r?"Event":r})),(0,r.addFilter)("rank_math_schema_convert_author","rank-math",(function(r,a,t){return(0,e.isObject)(t)||(0,e.isUndefined)(a.properties)?r:(a.properties[1].value=t,a)})),(0,r.addFilter)("rank_math_schema_convert_value","rank-math",(function(r,a,t,i){var n=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(!a.map.isArray)return r;var o=a.map,s=o.arrayMap,l=void 0!==s&&s,m=o.arrayProps,d=void 0===m?{}:m;return l?((0,e.forEach)(i,(function(e){a.properties.push(D(e,l,d,n))})),a):(i=function(r){return(0,e.isObject)(r)?r:[r]}(i),(0,e.forEach)(i,(function(r,t){var i=(0,e.get)(r,"@type",!1);if(!1===i){var o=O();o.property=t,o.value=r,a.properties.push(o)}else a.properties.push(D(r,i,d,n))})),a)})),(0,r.addFilter)("rank_math_schema_convert_value","rank-math",(function(e,r,a,t){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];return r.map.isArray||!r.map.isGroup||i?e:x(r,t,i)}),20),(0,r.addFilter)("rank_math_pre_schema_Product","rank-math",(function(r){return(0,e.has)(r,"brand")&&!(0,e.isObject)(r.brand)&&(r.brand={"@type":"Brand",name:r.brand}),r})),(0,r.addFilter)("rank_math_processed_schema_Recipe","rank-math",(function(e){var r=e.instructionType;return delete e.instructionType,"SingleField"===r&&(e.recipeInstructions=e.instructionsSingleField,delete e.instructionsSingleField),"HowToStep"===r&&(1===e.instructionsHowToStep.length&&(e.recipeInstructions={"@type":"HowToSection",name:e.instructionsHowToStep[0].name,itemListElement:e.instructionsHowToStep[0].itemListElement}),e.instructionsHowToStep.length>1&&(e.recipeInstructions=de(e.instructionsHowToStep)),delete e.instructionsHowToStep),e}),20),(0,r.addFilter)("rank_math_pre_schema_Recipe","rank-math",(function(r){var a=r.recipeInstructions;if((0,e.isString)(a)&&(r.instructionType="SingleField",r.instructionsSingleField=a,delete r.recipeInstructions),(0,e.forEach)(["cookTime","prepTime","totalTime"],(function(a){(0,e.isUndefined)(r[a])||"PT"!==r[a]||delete r[a]})),(0,e.isArray)(a)&&(r.instructionType="HowToStep",(0,e.forEach)(a,(function(r,t){(0,e.isUndefined)(r.type)||(r["@type"]=r.type,delete r.type),(0,e.isUndefined)(r.itemListElement)||((0,e.isUndefined)(r.itemListElement.type)||(r.itemListElement["@type"]=r.itemListElement.type,delete r.itemListElement.type),(0,e.isArray)(r.itemListElement)||(r.itemListElement=[r.itemListElement])),a[t]=r})),r.instructionsHowToStep=a,delete r.recipeInstructions),!(0,e.isArray)(a)&&(0,e.isObject)(a)){var t=le(le({},a),{},{"@type":"HowToSection"});r.instructionType="HowToStep",r.instructionsHowToStep=[],r.instructionsHowToStep.push(t),delete r.recipeInstructions}return r})),(0,r.addFilter)("rank_math_processed_schema","rank-math",(function(r){var a=r.eventAttendanceMode;return(0,e.isUndefined)(a)||("MixedEventAttendanceMode"===a&&(r.location=[r.VirtualLocation,r.location],delete r.VirtualLocation),"OnlineEventAttendanceMode"===a&&(r.location=r.VirtualLocation,delete r.VirtualLocation)),r}),20),(0,r.addFilter)("rank_math_pre_schema","rank-math",(function(r){var a=r.eventAttendanceMode;return(0,e.isUndefined)(a)||("MixedEventAttendanceMode"===a&&(r.VirtualLocation=(0,e.find)(r.location,["@type","VirtualLocation"]),r.location=(0,e.find)(r.location,["@type","Place"])),"OnlineEventAttendanceMode"===a&&(r.VirtualLocation=r.location,delete r.location)),r})),(0,r.addFilter)("rank_math_pre_edited_schema","rank-math",(function(r,a){var t=r.properties;return(0,e.isEmpty)(t)||"new-9999"!==a||(r.properties=(0,e.map)(t,(function(a){return(0,e.includes)(["name","headline","title"],a.property)&&(a.value=r.metadata.name),"description"===a.property&&(a.value=r.metadata.description),a}))),r})),(0,r.addFilter)("rank_math_schema_apply_metadata_values_Job_Posting","rank-math",(function(r){return r.properties.map((function(a){return"unpublish"!==a.property||(0,e.isUndefined)(r.metadata.unpublish)||(a.value=r.metadata.unpublish),a})),r}))},ue=a(184),he=a.n(ue),ye=wp.keycodes,fe=wp.element,_e=a(697),Re=a.n(_e),be=function(r){var a=r.position,t=void 0===a?"middle right":a,i=r.value,o=r.onChange,s=r.children;return wp.element.createElement(n.Dropdown,{position:t,className:"rank-math-datepicker",contentClassName:"rank-math-datepicker__dialog",renderToggle:function(e){var r=e.onToggle,a=e.isOpen;return wp.element.createElement(fe.Fragment,null,s,wp.element.createElement(n.Button,{icon:"calendar-alt",onClick:r,"aria-expanded":a}))},renderContent:function(){return wp.element.createElement(n.DatePicker,{currentDate:i.split("T")[0],onMonthPreviewed:e.noop,onChange:function(e){o(e.split("T")[0])}})}})},ke=function(e){var r=e.position,a=void 0===r?"middle right":r,t=e.value,i=e.onChange,o=e.children;return wp.element.createElement(n.Dropdown,{position:a,className:"rank-math-datepicker",contentClassName:"rank-math-datepicker__dialog",renderToggle:function(e){var r=e.onToggle,a=e.isOpen;return wp.element.createElement(fe.Fragment,null,o,wp.element.createElement(n.Button,{icon:"calendar-alt",onClick:r,"aria-expanded":a}))},renderContent:function(){return wp.element.createElement(n.DateTimePicker,{is12Hour:!0,currentDate:t,onChange:i})}})};var ge={};(0,e.isUndefined)(rankMath.assessor)||(0,e.forEach)(rankMath.assessor.diacritics,(function(e,r){return ge[r]=new RegExp(e,"g")}));function ve(r){return(0,e.map)(r,(function(e,r){return{label:e,value:r}}))}var Ae=["value","onChange","type","options"];function we(e){return function(e){if(Array.isArray(e))return Ge(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,r){if(!e)return;if("string"==typeof e)return Ge(e,r);var a=Object.prototype.toString.call(e).slice(8,-1);"Object"===a&&e.constructor&&(a=e.constructor.name);if("Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return Ge(e,r)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ge(e,r){(null==r||r>e.length)&&(r=e.length);for(var a=0,t=new Array(r);a<r;a++)t[a]=e[a];return t}function qe(){return qe=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e},qe.apply(this,arguments)}function Ee(e,r){if(null==e)return{};var a,t,i=function(e,r){if(null==e)return{};var a,t,i={},n=Object.keys(e);for(t=0;t<n.length;t++)a=n[t],r.indexOf(a)>=0||(i[a]=e[a]);return i}(e,r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(t=0;t<n.length;t++)a=n[t],r.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(e,a)&&(i[a]=e[a])}return i}var Se=function(r){var a=r.value,t=r.onChange,i=r.type,o=r.options,s=void 0===o?{}:o,l=Ee(r,Ae);return(0,e.isUndefined)(l.help)||(l.help=(0,fe.RawHTML)({children:l.help})),"radio"===i?wp.element.createElement(n.RadioControl,qe({selected:a,options:ve(s),onChange:t},l)):"select"===i?(!(0,e.has)(l,"multiple")&&(0,e.isArray)(a)&&(a=a[0]),wp.element.createElement(n.SelectControl,qe({value:a,options:ve(s),onChange:t},l))):"toggle"===i?wp.element.createElement(n.ToggleControl,qe({checked:a,onChange:t},l)):"number"===i?wp.element.createElement(n.TextControl,qe({type:"number",autoComplete:"off",step:"0.01",value:a,onChange:t},l)):"url"===i?wp.element.createElement(n.TextControl,qe({type:"url",autoComplete:"off",value:a,onChange:t},l)):"datepicker"===i?wp.element.createElement(be,{value:a,position:"bottom left",onChange:t},wp.element.createElement(n.TextControl,qe({value:a,onChange:t},l))):"datetimepicker"===i?wp.element.createElement(ke,{value:a,position:"bottom left",onChange:t},wp.element.createElement(n.TextControl,qe({value:a,onChange:t},l))):"textarea"===i?wp.element.createElement(n.TextareaControl,qe({rows:5,value:a,onChange:t},l)):"checkbox"===i?wp.element.createElement("div",{className:"rank-math-checkbox-component components-base-control schema-property--value"},wp.element.createElement("label",{htmlFor:"checklist-label",className:"components-base-control__label"},l.label),wp.element.createElement("div",null,ve(s).map((function(r){return wp.element.createElement(n.CheckboxControl,{key:(0,e.uniqueId)("checkbox-"),label:r.label,checked:(0,e.includes)(a,r.value),onChange:function(i){var n=we(a);i?n.push(r.value):(0,e.remove)(n,(function(e){return e===r.value})),t(n)}})})))):wp.element.createElement(n.TextControl,qe({value:a,onChange:t},l))};Se.default={value:"",type:"text",onChange:null},Se.propTypes={id:Re().string,onChange:Re().func.isRequired};var Pe=Se;function Oe(e){return Oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Oe(e)}function Ce(){return Ce=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e},Ce.apply(this,arguments)}function Te(e,r){return function(e){if(Array.isArray(e))return e}(e)||function(e,r){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var t,i,n,o,s=[],l=!0,m=!1;try{if(n=(a=a.call(e)).next,0===r){if(Object(a)!==a)return;l=!1}else for(;!(l=(t=n.call(a)).done)&&(s.push(t.value),s.length!==r);l=!0);}catch(e){m=!0,i=e}finally{try{if(!l&&null!=a.return&&(o=a.return(),Object(o)!==o))return}finally{if(m)throw i}}return s}}(e,r)||function(e,r){if(!e)return;if("string"==typeof e)return je(e,r);var a=Object.prototype.toString.call(e).slice(8,-1);"Object"===a&&e.constructor&&(a=e.constructor.name);if("Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return je(e,r)}(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function je(e,r){(null==r||r>e.length)&&(r=e.length);for(var a=0,t=new Array(r);a<r;a++)t[a]=e[a];return t}function Ne(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),a.push.apply(a,t)}return a}function Le(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?Ne(Object(a),!0).forEach((function(r){Me(e,r,a[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):Ne(Object(a)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))}))}return e}function Me(e,r,a){return(r=function(e){var r=function(e,r){if("object"!==Oe(e)||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!==Oe(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"===Oe(r)?r:String(r)}(r))in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}var xe=(0,Q.compose)((0,i.withSelect)((function(e,r){return Le(Le({},r),{},{isPro:e("rank-math").isPro()})})))((function(a){var t=a.data,i=t.property,o=t.id,s=t.map,l=a.actions,m=l.removeProperty,d=l.propertyChange,p=l.duplicateProperty,c=(0,e.get)(s,"field",{label:!1}),u=Le({},c),h=Te((0,fe.useState)(),2),y=h[0],f=h[1];if((0,fe.useEffect)((function(){f(function(a,t){var i=a.data,n=i.property,o=i.value,s=a.schema.metadata;return o===(0,e.get)(t,"placeholder","")&&(o=""),"reviewLocation"===n&&(0,e.has)(s,"reviewLocation")&&(o=s.reviewLocation),"unpublish"===n&&(0,e.has)(s,"unpublish")&&(o=s.unpublish),a.isPro&&"[rank_math_rich_snippet]"===o&&(o='[rank_math_rich_snippet id="'+s.shortcode+'"]'),(0,r.applyFilters)("rank_math_schema_property_value",o,a)}(a,c))}),[o]),!1===J(a.data,a.schema))return a.data.map.isHidden=!0,null;s.isRequired&&(c.label&&(u.label=wp.element.createElement(fe.Fragment,null,c.label," ",wp.element.createElement("span",null,"*"))),c.placeholder||(u.required="required")),a.isCustom&&(u.type=(0,r.applyFilters)("rank_math_schema_custom_field_type","text",i),delete u.label);var _=he()("schema-group-or-property-container schema-property-container",(0,e.get)(c,"classes",!1),{"hide-property":"@type"===i});return a.data.map.isHidden=!1,wp.element.createElement("div",{className:_},wp.element.createElement("div",{className:"schema-group-or-property schema-property"},wp.element.createElement("div",{className:"schema-property--body"},!a.isCustom&&c.label?null:wp.element.createElement("div",{className:"schema-property--field"},wp.element.createElement(n.TextControl,{value:i,onChange:function(e){d(o,"property",e)}})),wp.element.createElement("div",{className:"schema-property--value"},wp.element.createElement(Pe,Ce({value:y},u,{onChange:function(e){f(e),d(o,"value",e)}})),(0,e.has)(u,"notice")&&wp.element.createElement(n.Notice,Ce({isDismissible:!1},u.notice),u.notice.content)),!s.isRequired&&wp.element.createElement("div",{className:"schema-property--header"},wp.element.createElement(n.Button,{isSecondary:!0,className:"button rank-math-duplicate-property",onClick:function(){return p(o,a.parentId,a.data)}},wp.element.createElement("i",{className:"rm-icon rm-icon-copy"})),wp.element.createElement(re,{key:o,onClick:function(){return m(o,a.parentId)}},(function(e){return wp.element.createElement(n.Button,{isSecondary:!0,className:"button rank-math-delete-group",onClick:function(){return e(!0)}},wp.element.createElement("i",{className:"rm-icon rm-icon-trash"}))}))))))})),Ie={},De=function a(i){if(!1===J(i.data,i.schema))return i.data.map.isHidden=!0,null;var o=i.parentId,s=i.isCustom,l=i.isPro,m=i.isMain,d=void 0!==m&&m,p=i.data,c=p.id,u=p.property,h=p.properties,y=p.map,f=p.metadata,_=i.actions,R=_.addProperty,b=_.addGroup,k=_.removeGroup,g=_.propertyChange,v=_.duplicateGroup,A=(0,e.get)(y,"field",{label:!1}),w=he()("schema-group-or-property-container schema-group-container",(0,e.get)(y,"classes",!1),{"hide-property":"metadata"===u,"is-group":y.isGroup,"is-array":y.isArray,"no-array-map":(0,e.isUndefined)(y.arrayMap)});return y.isArray&&(Ie[c]=0),i.data.map.isHidden=!1,wp.element.createElement("div",{className:w},wp.element.createElement("div",{className:"schema-group-or-property schema-group"},(0,r.applyFilters)("rank_math_schema_before_fields","",u),wp.element.createElement("div",{className:"schema-group-header"},function(){if(i.isArray)return Ie[o]+=1,wp.element.createElement("div",{className:"schema-property--label"},(0,e.startCase)(u)," ",Ie[o]);if(!s&&A.label)return wp.element.createElement("div",{className:"schema-property--label"},A.label,A.labelHelp&&wp.element.createElement("span",{className:"schema-property--label-help"},(0,fe.RawHTML)({children:A.labelHelp})));var r="WooCommerceProduct"!==u?u:"WooCommerce Product";return wp.element.createElement("div",{className:"schema-property--field"},wp.element.createElement(n.TextControl,{value:(0,e.isUndefined)(f)||(0,e.isEmpty)(f.title)?r:f.title,disabled:!l,onChange:function(e){g(c,"property",e)}}))}(),wp.element.createElement(n.ButtonGroup,{className:"schema-group--actions schema-group--actions--tr"},wp.element.createElement(n.Button,{className:"button rank-math-add-property",isLink:!0,onClick:function(){return R(c)}},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,t.__)("Add Property","rank-math"))),wp.element.createElement(n.Button,{className:"button rank-math-add-property-group",isLink:!0,onClick:function(){return b(c,y)}},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,t.__)("Add Property Group","rank-math"))),!d&&wp.element.createElement(n.Button,{className:"button rank-math-duplicate-property-group",isLink:!0,onClick:function(){return v(c,i.parentId,i.data)}},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,t.__)("Duplicate Group","rank-math"))),wp.element.createElement(re,{key:c,onClick:function(){return k(c,i.parentId)}},(function(e){return wp.element.createElement(n.Button,{className:"button rank-math-delete-group",isLink:!0,onClick:function(){return e(!0)}},wp.element.createElement("i",{className:"rm-icon rm-icon-trash"}),wp.element.createElement("span",null,(0,t.__)("Delete","rank-math")))})))),wp.element.createElement("div",{className:"schema-group--children"},(0,e.map)(h,(function(e,r){return e.map.isGroup?wp.element.createElement(a,{key:r,data:e,parentId:c,isArray:y.isArray,isCustom:s,schema:i.schema,actions:i.actions,isPro:l}):wp.element.createElement(xe,{key:r,data:e,parentId:c,isCustom:s,schema:i.schema,actions:i.actions})}))),d&&wp.element.createElement("div",{className:"schema-group-footer"},wp.element.createElement(n.ButtonGroup,{className:"schema-group--actions schema-group--actions--tr"},wp.element.createElement(n.Button,{className:"button rank-math-add-property",isLink:!0,onClick:function(){return R(c)}},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,t.__)("Add Property","rank-math"))),wp.element.createElement(n.Button,{className:"button rank-math-add-property-group",isLink:!0,onClick:function(){return b(c,y)}},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,t.__)("Add Property Group","rank-math"))),wp.element.createElement(re,{key:c,onClick:function(){return k(c,i.parentId)}},(function(e){return wp.element.createElement(n.Button,{className:"button rank-math-delete-group",isLink:!0,onClick:function(){return e(!0)}},wp.element.createElement("i",{className:"rm-icon rm-icon-trash"}),wp.element.createElement("span",null,(0,t.__)("Delete","rank-math")))}))))))};function Ue(e){return Ue="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ue(e)}function Fe(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),a.push.apply(a,t)}return a}function He(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?Fe(Object(a),!0).forEach((function(r){Je(e,r,a[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):Fe(Object(a)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))}))}return e}function Be(e,r){for(var a=0;a<r.length;a++){var t=r[a];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,Qe(t.key),t)}}function Ve(e,r){return Ve=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,r){return e.__proto__=r,e},Ve(e,r)}function Ye(e){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var a,t=ze(e);if(r){var i=ze(this).constructor;a=Reflect.construct(t,arguments,i)}else a=t.apply(this,arguments);return function(e,r){if(r&&("object"===Ue(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return We(e)}(this,a)}}function We(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ze(e){return ze=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ze(e)}function Je(e,r,a){return(r=Qe(r))in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}function Qe(e){var r=function(e,r){if("object"!==Ue(e)||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!==Ue(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"===Ue(r)?r:String(r)}var Ke=function(a){!function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),r&&Ve(e,r)}(m,a);var i,o,s,l=Ye(m);function m(){var a;return function(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}(this,m),Je(We(a=l.apply(this,arguments)),"addGroup",(function(e,r){var t=He({},a.state.data),i=k(e,t),n=r.isArray,o=r.arrayMap,s=void 0!==o&&o,l=r.arrayProps,m=n&&s?j(s,void 0===l?{}:l):P();i.properties.push(m),a.setState({data:t})})),Je(We(a),"addProperty",(function(e){var r=He({},a.state.data);k(e,r).properties.push(O()),a.setState({data:r})})),Je(We(a),"duplicateGroup",(function(r,t,i){var n=He({},a.state.data),o=k(t,n),s=(0,e.cloneDeep)(i),l=o.properties.findIndex((function(e){return e.id===r}));o.properties.splice(l,0,C(s)),a.setState({data:n})})),Je(We(a),"duplicateProperty",(function(e,r,t){var i=He({},a.state.data),n=k(r,i),o=He({},t),s=n.properties.findIndex((function(r){return r.id===e}));o.id="p-".concat(S()),n.properties.splice(s,0,o),a.setState({data:i})})),Je(We(a),"removeGroup",(function(e,r){var t=He({},a.state.data),i=k(r,t);if(i.id!==e){var n=i.properties.findIndex((function(r){return r.id===e}));i.properties.splice(n,1),a.setState({data:t})}else a.setState({data:P()})})),Je(We(a),"removeProperty",(function(e,r){var t=He({},a.state.data),i=k(r,t),n=i.properties.findIndex((function(r){return r.id===e}));i.properties.splice(n,1),a.setState({data:t})})),Je(We(a),"propertyChange",(function(t,i,n){var o=He({},a.state.data),s=k(t,o);Object.assign(s,Je({},i,n)),!(0,e.isEmpty)(o.metadata)&&(0,e.has)(o.metadata,s.property)&&"title"!==s.property&&(o.metadata[s.property]=n),"property"!==i||(0,e.isUndefined)(s.metadata)||(s.metadata.title=n),a.setState({data:o}),(0,r.doAction)("rank_math_property_changed",o,s,a.setState,i,n,a.state)})),a.options=(0,e.get)(a.props.data,"metadata",{}),a.state={data:a.props.data,loading:!1,showNotice:!1,postId:rankMath.objectID},a.setState=a.setState.bind(We(a)),a.templateSaveCount=0,a.isEditingTemplate=(0,e.get)(rankMath,"isTemplateScreen",!1),a}return i=m,(o=[{key:"getWrapperClasses",value:function(){var r=(0,e.get)(rankMath,"knowledgegraphType",!1);r=!1===r?"empty":"local-"+r;var a=(0,e.get)(this.props.data,"property","");return(0,e.isArray)(a)&&(a=a.join("-")),a="schema-"+a.toLowerCase(),he()("schema-builder",a,Je({"schema-template-pre-defined":"template"===this.options.type,"schema-template-custom":"custom"===this.options.type},"".concat(r),"template"===this.options.type))}},{key:"render",value:function(){var e=this;return wp.element.createElement("form",{className:this.getWrapperClasses()},wp.element.createElement(De,{data:this.state.data,schema:this.state.data,isPro:this.props.isPro,parentId:null,isMain:!0,isArray:!1,isCustom:"custom"===this.options.type,actions:{addGroup:this.addGroup,addProperty:this.addProperty,removeGroup:this.removeGroup,removeProperty:this.removeProperty,propertyChange:this.propertyChange,duplicateGroup:this.duplicateGroup,duplicateProperty:this.duplicateProperty}}),wp.element.createElement("div",{className:"schema-builder-save-as"},this.props.isPro&&wp.element.createElement(fe.Fragment,null,"custom"!==this.options.type&&wp.element.createElement(n.Button,{isSecondary:!0,onClick:function(){return e.props.toggleMode(e.props.id,e.state.data)}},(0,t.__)("Advanced Editor","rank-math")),!this.isEditingTemplate&&wp.element.createElement(n.Button,{isSecondary:!0,className:this.state.loading?"save-as-template saving":"save-as-template",onClick:function(){e.templateSaveCount>=1&&!confirm((0,t.__)("Each save will create a new template.","rank-math"))||(e.templateSaveCount+=1,e.props.saveTemplate(e.state.data,e.setState))}},this.state.showNotice?(0,t.__)("Template saved.","rank-math"):(0,t.__)("Save as Template","rank-math")),this.state.showNotice&&wp.element.createElement("div",{className:"rank-math-save-template-confirmation"},(0,t.__)("Template saved.","rank-math"))),this.isEditingTemplate&&wp.element.createElement(n.Button,{isPrimary:!0,className:"button",onClick:function(){return e.props.saveTemplate(e.state.data,e.setState,e.state.postId)}},this.state.loading?(0,t.__)("Saving","rank-math"):this.state.showNotice?(0,t.__)("Saved","rank-math"):(0,t.__)("Save","rank-math")),!this.isEditingTemplate&&wp.element.createElement(n.Button,{isPrimary:!0,className:"button",onClick:function(){return e.props.saveSchema(e.props.id,e.state.data)}},"term"===rankMath.objectType?(0,t.__)("Save for this Term","rank-math"):(0,t.__)("Save for this Post","rank-math"))))}}])&&Be(i.prototype,o),s&&Be(i,s),Object.defineProperty(i,"prototype",{writable:!1}),m}(fe.Component);Ke.defaultProps={query:null,fields:[],onQueryChange:null},Ke.propTypes={query:Re().object,fields:Re().array.isRequired,onQueryChange:Re().func};var $e=(0,Q.compose)((0,i.withSelect)((function(a,t){var i=a("rank-math").getEditingSchema(),n=a("rank-math").getEditSchemas(),o=(0,e.isString)(i.data.property)?i.data.property:(0,e.isArray)(i.data.property)?i.data.property[0]:"";return(0,r.applyFilters)("rank_math_schema_apply_metadata_values_"+o.replaceAll(" ","_"),i.data),He(He(He({},t),i),{},{schemas:n,isPro:a("rank-math").isPro()})})),(0,i.withDispatch)((function(r,a){var i=a.onSave,n=void 0!==i&&i,o=a.isPro,s=a.schemas;return{toggleMode:function(e,a){if(confirm((0,t.__)("Are you sure you want to convert? You can't use simple mode for this edited Schema.","rank-math"))){var i=He({},a);i.metadata.type="custom",r("rank-math").updateEditSchema(e,i)}},saveSchema:function(a,t){var i=ne()("form.schema-builder").get(0);if(i.checkValidity()){o||(0,e.forEach)(s,(function(e,t){a!==t&&r("rank-math").deleteSchema(t)}));var l=W(t);r("rank-math").updateEditSchema(a,t),r("rank-math").saveSchema(a,l),n&&n(a,l),r("rank-math").schemaUpdated(!0),r("rank-math").toggleSchemaTemplates(!1),r("rank-math").toggleSchemaEditor(!1)}else i.reportValidity()},saveTemplate:function(e,a,t){r("rank-math").saveTemplate(W(e),a,t)}}})))(Ke),Xe={"@context":"https://schema.org/","@graph":[{"@type":"Article",headline:"Power Words: The Art of Writing Headlines That Get Clicked",description:"Power words are words with strong meaning that smart copywriters (as well as marketers) use to increase CTR and boost conversions.",author:{"@type":"Person",name:"Rank Math"},datePublished:"2020-09-12GMT+000015:45:32+00:00",dateModified:"2020-09-12GMT+000015:45:29+00:00","@id":"https://rankmath.com/blog/power-words/#schema-44838",mainEntityOfPage:{"@id":"https://rankmath.com/blog/power-words/#webpage"},isPartOf:{"@id":"https://rankmath.com/blog/power-words/#webpage"},publisher:{"@id":"/#organization"},inLanguage:"en-US"}]},Ze=function(){var e=JSON.stringify(Xe,null,2);return wp.element.createElement(fe.Fragment,null,wp.element.createElement("div",{className:"rank-math-pretty-json free-version"},wp.element.createElement("form",{method:"post",target:"_blank",action:"https://search.google.com/test/rich-results"},wp.element.createElement("h4",{className:"rank-math-schema-section-title"},(0,t.__)("JSON-LD Code","rank-math")),wp.element.createElement("button",{className:"button structured-data-copy is-small",type:"button","data-clipboard-text":e},wp.element.createElement("i",{className:"rm-icon rm-icon-copy"}),wp.element.createElement("span",{className:"original-text"},(0,t.__)("Copy","rank-math")),wp.element.createElement("span",{className:"success","aria-hidden":"true"},(0,t.__)("Copied!","rank-math"))),wp.element.createElement("button",{className:"button structured-data-test is-small",type:"submit"},wp.element.createElement("i",{className:"rm-icon rm-icon-google"})," ",wp.element.createElement("span",null,(0,t.__)("Test with Google","rank-math"))),wp.element.createElement("textarea",{name:"code_snippet",defaultValue:e})),wp.element.createElement("pre",{className:"code-output"},wp.element.createElement("code",{className:"language-javascript",dangerouslySetInnerHTML:{__html:z(e)}})),wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks width-70"},wp.element.createElement("h3",null,(0,t.__)("Preview & Validate Your Schema Markup","rank-math")),wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,t.__)("Advanced Schema markup viewer","rank-math")),wp.element.createElement("li",null,(0,t.__)("Live testing with Google","rank-math")),wp.element.createElement("li",null,(0,t.__)("No other SEO plugin offers this feature","rank-math"))),wp.element.createElement("a",{className:"button button-primary is-green",href:"https://rankmath.com/pricing/?utm_source=Plugin&utm_medium=Code+Validation&utm_campaign=WP",rel:"noreferrer noopener",target:"_blank"},(0,t.__)("Upgrade","rank-math"))))))},er=function(e){var a,i=e.selectedTab;return wp.element.createElement(n.TabPanel,{className:"rank-math-tabs rank-math-editor rank-math-schema-tabs",activeClass:"is-active",initialTabName:i,tabs:Object.values((a={schemaBuilder:{name:"schemaBuilder",title:wp.element.createElement(fe.Fragment,null,wp.element.createElement("span",null,(0,t.__)("Edit","rank-math"))),view:$e,className:"rank-math-tab-templates"},codeValidation:{name:"codeValidation",title:wp.element.createElement(fe.Fragment,null,wp.element.createElement("span",null,(0,t.__)("Code Validation","rank-math"))),view:Ze,className:"rank-math-tab-code-validation"}},(0,r.applyFilters)("rank_math_schema_editor_tabs",a)))},(function(e){return wp.element.createElement("div",{className:"components-panel__body rank-math-schema-tab-content-"+e.name},(0,fe.createElement)(e.view))}))};function rr(e){return rr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},rr(e)}function ar(e,r,a){return(r=function(e){var r=function(e,r){if("object"!==rr(e)||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!==rr(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"===rr(r)?r:String(r)}(r))in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}var tr=(0,Q.compose)((0,i.withSelect)((function(r){var a=r("rank-math").getEditingSchema();return{isOpen:r("rank-math").isSchemaEditorOpen(),selectedTab:r("rank-math").getEditorTab(),isCutomSchema:(0,e.get)(a,"data.metadata.type",!1)}})),(0,i.withDispatch)((function(e,r){return{toggleModal:function(){e("rank-math").setEditorTab(""),e("rank-math").toggleSchemaEditor(!r.isOpen)}}})))((function(e){var r=e.isOpen,a=void 0!==r&&r,i=e.toggleModal,o=e.selectedTab,s=e.isCutomSchema;if(!a)return null;var l=he()("rank-math-modal rank-math-schema-generator rank-math-schema-modal",{"rank-math-schema-modal-no-map":"custom"===s});return wp.element.createElement(n.Modal,{title:(0,t.__)("Schema Builder","rank-math"),closeButtonLabel:(0,t.__)("Close","rank-math"),shouldCloseOnClickOutside:!1,onRequestClose:i,className:l,overlayClassName:"rank-math-modal-overlay"},wp.element.createElement(n.KeyboardShortcuts,{shortcuts:ar(ar(ar(ar({},ye.rawShortcut.ctrl("z"),(function(e){return e.stopImmediatePropagation()})),ye.rawShortcut.ctrlShift("z"),(function(e){return e.stopImmediatePropagation()})),ye.rawShortcut.primary("z"),(function(e){return e.stopImmediatePropagation()})),ye.rawShortcut.primaryShift("z"),(function(e){return e.stopImmediatePropagation()}))},wp.element.createElement("a",{href:$("rich-snippets","Schema Builder Header"),rel:"noopener noreferrer",target:"_blank",title:(0,t.__)("More Info","rank-math"),className:"rank-math-schema-info"},wp.element.createElement(n.Dashicon,{icon:"info"})),wp.element.createElement(er,{selectedTab:o})))}));function ir(e){return ir="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ir(e)}function nr(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),a.push.apply(a,t)}return a}function or(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?nr(Object(a),!0).forEach((function(r){sr(e,r,a[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):nr(Object(a)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))}))}return e}function sr(e,r,a){return(r=function(e){var r=function(e,r){if("object"!==ir(e)||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!==ir(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"===ir(r)?r:String(r)}(r))in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}var lr=(0,Q.compose)((0,i.withSelect)((function(r,a){var t=r("rank-math").isPro(),i=r("rank-math").getSchemas(),n=(0,e.findKey)(i,"metadata.isPrimary");return n=(0,e.isEmpty)(n)?{}:{id:n,type:i[n]["@type"]},or(or({},a),{},{primarySchema:n,isPro:t})})),(0,i.withDispatch)((function(r,a){var t=a.isPro,i=a.primarySchema,n=void 0!==i&&i,o=!(!t||!(0,e.isEmpty)(n));return t||(o=!0),{addSchema:function(a){var t=(0,e.uniqueId)("new-"),i=(0,e.get)(a,"schema",!1);!1===i&&(i={"@type":a.type,metadata:{type:"template",shortcode:"s-".concat(S())}}),i.metadata.isPrimary=o,r("rank-math").setEditingSchemaId(t),r("rank-math").updateEditSchema(t,U(i)),r("rank-math").toggleSchemaEditor(!0)},editSchema:function(e){r("rank-math").setEditingSchemaId(e),r("rank-math").toggleSchemaEditor(!0)}}})))((function(e){var a=e.search,i=e.templates,o=e.isPro,s=e.addSchema,l=e.editSchema,m=e.primarySchema;""!==a&&(i=i.filter((function(e){return e.title.toLowerCase().includes(a)})));var d=m?(0,r.applyFilters)("rank_math_schema_type",m.type):"";return wp.element.createElement("div",{className:"rank-math-schema-catalog"},i.map((function(e,r){var a=!o&&d===e.type,i=he()("rank-math-schema-item rank-math-use-schema row button",{"in-use":a,"schema-pro":e.isPro});return wp.element.createElement("div",{id:"rank-math-schema-list-wrapper",key:r},wp.element.createElement(n.Button,{key:r,id:"rank-math-schema-item",className:i,href:e.isPro?$("pro","PRO Schema Type"):"#",target:e.isPro?"_blank":"",isLink:!0,onClick:function(){if(!e.isPro)return a?l(m.id):s(e)}},wp.element.createElement("input",{type:"radio",name:"primarySchema",value:e.type,checked:d===e.type,onChange:function(){return s(e)},disabled:e.isPro}),wp.element.createElement("span",{className:"rank-math-schema-name"},wp.element.createElement("i",{className:K(e.type)}),e.title,e.isPro&&wp.element.createElement("span",{className:"rank-math-pro-badge"},(0,t.__)("Pro","rank-math"))),wp.element.createElement("span",{className:"button rank-math-schema-item-actions"},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,t.__)("Use","rank-math")))))})))}));function mr(e,r){return function(e){if(Array.isArray(e))return e}(e)||function(e,r){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var t,i,n,o,s=[],l=!0,m=!1;try{if(n=(a=a.call(e)).next,0===r){if(Object(a)!==a)return;l=!1}else for(;!(l=(t=n.call(a)).done)&&(s.push(t.value),s.length!==r);l=!0);}catch(e){m=!0,i=e}finally{try{if(!l&&null!=a.return&&(o=a.return(),Object(o)!==o))return}finally{if(m)throw i}}return s}}(e,r)||function(e,r){if(!e)return;if("string"==typeof e)return dr(e,r);var a=Object.prototype.toString.call(e).slice(8,-1);"Object"===a&&e.constructor&&(a=e.constructor.name);if("Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return dr(e,r)}(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dr(e,r){(null==r||r>e.length)&&(r=e.length);for(var a=0,t=new Array(r);a<r;a++)t[a]=e[a];return t}var pr=function(e){var a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"global"===e?f.getTemplates(a):(0,r.applyFilters)("rank_math_schema_templates_by_source",[],e)},cr=(0,Q.compose)((0,i.withSelect)((function(e){return{isPro:e("rank-math").isPro()}})))((function(e){var a=mr((0,X.useState)("global"),2),i=a[0],o=a[1],s=mr((0,X.useState)(""),2),l=s[0],m=s[1];return wp.element.createElement(fe.Fragment,null,e.isPro&&wp.element.createElement(te,null),wp.element.createElement("h4",{className:"rank-math-schema-section-title"},(0,t.__)("Available Schema Types","rank-math")),wp.element.createElement("div",{className:"rank-math-schema-filter"},wp.element.createElement(n.RadioControl,{selected:i,options:(0,r.applyFilters)("rank_math_schema_template_sources",[{value:"global",label:(0,t.__)("Schema Catalog","rank-math")}]),onChange:o}),wp.element.createElement("div",{className:"rank-math-schema-search"},wp.element.createElement(n.TextControl,{value:l,onChange:m,placeholder:(0,t.__)("Search…","rank-math")}))),wp.element.createElement(lr,{templates:pr(i,e.isPro),search:l.toLowerCase()}))})),ur=function(){return wp.element.createElement("div",{className:"components-panel__body rank-math-custom-schema-wrapper"},wp.element.createElement("img",{src:rankMath.customSchemaImage,alt:"",className:"custom-schema"}),wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks width-60"},wp.element.createElement("h3",null,(0,t.__)("Advanced Schema Builder","rank-math")),wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,t.__)("Possibility to create 700+ Schema Types","rank-math")),wp.element.createElement("li",null,(0,t.__)("Import Schema from ANY website","rank-math")),wp.element.createElement("li",null,(0,t.__)("Create Advanced templates","rank-math"))),wp.element.createElement("a",{className:"button button-primary is-green",href:$("pro","Custom Builder"),rel:"noreferrer noopener",target:"_blank"},(0,t.__)("Upgrade","rank-math")))))},hr=function(){return wp.element.createElement(n.TabPanel,{className:"rank-math-tabs rank-math-editor rank-math-schema-tabs",activeClass:"is-active",tabs:Object.values((e={templates:{name:"templates",title:wp.element.createElement(fe.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-schema"}),wp.element.createElement("span",null,(0,t.__)("Schema Templates","rank-math"))),view:cr,className:"rank-math-tab-templates"},newSchema:{name:"new-schema",title:wp.element.createElement(fe.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,t.__)("Custom Schema","rank-math"))),view:ur,className:"rank-math-tab-new-schema"}},(0,r.applyFilters)("rank_math_schema_templates_tabs",e)))},(function(e){return wp.element.createElement("div",{className:"components-panel__body rank-math-schema-tab-content-"+e.name},(0,fe.createElement)(e.view))}));var e};function yr(e){return yr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},yr(e)}function fr(e,r,a){return(r=function(e){var r=function(e,r){if("object"!==yr(e)||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!==yr(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"===yr(r)?r:String(r)}(r))in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}var _r=(0,Q.compose)((0,i.withSelect)((function(e){return{isOpen:e("rank-math").isSchemaTemplatesOpen()}})),(0,i.withDispatch)((function(e,r){return{toggleModal:function(){e("rank-math").toggleSchemaTemplates(!r.isOpen)}}})))((function(e){var r=e.isOpen,a=void 0!==r&&r,i=e.toggleModal;return a?wp.element.createElement(n.Modal,{title:(0,t.__)("Schema Generator","rank-math"),closeButtonLabel:(0,t.__)("Close","rank-math"),shouldCloseOnClickOutside:!1,onRequestClose:i,className:"rank-math-modal rank-math-schema-generator rank-math-schema-template-modal",overlayClassName:"rank-math-modal-overlay"},wp.element.createElement(n.KeyboardShortcuts,{shortcuts:fr(fr(fr(fr({},ye.rawShortcut.ctrl("z"),(function(e){return e.stopImmediatePropagation()})),ye.rawShortcut.ctrlShift("z"),(function(e){return e.stopImmediatePropagation()})),ye.rawShortcut.primary("z"),(function(e){return e.stopImmediatePropagation()})),ye.rawShortcut.primaryShift("z"),(function(e){return e.stopImmediatePropagation()}))},wp.element.createElement("a",{href:$("rich-snippets","Schema Generator Header"),rel:"noopener noreferrer",target:"_blank",title:(0,t.__)("More Info","rank-math"),className:"rank-math-schema-info"},wp.element.createElement(n.Dashicon,{icon:"info"})),wp.element.createElement(hr,null))):null}));ce();var Rr=function(){return wp.element.createElement(n.PanelBody,{initialOpen:!0,className:"rank-math-schema-in-use"},wp.element.createElement("p",{className:"cmb2-metabox-description"},(0,t.__)("Configure Schema Markup for your pages. Search engines, use structured data to display rich results in SERPs.","rank-math")," ",wp.element.createElement("a",{href:$("rich-snippets","Schema G Tab"),target:"_blank",rel:"noopener noreferrer"},(0,t.__)("Learn more.","rank-math"))),wp.element.createElement(te,null),wp.element.createElement(n.Button,{isPrimary:!0,onClick:function(){return(0,i.dispatch)("rank-math").toggleSchemaTemplates(!0)}},(0,t.__)("Schema Generator","rank-math")),wp.element.createElement(tr,null),wp.element.createElement(_r,null))},br=(0,i.withSelect)((function(r){var a=r("rank-math").getSchemas();return{type:function(){if((0,e.isEmpty)(a))return"off";var r=Object.keys(a);return(0,e.get)(a,[r[0],"@type"])}()}}))((function(e){var r=e.type;return wp.element.createElement(fe.Fragment,null,wp.element.createElement("i",{className:K(r)}),wp.element.createElement("span",null,(0,t.__)("Schema","rank-math")))})),kr=function(){return!(0,e.isNull)(document.getElementById("site-editor"))&&(0,e.endsWith)(wp.data.select("core/edit-site").getEditedPostId(),"//home")};(0,r.addFilter)("rank_math_sidebar_tabs","rank-math",(function(r){return kr()||rankMath.canUser.snippet&&!(0,e.isUndefined)(rankMath.schemas)&&r.splice(2,0,{name:"schema",title:wp.element.createElement(br,null),view:Rr,className:"rank-math-schema-tab"}),r}))}()}();