!function(){var e={184:function(e,t){var r;!function(){"use strict";var a={}.hasOwnProperty;function i(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var n=typeof r;if("string"===n||"number"===n)e.push(r);else if(Array.isArray(r)){if(r.length){var o=i.apply(null,r);o&&e.push(o)}}else if("object"===n){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){e.push(r.toString());continue}for(var s in r)a.call(r,s)&&r[s]&&e.push(s)}}}return e.join(" ")}e.exports?(i.default=i,e.exports=i):void 0===(r=function(){return i}.apply(t,[]))||(e.exports=r)}()},703:function(e,t,r){"use strict";var a=r(414);function i(){}function n(){}n.resetWarningCache=i,e.exports=function(){function e(e,t,r,i,n,o){if(o!==a){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:n,resetWarningCache:i};return r.PropTypes=r,r}},697:function(e,t,r){e.exports=r(703)()},414:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}},t={};function r(a){var i=t[a];if(void 0!==i)return i.exports;var n=t[a]={exports:{}};return e[a](n,n.exports,r),n.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){"use strict";var e={};r.r(e),r.d(e,{deleteSchema:function(){return st},lockModifiedDate:function(){return A},refreshResults:function(){return Le},resetDirtyMetadata:function(){return G},resetRedirection:function(){return F},resetStore:function(){return S},saveSchema:function(){return ot},saveTemplate:function(){return lt},schemaUpdated:function(){return Ze},setEditingSchemaId:function(){return et},setEditorTab:function(){return tt},setTemplateTab:function(){return rt},setVersion:function(){return xe},toggleFrontendScore:function(){return b},toggleIsDiviPageSettingsBarActive:function(){return Fe},toggleIsDiviRankMathModalActive:function(){return He},toggleLoaded:function(){return Me},toggleSchemaEditor:function(){return $e},toggleSchemaTemplates:function(){return Xe},toggleSnippetEditor:function(){return pe},updateAIScore:function(){return mt},updateAdvancedRobots:function(){return k},updateAnalysisScore:function(){return R},updateAppData:function(){return h},updateAppUi:function(){return y},updateBreadcrumbTitle:function(){return w},updateCanonicalUrl:function(){return g},updateDescription:function(){return M},updateEditSchema:function(){return nt},updateEditSchemas:function(){return at},updateFacebookDescription:function(){return E},updateFacebookHasOverlay:function(){return T},updateFacebookImage:function(){return P},updateFacebookImageID:function(){return O},updateFacebookImageOverlay:function(){return D},updateFacebookTitle:function(){return q},updateFeaturedImage:function(){return U},updateHasRedirect:function(){return H},updateHighlightedParagraphs:function(){return Be},updateKeywords:function(){return f},updatePermalink:function(){return I},updatePillarContent:function(){return _},updatePostID:function(){return C},updatePrimaryTermID:function(){return N},updateRedirection:function(){return L},updateRedirectionItem:function(){return x},updateRobots:function(){return v},updateSchemas:function(){return it},updateSelectedKeyword:function(){return Ue},updateSerpDescription:function(){return me},updateSerpSlug:function(){return le},updateSerpTitle:function(){return se},updateSnippetPreviewType:function(){return de},updateSocialTab:function(){return Ne},updateTitle:function(){return j},updateTwitterAppCountry:function(){return je},updateTwitterAppDescription:function(){return Ge},updateTwitterAppGoogleplayID:function(){return De},updateTwitterAppGoogleplayName:function(){return Ce},updateTwitterAppGoogleplayUrl:function(){return Ie},updateTwitterAppIpadID:function(){return Pe},updateTwitterAppIpadName:function(){return Oe},updateTwitterAppIpadUrl:function(){return Te},updateTwitterAppIphoneID:function(){return Se},updateTwitterAppIphoneName:function(){return qe},updateTwitterAppIphoneUrl:function(){return Ee},updateTwitterAuthor:function(){return fe},updateTwitterCardType:function(){return ce},updateTwitterDescription:function(){return ye},updateTwitterHasOverlay:function(){return Re},updateTwitterImage:function(){return be},updateTwitterImageID:function(){return _e},updateTwitterImageOverlay:function(){return ge},updateTwitterPlayerSize:function(){return ve},updateTwitterPlayerStreamCtype:function(){return Ae},updateTwitterPlayerStreamUrl:function(){return we},updateTwitterPlayerUrl:function(){return ke},updateTwitterTitle:function(){return he},updateTwitterUseFacebook:function(){return ue}});var t={};r.r(t),r.d(t,{appData:function(){return yt},appUi:function(){return kt}});var a={};r.r(a),r.d(a,{getAdvancedRobots:function(){return Et},getAnalysisScore:function(){return At},getAppData:function(){return vt},getBreadcrumbTitle:function(){return Ot},getCanonicalUrl:function(){return Pt},getDescription:function(){return Xt},getDirtyMetadata:function(){return wt},getEditSchemas:function(){return _r},getEditingSchema:function(){return br},getEditorTab:function(){return gr},getFacebookAuthor:function(){return Vt},getFacebookDescription:function(){return Bt},getFacebookHasOverlay:function(){return zt},getFacebookImage:function(){return Wt},getFacebookImageID:function(){return Yt},getFacebookImageOverlay:function(){return Kt},getFacebookTitle:function(){return Ht},getFeaturedImage:function(){return Zt},getFeaturedImageHtml:function(){return er},getHighlightedParagraphs:function(){return Ft},getKeywords:function(){return Gt},getPermalink:function(){return $t},getPillarContent:function(){return St},getPostID:function(){return Jt},getPreviewSchema:function(){return Rr},getPrimaryTermID:function(){return tr},getRedirectionID:function(){return rr},getRedirectionItem:function(){return nr},getRedirectionType:function(){return ar},getRedirectionUrl:function(){return ir},getRichSnippets:function(){return Tt},getRobots:function(){return qt},getSchemas:function(){return fr},getSelectedKeyword:function(){return jt},getSerpDescription:function(){return pr},getSerpSlug:function(){return mr},getSerpTitle:function(){return lr},getShowScoreFrontend:function(){return Dt},getSnippetPreviewType:function(){return ur},getSocialTab:function(){return Mt},getTemplateTab:function(){return kr},getTitle:function(){return Qt},getTwitterAppCountry:function(){return Fr},getTwitterAppDescription:function(){return Tr},getTwitterAppGoogleplayID:function(){return Nr},getTwitterAppGoogleplayName:function(){return Lr},getTwitterAppGoogleplayUrl:function(){return xr},getTwitterAppIpadID:function(){return jr},getTwitterAppIpadName:function(){return Mr},getTwitterAppIpadUrl:function(){return Ur},getTwitterAppIphoneID:function(){return Dr},getTwitterAppIphoneName:function(){return Cr},getTwitterAppIphoneUrl:function(){return Ir},getTwitterAuthor:function(){return Sr},getTwitterCardType:function(){return wr},getTwitterDescription:function(){return Gr},getTwitterHasOverlay:function(){return Pr},getTwitterImage:function(){return Er},getTwitterImageID:function(){return qr},getTwitterImageOverlay:function(){return Or},getTwitterPlayerSize:function(){return Br},getTwitterPlayerStream:function(){return Vr},getTwitterPlayerStreamCtype:function(){return Yr},getTwitterPlayerUrl:function(){return Hr},getTwitterTitle:function(){return Ar},getTwitterUseFacebook:function(){return vr},hasRedirect:function(){return or},hasSchemaUpdated:function(){return yr},isDiviPageSettingsBarActive:function(){return Lt},isDiviRankMathModalActive:function(){return xt},isLoaded:function(){return It},isModifiedDateLocked:function(){return Ct},isPro:function(){return Nt},isRefreshing:function(){return Ut},isSchemaEditorOpen:function(){return cr},isSchemaTemplatesOpen:function(){return hr},isSnippetEditorOpen:function(){return dr}});var i=jQuery,n=r.n(i),o=lodash,s=wp.data,l=wp.hooks,m=wp.element;function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function d(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,u(a.key),a)}}function u(e){var t=function(e,t){if("object"!==p(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!==p(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===p(t)?t:String(t)}var c=new(function(){function e(){var t,r,a;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,a=null,(r=u(r="map"))in t?Object.defineProperty(t,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[r]=a}var t,r,a;return t=e,(r=[{key:"swap",value:function(e,t){var r=this;if(!(e=e||""))return"";var a=new RegExp(/%(([a-z0-9_-]+)\(([^)]*)\)|[^\s]+)%/,"giu");return e.replace(" %page%","").replace("%sep% %sep%","%sep%").replace(a,(function(e){return r.replace(t,e)})).trim()}},{key:"replace",value:function(e,t){var r=t.toLowerCase().slice(1,-1);return["term_description","user_description"].includes(r)?"undefined"!=typeof tinymce&&void 0!==tinymce.editors.rank_math_description_editor?tinymce.editors.rank_math_description_editor.getContent():n()("#description").val():r.includes("customfield(")?(r=r.replace("customfield(","").replace(")",""))in rankMath.customFields?rankMath.customFields[r]:"":(e=e||this.getMap(),(r="seo_description"===(r="seo_title"===(r=r.includes("(")?r.split("(")[0]:r)?"title":r)?"excerpt":r)in e?e[r]:"")}},{key:"getMap",value:function(){var e=this;return null!==this.map||(this.map={},n().each(rankMath.variables,(function(t,r){t=t.toLowerCase().replace(/%+/g,"").split("(")[0],e.map[t]=r.example}))),this.map}},{key:"setVariable",value:function(e,t){null!==this.map?this.map[e]=t:void 0!==rankMath.variables[e]&&(rankMath.variables[e].example=t)}}])&&d(t.prototype,r),a&&d(t,a),Object.defineProperty(t,"prototype",{writable:!1}),e}());function h(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return t=(0,l.applyFilters)("rank_math_sanitize_data",t,e,r),null!==a&&(a=(0,l.applyFilters)("rank_math_sanitize_meta_value",a,e,r)),a=null===a?t:a,(0,l.doAction)("rank_math_data_changed",e,t,r),{type:"RANK_MATH_APP_DATA",key:e,value:t,metaKey:r,metaValue:a}}function y(e,t){return(0,l.doAction)("rank_math_update_app_ui",e,t),{type:"RANK_MATH_APP_UI",key:e,value:t}}function f(e){return c.setVariable("focuskw",e.split(",")[0]),rankMathEditor.refresh("keyword"),h("keywords",e,"rank_math_focus_keyword")}function _(e){return h("pillarContent",e,"rank_math_pillar_content",!0===e?"on":"off")}function b(e){return h("showScoreFrontend",e,"rank_math_dont_show_seo_score",!0===e?"off":"on")}function R(e){return h("score",e,"rank_math_seo_score")}function g(e){return h("canonicalUrl",e,"rank_math_canonical_url")}function k(e){return h("advancedRobots",e,"rank_math_advanced_robots")}function v(e){return h("robots",e,"rank_math_robots",Object.keys(e))}function w(e){return h("breadcrumbTitle",e,"rank_math_breadcrumb_title")}function A(e){return h("lockModifiedDate",e,"rank_math_lock_modified_date")}function G(){return h("dirtyMetadata",{})}function S(e){return{type:"RESET_STORE",value:e}}function q(e){return h("facebookTitle",e,"rank_math_facebook_title")}function E(e){return h("facebookDescription",e,"rank_math_facebook_description")}function P(e){return h("facebookImage",e,"rank_math_facebook_image")}function O(e){return h("facebookImageID",e,"rank_math_facebook_image_id")}function T(e){return h("facebookHasOverlay",e,"rank_math_facebook_enable_image_overlay",!0===e?"on":"off")}function D(e){return h("facebookImageOverlay",e,"rank_math_facebook_image_overlay")}function C(e){return rankMath.objectID=e,h("postID",e)}function I(e){return h("permalink",e,"permalink")}function j(e){return h("title",e,"rank_math_title")}function M(e){return h("description",e,"rank_math_description")}function U(e){return h("featuredImage",e)}function N(e,t){return h("primaryTerm",parseInt(e),"rank_math_primary_"+t)}function L(e,t){return h(e,t)}function x(e){return y("redirectionItem",e)}function F(){return y("redirectionItem",{})}function H(e){return y("hasRedirect",e)}var B=function(e){return e.replace(/<\/?[a-z][^>]*?>/gi,"\n")},V=function(e){return e.replace(/<style[^>]*>([\S\s]*?)<\/style>/gim,"")},Y=function(e){return e.replace(/&nbsp;|&#160;/gi," ").replace(/\s{2,}/g," ").replace(/\s\./g,".").replace(/^\s+|\s+$/g,"")},W=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,t)},z=function(e){return e.replace(/[‘’‛`]/g,"'").replace(/[“”〝〞〟‟„]/g,'"')},K=function(e){return e.replace(/<!--[\s\S]*?-->/g,"")},J=function(e){return e.replace(/&\S+?;/g,"")};function Q(e){return(0,o.isUndefined)(e)||!e?"":(0,o.flow)([V,W,B,K,J,Y,z])(e)}var $=wp.autop,X="[^<>&/\\[\\]\0- =]+?",Z=new RegExp("\\["+X+"( [^\\]]+?)?\\]","g"),ee=new RegExp("\\[/"+X+"\\]","g"),te=function(e){return e.replace(Z,"").replace(ee,"")},re=function(e,t){var r=function(e,t){for(var r,a=/<p(?:[^>]+)?>(.*?)<\/p>/gi,i=[];null!==(r=a.exec(e));)i.push(r);return(0,o.map)(i,(function(e){return t?Q(e[1]):e[1]}))}(e=(0,o.flow)([te,K,$.autop])(e),t=t||!1);return 0<r.length?r:[t?Q(e):e]},ae=document.createElement("div");function ie(e){return e&&"string"==typeof e&&(e=e.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim,"").replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gim,""),ae.innerHTML=e,e=ae.textContent,ae.textContent=""),e}var ne=function(e,t){return e=(e=Q(e)).replace(/\r?\n|\r/g," "),t?(0,o.truncate)(e,{length:t,separator:" "}):e},oe=function(e){if((0,o.isEmpty)(e))return"";e=W(e),e=V(e),e=(0,o.unescape)(e).replace(/\[caption[^\]]*\](.*)\[\/caption\]/g,"");var t=(0,o.filter)(re(e,!0),(function(e){return""!==e.trim()}));if(!t.length)return"";var r=rankMathEditor.getPrimaryKeyword();if(""!==r){var a=(0,o.filter)(t,(function(e){return(0,o.includes)(e.toLowerCase(),r.toLowerCase())}));if(0<a.length)return ne(a[0],160)}return ne(t[0],160)};function se(e){return e=c.swap(""!==e?e:rankMath.assessor.serpData.titleTemplate),rankMathEditor.refresh("title"),y("serpTitle",ie(e))}function le(e){return e=""!==e?e:rankMathEditor.assessor.dataCollector.getSlug(),rankMathEditor.refresh("permalink"),y("serpSlug",e)}function me(e){return e=c.swap(function(e){var t=rankMathEditor.assessor.dataCollector.getData(),r=t.excerpt,a=oe(t.content),i=(0,o.isUndefined)(r)||(0,o.isEmpty)(r)?a:(0,o.unescape)(r);if(c.setVariable("excerpt",i),c.setVariable("seo_description",i),""!==(e=ie((0,l.applyFilters)("rankMath/description",e))))return Q(e);if(!(0,o.isUndefined)(r)&&!(0,o.isEmpty)(r))return Q(r);var n=(0,o.unescape)(rankMath.assessor.serpData.descriptionTemplate);return(0,o.isUndefined)(n)||""===n?a:Q(n)}(e)),rankMathEditor.refresh("description"),y("serpDescription",e)}function pe(e){return y("isSnippetEditorOpen",e)}function de(e){return y("snippetPreviewType",e)}function ue(e){return h("twitterUseFacebook",e,"rank_math_twitter_use_facebook",!0===e?"on":"off")}function ce(e){return h("twitterCardType",e,"rank_math_twitter_card_type")}function he(e){return h("twitterTitle",e,"rank_math_twitter_title")}function ye(e){return h("twitterDescription",e,"rank_math_twitter_description")}function fe(e){return h("twitterAuthor",e,"rank_math_twitter_author")}function _e(e){return h("twitterImageID",e,"rank_math_twitter_image_id")}function be(e){return h("twitterImage",e,"rank_math_twitter_image")}function Re(e){return h("twitterHasOverlay",e,"rank_math_twitter_enable_image_overlay",!0===e?"on":"off")}function ge(e){return h("twitterImageOverlay",e,"rank_math_twitter_image_overlay")}function ke(e){return h("twitterPlayerUrl",e,"rank_math_twitter_player_url")}function ve(e){return h("twitterPlayerSize",e,"rank_math_twitter_player_size")}function we(e){return h("twitterPlayerStream",e,"rank_math_twitter_player_stream")}function Ae(e){return h("twitterPlayerStreamCtype",e,"rank_math_twitter_player_stream_ctype")}function Ge(e){return h("twitterAppDescription",e,"rank_math_twitter_app_description")}function Se(e){return h("twitterAppIphoneID",e,"rank_math_twitter_app_iphone_id")}function qe(e){return h("twitterAppIphoneName",e,"rank_math_twitter_app_iphone_name")}function Ee(e){return h("twitterAppIphoneUrl",e,"rank_math_twitter_app_iphone_url")}function Pe(e){return h("twitterAppIpadID",e,"rank_math_twitter_app_ipad_id")}function Oe(e){return h("twitterAppIpadName",e,"rank_math_twitter_app_ipad_name")}function Te(e){return h("twitterAppIpadUrl",e,"rank_math_twitter_app_ipad_url")}function De(e){return h("twitterAppGoogleplayID",e,"rank_math_twitter_app_googleplay_id")}function Ce(e){return h("twitterAppGoogleplayName",e,"rank_math_twitter_app_googleplay_name")}function Ie(e){return h("twitterAppGoogleplayUrl",e,"rank_math_twitter_app_googleplay_url")}function je(e){return h("twitterAppCountry",e,"rank_math_twitter_app_country")}function Me(e){return y("isLoaded",e)}function Ue(e){return y("selectedKeyword",e)}function Ne(e){return y("socialTab",e)}function Le(){return y("refreshResults",Date.now())}function xe(){return y("isPro",!0)}function Fe(e){return y("isDiviPageSettingsBarActive",e)}function He(e){return y("isDiviRankMathModalActive",e)}function Be(e){return y("highlightedParagraphs",e)}var Ve=wp.i18n,Ye=wp.apiFetch,We=r.n(Ye);function ze(e){return ze="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ze(e)}function Ke(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function Je(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ke(Object(r),!0).forEach((function(t){Qe(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ke(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Qe(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==ze(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!==ze(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ze(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $e(e){return y("isSchemaEditorOpen",e)}function Xe(e){return y("isSchemaTemplatesOpen",e)}function Ze(e){return y("schemaUpdated",e)}function et(e){return y("editingSchemaId",e)}function tt(e){return y("editorTab",e)}function rt(e){return y("templateTab",e)}function at(e){return y("editSchemas",e)}function it(e){return h("schemas",e)}function nt(e,t){var r=Je({},(0,s.select)("rank-math").getEditSchemas());return r[e]=t,y("editSchemas",r)}function ot(e,t){var r=Je({},(0,s.select)("rank-math").getSchemas());return r[e]=t,h("schemas",r)}function st(e){var t=Je({},(0,s.select)("rank-math").getSchemas());return delete t[e],(0,l.doAction)("rank_math_schema_trash",e),h("schemas",t,"rank_math_delete_"+e,"")}function lt(e,t,r){return We()({method:"POST",path:"rankmath/v1/saveTemplate",data:{schema:e,postId:r}}).then((function(r){t({loading:!1,showNotice:!0,postId:r.id}),setTimeout((function(){t({showNotice:!1}),(0,o.get)(rankMath,"isTemplateScreen",!1)&&(document.title=(0,Ve.__)("Edit Schema","rank-math"),window.history.pushState(null,"",r.link.replace(/&amp;/g,"&")))}),2e3),rankMath.schemaTemplates.push({schema:e,title:e.metadata.title,type:e["@type"]})})),t({loading:!0}),{type:"DONT_WANT_TO_DO_SOMETHING"}}function mt(e){return h("contentAIScore",e,"rank_math_contentai_score",e)}function pt(e){return pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pt(e)}function dt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function ut(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dt(Object(r),!0).forEach((function(t){ct(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ct(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==pt(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!==pt(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===pt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ht=function(e){var t=e.assessor.serpData,r=e.assessor.hasRedirection;return{postID:null,title:t.title?t.title:t.titleTemplate,description:t.description,keywords:t.focusKeywords?t.focusKeywords:"",pillarContent:t.pillarContent,featuredImage:"",permalink:!1,primaryTerm:t.primaryTerm,robots:t.robots,advancedRobots:t.advancedRobots,canonicalUrl:t.canonicalUrl,breadcrumbTitle:t.breadcrumbTitle,showScoreFrontend:t.showScoreFrontend,lockModifiedDate:t.lockModifiedDate,redirectionID:r?(0,o.get)(e.assessor,"redirection.id",""):"",redirectionType:r?(0,o.get)(e.assessor,"redirection.header_code",""):"",redirectionUrl:r?(0,o.get)(e.assessor,"redirection.url_to",""):"",facebookTitle:t.facebookTitle,facebookImage:t.facebookImage,facebookImageID:t.facebookImageID,facebookAuthor:t.facebookAuthor,facebookDescription:t.facebookDescription,facebookHasOverlay:t.facebookHasOverlay,facebookImageOverlay:t.facebookImageOverlay,twitterTitle:t.twitterTitle,twitterImage:t.twitterImage,twitterAuthor:t.twitterAuthor,twitterImageID:t.twitterImageID,twitterCardType:t.twitterCardType,twitterUseFacebook:t.twitterUseFacebook,twitterDescription:t.twitterDescription,twitterHasOverlay:t.twitterHasOverlay,twitterImageOverlay:t.twitterImageOverlay,twitterPlayerUrl:t.twitterPlayerUrl,twitterPlayerSize:t.twitterPlayerSize,twitterPlayerStream:t.twitterPlayerStream,twitterPlayerStreamCtype:t.twitterPlayerStreamCtype,twitterAppDescription:t.twitterAppDescription,twitterAppIphoneName:t.twitterAppIphoneName,twitterAppIphoneID:t.twitterAppIphoneID,twitterAppIphoneUrl:t.twitterAppIphoneUrl,twitterAppIpadName:t.twitterAppIpadName,twitterAppIpadID:t.twitterAppIpadID,twitterAppIpadUrl:t.twitterAppIpadUrl,twitterAppGoogleplayName:t.twitterAppGoogleplayName,twitterAppGoogleplayID:t.twitterAppGoogleplayID,twitterAppGoogleplayUrl:t.twitterAppGoogleplayUrl,twitterAppCountry:t.twitterAppCountry,schemas:(0,o.get)(e,"schemas",{}),score:0,dirtyMetadata:{}}};function yt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ht(rankMath),t=arguments.length>1?arguments[1]:void 0,r=ut({},e.dirtyMetadata);return!1!==t.metaKey&&(r[t.metaKey]=t.metaValue),"RESET_STORE"===t.type?ut({},ht(t.value)):"RANK_MATH_APP_DATA"===t.type?"dirtyMetadata"===t.key?ut(ut({},e),{},{dirtyMetadata:t.value}):ut(ut({},e),{},ct(ct({},t.key,t.value),"dirtyMetadata",r)):e}function ft(e){return ft="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ft(e)}function _t(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function bt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_t(Object(r),!0).forEach((function(t){Rt(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Rt(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==ft(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!==ft(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ft(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var gt={isLoaded:!1,isPro:!1,selectedKeyword:{tag:"",index:0,data:{value:""}},hasRedirect:rankMath.assessor.hasRedirection&&!(0,o.isEmpty)((0,o.get)(rankMath.assessor,"redirection.id",""))&&!(0,o.isEmpty)((0,o.get)(rankMath.assessor,"redirection.url_to","")),serpTitle:"",serpSlug:"",serpDescription:(0,o.get)(rankMath.assessor,"serpData.description",""),isSnippetEditorOpen:!1,snippetPreviewType:"",refreshResults:"",redirectionItem:{},socialTab:"facebook",highlightedParagraphs:[],editorTab:"",templateTab:"",editSchemas:{},editingSchemaId:"",isSchemaEditorOpen:!1,isSchemaTemplatesOpen:!1,schemaUpdated:!1,isDiviRankMathModalActive:!1,isDiviPageSettingsBarActive:!1};function kt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:gt,t=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===t.type?bt(bt({},e),{},Rt({},t.key,t.value)):e}function vt(e){return e.appData}function wt(e){return e.appData.dirtyMetadata}function At(e){return e.appData.score}function Gt(e){return e.appData.keywords}function St(e){return e.appData.pillarContent}function qt(e){return e.appData.robots}function Et(e){return e.appData.advancedRobots}function Pt(e){return e.appData.canonicalUrl}function Ot(e){return e.appData.breadcrumbTitle}function Tt(e){return"todo"}function Dt(e){return e.appData.showScoreFrontend}function Ct(e){return e.appData.lockModifiedDate}function It(e){return e.appUi.isLoaded}function jt(e){return e.appUi.selectedKeyword}function Mt(e){return e.appUi.socialTab}function Ut(e){return e.appUi.refreshResults}function Nt(e){return e.appUi.isPro}function Lt(e){return e.appUi.isDiviPageSettingsBarActive}function xt(e){return e.appUi.isDiviRankMathModalActive}function Ft(e){return e.appUi.highlightedParagraphs}function Ht(e){return e.appData.facebookTitle}function Bt(e){return e.appData.facebookDescription}function Vt(e){return e.appData.facebookAuthor}function Yt(e){return e.appData.facebookImageID}function Wt(e){return e.appData.facebookImage}function zt(e){return e.appData.facebookHasOverlay}function Kt(e){return""!==e.appData.facebookImageOverlay?e.appData.facebookImageOverlay:"play"}function Jt(e){return e.appData.postID}function Qt(e){return e.appData.title}function $t(e){return e.appData.permalink}function Xt(e){return e.appData.description}function Zt(e){return e.appData.featuredImage}function er(e){var t=e.appData.featuredImage;return'<img src="'.concat(t.source_url,'" alt="').concat(t.alt_text,'" >')}function tr(e){return e.appData.primaryTerm}function rr(e){return String(e.appData.redirectionID)}function ar(e){return e.appData.redirectionType}function ir(e){return e.appData.redirectionUrl}function nr(e){return e.appUi.redirectionItem}function or(e){return e.appUi.hasRedirect}var sr=wp.url;function lr(e){return ie(e.appUi.serpTitle)}function mr(e){return(0,sr.safeDecodeURIComponent)(e.appUi.serpSlug)}function pr(e){return e.appUi.serpDescription}function dr(e){return e.appUi.isSnippetEditorOpen}function ur(e){return e.appUi.snippetPreviewType}function cr(e){return e.appUi.isSchemaEditorOpen}function hr(e){return e.appUi.isSchemaTemplatesOpen}function yr(e){return e.appUi.schemaUpdated}function fr(e){return e.appData.schemas}function _r(e){return e.appUi.editSchemas}function br(e){return{id:e.appUi.editingSchemaId,data:e.appUi.editSchemas[e.appUi.editingSchemaId]}}function Rr(e){return e.appData.schemas[e.appUi.editingSchemaId]}function gr(e){return e.appUi.editorTab}function kr(e){return e.appUi.templateTab}function vr(e){return e.appData.twitterUseFacebook}function wr(e){return e.appData.twitterCardType}function Ar(e){return e.appData.twitterTitle}function Gr(e){return e.appData.twitterDescription}function Sr(e){return e.appData.twitterAuthor}function qr(e){return e.appData.twitterImageID}function Er(e){return e.appData.twitterImage}function Pr(e){return e.appData.twitterHasOverlay}function Or(e){return""!==e.appData.twitterImageOverlay?e.appData.twitterImageOverlay:"play"}function Tr(e){return e.appData.twitterAppDescription}function Dr(e){return e.appData.twitterAppIphoneID}function Cr(e){return e.appData.twitterAppIphoneName}function Ir(e){return e.appData.twitterAppIphoneUrl}function jr(e){return e.appData.twitterAppIpadID}function Mr(e){return e.appData.twitterAppIpadName}function Ur(e){return e.appData.twitterAppIpadUrl}function Nr(e){return e.appData.twitterAppGoogleplayID}function Lr(e){return e.appData.twitterAppGoogleplayName}function xr(e){return e.appData.twitterAppGoogleplayUrl}function Fr(e){return e.appData.twitterAppCountry}function Hr(e){return e.appData.twitterPlayerUrl}function Br(e){return e.appData.twitterPlayerSize}function Vr(e){return e.appData.twitterPlayerStream}function Yr(e){return e.appData.twitterPlayerStreamCtype}(0,s.registerStore)("rank-math",{reducer:(0,s.combineReducers)(t),selectors:a,actions:e});var Wr={version:"1.0.0",properties:{author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Author Name","rank-math"),placeholder:"%name%"}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Image URL","rank-math")}}}},rating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating","rank-math"),help:(0,Ve.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Minimum","rank-math"),help:(0,Ve.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Maximum","rank-math"),help:(0,Ve.__)("Rating maximum score","rank-math"),placeholder:5}}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating","rank-math"),help:(0,Ve.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Minimum","rank-math"),help:(0,Ve.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Maximum","rank-math"),help:(0,Ve.__)("Rating maximum score","rank-math"),placeholder:5}}}}},bookEditions:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Edition","rank-math"),help:(0,Ve.__)("Either a specific edition of the written work, or the volume of the work","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Book"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Title","rank-math"),help:(0,Ve.__)("The title of the tome. Use for the title of the tome if it differs from the book. *Optional when tome has the same title as the book","rank-math")}}},bookEdition:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Edition","rank-math"),help:(0,Ve.__)("The edition of the book","rank-math")}}},isbn:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("ISBN","rank-math"),help:(0,Ve.__)("The ISBN of the print book","rank-math")}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("URL","rank-math"),help:(0,Ve.__)("URL specific to this edition if one exists","rank-math")}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Author Name","rank-math"),placeholder:"%name%"}}}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,Ve.__)("Date Published","rank-math"),help:(0,Ve.__)("Date of first publication of this tome","rank-math")}}},bookFormat:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"radio",label:(0,Ve.__)("Book Format","rank-math"),desc:"The format of the book.",options:{"https://schema.org/EBook":"eBook","https://schema.org/Hardcover":"Hardcover","https://schema.org/Paperback":"Paperback","https://schema.org/AudioBook":"Audio Book"},default:"https://schema.org/Hardcover"}}}},provider:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{type:"radio",label:(0,Ve.__)("Course Provider","rank-math"),classes:"show-property",options:{Organization:"Organization",Person:"Person"},default:"Organization"}}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Course Provider Name","rank-math")}}},sameAs:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Course Provider URL","rank-math")}}}},courseSchedule:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Course Schedule","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,value:"Schedule"}},duration:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Duration","rank-math"),help:(0,Ve.__)("Suggested pacing in repeatFrequency units (8601 duration format). For example, PT5H monthly means 5 hours per month. Use courseWorkload for total length.","rank-math")}}},repeatCount:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Repeat Count","rank-math"),help:(0,Ve.__)("The numerical value for how long the course lasts for, in repeatFrequency units. For example, if the repeatFrequency is monthly and the repeatCount is 4, the course lasts for 4 months.","rank-math")}}},repeatFrequency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Repeat Frequency","rank-math"),help:(0,Ve.__)("The duration and repeatCount properties are relative to this field. ","rank-math"),options:{"":(0,Ve.__)("Select Repeat Frequency","rank-math"),Daily:(0,Ve.__)("Daily","rank-math"),Weekly:(0,Ve.__)("Weekly","rank-math"),Monthly:(0,Ve.__)("Monthly","rank-math"),Yearly:(0,Ve.__)("Yearly","rank-math")},default:""}}},startDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,Ve.__)("Start Date","rank-math"),help:(0,Ve.__)("The start date for the course, in 8601 date format (YYYY-MM-DD), if applicable.","rank-math"),classes:"col-4"}}},endDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,Ve.__)("End Date","rank-math"),help:(0,Ve.__)("The end date for the course, in 8601 date format (YYYY-MM-DD), if applicable.","rank-math"),classes:"col-4"}}}},courseInstance:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Course Instance","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"CourseInstance"}},courseMode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Course Mode","rank-math"),help:(0,Ve.__)("The medium through which the course will be delivered.","rank-math"),options:{Online:"Online",Onsite:"Onsite",Blended:"Blended"},default:"Online"}}},courseWorkload:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Course Workload","rank-math"),help:(0,Ve.__)("Total time to watch all videos and complete all assignments and exams for the course. Use the 8601 format. Example: PT22H","rank-math")}}},courseSchedule:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Course Schedule","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,value:"Schedule"}},duration:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Duration","rank-math"),help:(0,Ve.__)("Suggested pacing in repeatFrequency units (8601 duration format). For example, PT5H monthly means 5 hours per month. Use courseWorkload for total length.","rank-math")}}},repeatCount:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Repeat Count","rank-math"),help:(0,Ve.__)("The numerical value for how long the course lasts for, in repeatFrequency units. For example, if the repeatFrequency is monthly and the repeatCount is 4, the course lasts for 4 months.","rank-math")}}},repeatFrequency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Repeat Frequency","rank-math"),help:(0,Ve.__)("The duration and repeatCount properties are relative to this field. ","rank-math"),options:{"":"Select Repeat Frequency",Daily:"Daily",Weekly:"Weekly",Monthly:"Monthly",Yearly:"Yearly"},default:""}}},startDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,Ve.__)("Start Date","rank-math"),help:(0,Ve.__)("The start date for the course, in 8601 date format (YYYY-MM-DD), if applicable.","rank-math"),classes:"col-4"}}},endDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,Ve.__)("End Date","rank-math"),help:(0,Ve.__)("The end date for the course, in 8601 date format (YYYY-MM-DD), if applicable.","rank-math"),classes:"col-4"}}}}},offers:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Offers","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Offer"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Name","rank-math")}}},category:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Category","rank-math")}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("URL","rank-math")}}},price:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Price","rank-math")}}},priceCurrency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Currency","rank-math")}}},availability:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Availability","rank-math"),help:(0,Ve.__)("Offer availability","rank-math"),classes:"col-4",options:{InStock:"In Stock",SoldOut:"Sold Out",PreOrder:"Preorder"},default:"InStock"}}},validFrom:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,Ve.__)("Price Valid From","rank-math"),help:(0,Ve.__)("The date when the item becomes valid.","rank-math")}}},priceValidUntil:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,Ve.__)("Price Valid Until","rank-math"),help:(0,Ve.__)("The date after which the price will no longer be available","rank-math")}}},inventoryLevel:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Inventory Level","rank-math")}}}},"virtual-location":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{value:"VirtualLocation"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Online Event URL","rank-math"),help:(0,Ve.__)("The URL of the online event, where people can join. This property is required if your event is happening online","rank-math")}}}},address:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Address","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"PostalAddress"}},streetAddress:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Street Address","rank-math")}}},addressLocality:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Locality","rank-math")}}},addressRegion:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Region","rank-math")}}},postalCode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Postal Code","rank-math")}}},addressCountry:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Country","rank-math")}}}},"physical-location":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Place"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Venue Name","rank-math"),help:(0,Ve.__)("The venue name.","rank-math")}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Venue URL","rank-math"),help:(0,Ve.__)("Website URL of the venue","rank-math")}}},address:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Address","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"PostalAddress"}},streetAddress:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Street Address","rank-math")}}},addressLocality:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Locality","rank-math")}}},addressRegion:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Region","rank-math")}}},postalCode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Postal Code","rank-math")}}},addressCountry:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Country","rank-math")}}}}},"event-performer":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Performer Information","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"radio",label:(0,Ve.__)("Performer","rank-math"),classes:"show-property",options:{Organization:"Organization",Person:"Person"},default:"Person"}}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Name","rank-math")}}},sameAs:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Website or Social Link","rank-math")}}}},"monetary-amount-unit":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Salary","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"QuantitativeValue"}},value:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Salary (Recommended)","rank-math"),help:(0,Ve.__)("Insert amount, e.g. 50.00, or a salary range, e.g. 40.00-50.00","rank-math"),classes:"col-4"}}},unitText:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Payroll (Recommended)","rank-math"),help:(0,Ve.__)("Salary amount is for","rank-math"),options:{"":"None",YEAR:"Yearly",MONTH:"Monthly",WEEK:"Weekly",DAY:"Daily",HOUR:"Hourly"},classes:"col-4"}}}},"monetary-amount":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"MonetaryAmount"}},currency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Salary Currency","rank-math"),help:(0,Ve.__)("ISO 4217 Currency code. Example: EUR","rank-math"),classes:"col-4"}}},value:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Salary","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"QuantitativeValue"}},value:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Salary (Recommended)","rank-math"),help:(0,Ve.__)("Insert amount, e.g. 50.00, or a salary range, e.g. 40.00-50.00","rank-math"),classes:"col-4"}}},unitText:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Payroll (Recommended)","rank-math"),help:(0,Ve.__)("Salary amount is for","rank-math"),options:{"":"None",YEAR:"Yearly",MONTH:"Monthly",WEEK:"Weekly",DAY:"Daily",HOUR:"Hourly"},classes:"col-4"}}}}},"hiring-organization":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Organization"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Hiring Organization","rank-math"),placeholder:"%org_name%",help:(0,Ve.__)("The name of the company. Leave empty to use your own company information.","rank-math"),classes:"col-4"}}},sameAs:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Organization URL (Recommended)","rank-math"),placeholder:"%org_url%",help:(0,Ve.__)("The URL of the organization offering the job position. Leave empty to use your own company information","rank-math"),classes:"col-6"}}},logo:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Organization Logo (Recommended)","rank-math"),placeholder:"%org_logo%",help:(0,Ve.__)("Logo URL of the organization offering the job position. Leave empty to use your own company information","rank-math"),classes:"col-6"}}}},brand:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Brand"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Brand Name","rank-math")}}}},calories:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"NutritionInformation"}},calories:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Calories","rank-math"),help:(0,Ve.__)("The number of calories in the recipe. Optional.","rank-math")}}}},"video-object":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Video","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"VideoObject"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Name","rank-math"),help:(0,Ve.__)("A recipe video Name","rank-math"),classes:"col-6"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),help:(0,Ve.__)("A recipe video Description","rank-math")}}},embedUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Video URL","rank-math"),help:(0,Ve.__)("A video URL. Optional.","rank-math"),classes:"col-6"}}},contentUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Content URL","rank-math"),help:(0,Ve.__)("A URL pointing to the actual video media file","rank-math"),classes:"col-6"}}},thumbnailUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Recipe Video Thumbnail","rank-math"),help:(0,Ve.__)("A recipe video thumbnail URL","rank-math"),classes:"col-6"}}},duration:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Duration","rank-math"),help:(0,Ve.__)("ISO 8601 duration format. Example: PT1H30M","rank-math"),classes:"col-6"}}},uploadDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,Ve.__)("Video Upload Date","rank-math"),classes:"col-6"}}}},instructionText:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"HowtoStep"}},text:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"textarea"}}}},instructions:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Recipe Instructions","rank-math"),help:(0,Ve.__)("Either a specific edition of the written work, or the volume of the work","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"HowToSection"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Name","rank-math"),help:(0,Ve.__)("Instruction name of the recipe.","rank-math")}}},itemListElement:{map:{isArray:!0,isGroup:!0,isRequired:!1,isRecommended:!1,arrayMap:"instructionText",arrayProps:{map:{classes:"show-delete-property-group"}},classes:"show-add-property-group",field:{label:(0,Ve.__)("Instruction Texts","rank-math")}}}},"geo-coordinates":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Geo Coordinates","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"GeoCoordinates"}},latitude:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Latitude","rank-math")}}},longitude:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Longitude","rank-math")}}}},"opening-hours":{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Timings","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"OpeningHoursSpecification"}},dayOfWeek:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"checkbox",label:(0,Ve.__)("Open Days","rank-math"),options:{monday:"Monday",tuesday:"Tuesday",wednesday:"Wednesday",thursday:"Thursday",friday:"Friday",saturday:"Saturday",sunday:"Sunday"},default:[]}}},opens:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"timepicker",label:(0,Ve.__)("Opening Time","rank-math"),classes:"col-6",placeholder:"09:00 AM"}}},closes:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"timepicker",label:(0,Ve.__)("Closing Time","rank-math"),classes:"col-6",placeholder:"05:00 PM"}}}},cuisine:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1},cuisine:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Cuisine","rank-math")}}}}},schemas:{Article:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("Article","rank-math"),defaultEn:"Article"},headline:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),placeholder:"%seo_description%"}}},keywords:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Keywords","rank-math"),placeholder:"%keywords%"}}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{type:"radio",label:(0,Ve.__)("Article Type","rank-math"),classes:"show-property",options:{Article:"Article",BlogPosting:"Blog Post",NewsArticle:"News Article"},notice:{status:"warning",className:"article-notice",content:(0,Ve.__)("Google does not allow Person as the Publisher for articles. Organization will be used instead.","rank-math")}}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Author Name","rank-math"),placeholder:"%name%"}}}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Published Date","rank-math"),classes:"hide-group",default:"%date(Y-m-d\\TH:i:sP)%"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Modified Date","rank-math"),classes:"hide-group",default:"%modified(Y-m-d\\TH:i:sP)%"}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}},articleSection:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Article Section","rank-math"),classes:"hide-group",default:"%primary_taxonomy_terms%"}}}},Book:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("Book","rank-math"),defaultEn:"Book"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},reviewLocation:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,Ve.__)("Review Location","rank-math"),help:(0,Ve.__)("The review or rating must be displayed on the page to comply with Google's Schema guidelines.","rank-math"),options:{bottom:"Below Content",top:"Above Content",both:"Above and Below Content",custom:"Custom (use shortcode)"},default:"custom"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,Ve.__)("Shortcode","rank-math"),help:(0,Ve.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]",dependency:[{field:"reviewLocation",value:"custom"}]}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("URL","rank-math")}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Author Name","rank-math"),placeholder:"%name%"}}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating","rank-math"),help:(0,Ve.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Minimum","rank-math"),help:(0,Ve.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Maximum","rank-math"),help:(0,Ve.__)("Rating maximum score","rank-math"),placeholder:5}}}}},hasPart:{map:{isArray:!0,isGroup:!0,isRequired:!1,isRecommended:!1,arrayMap:"bookEditions",arrayProps:{map:{classes:"show-delete-property-group"}},field:{label:(0,Ve.__)("Editions","rank-math")}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},Course:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("Course","rank-math"),defaultEn:"Course"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},reviewLocation:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,Ve.__)("Review Location","rank-math"),help:(0,Ve.__)("The review or rating must be displayed on the page to comply with Google's Schema guidelines.","rank-math"),options:{bottom:"Below Content",top:"Above Content",both:"Above and Below Content",custom:"Custom (use shortcode)"},default:"custom"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,Ve.__)("Shortcode","rank-math"),help:(0,Ve.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]",dependency:[{field:"reviewLocation",value:"custom"}]}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),placeholder:"%seo_description%"}}},provider:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{type:"radio",label:(0,Ve.__)("Course Provider","rank-math"),classes:"show-property",options:{Organization:"Organization",Person:"Person"},default:"Organization"}}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Course Provider Name","rank-math")}}},sameAs:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Course Provider URL","rank-math")}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}},hasCourseInstance:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Course Instance","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"CourseInstance"}},courseMode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Course Mode","rank-math"),help:(0,Ve.__)("The medium through which the course will be delivered.","rank-math"),options:{Online:"Online",Onsite:"Onsite",Blended:"Blended"},default:"Online"}}},courseWorkload:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Course Workload","rank-math"),help:(0,Ve.__)("Total time to watch all videos and complete all assignments and exams for the course. Use the 8601 format. Example: PT22H","rank-math")}}},courseSchedule:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Course Schedule","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,value:"Schedule"}},duration:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Duration","rank-math"),help:(0,Ve.__)("Suggested pacing in repeatFrequency units (8601 duration format). For example, PT5H monthly means 5 hours per month. Use courseWorkload for total length.","rank-math")}}},repeatCount:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Repeat Count","rank-math"),help:(0,Ve.__)("The numerical value for how long the course lasts for, in repeatFrequency units. For example, if the repeatFrequency is monthly and the repeatCount is 4, the course lasts for 4 months.","rank-math")}}},repeatFrequency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Repeat Frequency","rank-math"),help:(0,Ve.__)("The duration and repeatCount properties are relative to this field. ","rank-math"),options:{"":"Select Repeat Frequency",Daily:"Daily",Weekly:"Weekly",Monthly:"Monthly",Yearly:"Yearly"},default:""}}},startDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,Ve.__)("Start Date","rank-math"),help:(0,Ve.__)("The start date for the course, in 8601 date format (YYYY-MM-DD), if applicable.","rank-math"),classes:"col-4"}}},endDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,Ve.__)("End Date","rank-math"),help:(0,Ve.__)("The end date for the course, in 8601 date format (YYYY-MM-DD), if applicable.","rank-math"),classes:"col-4"}}}}},offers:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Offers","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Offer"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Name","rank-math"),classes:"hide-group"}}},category:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Category","rank-math"),help:(0,Ve.__)("The pricing category of the course. Example: Free, Partially Free, Subscription, Paid","rank-math")}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("URL","rank-math"),classes:"hide-group"}}},price:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Price","rank-math"),help:(0,Ve.__)("The numerical price of the course, if applicable.","rank-math")}}},priceCurrency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Currency","rank-math"),help:(0,Ve.__)("The currency of the price of the course, in ISO 4217 currency format (3 letter code), if applicable.","rank-math")}}},availability:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Availability","rank-math"),help:(0,Ve.__)("Offer availability","rank-math"),classes:["col-4","hide-group"],options:{InStock:"In Stock",SoldOut:"Sold Out",PreOrder:"Preorder"},default:"InStock"}}},validFrom:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,Ve.__)("Price Valid From","rank-math"),help:(0,Ve.__)("The date when the item becomes valid.","rank-math"),classes:"hide-group"}}},priceValidUntil:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,Ve.__)("Price Valid Until","rank-math"),help:(0,Ve.__)("The date after which the price will no longer be available","rank-math"),classes:"hide-group"}}},inventoryLevel:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Inventory Level","rank-math"),classes:"hide-group"}}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating","rank-math"),help:(0,Ve.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Minimum","rank-math"),help:(0,Ve.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Maximum","rank-math"),help:(0,Ve.__)("Rating maximum score","rank-math"),placeholder:5}}}}}},Event:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("Event","rank-math"),defaultEn:"Event"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},reviewLocation:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,Ve.__)("Review Location","rank-math"),help:(0,Ve.__)("The review or rating must be displayed on the page to comply with Google's Schema guidelines.","rank-math"),options:{bottom:"Below Content",top:"Above Content",both:"Above and Below Content",custom:"Custom (use shortcode)"},default:"custom"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,Ve.__)("Shortcode","rank-math"),help:(0,Ve.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]",dependency:[{field:"reviewLocation",value:"custom"}]}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),placeholder:"%seo_description%"}}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"select",label:(0,Ve.__)("Event Type","rank-math"),help:(0,Ve.__)("Type of the event","rank-math"),classes:"show-property col-4",options:{Event:"Event",BusinessEvent:"Business Event",ChildrensEvent:"Childrens Event",ComedyEvent:"Comedy Event",DanceEvent:"Dance Event",DeliveryEvent:"Delivery Event",EducationEvent:"Education Event",ExhibitionEvent:"Exhibition Event",Festival:"Festival",FoodEvent:"Food Event",LiteraryEvent:"Literary Event",MusicEvent:"Music Event",PublicationEvent:"Publication Event",SaleEvent:"Sale Event",ScreeningEvent:"Screening Event",SocialEvent:"Social Event",SportsEvent:"Sports Event",TheaterEvent:"Theater Event",VisualArtsEvent:"Visual Arts Event"}}}},eventStatus:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Event Status","rank-math"),help:(0,Ve.__)("Current status of the event (optional)","rank-math"),options:{"":"None",EventScheduled:"Scheduled",EventCancelled:"Cancelled",EventPostponed:"Postponed",EventRescheduled:"Rescheduled",EventMovedOnline:"Moved Online"},classes:"col-4",default:"EventScheduled"}}},eventAttendanceMode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Event Attendance Mode","rank-math"),help:(0,Ve.__)("Indicates whether the event occurs online, offline at a physical location, or a mix of both online and offline.","rank-math"),options:{OfflineEventAttendanceMode:"Offline",OnlineEventAttendanceMode:"Online",MixedEventAttendanceMode:"Online + Offline"},default:"OfflineEventAttendanceMode",classes:"col-4"}}},VirtualLocation:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header",dependency:[{field:"eventAttendanceMode",value:["OnlineEventAttendanceMode","MixedEventAttendanceMode"]}]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{value:"VirtualLocation"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Online Event URL","rank-math"),help:(0,Ve.__)("The URL of the online event, where people can join. This property is required if your event is happening online","rank-math")}}}},location:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header",dependency:[{field:"eventAttendanceMode",value:["OfflineEventAttendanceMode","MixedEventAttendanceMode"]}]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Place"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Venue Name","rank-math"),help:(0,Ve.__)("The venue name.","rank-math")}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Venue URL","rank-math"),help:(0,Ve.__)("Website URL of the venue","rank-math")}}},address:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Address","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"PostalAddress"}},streetAddress:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Street Address","rank-math")}}},addressLocality:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Locality","rank-math")}}},addressRegion:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Region","rank-math")}}},postalCode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Postal Code","rank-math")}}},addressCountry:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Country","rank-math")}}}}},performer:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Performer Information","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"radio",label:(0,Ve.__)("Performer","rank-math"),classes:"show-property",options:{Organization:"Organization",Person:"Person"},default:"Person"}}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Name","rank-math")}}},sameAs:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Website or Social Link","rank-math")}}}},startDate:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Start Date","rank-math"),help:(0,Ve.__)("Date and time of the event","rank-math"),classes:"col-4"}}},endDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("End Date","rank-math"),help:(0,Ve.__)("End date and time of the event","rank-math"),classes:"col-4"}}},offers:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Offers","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Offer"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Name","rank-math"),classes:"hide-group",placeholder:"General Admission"}}},category:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Category","rank-math"),classes:"hide-group",placeholder:"primary"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("URL","rank-math")}}},price:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Price","rank-math")}}},priceCurrency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Currency","rank-math")}}},availability:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Availability","rank-math"),help:(0,Ve.__)("Offer availability","rank-math"),classes:"col-4",options:{InStock:"In Stock",SoldOut:"Sold Out",PreOrder:"Preorder"},default:"InStock"}}},validFrom:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,Ve.__)("Price Valid From","rank-math"),help:(0,Ve.__)("The date when the item becomes valid.","rank-math")}}},priceValidUntil:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,Ve.__)("Price Valid Until","rank-math"),help:(0,Ve.__)("The date after which the price will no longer be available","rank-math"),classes:"hide-group"}}},inventoryLevel:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Inventory Level","rank-math")}}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating","rank-math"),help:(0,Ve.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Minimum","rank-math"),help:(0,Ve.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Maximum","rank-math"),help:(0,Ve.__)("Rating maximum score","rank-math"),placeholder:5}}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},JobPosting:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("Job Posting","rank-math"),defaultEn:"Job Posting"},title:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),placeholder:"%seo_description%"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,Ve.__)("Shortcode","rank-math"),help:(0,Ve.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]"}},baseSalary:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"MonetaryAmount"}},currency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Salary Currency","rank-math"),help:(0,Ve.__)("ISO 4217 Currency code. Example: EUR","rank-math"),classes:"col-4"}}},value:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Salary","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"QuantitativeValue"}},value:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Salary (Recommended)","rank-math"),help:(0,Ve.__)("Insert amount, e.g. 50.00, or a salary range, e.g. 40.00-50.00","rank-math"),classes:"col-4"}}},unitText:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Payroll (Recommended)","rank-math"),help:(0,Ve.__)("Salary amount is for","rank-math"),options:{"":"None",YEAR:"Yearly",MONTH:"Monthly",WEEK:"Weekly",DAY:"Daily",HOUR:"Hourly"},classes:"col-4"}}}}},datePosted:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,Ve.__)("Date Posted","rank-math"),placeholder:"%date(Y-m-d)%",help:(0,Ve.__)("The original date on which employer posted the job. You can leave it empty to use the post publication date as job posted date","rank-math"),classes:"col-4"}}},validThrough:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,Ve.__)("Expiry Posted","rank-math"),help:(0,Ve.__)("The date when the job posting will expire. If a job posting never expires, or you do not know when the job will expire, do not include this property","rank-math"),classes:"col-4"}}},unpublish:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,Ve.__)("Unpublish when expired","rank-math"),options:{on:"Yes",off:"No"},help:(0,Ve.__)("If checked, post status will be changed to Draft and its URL will return a 404 error, as required by the Rich Result guidelines","rank-math"),classes:"col-4",default:"on"}}},employmentType:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"checkbox",multiple:!0,label:(0,Ve.__)("Employment Type (Recommended)","rank-math"),help:(0,Ve.__)("Type of employment. You can choose more than one value","rank-math"),options:{"":"None",FULL_TIME:"Full Time",PART_TIME:"Part Time",CONTRACTOR:"Contractor",TEMPORARY:"Temporary",INTERN:"Intern",VOLUNTEER:"Volunteer",PER_DIEM:"Per Diem",OTHER:"Other"},default:[]}}},hiringOrganization:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Organization"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Hiring Organization","rank-math"),placeholder:"%org_name%",help:(0,Ve.__)("The name of the company. Leave empty to use your own company information.","rank-math"),classes:"col-4"}}},sameAs:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Organization URL (Recommended)","rank-math"),placeholder:"%org_url%",help:(0,Ve.__)("The URL of the organization offering the job position. Leave empty to use your own company information","rank-math"),classes:"col-6"}}},logo:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Organization Logo (Recommended)","rank-math"),placeholder:"%org_logo%",help:(0,Ve.__)("Logo URL of the organization offering the job position. Leave empty to use your own company information","rank-math"),classes:"col-6"}}}},id:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Posting ID (Recommended)","rank-math"),help:(0,Ve.__)("The hiring organization's unique identifier for the job.","rank-math"),classes:"col-6"}}},jobLocation:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Place"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Venue Name","rank-math"),help:(0,Ve.__)("The venue name.","rank-math"),classes:"hide-group"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Venue URL","rank-math"),help:(0,Ve.__)("Website URL of the venue","rank-math"),classes:"hide-group"}}},address:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Address","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"PostalAddress"}},streetAddress:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Street Address","rank-math")}}},addressLocality:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Locality","rank-math")}}},addressRegion:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Region","rank-math")}}},postalCode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Postal Code","rank-math")}}},addressCountry:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Country","rank-math")}}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},Music:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("Music","rank-math"),defaultEn:"Music"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),placeholder:"%seo_description%"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,Ve.__)("Shortcode","rank-math"),help:(0,Ve.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("URL","rank-math"),placeholder:"%url%"}}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"radio",label:(0,Ve.__)("Music Type","rank-math"),classes:"show-property",options:{MusicGroup:"MusicGroup",MusicAlbum:"MusicAlbum"},default:"MusicGroup"}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},Person:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("Person","rank-math"),defaultEn:"Person"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),placeholder:"%seo_description%"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,Ve.__)("Shortcode","rank-math"),help:(0,Ve.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]"}},email:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Email","rank-math")}}},address:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Address","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"PostalAddress"}},streetAddress:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Street Address","rank-math")}}},addressLocality:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Locality","rank-math")}}},addressRegion:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Region","rank-math")}}},postalCode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Postal Code","rank-math")}}},addressCountry:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Country","rank-math")}}}},gender:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Gender","rank-math"),classes:"col-6"}}},jobTitle:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Job title","rank-math"),help:(0,Ve.__)("The job title of the person (for example, Financial Manager).","rank-math"),classes:"col-6"}}}},Product:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("Product","rank-math"),defaultEn:"Product"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Product name","rank-math"),placeholder:"%seo_title%"}}},reviewLocation:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,Ve.__)("Review Location","rank-math"),help:(0,Ve.__)("The review or rating must be displayed on the page to comply with Google's Schema guidelines.","rank-math"),options:{bottom:"Below Content",top:"Above Content",both:"Above and Below Content",custom:"Custom (use shortcode)"},default:"custom"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,Ve.__)("Shortcode","rank-math"),help:(0,Ve.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]",dependency:[{field:"reviewLocation",value:"custom"}]}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),placeholder:"%seo_description%"}}},sku:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Product SKU","rank-math")}}},brand:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Brand"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Brand Name","rank-math")}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}},gtin8:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Gtin","rank-math"),classes:"hide-group"}}},mpn:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("MPN","rank-math"),classes:"hide-group"}}},isbn:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("ISBN","rank-math"),classes:"hide-group"}}},offers:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Offers","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Offer"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Name","rank-math"),classes:"hide-group"}}},category:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Category","rank-math"),classes:"hide-group"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("URL","rank-math"),classes:"hide-group",placeholder:"%url%"}}},price:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Price","rank-math")}}},priceCurrency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Currency","rank-math")}}},availability:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Availability","rank-math"),help:(0,Ve.__)("Offer availability","rank-math"),classes:"col-4",options:{InStock:"In Stock",SoldOut:"Sold Out",PreOrder:"Preorder"},default:"InStock"}}},validFrom:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,Ve.__)("Price Valid From","rank-math"),help:(0,Ve.__)("The date when the item becomes valid.","rank-math"),classes:"hide-group"}}},priceValidUntil:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,Ve.__)("Price Valid Until","rank-math"),help:(0,Ve.__)("The date after which the price will no longer be available","rank-math")}}},inventoryLevel:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Inventory Level","rank-math"),classes:"hide-group"}}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating","rank-math"),help:(0,Ve.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Minimum","rank-math"),help:(0,Ve.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Maximum","rank-math"),help:(0,Ve.__)("Rating maximum score","rank-math"),placeholder:5}}}}}},Recipe:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("Recipe","rank-math"),defaultEn:"Recipe"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},reviewLocation:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,Ve.__)("Review Location","rank-math"),help:(0,Ve.__)("The review or rating must be displayed on the page to comply with Google's Schema guidelines.","rank-math"),options:{bottom:"Below Content",top:"Above Content",both:"Above and Below Content",custom:"Custom (use shortcode)"},default:"custom"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,Ve.__)("Shortcode","rank-math"),help:(0,Ve.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]",dependency:[{field:"reviewLocation",value:"custom"}]}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Published Date","rank-math"),classes:"hide-group",default:"%date(Y-m-d\\TH:i:sP)%"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Author Name","rank-math"),placeholder:"%name%"}}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),placeholder:"%seo_description%"}}},prepTime:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Preparation Time","rank-math"),help:(0,Ve.__)("ISO 8601 duration format. Example: PT1H30M","rank-math"),classes:"col-4"}}},cookTime:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Cooking Time","rank-math"),help:(0,Ve.__)("ISO 8601 duration format. Example: PT1H30M","rank-math"),classes:"col-4"}}},totalTime:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Total Time","rank-math"),help:(0,Ve.__)("ISO 8601 duration format. Example: PT1H30M","rank-math"),classes:"col-4"}}},recipeCategory:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Type","rank-math"),help:(0,Ve.__)("Type of dish, for example appetizer, or dessert.","rank-math"),classes:"col-4"}}},recipeCuisine:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Cuisine","rank-math"),help:(0,Ve.__)("The cuisine of the recipe (for example, French or Ethiopian).","rank-math"),classes:"col-4"}}},keywords:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Keywords","rank-math"),help:(0,Ve.__)("Other terms for your recipe such as the season, the holiday, or other descriptors. Separate multiple entries with commas.","rank-math"),classes:"col-4"}}},recipeYield:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Recipe Yield","rank-math"),help:(0,Ve.__)("Quantity produced by the recipe, for example 4 servings","rank-math"),classes:"col-4"}}},nutrition:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"NutritionInformation"}},calories:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Calories","rank-math"),help:(0,Ve.__)("The number of calories in the recipe. Optional.","rank-math")}}}},recipeIngredient:{map:{isArray:!0,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"show-add-property show-delete-property",field:{label:(0,Ve.__)("Recipe Ingredients","rank-math"),help:(0,Ve.__)("Recipe ingredients, add one item per line","rank-math")}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating","rank-math"),help:(0,Ve.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Minimum","rank-math"),help:(0,Ve.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Maximum","rank-math"),help:(0,Ve.__)("Rating maximum score","rank-math"),placeholder:5}}}}},video:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Video","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"VideoObject"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Name","rank-math"),help:(0,Ve.__)("A recipe video Name","rank-math"),classes:"col-6"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),help:(0,Ve.__)("A recipe video Description","rank-math")}}},embedUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Video URL","rank-math"),help:(0,Ve.__)("A video URL. Optional.","rank-math"),classes:"col-6"}}},contentUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Content URL","rank-math"),help:(0,Ve.__)("A URL pointing to the actual video media file","rank-math"),classes:"col-6"}}},thumbnailUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Recipe Video Thumbnail","rank-math"),help:(0,Ve.__)("A recipe video thumbnail URL","rank-math"),classes:"col-6"}}},duration:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Duration","rank-math"),help:(0,Ve.__)("ISO 8601 duration format. Example: PT1H30M","rank-math"),classes:"col-6"}}},uploadDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datepicker",label:(0,Ve.__)("Video Upload Date","rank-math"),classes:"col-6"}}}},instructionType:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"radio",label:(0,Ve.__)("Instruction Type","rank-math"),options:{SingleField:"Single Field",HowToStep:"How To Step"},default:"SingleField"}}},instructionsSingleField:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,Ve.__)("Recipe Instructions","rank-math")},dependency:[{field:"instructionType",value:"SingleField"}]}},instructionsHowToStep:{map:{isArray:!0,isGroup:!0,isRequired:!1,isRecommended:!1,arrayMap:"instructions",arrayProps:{map:{classes:"show-delete-property-group"}},field:{label:(0,Ve.__)("Recipe Instructions","rank-math")},dependency:[{field:"instructionType",value:["HowToStep"]}]}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},Restaurant:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("Restaurant","rank-math"),defaultEn:"Restaurant"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),placeholder:"%seo_description%"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,Ve.__)("Shortcode","rank-math"),help:(0,Ve.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]"}},telephone:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Phone Number","rank-math")}}},priceRange:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Price Range","rank-math"),classes:"col-4"}}},address:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Address","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"PostalAddress"}},streetAddress:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Street Address","rank-math")}}},addressLocality:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Locality","rank-math")}}},addressRegion:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Region","rank-math")}}},postalCode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Postal Code","rank-math")}}},addressCountry:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"text",label:(0,Ve.__)("Country","rank-math")}}}},geo:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Geo Coordinates","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"GeoCoordinates"}},latitude:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Latitude","rank-math")}}},longitude:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Longitude","rank-math")}}}},openingHoursSpecification:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Timings","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"OpeningHoursSpecification"}},dayOfWeek:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"checkbox",label:(0,Ve.__)("Open Days","rank-math"),options:{monday:"Monday",tuesday:"Tuesday",wednesday:"Wednesday",thursday:"Thursday",friday:"Friday",saturday:"Saturday",sunday:"Sunday"},default:[]}}},opens:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"timepicker",label:(0,Ve.__)("Opening Time","rank-math"),classes:"col-6",placeholder:"09:00 AM"}}},closes:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"timepicker",label:(0,Ve.__)("Closing Time","rank-math"),classes:"col-6",placeholder:"05:00 PM"}}}},servesCuisine:{map:{isArray:!0,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"show-add-property show-delete-property",field:{label:(0,Ve.__)("Serves Cuisine","rank-math")}}},hasMenu:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Menu URL","rank-math"),help:(0,Ve.__)("URL pointing to the menu of the restaurant.","rank-math"),classes:"col-6"}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},Service:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("Service","rank-math"),defaultEn:"Service"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),placeholder:"%seo_description%"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,Ve.__)("Shortcode","rank-math"),help:(0,Ve.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]"}},serviceType:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Service Type","rank-math"),help:(0,Ve.__)("The type of service being offered, e.g. veterans' benefits, emergency relief, etc.","rank-math"),classes:"col-4"}}},offers:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Offers","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Offer"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Name","rank-math"),classes:"hide-group"}}},category:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Category","rank-math"),classes:"hide-group"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("URL","rank-math"),classes:"hide-group"}}},price:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Price","rank-math")}}},priceCurrency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Currency","rank-math")}}},availability:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Availability","rank-math"),help:(0,Ve.__)("Offer availability","rank-math"),classes:["col-4","hide-group"],options:{InStock:"In Stock",SoldOut:"Sold Out",PreOrder:"Preorder"},default:"InStock"}}},validFrom:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,Ve.__)("Price Valid From","rank-math"),help:(0,Ve.__)("The date when the item becomes valid.","rank-math"),classes:"hide-group"}}},priceValidUntil:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,Ve.__)("Price Valid Until","rank-math"),help:(0,Ve.__)("The date after which the price will no longer be available","rank-math"),classes:"hide-group"}}},inventoryLevel:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Inventory Level","rank-math"),classes:"hide-group"}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},SoftwareApplication:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("Software","rank-math"),defaultEn:"Software"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},reviewLocation:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"select",label:(0,Ve.__)("Review Location","rank-math"),help:(0,Ve.__)("The review or rating must be displayed on the page to comply with Google's Schema guidelines.","rank-math"),options:{bottom:"Below Content",top:"Above Content",both:"Above and Below Content",custom:"Custom (use shortcode)"},default:"custom"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,Ve.__)("Shortcode","rank-math"),help:(0,Ve.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]",dependency:[{field:"reviewLocation",value:"custom"}]}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),placeholder:"%seo_description%"}}},operatingSystem:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Operating System","rank-math"),help:(0,Ve.__)("For example, Windows 7, OSX 10.6, Android 1.6","rank-math"),classes:"col-6"}}},applicationCategory:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Application Category","rank-math"),help:(0,Ve.__)("For example, Game, Multimedia","rank-math"),classes:"col-6"}}},offers:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Offers","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Offer"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Name","rank-math"),classes:"hide-group"}}},category:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Category","rank-math"),classes:"hide-group"}}},url:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("URL","rank-math"),classes:"hide-group"}}},price:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Price","rank-math")}}},priceCurrency:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{label:(0,Ve.__)("Currency","rank-math")}}},availability:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"select",label:(0,Ve.__)("Availability","rank-math"),help:(0,Ve.__)("Offer availability","rank-math"),classes:["col-4","hide-group"],options:{InStock:"In Stock",SoldOut:"Sold Out",PreOrder:"Preorder"},default:"InStock"}}},validFrom:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,Ve.__)("Price Valid From","rank-math"),help:(0,Ve.__)("The date when the item becomes valid.","rank-math"),classes:"hide-group"}}},priceValidUntil:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"datepicker",label:(0,Ve.__)("Price Valid Until","rank-math"),help:(0,Ve.__)("The date after which the price will no longer be available","rank-math"),classes:"hide-group"}}},inventoryLevel:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Inventory Level","rank-math"),classes:"hide-group"}}}},review:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Review","rank-math")}},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Review"}},datePublished:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Published Date","rank-math"),placeholder:"%date(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},dateModified:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"datetimepicker",label:(0,Ve.__)("Modified Date","rank-math"),placeholder:"%modified(Y-m-d\\TH:i:sP)%",classes:"hide-group"}}},author:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Person"}},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Author Name","rank-math"),placeholder:"%name%"}}}},reviewRating:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:"hide-group-header"},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"Rating"}},ratingValue:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating","rank-math"),help:(0,Ve.__)("Rating score","rank-math")}}},worstRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Minimum","rank-math"),help:(0,Ve.__)("Rating minimum score","rank-math"),placeholder:1}}},bestRating:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{type:"number",label:(0,Ve.__)("Rating Maximum","rank-math"),help:(0,Ve.__)("Rating maximum score","rank-math"),placeholder:5}}}}},image:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,classes:["hide-group-header","hide-group"]},"@type":{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,value:"ImageObject"}},url:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Image URL","rank-math"),placeholder:"%post_thumbnail%"}}}}},VideoObject:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("Video","rank-math"),defaultEn:"Video"},name:{map:{isArray:!1,isGroup:!1,isRequired:!0,isRecommended:!1,field:{label:(0,Ve.__)("Headline","rank-math"),placeholder:"%seo_title%"}}},description:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!0,field:{type:"textarea",label:(0,Ve.__)("Description","rank-math"),placeholder:"%seo_description%"}}},reviewLocationShortcode:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,save:"metadata",field:{type:"text",label:(0,Ve.__)("Shortcode","rank-math"),help:(0,Ve.__)("You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>.","rank-math"),readonly:"readonly"},value:"[rank_math_rich_snippet]"}},uploadDate:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Upload Date","rank-math"),classes:"hide-group",placeholder:"%date(Y-m-d\\TH:i:sP)%"}}},embedUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Embed URL","rank-math"),help:(0,Ve.__)("A URL pointing to the embeddable player for the video. Example: <code>https://www.youtube.com/embed/VIDEOID</code>","rank-math"),classes:"col-6"}}},contentUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Content URL","rank-math"),help:(0,Ve.__)("A URL pointing to the actual video media file like MP4, MOV, etc. Please leave it empty if you don't know the URL.","rank-math"),classes:"col-6"}}},duration:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Duration","rank-math"),help:(0,Ve.__)("ISO 8601 duration format. Example: 1H30M","rank-math"),classes:"col-6"}}},thumbnailUrl:{map:{isArray:!1,isGroup:!1,isRequired:!1,isRecommended:!1,field:{label:(0,Ve.__)("Video Thumbnail","rank-math"),help:(0,Ve.__)("A video thumbnail URL","rank-math"),classes:"hide-group",placeholder:"%post_thumbnail%"}}}},WooCommerceProduct:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("WooCommerce Product","rank-math"),defaultEn:"WooCommerce Product"}},EDDProduct:{map:{isArray:!1,isGroup:!0,isRequired:!1,isRecommended:!1,title:(0,Ve.__)("EDD Product","rank-math"),defaultEn:"EDD Product"}}}},zr={set:function(e,t,r){return!!e&&(localStorage.setItem(e,JSON.stringify({value:t,expires:this.expiry(r)})),!0)},get:function(e){if(!e)return!1;var t=localStorage.getItem(e);return!!t&&((t=JSON.parse(t)).expires&&Date.now()>t.expires?(localStorage.removeItem(e),!1):t.value)},remove:function(e){return!!e&&(localStorage.removeItem(e),!0)},expiry:function(e){if(!e)return!1;if(-1===e){var t=new Date;return t.setYear(1970),t.getTime()}var r=parseInt(e),a=e.replace(r,"");return"d"===a&&(r=24*r*60*60*1e3),"h"===a&&(r=60*r*60*1e3),"m"===a&&(r=60*r*1e3),"s"===a&&(r*=1e3),Date.now()+r}};function Kr(e){return Kr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kr(e)}function Jr(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,$r(a.key),a)}}function Qr(e,t,r){return(t=$r(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $r(e){var t=function(e,t){if("object"!==Kr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!==Kr(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Kr(t)?t:String(t)}var Xr="rank_math_schema_templates_store",Zr=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Qr(this,"cache",null),Qr(this,"templates",null),Qr(this,"verion","1.1.0"),"product"!==rankMath.postType&&delete Wr.schemas.WooCommerceProduct,"download"!==rankMath.postType&&delete Wr.schemas.EDDProduct,this.cache=(0,l.applyFilters)("rank_math_schema_maps",Wr),(0,l.doAction)("rank_math_schema_template_loaded")}var t,r,a;return t=e,r=[{key:"verifyCache",value:function(){var e=zr.get(Xr);return!1!==e&&this.version===e.version&&(this.cache=e,(0,l.doAction)("rank_math_schema_template_loaded"),!0)}},{key:"fetchStore",value:function(){var e=this;We()({method:"GET",url:"//"+window.location.host+"/wp-json/rankmath/v1/getSchemas"}).then((function(t){zr.set(Xr,t,"30d"),e.cache=t,(0,l.doAction)("rank_math_schema_template_loaded")}))}},{key:"getMap",value:function(e){var t=(0,o.get)(this.cache.properties,e,!1);return t||(0,o.get)(this.cache.schemas,e,!1)}},{key:"getTemplates",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return null===this.templates&&(this.templates=[],(0,o.forEach)(this.cache.schemas,(function(t,r){var a=(0,o.get)(t,"map.title",r);e.templates.push({type:r,title:"WooCommerceProduct"!==r?a:(0,Ve.__)("WooCommerce Product","rank-math")})})),t||this.templates.push({type:"PodcastEpisode",title:(0,Ve.__)("Podcast Episode","rank-math"),isPro:!0},{type:"Dataset",title:(0,Ve.__)("Dataset","rank-math"),isPro:!0},{type:"FactCheck",title:(0,Ve.__)("Fact Check","rank-math"),isPro:!0},{type:"Movie",title:(0,Ve.__)("Movie","rank-math"),isPro:!0},{type:"FAQ",title:(0,Ve.__)("FAQ","rank-math"),isPro:!0},{type:"HowTo",title:(0,Ve.__)("HowTo","rank-math"),isPro:!0}),this.templates=(0,o.orderBy)(this.templates,"type")),this.templates}}],r&&Jr(t.prototype,r),a&&Jr(t,a),Object.defineProperty(t,"prototype",{writable:!1}),e}(),ea=new Zr;function ta(e){return ea.getMap(e)}function ra(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return aa(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return aa(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var a=0,i=function(){};return{s:i,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,o=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,n=e},f:function(){try{o||null==r.return||r.return()}finally{if(s)throw n}}}}function aa(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=new Array(t);r<t;r++)a[r]=e[r];return a}function ia(e,t){if((0,o.isEmpty)(e)||t.id===e)return t;var r,a=ra(t.properties);try{for(a.s();!(r=a.n()).done;){var i=r.value;if(i.id===e)return i;if(i.map.isGroup){var n=ia(e,i);if(n)return n}}}catch(e){a.e(e)}finally{a.f()}}function na(e,t){if((0,o.isEmpty)(e))return t;var r,a=ra(t.properties);try{for(a.s();!(r=a.n()).done;){var i=r.value;if(i.property===e)return i.value=(0,o.has)(t.metadata,e)?t.metadata[e]:i.value,i}}catch(e){a.e(e)}finally{a.f()}return!1}var oa={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let sa;const la=new Uint8Array(16);function ma(){if(!sa&&(sa="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!sa))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return sa(la)}const pa=[];for(let e=0;e<256;++e)pa.push((e+256).toString(16).slice(1));function da(e,t=0){return pa[e[t+0]]+pa[e[t+1]]+pa[e[t+2]]+pa[e[t+3]]+"-"+pa[e[t+4]]+pa[e[t+5]]+"-"+pa[e[t+6]]+pa[e[t+7]]+"-"+pa[e[t+8]]+pa[e[t+9]]+"-"+pa[e[t+10]]+pa[e[t+11]]+pa[e[t+12]]+pa[e[t+13]]+pa[e[t+14]]+pa[e[t+15]]}var ua=function(e,t,r){if(oa.randomUUID&&!t&&!e)return oa.randomUUID();const a=(e=e||{}).random||(e.rng||ma)();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=a[e];return t}return da(a)};function ca(){return{id:"g-".concat(ua()),property:"",properties:[],map:{isGroup:!0,isArray:!1,isRequired:!1,isRecommended:!1}}}function ha(){return{id:"p-".concat(ua()),property:"",value:"",map:{isGroup:!1,isArray:!1,isRequired:!1,isRecommended:!1}}}function ya(e){return e.id="g-".concat(ua()),e.properties.forEach((function(e){e.map.isGroup?ya(e):e.id="p-".concat(ua())})),e}var fa=function e(t){if(!t)return ca();var r=t.map.isGroup?ca():ha();return(0,o.forEach)(t,(function(t,a){if("map"!==a){var i=ha();t.map.isGroup&&(i=e(t)),i.map=t.map,i.property=a,i.value=(0,o.get)(t.map,"value",(0,o.get)(t.map,"field.default","")),r.properties.push(i)}else(0,o.has)(t,"title")&&(r.map.title=t.title,r.map.defaultEn=t.defaultEn)})),r};function _a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=!1;arguments.length>2&&void 0!==arguments[2]&&arguments[2]||(r=(0,o.isString)(e)?ta(e):e);var a=fa(r);return(a=(0,o.merge)(a,t)).property=e,a}function ba(e){return ba="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ba(e)}function Ra(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function ga(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==ba(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!==ba(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ba(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ka(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return(0,o.isEmpty)(t)||(0,o.forEach)(t,(function(t,a){if("@context"!==a){var i=na(a,e);if(i)va(i,a,t,r);else{var n=!r&&ta(a);(0,o.isArray)(t)?(i=ca()).map.isArray=!0:i=(0,o.isObject)(t)||n?ka(n?_a(n):ca(),t,r):ha(),va(i,a,t,r),i.property=a,e.properties.push(i)}}})),e}var va=function(e,t,r){var a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!t)return e;var i=(0,l.applyFilters)("rank_math_schema_convert_value",!1,e,t,r,a);return!1!==i||!1!==(i=(0,l.applyFilters)("rank_math_schema_convert_"+t,!1,e,r))?i:(e.value=r,e)};function wa(e,t){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return ka(_a(t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r),e,r)}function Aa(e){var t=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ra(Object(r),!0).forEach((function(t){ga(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ra(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},e),r=(0,l.applyFilters)("rank_math_schema_type",(0,o.get)(t,"@type","")),a=(0,o.get)(t,"metadata",{type:"template"});delete t.metadata;var i="custom"===a.type?ca():_a(r);(0,o.has)(i.map,"title")&&!(0,o.has)(a,"title")&&(a.title=i.map.title),(0,o.has)(a,"title")&&a.title===i.map.defaultEn&&(a.title=i.map.title),i.property=(0,o.get)(a,"title",r),i.metadata=a;var n=na("@type",i);if(!1!==n&&""!==n.value&&(t["@type"]=n.value),i=ka(i,t,"custom"===a.type),"custom"!==a.type){var s=i.properties.pop();i.properties.unshift(s)}return i}function Ga(e){return Ga="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ga(e)}function Sa(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function qa(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Ga(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!==Ga(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ga(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ea=function(e){if(!(0,o.isEmpty)(e.value)||!(0,o.isUndefined)(e.map.field)&&"toggle"===e.map.field.type)return e.value;var t=(0,o.get)(e,"map.field.placeholder");return(0,o.isEmpty)(t)?(0,o.get)(e,"map.field.default",!1):t},Pa=function(e){if((0,o.isEmpty)(e)||(0,o.isUndefined)(e.properties))return e;var t={};return"metadata"in e&&(t.metadata=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Sa(Object(r),!0).forEach((function(t){qa(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sa(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},e.metadata),t.metadata.title=t.metadata.title?t.metadata.title:e.property),(0,o.map)(e.properties,(function(e){var r=(0,o.get)(e,"map.save",!0),a=(0,o.get)(e,"map.isHidden",!1);if((0,o.isEmpty)(e.properties)||(0,o.isEmpty)(e.properties[0])||(0,o.isEmpty)(e.properties[0].property)||"0"!==e.properties[0].property||(e.map.isArray=!0),!1!==r&&!a)if("metadata"!==r){var i=function(e){var t=!1;return t=(0,l.applyFilters)("rank_math_schema_process_value",t,e),(0,l.applyFilters)("rank_math_schema_process_"+e.property,t,e)}(e);if(!1===i){if(e.map.isArray){var n=[];return(0,o.map)(e.properties,(function(e){n.push((0,o.isUndefined)(e.properties)?e.value:Oa(e))})),void(t[e.property]=n)}if(e.map.isGroup){var s=Oa(e),m=(0,o.get)(s,"@type",(0,o.isUndefined)(s["@id"])?e.property:"");return m&&(s["@type"]=m),void(t[e.property]=s)}var p=Ea(e);!(0,o.isBoolean)(p)&&(0,o.isEmpty)(p)||!p||(t[e.property]=p)}else t[e.property]=i}else{var d=Ea(e);if((0,o.isEmpty)(d)&&"toggle"!==e.map.field.type)return;t.metadata[e.property]=d}})),t};function Oa(e){var t=Pa(e);return t=(0,l.applyFilters)("rank_math_processed_schema_"+t["@type"],t),(0,l.applyFilters)("rank_math_processed_schema",t)}function Ta(e,t){if(!(0,o.has)(e,"map.dependency"))return!0;var r=null,a=e.map.dependency,i=(0,o.get)(a,"relation","or");return(0,o.forEach)(a,(function(e){var a,n,s,l=na(e.field,t),m=(a=l.value,n=(0,o.get)(e,"value",!1),s=(0,o.get)(e,"comparison","="),(0,o.isArray)(n)&&"="===s?n.includes(a):(0,o.isArray)(n)&&"!="===s?!n.includes(a):"="===s&&a===n||"=="===s&&a===n||">="===s&&a>=n||"<="===s&&a<=n||">"===s&&a>n||"<"===s&&a<n||"!="===s&&a!==n);if("or"===i&&m)return r=!0,!1;"and"===i&&(r=(null===r||r)&&m)})),!!r}function Da(e){return Da="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Da(e)}function Ca(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function Ia(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ca(Object(r),!0).forEach((function(t){ja(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ca(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ja(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Da(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!==Da(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Da(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ma(e){return function(e){if(Array.isArray(e))return Ua(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Ua(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ua(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ua(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=new Array(t);r<t;r++)a[r]=e[r];return a}var Na=function(){n()((function(e){new ClipboardJS(".structured-data-copy").on("success",(function(t){var r=e(t.trigger);r.addClass("copied"),setTimeout((function(){r.removeClass("copied")}),2e3)}))})),(0,l.addAction)("rank_math_loaded","rank-math",(function(){var e={},t=(0,o.get)(rankMath,"schemas",{});(0,o.map)(t,(function(t,r){var a=(0,o.get)(t,"@type");t=(0,l.applyFilters)("rank_math_pre_schema_"+a,t),t=Aa((0,l.applyFilters)("rank_math_pre_schema",t)),e[r]=(0,l.applyFilters)("rank_math_pre_edited_schema",t,r)})),(0,s.dispatch)("rank-math").updateEditSchemas(e)})),(0,l.addFilter)("rank_math_schema_type","rank-math",(function(e){return(0,o.isUndefined)(e)||(0,o.isEmpty)(e)?e:"NewsArticle"===e||"BlogPosting"===e?"Article":"MusicGroup"===e||"MusicAlbum"===e?"Music":e.includes("Event")||"Festival"===e?"Event":e})),(0,l.addFilter)("rank_math_schema_convert_author","rank-math",(function(e,t,r){return(0,o.isObject)(r)||(0,o.isUndefined)(t.properties)?e:(t.properties[1].value=r,t)})),(0,l.addFilter)("rank_math_schema_convert_value","rank-math",(function(e,t,r,a){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(!t.map.isArray)return e;var n=t.map,s=n.arrayMap,l=void 0!==s&&s,m=n.arrayProps,p=void 0===m?{}:m;return l?((0,o.forEach)(a,(function(e){t.properties.push(wa(e,l,p,i))})),t):(a=function(e){return(0,o.isObject)(e)?e:[e]}(a),(0,o.forEach)(a,(function(e,r){var a=(0,o.get)(e,"@type",!1);if(!1===a){var n=ha();n.property=r,n.value=e,t.properties.push(n)}else t.properties.push(wa(e,a,p,i))})),t)})),(0,l.addFilter)("rank_math_schema_convert_value","rank-math",(function(e,t,r,a){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];return t.map.isArray||!t.map.isGroup||i?e:ka(t,a,i)}),20),(0,l.addFilter)("rank_math_pre_schema_Product","rank-math",(function(e){return(0,o.has)(e,"brand")&&!(0,o.isObject)(e.brand)&&(e.brand={"@type":"Brand",name:e.brand}),e})),(0,l.addFilter)("rank_math_processed_schema_Recipe","rank-math",(function(e){var t=e.instructionType;return delete e.instructionType,"SingleField"===t&&(e.recipeInstructions=e.instructionsSingleField,delete e.instructionsSingleField),"HowToStep"===t&&(1===e.instructionsHowToStep.length&&(e.recipeInstructions={"@type":"HowToSection",name:e.instructionsHowToStep[0].name,itemListElement:e.instructionsHowToStep[0].itemListElement}),e.instructionsHowToStep.length>1&&(e.recipeInstructions=Ma(e.instructionsHowToStep)),delete e.instructionsHowToStep),e}),20),(0,l.addFilter)("rank_math_pre_schema_Recipe","rank-math",(function(e){var t=e.recipeInstructions;if((0,o.isString)(t)&&(e.instructionType="SingleField",e.instructionsSingleField=t,delete e.recipeInstructions),(0,o.forEach)(["cookTime","prepTime","totalTime"],(function(t){(0,o.isUndefined)(e[t])||"PT"!==e[t]||delete e[t]})),(0,o.isArray)(t)&&(e.instructionType="HowToStep",(0,o.forEach)(t,(function(e,r){(0,o.isUndefined)(e.type)||(e["@type"]=e.type,delete e.type),(0,o.isUndefined)(e.itemListElement)||((0,o.isUndefined)(e.itemListElement.type)||(e.itemListElement["@type"]=e.itemListElement.type,delete e.itemListElement.type),(0,o.isArray)(e.itemListElement)||(e.itemListElement=[e.itemListElement])),t[r]=e})),e.instructionsHowToStep=t,delete e.recipeInstructions),!(0,o.isArray)(t)&&(0,o.isObject)(t)){var r=Ia(Ia({},t),{},{"@type":"HowToSection"});e.instructionType="HowToStep",e.instructionsHowToStep=[],e.instructionsHowToStep.push(r),delete e.recipeInstructions}return e})),(0,l.addFilter)("rank_math_processed_schema","rank-math",(function(e){var t=e.eventAttendanceMode;return(0,o.isUndefined)(t)||("MixedEventAttendanceMode"===t&&(e.location=[e.VirtualLocation,e.location],delete e.VirtualLocation),"OnlineEventAttendanceMode"===t&&(e.location=e.VirtualLocation,delete e.VirtualLocation)),e}),20),(0,l.addFilter)("rank_math_pre_schema","rank-math",(function(e){var t=e.eventAttendanceMode;return(0,o.isUndefined)(t)||("MixedEventAttendanceMode"===t&&(e.VirtualLocation=(0,o.find)(e.location,["@type","VirtualLocation"]),e.location=(0,o.find)(e.location,["@type","Place"])),"OnlineEventAttendanceMode"===t&&(e.VirtualLocation=e.location,delete e.location)),e})),(0,l.addFilter)("rank_math_pre_edited_schema","rank-math",(function(e,t){var r=e.properties;return(0,o.isEmpty)(r)||"new-9999"!==t||(e.properties=(0,o.map)(r,(function(t){return(0,o.includes)(["name","headline","title"],t.property)&&(t.value=e.metadata.name),"description"===t.property&&(t.value=e.metadata.description),t}))),e})),(0,l.addFilter)("rank_math_schema_apply_metadata_values_Job_Posting","rank-math",(function(e){return e.properties.map((function(t){return"unpublish"!==t.property||(0,o.isUndefined)(e.metadata.unpublish)||(t.value=e.metadata.unpublish),t})),e}))},La=r(184),xa=r.n(La),Fa=wp.compose,Ha=wp.components,Ba=r(697),Va=r.n(Ba),Ya=function(e){var t=e.position,r=void 0===t?"middle right":t,a=e.value,i=e.onChange,n=e.children;return wp.element.createElement(Ha.Dropdown,{position:r,className:"rank-math-datepicker",contentClassName:"rank-math-datepicker__dialog",renderToggle:function(e){var t=e.onToggle,r=e.isOpen;return wp.element.createElement(m.Fragment,null,n,wp.element.createElement(Ha.Button,{icon:"calendar-alt",onClick:t,"aria-expanded":r}))},renderContent:function(){return wp.element.createElement(Ha.DatePicker,{currentDate:a.split("T")[0],onMonthPreviewed:o.noop,onChange:function(e){i(e.split("T")[0])}})}})},Wa=function(e){var t=e.position,r=void 0===t?"middle right":t,a=e.value,i=e.onChange,n=e.children;return wp.element.createElement(Ha.Dropdown,{position:r,className:"rank-math-datepicker",contentClassName:"rank-math-datepicker__dialog",renderToggle:function(e){var t=e.onToggle,r=e.isOpen;return wp.element.createElement(m.Fragment,null,n,wp.element.createElement(Ha.Button,{icon:"calendar-alt",onClick:t,"aria-expanded":r}))},renderContent:function(){return wp.element.createElement(Ha.DateTimePicker,{is12Hour:!0,currentDate:a,onChange:i})}})},za={};(0,o.isUndefined)(rankMath.assessor)||(0,o.forEach)(rankMath.assessor.diacritics,(function(e,t){return za[t]=new RegExp(e,"g")}));function Ka(e){return(0,o.map)(e,(function(e,t){return{label:e,value:t}}))}var Ja=["value","onChange","type","options"];function Qa(e){return function(e){if(Array.isArray(e))return $a(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return $a(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $a(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=new Array(t);r<t;r++)a[r]=e[r];return a}function Xa(){return Xa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},Xa.apply(this,arguments)}function Za(e,t){if(null==e)return{};var r,a,i=function(e,t){if(null==e)return{};var r,a,i={},n=Object.keys(e);for(a=0;a<n.length;a++)r=n[a],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)r=n[a],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var ei=function(e){var t=e.value,r=e.onChange,a=e.type,i=e.options,n=void 0===i?{}:i,s=Za(e,Ja);return(0,o.isUndefined)(s.help)||(s.help=(0,m.RawHTML)({children:s.help})),"radio"===a?wp.element.createElement(Ha.RadioControl,Xa({selected:t,options:Ka(n),onChange:r},s)):"select"===a?(!(0,o.has)(s,"multiple")&&(0,o.isArray)(t)&&(t=t[0]),wp.element.createElement(Ha.SelectControl,Xa({value:t,options:Ka(n),onChange:r},s))):"toggle"===a?wp.element.createElement(Ha.ToggleControl,Xa({checked:t,onChange:r},s)):"number"===a?wp.element.createElement(Ha.TextControl,Xa({type:"number",autoComplete:"off",step:"0.01",value:t,onChange:r},s)):"url"===a?wp.element.createElement(Ha.TextControl,Xa({type:"url",autoComplete:"off",value:t,onChange:r},s)):"datepicker"===a?wp.element.createElement(Ya,{value:t,position:"bottom left",onChange:r},wp.element.createElement(Ha.TextControl,Xa({value:t,onChange:r},s))):"datetimepicker"===a?wp.element.createElement(Wa,{value:t,position:"bottom left",onChange:r},wp.element.createElement(Ha.TextControl,Xa({value:t,onChange:r},s))):"textarea"===a?wp.element.createElement(Ha.TextareaControl,Xa({rows:5,value:t,onChange:r},s)):"checkbox"===a?wp.element.createElement("div",{className:"rank-math-checkbox-component components-base-control schema-property--value"},wp.element.createElement("label",{htmlFor:"checklist-label",className:"components-base-control__label"},s.label),wp.element.createElement("div",null,Ka(n).map((function(e){return wp.element.createElement(Ha.CheckboxControl,{key:(0,o.uniqueId)("checkbox-"),label:e.label,checked:(0,o.includes)(t,e.value),onChange:function(a){var i=Qa(t);a?i.push(e.value):(0,o.remove)(i,(function(t){return t===e.value})),r(i)}})})))):wp.element.createElement(Ha.TextControl,Xa({value:t,onChange:r},s))};ei.default={value:"",type:"text",onChange:null},ei.propTypes={id:Va().string,onChange:Va().func.isRequired};var ti=ei,ri=React;function ai(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,i,n,o,s=[],l=!0,m=!1;try{if(n=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(a=n.call(r)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){m=!0,i=e}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(m)throw i}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ii(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ii(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ii(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=new Array(t);r<t;r++)a[r]=e[r];return a}var ni=function(e){var t=e.onClick,r=e.children,a=(e.className,(0,ri.useRef)()),i=ai((0,ri.useState)(!1),2),n=i[0],o=i[1],s=function(e){a.current.contains(e.target)||o(!1)};return(0,ri.useEffect)((function(){return n?document.addEventListener("mousedown",s):document.removeEventListener("mousedown",s),function(){document.removeEventListener("mousedown",s)}}),[n]),wp.element.createElement("div",{ref:a,className:"rank-math-inline-confirmation"},!n&&r(o),n&&wp.element.createElement("div",{className:"rank-math-confirm-delete"},wp.element.createElement("span",null,(0,Ve.__)("Delete?","rank-math")),wp.element.createElement(Ha.Button,{isLink:!0,onClick:function(){o(!1),t()}},wp.element.createElement("span",null,(0,Ve.__)("Yes","rank-math"))),wp.element.createElement(Ha.Button,{isLink:!0,onClick:function(){return o(!1)}},wp.element.createElement("span",null,(0,Ve.__)("No","rank-math")))))};function oi(e){return oi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},oi(e)}function si(){return si=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},si.apply(this,arguments)}function li(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,i,n,o,s=[],l=!0,m=!1;try{if(n=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(a=n.call(r)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){m=!0,i=e}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(m)throw i}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return mi(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return mi(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function mi(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=new Array(t);r<t;r++)a[r]=e[r];return a}function pi(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function di(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pi(Object(r),!0).forEach((function(t){ui(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pi(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ui(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==oi(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!==oi(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===oi(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ci=(0,Fa.compose)((0,s.withSelect)((function(e,t){return di(di({},t),{},{isPro:e("rank-math").isPro()})})))((function(e){var t=e.data,r=t.property,a=t.id,i=t.map,n=e.actions,s=n.removeProperty,p=n.propertyChange,d=n.duplicateProperty,u=(0,o.get)(i,"field",{label:!1}),c=di({},u),h=li((0,m.useState)(),2),y=h[0],f=h[1];if((0,m.useEffect)((function(){f(function(e,t){var r=e.data,a=r.property,i=r.value,n=e.schema.metadata;return i===(0,o.get)(t,"placeholder","")&&(i=""),"reviewLocation"===a&&(0,o.has)(n,"reviewLocation")&&(i=n.reviewLocation),"unpublish"===a&&(0,o.has)(n,"unpublish")&&(i=n.unpublish),e.isPro&&"[rank_math_rich_snippet]"===i&&(i='[rank_math_rich_snippet id="'+n.shortcode+'"]'),(0,l.applyFilters)("rank_math_schema_property_value",i,e)}(e,u))}),[a]),!1===Ta(e.data,e.schema))return e.data.map.isHidden=!0,null;i.isRequired&&(u.label&&(c.label=wp.element.createElement(m.Fragment,null,u.label," ",wp.element.createElement("span",null,"*"))),u.placeholder||(c.required="required")),e.isCustom&&(c.type=(0,l.applyFilters)("rank_math_schema_custom_field_type","text",r),delete c.label);var _=xa()("schema-group-or-property-container schema-property-container",(0,o.get)(u,"classes",!1),{"hide-property":"@type"===r});return e.data.map.isHidden=!1,wp.element.createElement("div",{className:_},wp.element.createElement("div",{className:"schema-group-or-property schema-property"},wp.element.createElement("div",{className:"schema-property--body"},!e.isCustom&&u.label?null:wp.element.createElement("div",{className:"schema-property--field"},wp.element.createElement(Ha.TextControl,{value:r,onChange:function(e){p(a,"property",e)}})),wp.element.createElement("div",{className:"schema-property--value"},wp.element.createElement(ti,si({value:y},c,{onChange:function(e){f(e),p(a,"value",e)}})),(0,o.has)(c,"notice")&&wp.element.createElement(Ha.Notice,si({isDismissible:!1},c.notice),c.notice.content)),!i.isRequired&&wp.element.createElement("div",{className:"schema-property--header"},wp.element.createElement(Ha.Button,{isSecondary:!0,className:"button rank-math-duplicate-property",onClick:function(){return d(a,e.parentId,e.data)}},wp.element.createElement("i",{className:"rm-icon rm-icon-copy"})),wp.element.createElement(ni,{key:a,onClick:function(){return s(a,e.parentId)}},(function(e){return wp.element.createElement(Ha.Button,{isSecondary:!0,className:"button rank-math-delete-group",onClick:function(){return e(!0)}},wp.element.createElement("i",{className:"rm-icon rm-icon-trash"}))}))))))})),hi={},yi=function e(t){if(!1===Ta(t.data,t.schema))return t.data.map.isHidden=!0,null;var r=t.parentId,a=t.isCustom,i=t.isPro,n=t.isMain,s=void 0!==n&&n,p=t.data,d=p.id,u=p.property,c=p.properties,h=p.map,y=p.metadata,f=t.actions,_=f.addProperty,b=f.addGroup,R=f.removeGroup,g=f.propertyChange,k=f.duplicateGroup,v=(0,o.get)(h,"field",{label:!1}),w=xa()("schema-group-or-property-container schema-group-container",(0,o.get)(h,"classes",!1),{"hide-property":"metadata"===u,"is-group":h.isGroup,"is-array":h.isArray,"no-array-map":(0,o.isUndefined)(h.arrayMap)});return h.isArray&&(hi[d]=0),t.data.map.isHidden=!1,wp.element.createElement("div",{className:w},wp.element.createElement("div",{className:"schema-group-or-property schema-group"},(0,l.applyFilters)("rank_math_schema_before_fields","",u),wp.element.createElement("div",{className:"schema-group-header"},function(){if(t.isArray)return hi[r]+=1,wp.element.createElement("div",{className:"schema-property--label"},(0,o.startCase)(u)," ",hi[r]);if(!a&&v.label)return wp.element.createElement("div",{className:"schema-property--label"},v.label,v.labelHelp&&wp.element.createElement("span",{className:"schema-property--label-help"},(0,m.RawHTML)({children:v.labelHelp})));var e="WooCommerceProduct"!==u?u:"WooCommerce Product";return wp.element.createElement("div",{className:"schema-property--field"},wp.element.createElement(Ha.TextControl,{value:(0,o.isUndefined)(y)||(0,o.isEmpty)(y.title)?e:y.title,disabled:!i,onChange:function(e){g(d,"property",e)}}))}(),wp.element.createElement(Ha.ButtonGroup,{className:"schema-group--actions schema-group--actions--tr"},wp.element.createElement(Ha.Button,{className:"button rank-math-add-property",isLink:!0,onClick:function(){return _(d)}},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,Ve.__)("Add Property","rank-math"))),wp.element.createElement(Ha.Button,{className:"button rank-math-add-property-group",isLink:!0,onClick:function(){return b(d,h)}},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,Ve.__)("Add Property Group","rank-math"))),!s&&wp.element.createElement(Ha.Button,{className:"button rank-math-duplicate-property-group",isLink:!0,onClick:function(){return k(d,t.parentId,t.data)}},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,Ve.__)("Duplicate Group","rank-math"))),wp.element.createElement(ni,{key:d,onClick:function(){return R(d,t.parentId)}},(function(e){return wp.element.createElement(Ha.Button,{className:"button rank-math-delete-group",isLink:!0,onClick:function(){return e(!0)}},wp.element.createElement("i",{className:"rm-icon rm-icon-trash"}),wp.element.createElement("span",null,(0,Ve.__)("Delete","rank-math")))})))),wp.element.createElement("div",{className:"schema-group--children"},(0,o.map)(c,(function(r,n){return r.map.isGroup?wp.element.createElement(e,{key:n,data:r,parentId:d,isArray:h.isArray,isCustom:a,schema:t.schema,actions:t.actions,isPro:i}):wp.element.createElement(ci,{key:n,data:r,parentId:d,isCustom:a,schema:t.schema,actions:t.actions})}))),s&&wp.element.createElement("div",{className:"schema-group-footer"},wp.element.createElement(Ha.ButtonGroup,{className:"schema-group--actions schema-group--actions--tr"},wp.element.createElement(Ha.Button,{className:"button rank-math-add-property",isLink:!0,onClick:function(){return _(d)}},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,Ve.__)("Add Property","rank-math"))),wp.element.createElement(Ha.Button,{className:"button rank-math-add-property-group",isLink:!0,onClick:function(){return b(d,h)}},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,Ve.__)("Add Property Group","rank-math"))),wp.element.createElement(ni,{key:d,onClick:function(){return R(d,t.parentId)}},(function(e){return wp.element.createElement(Ha.Button,{className:"button rank-math-delete-group",isLink:!0,onClick:function(){return e(!0)}},wp.element.createElement("i",{className:"rm-icon rm-icon-trash"}),wp.element.createElement("span",null,(0,Ve.__)("Delete","rank-math")))}))))))};function fi(e){return fi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fi(e)}function _i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function bi(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_i(Object(r),!0).forEach((function(t){Ai(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ri(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,Gi(a.key),a)}}function gi(e,t){return gi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},gi(e,t)}function ki(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,a=wi(e);if(t){var i=wi(this).constructor;r=Reflect.construct(a,arguments,i)}else r=a.apply(this,arguments);return function(e,t){if(t&&("object"===fi(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return vi(e)}(this,r)}}function vi(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function wi(e){return wi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},wi(e)}function Ai(e,t,r){return(t=Gi(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Gi(e){var t=function(e,t){if("object"!==fi(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!==fi(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===fi(t)?t:String(t)}var Si=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&gi(e,t)}(n,e);var t,r,a,i=ki(n);function n(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),Ai(vi(e=i.apply(this,arguments)),"addGroup",(function(t,r){var a=bi({},e.state.data),i=ia(t,a),n=r.isArray,o=r.arrayMap,s=void 0!==o&&o,l=r.arrayProps,m=n&&s?_a(s,void 0===l?{}:l):ca();i.properties.push(m),e.setState({data:a})})),Ai(vi(e),"addProperty",(function(t){var r=bi({},e.state.data);ia(t,r).properties.push(ha()),e.setState({data:r})})),Ai(vi(e),"duplicateGroup",(function(t,r,a){var i=bi({},e.state.data),n=ia(r,i),s=(0,o.cloneDeep)(a),l=n.properties.findIndex((function(e){return e.id===t}));n.properties.splice(l,0,ya(s)),e.setState({data:i})})),Ai(vi(e),"duplicateProperty",(function(t,r,a){var i=bi({},e.state.data),n=ia(r,i),o=bi({},a),s=n.properties.findIndex((function(e){return e.id===t}));o.id="p-".concat(ua()),n.properties.splice(s,0,o),e.setState({data:i})})),Ai(vi(e),"removeGroup",(function(t,r){var a=bi({},e.state.data),i=ia(r,a);if(i.id!==t){var n=i.properties.findIndex((function(e){return e.id===t}));i.properties.splice(n,1),e.setState({data:a})}else e.setState({data:ca()})})),Ai(vi(e),"removeProperty",(function(t,r){var a=bi({},e.state.data),i=ia(r,a),n=i.properties.findIndex((function(e){return e.id===t}));i.properties.splice(n,1),e.setState({data:a})})),Ai(vi(e),"propertyChange",(function(t,r,a){var i=bi({},e.state.data),n=ia(t,i);Object.assign(n,Ai({},r,a)),!(0,o.isEmpty)(i.metadata)&&(0,o.has)(i.metadata,n.property)&&"title"!==n.property&&(i.metadata[n.property]=a),"property"!==r||(0,o.isUndefined)(n.metadata)||(n.metadata.title=a),e.setState({data:i}),(0,l.doAction)("rank_math_property_changed",i,n,e.setState,r,a,e.state)})),e.options=(0,o.get)(e.props.data,"metadata",{}),e.state={data:e.props.data,loading:!1,showNotice:!1,postId:rankMath.objectID},e.setState=e.setState.bind(vi(e)),e.templateSaveCount=0,e.isEditingTemplate=(0,o.get)(rankMath,"isTemplateScreen",!1),e}return t=n,(r=[{key:"getWrapperClasses",value:function(){var e=(0,o.get)(rankMath,"knowledgegraphType",!1);e=!1===e?"empty":"local-"+e;var t=(0,o.get)(this.props.data,"property","");return(0,o.isArray)(t)&&(t=t.join("-")),t="schema-"+t.toLowerCase(),xa()("schema-builder",t,Ai({"schema-template-pre-defined":"template"===this.options.type,"schema-template-custom":"custom"===this.options.type},"".concat(e),"template"===this.options.type))}},{key:"render",value:function(){var e=this;return wp.element.createElement("form",{className:this.getWrapperClasses()},wp.element.createElement(yi,{data:this.state.data,schema:this.state.data,isPro:this.props.isPro,parentId:null,isMain:!0,isArray:!1,isCustom:"custom"===this.options.type,actions:{addGroup:this.addGroup,addProperty:this.addProperty,removeGroup:this.removeGroup,removeProperty:this.removeProperty,propertyChange:this.propertyChange,duplicateGroup:this.duplicateGroup,duplicateProperty:this.duplicateProperty}}),wp.element.createElement("div",{className:"schema-builder-save-as"},this.props.isPro&&wp.element.createElement(m.Fragment,null,"custom"!==this.options.type&&wp.element.createElement(Ha.Button,{isSecondary:!0,onClick:function(){return e.props.toggleMode(e.props.id,e.state.data)}},(0,Ve.__)("Advanced Editor","rank-math")),!this.isEditingTemplate&&wp.element.createElement(Ha.Button,{isSecondary:!0,className:this.state.loading?"save-as-template saving":"save-as-template",onClick:function(){e.templateSaveCount>=1&&!confirm((0,Ve.__)("Each save will create a new template.","rank-math"))||(e.templateSaveCount+=1,e.props.saveTemplate(e.state.data,e.setState))}},this.state.showNotice?(0,Ve.__)("Template saved.","rank-math"):(0,Ve.__)("Save as Template","rank-math")),this.state.showNotice&&wp.element.createElement("div",{className:"rank-math-save-template-confirmation"},(0,Ve.__)("Template saved.","rank-math"))),this.isEditingTemplate&&wp.element.createElement(Ha.Button,{isPrimary:!0,className:"button",onClick:function(){return e.props.saveTemplate(e.state.data,e.setState,e.state.postId)}},this.state.loading?(0,Ve.__)("Saving","rank-math"):this.state.showNotice?(0,Ve.__)("Saved","rank-math"):(0,Ve.__)("Save","rank-math")),!this.isEditingTemplate&&wp.element.createElement(Ha.Button,{isPrimary:!0,className:"button",onClick:function(){return e.props.saveSchema(e.props.id,e.state.data)}},"term"===rankMath.objectType?(0,Ve.__)("Save for this Term","rank-math"):(0,Ve.__)("Save for this Post","rank-math"))))}}])&&Ri(t.prototype,r),a&&Ri(t,a),Object.defineProperty(t,"prototype",{writable:!1}),n}(m.Component);Si.defaultProps={query:null,fields:[],onQueryChange:null},Si.propTypes={query:Va().object,fields:Va().array.isRequired,onQueryChange:Va().func};var qi=(0,Fa.compose)((0,s.withSelect)((function(e,t){var r=e("rank-math").getEditingSchema(),a=e("rank-math").getEditSchemas(),i=(0,o.isString)(r.data.property)?r.data.property:(0,o.isArray)(r.data.property)?r.data.property[0]:"";return(0,l.applyFilters)("rank_math_schema_apply_metadata_values_"+i.replaceAll(" ","_"),r.data),bi(bi(bi({},t),r),{},{schemas:a,isPro:e("rank-math").isPro()})})),(0,s.withDispatch)((function(e,t){var r=t.onSave,a=void 0!==r&&r,i=t.isPro,s=t.schemas;return{toggleMode:function(t,r){if(confirm((0,Ve.__)("Are you sure you want to convert? You can't use simple mode for this edited Schema.","rank-math"))){var a=bi({},r);a.metadata.type="custom",e("rank-math").updateEditSchema(t,a)}},saveSchema:function(t,r){var l=n()("form.schema-builder").get(0);if(l.checkValidity()){i||(0,o.forEach)(s,(function(r,a){t!==a&&e("rank-math").deleteSchema(a)}));var m=Oa(r);e("rank-math").updateEditSchema(t,r),e("rank-math").saveSchema(t,m),a&&a(t,m),e("rank-math").schemaUpdated(!0),e("rank-math").toggleSchemaTemplates(!1),e("rank-math").toggleSchemaEditor(!1)}else l.reportValidity()},saveTemplate:function(t,r,a){e("rank-math").saveTemplate(Oa(t),r,a)}}})))(Si),Ei={"@context":"https://schema.org/","@graph":[{"@type":"Article",headline:"Power Words: The Art of Writing Headlines That Get Clicked",description:"Power words are words with strong meaning that smart copywriters (as well as marketers) use to increase CTR and boost conversions.",author:{"@type":"Person",name:"Rank Math"},datePublished:"2020-09-12GMT+000015:45:32+00:00",dateModified:"2020-09-12GMT+000015:45:29+00:00","@id":"https://rankmath.com/blog/power-words/#schema-44838",mainEntityOfPage:{"@id":"https://rankmath.com/blog/power-words/#webpage"},isPartOf:{"@id":"https://rankmath.com/blog/power-words/#webpage"},publisher:{"@id":"/#organization"},inLanguage:"en-US"}]},Pi=function(){var e,t=JSON.stringify(Ei,null,2);return wp.element.createElement(m.Fragment,null,wp.element.createElement("div",{className:"rank-math-pretty-json free-version"},wp.element.createElement("form",{method:"post",target:"_blank",action:"https://search.google.com/test/rich-results"},wp.element.createElement("h4",{className:"rank-math-schema-section-title"},(0,Ve.__)("JSON-LD Code","rank-math")),wp.element.createElement("button",{className:"button structured-data-copy is-small",type:"button","data-clipboard-text":t},wp.element.createElement("i",{className:"rm-icon rm-icon-copy"}),wp.element.createElement("span",{className:"original-text"},(0,Ve.__)("Copy","rank-math")),wp.element.createElement("span",{className:"success","aria-hidden":"true"},(0,Ve.__)("Copied!","rank-math"))),wp.element.createElement("button",{className:"button structured-data-test is-small",type:"submit"},wp.element.createElement("i",{className:"rm-icon rm-icon-google"})," ",wp.element.createElement("span",null,(0,Ve.__)("Test with Google","rank-math"))),wp.element.createElement("textarea",{name:"code_snippet",defaultValue:t})),wp.element.createElement("pre",{className:"code-output"},wp.element.createElement("code",{className:"language-javascript",dangerouslySetInnerHTML:{__html:(e=t,"string"!=typeof e&&(e=JSON.stringify(e,null,2)),(e=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")).replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,(function(e){var t="number";return/^"/.test(e)?t=/:$/.test(e)?"key":"string":/true|false/.test(e)?t="boolean":/null/.test(e)&&(t="null"),'<span class="'+t+'">'+e+"</span>"})))}})),wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks width-70"},wp.element.createElement("h3",null,(0,Ve.__)("Preview & Validate Your Schema Markup","rank-math")),wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,Ve.__)("Advanced Schema markup viewer","rank-math")),wp.element.createElement("li",null,(0,Ve.__)("Live testing with Google","rank-math")),wp.element.createElement("li",null,(0,Ve.__)("No other SEO plugin offers this feature","rank-math"))),wp.element.createElement("a",{className:"button button-primary is-green",href:"https://rankmath.com/pricing/?utm_source=Plugin&utm_medium=Code+Validation&utm_campaign=WP",rel:"noreferrer noopener",target:"_blank"},(0,Ve.__)("Upgrade","rank-math"))))))},Oi=n()("#post"),Ti=n()(".rank-math-schema"),Di=function(e,t){Ti.val(JSON.stringify(t)),setTimeout((function(){Oi.submit()}),1e3)},Ci=function(){return wp.element.createElement(Ha.TabPanel,{className:"rank-math-tabs rank-math-editor rank-math-schema-tabs",activeClass:"is-active",tabs:Object.values((e={builder:{name:"builder",title:wp.element.createElement(m.Fragment,null,wp.element.createElement("span",null,(0,Ve.__)("Edit","rank-math"))),view:function(){return wp.element.createElement(qi,{onSave:Di})},className:"rank-math-tab-builder"},codeValidation:{name:"codeValidation",title:wp.element.createElement(m.Fragment,null,wp.element.createElement("span",null,(0,Ve.__)("Code Validation","rank-math"))),view:Pi,className:"rank-math-tab-code-validation"}},(0,l.applyFilters)("rank_math_schema_editor_tabs",e)))},(function(e){return wp.element.createElement("div",{className:"components-panel__body rank-math-schema-tab-content-"+e.name},(0,m.createElement)(e.view))}));var e},Ii=(0,Fa.compose)((0,s.withSelect)((function(e){var t=e("rank-math").getEditingSchema();return{isOpen:e("rank-math").isSchemaEditorOpen(),isCutomSchema:(0,o.get)(t,"data.metadata.type",!1)}})))((function(e){var t=e.isOpen,r=void 0!==t&&t,a=e.isCutomSchema;if(!r)return null;var i=xa()("rank-math-modal rank-math-schema-generator rank-math-schema-modal",{"rank-math-schema-modal-no-map":"custom"===a}),n=(0,l.applyFilters)("rank_math_schema_modal_title",(0,Ve.__)("Select Schema","rank-math"));return wp.element.createElement(Ha.Modal,{title:n,closeButtonLabel:(0,Ve.__)("Close","rank-math"),shouldCloseOnClickOutside:!1,className:i,onRequestClose:function(){var e=window.location,t=e.origin,r=e.pathname;window.location=t+r.replace("post.php","edit.php?post_type=rank_math_schema").replace("post-new.php","edit.php?post_type=rank_math_schema")},overlayClassName:"rank-math-modal-overlay"},wp.element.createElement(Ci,null))}));function ji(e){return(0,o.get)({off:"rm-icon rm-icon-schema",Article:"rm-icon rm-icon-post",Book:"rm-icon rm-icon-book",Course:"rm-icon rm-icon-course",Dataset:"rm-icon rm-icon-dataset",Event:"rm-icon rm-icon-calendar",FactCheck:"rm-icon rm-icon-fact-check",JobPosting:"rm-icon rm-icon-job",Local:"rm-icon rm-icon-local-seo",Movie:"rm-icon rm-icon-movie",Music:"rm-icon rm-icon-music",Product:"rm-icon rm-icon-cart",Products:"rm-icon rm-icon-cart",WooCommerceProduct:"rm-icon rm-icon-cart",Recipe:"rm-icon rm-icon-recipe",Restaurant:"rm-icon rm-icon-restaurant",Video:"rm-icon rm-icon-video",Videos:"rm-icon rm-icon-video",VideoObject:"rm-icon rm-icon-video",Person:"rm-icon rm-icon-users",Review:"rm-icon rm-icon-star","Review snippets":"rm-icon rm-icon-star",Service:"rm-icon rm-icon-service",Software:"rm-icon rm-icon-software",SoftwareApplication:"rm-icon rm-icon-software","Sitelinks searchbox":"rm-icon rm-icon-search",FAQ:"rm-icon rm-icon-faq",FAQPage:"rm-icon rm-icon-faq",HowTo:"rm-icon rm-icon-howto",Breadcrumbs:"rm-icon rm-icon-redirection",PodcastEpisode:"rm-icon rm-icon-podcast"},e,"rm-icon rm-icon-schema")}function Mi(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=rankMath.links[e]||"";if(!r)return"#";if(!t)return r;var a={utm_source:"Plugin",utm_medium:encodeURIComponent(t),utm_campaign:"WP"};return r+"?"+Object.keys(a).map((function(e){return"".concat(e,"=").concat(a[e])})).join("&")}function Ui(e){return Ui="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ui(e)}function Ni(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function Li(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ni(Object(r),!0).forEach((function(t){xi(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ni(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function xi(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Ui(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!==Ui(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ui(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Fi=(0,Fa.compose)((0,s.withSelect)((function(e,t){var r=e("rank-math").isPro(),a=e("rank-math").getSchemas(),i=(0,o.findKey)(a,"metadata.isPrimary");return i=(0,o.isEmpty)(i)?{}:{id:i,type:a[i]["@type"]},Li(Li({},t),{},{primarySchema:i,isPro:r})})),(0,s.withDispatch)((function(e,t){var r=t.isPro,a=t.primarySchema,i=void 0!==a&&a,n=!(!r||!(0,o.isEmpty)(i));return r||(n=!0),{addSchema:function(t){var r=(0,o.uniqueId)("new-"),a=(0,o.get)(t,"schema",!1);!1===a&&(a={"@type":t.type,metadata:{type:"template",shortcode:"s-".concat(ua())}}),a.metadata.isPrimary=n,e("rank-math").setEditingSchemaId(r),e("rank-math").updateEditSchema(r,Aa(a)),e("rank-math").toggleSchemaEditor(!0)},editSchema:function(t){e("rank-math").setEditingSchemaId(t),e("rank-math").toggleSchemaEditor(!0)}}})))((function(e){var t=e.search,r=e.templates,a=e.isPro,i=e.addSchema,n=e.editSchema,o=e.primarySchema;""!==t&&(r=r.filter((function(e){return e.title.toLowerCase().includes(t)})));var s=o?(0,l.applyFilters)("rank_math_schema_type",o.type):"";return wp.element.createElement("div",{className:"rank-math-schema-catalog"},r.map((function(e,t){var r=!a&&s===e.type,l=xa()("rank-math-schema-item rank-math-use-schema row button",{"in-use":r,"schema-pro":e.isPro});return wp.element.createElement("div",{id:"rank-math-schema-list-wrapper",key:t},wp.element.createElement(Ha.Button,{key:t,id:"rank-math-schema-item",className:l,href:e.isPro?Mi("pro","PRO Schema Type"):"#",target:e.isPro?"_blank":"",isLink:!0,onClick:function(){if(!e.isPro)return r?n(o.id):i(e)}},wp.element.createElement("input",{type:"radio",name:"primarySchema",value:e.type,checked:s===e.type,onChange:function(){return i(e)},disabled:e.isPro}),wp.element.createElement("span",{className:"rank-math-schema-name"},wp.element.createElement("i",{className:ji(e.type)}),e.title,e.isPro&&wp.element.createElement("span",{className:"rank-math-pro-badge"},(0,Ve.__)("Pro","rank-math"))),wp.element.createElement("span",{className:"button rank-math-schema-item-actions"},wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,Ve.__)("Use","rank-math")))))})))})),Hi=(0,Ha.withFilters)("rankMath.schema.SchemaList")((function(e){var t=e.schemas,r=e.edit,a=e.trash,i=e.preview,n=e.showProNotice;return(0,o.isEmpty)(t)?null:wp.element.createElement("div",{className:"rank-math-schema-in-use"},wp.element.createElement("h4",{className:"rank-math-schema-section-title"},(0,Ve.__)("Schema in Use","rank-math")),n&&wp.element.createElement("div",{className:"components-notice rank-math-notice is-warning"},wp.element.createElement("div",{className:"components-notice__content"},(0,Ve.__)("Multiple Schemas are allowed in the","rank-math")," ",wp.element.createElement("a",{href:Mi("pro","Schema Tab Notice"),rel:"noreferrer noopener",target:"_blank"},wp.element.createElement("strong",null,(0,Ve.__)("PRO Version","rank-math"))))),(0,o.map)(t,(function(e,t){return wp.element.createElement("div",{key:t,id:"rank-math-schema-item",className:"rank-math-schema-item row"},wp.element.createElement("strong",{className:"rank-math-schema-name"},wp.element.createElement("i",{className:ji(e["@type"])}),(0,o.get)(e,"metadata.title",e["@type"])),wp.element.createElement("span",{className:"rank-math-schema-item-actions"},wp.element.createElement(Ha.Button,{className:"button rank-math-edit-schema",isLink:!0,onClick:function(){return r(t)}},wp.element.createElement("i",{className:"rm-icon rm-icon-edit"}),wp.element.createElement("span",null,(0,Ve.__)("Edit","rank-math"))),wp.element.createElement(Ha.Button,{className:"button rank-math-preview-schema",isLink:!0,onClick:function(){return i(t)}},wp.element.createElement("i",{className:"rm-icon rm-icon-eye"}),wp.element.createElement("span",null,(0,Ve.__)("Preview","rank-math"))),wp.element.createElement(ni,{key:t,onClick:function(){return a(t)}},(function(e){return wp.element.createElement(Ha.Button,{isLink:!0,className:"button rank-math-delete-schema",onClick:function(){return e(!0)}},wp.element.createElement("i",{className:"rm-icon rm-icon-trash"}),wp.element.createElement("span",null,(0,Ve.__)("Delete","rank-math")))}))))})))})),Bi=(0,Fa.compose)((0,s.withSelect)((function(e){var t=e("rank-math").getSchemas();return{schemas:t,showProNotice:1<=Object.keys(t).length}})),(0,s.withDispatch)((function(e){return{trash:function(t){e("rank-math").deleteSchema(t)},edit:function(t){e("rank-math").setEditingSchemaId(t),e("rank-math").toggleSchemaEditor(!0)},preview:function(t){e("rank-math").setEditingSchemaId(t),e("rank-math").setEditorTab("codeValidation"),e("rank-math").toggleSchemaEditor(!0)}}})))(Hi);function Vi(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,i,n,o,s=[],l=!0,m=!1;try{if(n=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(a=n.call(r)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){m=!0,i=e}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(m)throw i}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Yi(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Yi(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Yi(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=new Array(t);r<t;r++)a[r]=e[r];return a}var Wi=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"global"===e?ea.getTemplates(t):(0,l.applyFilters)("rank_math_schema_templates_by_source",[],e)},zi=(0,Fa.compose)((0,s.withSelect)((function(e){return{isPro:e("rank-math").isPro()}})))((function(e){var t=Vi((0,ri.useState)("global"),2),r=t[0],a=t[1],i=Vi((0,ri.useState)(""),2),n=i[0],o=i[1];return wp.element.createElement(m.Fragment,null,e.isPro&&wp.element.createElement(Bi,null),wp.element.createElement("h4",{className:"rank-math-schema-section-title"},(0,Ve.__)("Available Schema Types","rank-math")),wp.element.createElement("div",{className:"rank-math-schema-filter"},wp.element.createElement(Ha.RadioControl,{selected:r,options:(0,l.applyFilters)("rank_math_schema_template_sources",[{value:"global",label:(0,Ve.__)("Schema Catalog","rank-math")}]),onChange:a}),wp.element.createElement("div",{className:"rank-math-schema-search"},wp.element.createElement(Ha.TextControl,{value:n,onChange:o,placeholder:(0,Ve.__)("Search…","rank-math")}))),wp.element.createElement(Fi,{templates:Wi(r,e.isPro),search:n.toLowerCase()}))})),Ki=function(){return wp.element.createElement("div",{className:"components-panel__body rank-math-custom-schema-wrapper"},wp.element.createElement("img",{src:rankMath.customSchemaImage,alt:"",className:"custom-schema"}),wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks width-60"},wp.element.createElement("h3",null,(0,Ve.__)("Advanced Schema Builder","rank-math")),wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,Ve.__)("Possibility to create 700+ Schema Types","rank-math")),wp.element.createElement("li",null,(0,Ve.__)("Import Schema from ANY website","rank-math")),wp.element.createElement("li",null,(0,Ve.__)("Create Advanced templates","rank-math"))),wp.element.createElement("a",{className:"button button-primary is-green",href:Mi("pro","Custom Builder"),rel:"noreferrer noopener",target:"_blank"},(0,Ve.__)("Upgrade","rank-math")))))},Ji=function(){return wp.element.createElement(Ha.TabPanel,{className:"rank-math-tabs rank-math-editor rank-math-schema-tabs",activeClass:"is-active",tabs:Object.values((e={templates:{name:"templates",title:wp.element.createElement(m.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-schema"}),wp.element.createElement("span",null,(0,Ve.__)("Schema Templates","rank-math"))),view:zi,className:"rank-math-tab-templates"},newSchema:{name:"new-schema",title:wp.element.createElement(m.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-circle-plus"}),wp.element.createElement("span",null,(0,Ve.__)("Custom Schema","rank-math"))),view:Ki,className:"rank-math-tab-new-schema"}},(0,l.applyFilters)("rank_math_schema_templates_tabs",e)))},(function(e){return wp.element.createElement("div",{className:"components-panel__body rank-math-schema-tab-content-"+e.name},(0,m.createElement)(e.view))}));var e};function Qi(e){return Qi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Qi(e)}function $i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function Xi(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Qi(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!==Qi(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Qi(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Zi=(0,Fa.compose)((0,s.withSelect)((function(e,t){return function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?$i(Object(r),!0).forEach((function(t){Xi(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},t)})))((function(e){return e.isOpen?wp.element.createElement(Ha.Modal,{title:(0,Ve.__)("Schema Generator","rank-math"),closeButtonLabel:(0,Ve.__)("Close","rank-math"),shouldCloseOnClickOutside:!1,className:"rank-math-modal rank-math-schema-generator rank-math-schema-modal",onRequestClose:function(){if(!wp.data.select("rank-math").isSchemaEditorOpen()){var e=window.location,t=e.origin,r=e.pathname;window.location=t+r.replace("post.php","edit.php?post_type=rank_math_schema").replace("post-new.php","edit.php?post_type=rank_math_schema")}},overlayClassName:"rank-math-modal-overlay"},wp.element.createElement("a",{href:Mi("rich-snippets","Schema Generator Header"),rel:"noopener noreferrer",target:"_blank",title:(0,Ve.__)("More Info","rank-math"),className:"rank-math-schema-info"},wp.element.createElement(Ha.Dashicon,{icon:"info"})),wp.element.createElement(Ji,null)):null}));n()((function(){var e=Object.keys(rankMath.schemas),t=n()(".rank-math-schema-meta-id");t.val((0,o.get)(e,0,"").replace("schema-","")),Na(),(0,s.dispatch)("rank-math").setVersion(),(0,s.dispatch)("rank-math").setEditingSchemaId(e[0]);var r=!0;""!==t.val()&&"new-9999"!==t.val()&&(r=!1,(0,s.dispatch)("rank-math").toggleSchemaEditor(!0)),(0,l.addAction)("rank_math_loaded","rank-math",(function(){(0,m.createRoot)(document.getElementById("rank-math-schema-template")).render((0,m.createElement)((function(){return wp.element.createElement(m.Fragment,null,wp.element.createElement(Zi,{isOpen:r}),wp.element.createElement(Ii,null))})))})),(0,l.doAction)("rank_math_loaded")}))}()}();