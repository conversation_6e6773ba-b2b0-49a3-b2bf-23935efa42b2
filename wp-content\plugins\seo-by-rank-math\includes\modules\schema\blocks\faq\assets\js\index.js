!function(){var e={184:function(e,t){var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var a=typeof n;if("string"===a||"number"===a)e.push(n);else if(Array.isArray(n)){if(n.length){var i=o.apply(null,n);i&&e.push(i)}}else if("object"===a){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var l in n)r.call(n,l)&&n[l]&&e.push(l)}}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";var e=wp.blocks,t=lodash,r=wp.i18n,o=wp.element,a=wp.components,i=wp.blockEditor;function l(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=rankMath.links[e]||"";if(!n)return"#";if(!t)return n;var r={utm_source:"Plugin",utm_medium:encodeURIComponent(t),utm_campaign:"WP"};return n+"?"+Object.keys(r).map((function(e){return"".concat(e,"=").concat(r[e])})).join("&")}var s=wp.data;function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==u(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===u(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var f=(0,s.withSelect)((function(e,t){var n=(0,e("core/block-editor").getSettings)().imageSizes;return m(m({},t),{},{imageSizes:n})}))((function(e){var n=e.imageSizes,o=e.attributes,l=e.setAttributes,s=function(e){return(0,t.map)(e,(function(e){var t=e.name;return{value:e.slug,label:t}}))}(n);return wp.element.createElement(i.InspectorControls,{key:"inspector"},wp.element.createElement(a.PanelBody,{title:(0,r.__)("FAQ Options","rank-math")},wp.element.createElement(a.SelectControl,{label:(0,r.__)("List Style","rank-math"),value:o.listStyle,options:[{value:"",label:(0,r.__)("None","rank-math")},{value:"numbered",label:(0,r.__)("Numbered","rank-math")},{value:"unordered",label:(0,r.__)("Unordered","rank-math")}],onChange:function(e){l({listStyle:e})}}),wp.element.createElement(a.SelectControl,{label:(0,r.__)("Title Wrapper","rank-math"),value:o.titleWrapper,options:[{value:"h2",label:(0,r.__)("H2","rank-math")},{value:"h3",label:(0,r.__)("H3","rank-math")},{value:"h4",label:(0,r.__)("H4","rank-math")},{value:"h5",label:(0,r.__)("H5","rank-math")},{value:"h6",label:(0,r.__)("H6","rank-math")},{value:"p",label:(0,r.__)("P","rank-math")},{value:"div",label:(0,r.__)("DIV","rank-math")}],onChange:function(e){l({titleWrapper:e})}}),wp.element.createElement(a.SelectControl,{label:(0,r.__)("Image Size","rank-math"),value:o.sizeSlug,options:s,onChange:function(e){l({sizeSlug:e})}})),wp.element.createElement(a.PanelBody,{title:(0,r.__)("Styling Options","rank-math")},wp.element.createElement(a.TextControl,{label:(0,r.__)("Title Wrapper CSS Class(es)","rank-math"),value:o.titleCssClasses,onChange:function(e){l({titleCssClasses:e})}}),wp.element.createElement(a.TextControl,{label:(0,r.__)("Content Wrapper CSS Class(es)","rank-math"),value:o.contentCssClasses,onChange:function(e){l({contentCssClasses:e})}}),wp.element.createElement(a.TextControl,{label:(0,r.__)("List CSS Class(es)","rank-math"),value:o.listCssClasses,onChange:function(e){l({listCssClasses:e})}})))})),b=n(184),v=n.n(b);function y(e,n){var r=(0,s.select)("core").getMedia,o=e?r(e):null;return null===o?null:n?function(e,n){var r=(0,t.get)(e,["media_details","sizes",n,"source_url"]);return r||(0,t.get)(e,["media_details","sizes","full","source_url"])}(o,n):o}var h=(0,s.withSelect)((function(e,t){var n=t.imageID,r=t.sizeSlug;return{imageUrl:n?y(n,r):null}}))((function(e){var t=e.imageUrl;return t?wp.element.createElement("img",{src:t,alt:""}):null})),d=function(e){var t=e.imageID,n=e.sizeSlug,o=e.open,i=e.removeImage,l=e.addButtonLabel,s=void 0===l?(0,r.__)("Add Image","rank-math"):l;return wp.element.createElement("div",{className:"rank-math-media-placeholder"},t>0&&wp.element.createElement(h,{imageID:t,sizeSlug:n}),t>0?wp.element.createElement(a.Button,{icon:"edit",className:"rank-math-replace-image",onClick:o}):wp.element.createElement(a.Button,{onClick:o,className:"rank-math-add-image",isPrimary:!0},s),t>0&&wp.element.createElement(a.Button,{icon:"no-alt",className:"rank-math-delete-image",onClick:i}))},g=wp.hooks;function w(e){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w(e)}function k(e){return function(e){if(Array.isArray(e))return _(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return _(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function S(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,A(r.key),r)}}function C(e,t){return C=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},C(e,t)}function E(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=j(e);if(t){var o=j(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return function(e,t){if(t&&("object"===w(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return O(e)}(this,n)}}function O(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function j(e){return j=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},j(e)}function P(e,t,n){return(t=A(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function A(e){var t=function(e,t){if("object"!==w(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==w(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===w(t)?t:String(t)}var q=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&C(e,t)}(s,e);var t,n,o,l=E(s);function s(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s);for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return P(O(e=l.call.apply(l,[this].concat(n))),"toggleVisibility",(function(){var t=e.props,n=t.setAttributes,r=t.index,o=k(e.props.questions);o[r].visible=!e.props.visible,n({questions:o})})),P(O(e),"deleteQuestion",(function(){var t=e.props,n=t.setAttributes,r=t.index,o=k(e.props.questions);o.splice(r,1),n({questions:o})})),e}return t=s,(n=[{key:"render",value:function(){var e=this,t=this.props,n=t.title,o=t.content,l=t.visible,s=t.imageID,u=t.sizeSlug,c=t.titleWrapper,m=t.titleCssClasses,p=t.contentCssClasses,f=v()("rank-math-question-wrapper",{"question-not-visible":!l});return wp.element.createElement("div",{className:f},wp.element.createElement("div",{className:"rank-math-item-header"},wp.element.createElement(i.RichText,{tagName:c,className:"rank-math-faq-question rank-math-block-title"+m,value:n,onChange:function(t){e.setQuestionProp("title",t)},placeholder:(0,r.__)("Question…","rank-math")}),wp.element.createElement("div",{className:"rank-math-block-actions"},(0,g.applyFilters)("rank_math_block_faq_actions","",this.props,this),wp.element.createElement(a.Button,{className:"rank-math-item-visbility",icon:l?"visibility":"hidden",onClick:this.toggleVisibility,label:(0,r.__)("Hide Question","rank-math"),showTooltip:!0}),wp.element.createElement(a.Button,{icon:"trash",className:"rank-math-item-delete",onClick:this.deleteQuestion,label:(0,r.__)("Delete Question","rank-math"),showTooltip:!0}))),wp.element.createElement("div",{className:"rank-math-item-content"},wp.element.createElement(i.RichText,{tagName:"div",className:"rank-math-faq-answer "+p,value:o,onChange:function(t){e.setQuestionProp("content",t)},placeholder:(0,r.__)("Enter the answer to the question","rank-math")}),wp.element.createElement(i.MediaUpload,{allowedTypes:["image"],multiple:!1,value:s,render:function(t){var n=t.open;return wp.element.createElement(d,{imageID:s,sizeSlug:u,open:n,removeImage:function(){e.setQuestionProp("imageID",0)}})},onSelect:function(t){e.setQuestionProp("imageID",t.id)}})))}},{key:"setQuestionProp",value:function(e,t){var n=this.props,r=n.setAttributes,o=n.index,a=k(this.props.questions);a[o][e]=t,r({questions:a})}}])&&S(t.prototype,n),o&&S(t,o),Object.defineProperty(t,"prototype",{writable:!1}),s}(o.Component),N=q,x=function(e){return"".concat(e,"-").concat((new Date).getTime())};function T(e){return function(e){if(Array.isArray(e))return I(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return I(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return I(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function I(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},B.apply(this,arguments)}var D={from:[{type:"block",blocks:["yoast/faq-block"],transform:function(t){var n={titleWrapper:"h3",questions:t.questions.map((function(e){return{title:e.jsonQuestion,content:e.jsonAnswer,visible:!0}})),className:t.className};return(0,e.createBlock)("rank-math/faq-block",n)}}]};(0,e.registerBlockType)("rank-math/faq-block",{example:{attributes:{questions:[{visible:!0,titleWrapper:"div",title:"Question",content:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}]}},edit:function(e){var n=e.className,s=e.isSelected,u=e.attributes.textAlign,c=(0,i.useBlockProps)();return wp.element.createElement("div",c,wp.element.createElement("div",{id:"rank-math-faq",className:"rank-math-block "+n},s&&wp.element.createElement(f,e),s&&wp.element.createElement(o.Fragment,null,wp.element.createElement(i.BlockControls,null,wp.element.createElement(i.AlignmentToolbar,{value:u,onChange:function(t){return e.setAttributes({textAlign:t})}}))),wp.element.createElement("ul",{style:{textAlign:u}},function(e){var n=e.attributes,r=n.sizeSlug,o=n.titleWrapper,a=n.titleCssClasses,i=n.contentCssClasses,l=e.attributes.questions;return(0,t.isEmpty)(l)&&(l=[{id:x("faq-question"),title:"",content:"",visible:!0}],e.setAttributes({questions:l})),l.map((function(t,n){return wp.element.createElement("li",{key:t.id},wp.element.createElement(N,B({},t,{index:n,key:t.id+"-question",questions:l,setAttributes:e.setAttributes,sizeSlug:r,titleWrapper:o,titleCssClasses:a,contentCssClasses:i})))}))}(e)),wp.element.createElement(a.Button,{variant:"primary",onClick:function(){!function(e){var t=T(e.attributes.questions);t.push({id:x("faq-question"),title:"",content:"",visible:!0}),e.setAttributes({questions:t})}(e)}},(0,r.__)("Add New FAQ","rank-math")),wp.element.createElement("a",{href:l("faq-schema-block","Add New FAQ"),rel:"noopener noreferrer",target:"_blank",title:(0,r.__)("More Info","rank-math"),className:"rank-math-block-info"},wp.element.createElement(a.Dashicon,{icon:"info"}))))},save:function(e){var n=e.attributes,r=n.questions,o=n.titleWrapper;return(0,t.isEmpty)(r)?null:wp.element.createElement("div",i.useBlockProps.save(),r.map((function(e,n){return(0,t.isEmpty)(e.title)||(0,t.isEmpty)(e.content)||!1===e.visible?null:wp.element.createElement("div",{className:"rank-math-faq-item",key:n},wp.element.createElement(i.RichText.Content,{tagName:o,value:e.title,className:"rank-math-question"}),wp.element.createElement(i.RichText.Content,{tagName:"div",value:e.content,className:"rank-math-answer"}))})))},transforms:D})}()}();