!function(){var e={184:function(e,t){var n;!function(){"use strict";var r={}.hasOwnProperty;function a(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var o=typeof n;if("string"===o||"number"===o)e.push(n);else if(Array.isArray(n)){if(n.length){var l=a.apply(null,n);l&&e.push(l)}}else if("object"===o){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var i in n)r.call(n,i)&&n[i]&&e.push(i)}}}return e.join(" ")}e.exports?(a.default=a,e.exports=a):void 0===(n=function(){return a}.apply(t,[]))||(e.exports=n)}()}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";var e=wp.blocks,t=lodash,r=wp.i18n,a=wp.element,o=wp.hooks,l=wp.components,i=wp.blockEditor,s=n(184),c=n.n(s),u=wp.data;function m(e,n){var r=(0,u.select)("core").getMedia,a=e?r(e):null;return null===a?null:n?function(e,n){var r=(0,t.get)(e,["media_details","sizes",n,"source_url"]);return r||(0,t.get)(e,["media_details","sizes","full","source_url"])}(a,n):a}var p=(0,u.withSelect)((function(e,t){var n=t.imageID,r=t.sizeSlug;return{imageUrl:n?m(n,r):null}}))((function(e){var t=e.imageUrl;return t?wp.element.createElement("img",{src:t,alt:""}):null})),f=function(e){var t=e.imageID,n=e.sizeSlug,a=e.open,o=e.removeImage,i=e.addButtonLabel,s=void 0===i?(0,r.__)("Add Image","rank-math"):i;return wp.element.createElement("div",{className:"rank-math-media-placeholder"},t>0&&wp.element.createElement(p,{imageID:t,sizeSlug:n}),t>0?wp.element.createElement(l.Button,{icon:"edit",className:"rank-math-replace-image",onClick:a}):wp.element.createElement(l.Button,{onClick:a,className:"rank-math-add-image",isPrimary:!0},s),t>0&&wp.element.createElement(l.Button,{icon:"no-alt",className:"rank-math-delete-image",onClick:o}))};function h(e){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function b(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function v(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,S(r.key),r)}}function y(e,t){return y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},y(e,t)}function g(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=k(e);if(t){var a=k(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return function(e,t){if(t&&("object"===h(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return w(e)}(this,n)}}function w(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function k(e){return k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},k(e)}function _(e,t,n){return(t=S(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function S(e){var t=function(e,t){if("object"!==h(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==h(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===h(t)?t:String(t)}var C=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&y(e,t)}(u,e);var t,n,a,s=g(u);function u(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u);for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return _(w(e=s.call.apply(s,[this].concat(n))),"toggleVisibility",(function(){var t=e.props,n=t.setAttributes,r=t.index,a=b(e.props.steps);a[r].visible=!e.props.visible,n({steps:a})})),_(w(e),"deleteStep",(function(){var t=e.props,n=t.setAttributes,r=t.index,a=b(e.props.steps);a.splice(r,1),n({steps:a})})),e}return t=u,(n=[{key:"render",value:function(){var e=this,t=this.props,n=t.title,a=t.content,s=t.visible,u=t.imageID,m=t.sizeSlug,p=t.titleWrapper,h=t.titleCssClasses,b=t.contentCssClasses,d=c()("rank-math-step-wrapper",{"step-not-visible":!s});return wp.element.createElement("div",{className:d},wp.element.createElement("div",{className:"rank-math-item-header"},wp.element.createElement(i.RichText,{tagName:p,className:"rank-math-howto-step-title rank-math-block-title"+h,value:n,onChange:function(t){e.setStepProp("title",t)},placeholder:(0,r.__)("Enter a step title","rank-math")}),wp.element.createElement("div",{className:"rank-math-block-actions"},(0,o.applyFilters)("rank_math_block_howto_actions","",this.props),wp.element.createElement(l.Button,{className:"rank-math-item-visbility",icon:s?"visibility":"hidden",onClick:this.toggleVisibility,title:(0,r.__)("Hide Step","rank-math")}),wp.element.createElement(l.Button,{icon:"trash",className:"rank-math-item-delete",onClick:this.deleteStep,title:(0,r.__)("Delete Step","rank-math")}))),wp.element.createElement(i.MediaUpload,{allowedTypes:["image"],multiple:!1,value:u,render:function(t){var n=t.open;return wp.element.createElement(f,{imageID:u,sizeSlug:m,open:n,addButtonLabel:(0,r.__)("Add Step Image","rank-math"),removeImage:function(){e.setStepProp("imageID",0)}})},onSelect:function(t){e.setStepProp("imageID",t.id)}}),wp.element.createElement(i.RichText,{tagName:"div",className:"rank-math-howto-step-content"+b,value:a,onChange:function(t){e.setStepProp("content",t)},placeholder:(0,r.__)("Enter a step description","rank-math")}))}},{key:"setStepProp",value:function(e,t){var n=this.props,r=n.setAttributes,a=n.index,o=b(this.props.steps);o[a][e]=t,r({steps:o})}}])&&v(t.prototype,n),a&&v(t,a),Object.defineProperty(t,"prototype",{writable:!1}),u}(a.Component),E=C;function O(e){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},O(e)}function j(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function A(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?j(Object(n),!0).forEach((function(t){P(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function P(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==O(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==O(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===O(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var N=(0,u.withSelect)((function(e,t){var n=(0,e("core/block-editor").getSettings)().imageSizes;return A(A({},t),{},{imageSizes:n})}))((function(e){var n=e.imageSizes,a=e.attributes,o=e.setAttributes,s=function(e){return(0,t.map)(e,(function(e){var t=e.name;return{value:e.slug,label:t}}))}(n);return wp.element.createElement(i.InspectorControls,{key:"inspector"},wp.element.createElement(l.PanelBody,{title:(0,r.__)("HowTo Options","rank-math")},wp.element.createElement(l.SelectControl,{label:(0,r.__)("List Style","rank-math"),value:a.listStyle,options:[{value:"",label:(0,r.__)("None","rank-math")},{value:"numbered",label:(0,r.__)("Numbered","rank-math")},{value:"unordered",label:(0,r.__)("Unordered","rank-math")}],onChange:function(e){o({listStyle:e})}}),wp.element.createElement(l.SelectControl,{label:(0,r.__)("Title Wrapper","rank-math"),value:a.titleWrapper,options:[{value:"h2",label:(0,r.__)("H2","rank-math")},{value:"h3",label:(0,r.__)("H3","rank-math")},{value:"h4",label:(0,r.__)("H4","rank-math")},{value:"h5",label:(0,r.__)("H5","rank-math")},{value:"h6",label:(0,r.__)("H6","rank-math")},{value:"p",label:(0,r.__)("P","rank-math")},{value:"div",label:(0,r.__)("DIV","rank-math")}],onChange:function(e){o({titleWrapper:e})}}),wp.element.createElement(l.SelectControl,{label:(0,r.__)("Main Image Size","rank-math"),value:a.mainSizeSlug,options:s,onChange:function(e){o({mainSizeSlug:e})}}),wp.element.createElement(l.SelectControl,{label:(0,r.__)("Image Size","rank-math"),value:a.sizeSlug,options:s,onChange:function(e){o({sizeSlug:e})}})),wp.element.createElement(l.PanelBody,{title:(0,r.__)("Styling Options","rank-math")},wp.element.createElement(l.TextControl,{label:(0,r.__)("Step Title Wrapper CSS Class(es)","rank-math"),value:a.titleCssClasses,onChange:function(e){o({titleCssClasses:e})}}),wp.element.createElement(l.TextControl,{label:(0,r.__)("Step Content Wrapper CSS Class(es)","rank-math"),value:a.contentCssClasses,onChange:function(e){o({contentCssClasses:e})}}),wp.element.createElement(l.TextControl,{label:(0,r.__)("Step List CSS Class(es)","rank-math"),value:a.listCssClasses,onChange:function(e){o({listCssClasses:e})}})))})),x=function(e){return"".concat(e,"-").concat((new Date).getTime())};function D(){return D=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},D.apply(this,arguments)}function T(e){return function(e){if(Array.isArray(e))return I(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return I(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return I(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function I(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var z={from:[{type:"block",blocks:["yoast/how-to-block"],transform:function(t){var n={steps:t.steps.map((function(e){return{visible:!0,id:x("howto-step"),title:e.jsonName,content:e.jsonText}})),titleWrapper:"h3",hasDuration:t.hasDuration,days:t.days,hours:t.hours,minutes:t.minutes,description:t.jsonDescription,className:t.className,listStyle:t.unorderedList?"unordered":""};return(0,e.createBlock)("rank-math/howto-block",n)}}]};(0,e.registerBlockType)("rank-math/howto-block",{example:{attributes:{steps:[{visible:!0,titleWrapper:"div",title:"Step # 1",content:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}]}},edit:function(e){var n=e.className,s=e.isSelected,c=e.attributes,u=e.setAttributes,m=c.imageID,p=c.mainSizeSlug,h=c.textAlign,b=(0,i.useBlockProps)();return wp.element.createElement("div",b,wp.element.createElement("div",{id:"rank-math-howto",className:"rank-math-block "+n},s&&wp.element.createElement(N,e),s&&wp.element.createElement(a.Fragment,null,wp.element.createElement(i.BlockControls,null,wp.element.createElement(i.AlignmentToolbar,{value:h,onChange:function(t){return e.setAttributes({textAlign:t})}}))),wp.element.createElement(i.MediaUpload,{allowedTypes:["image"],multiple:!1,value:m,render:function(t){var n=t.open;return wp.element.createElement("div",{className:"rank-math-howto-final-image"},wp.element.createElement(f,{imageID:m,sizeSlug:p,open:n,addButtonLabel:(0,r.__)("Add Final Image","rank-math"),removeImage:function(){!function(e){(0,e.setAttributes)({imageID:0})}(e)}}))},onSelect:function(t){!function(e,t){(0,t.setAttributes)({imageID:e.id})}(t,e)}}),wp.element.createElement(i.RichText,{style:{textAlign:h},tagName:"div",className:"rank-math-howto-description",value:c.description,onChange:function(e){u({description:e})},placeholder:(0,r.__)("Enter a main description","rank-math")}),wp.element.createElement("div",{className:"rank-math-howto-duration"},wp.element.createElement("div",{className:"components-base-control rank-math-howto-duration-label"},wp.element.createElement("span",null,(0,r.__)("Duration","rank-math")),wp.element.createElement(l.ToggleControl,{checked:c.hasDuration,onChange:function(){!function(e){e.setAttributes({hasDuration:!e.attributes.hasDuration})}(e)}})),wp.element.createElement("div",{className:"rank-math-howto-duration-fields"+(c.hasDuration?"":" hidden")},wp.element.createElement(l.TextControl,{value:c.timeLabel,placeholder:(0,r.__)("Total time:","rank-math"),onChange:function(e){u({timeLabel:e})}}),wp.element.createElement(l.TextControl,{type:"number",value:c.days,placeholder:(0,r.__)("DD","rank-math"),onChange:function(e){u({days:e})}}),wp.element.createElement(l.TextControl,{type:"number",value:c.hours,placeholder:(0,r.__)("HH","rank-math"),onChange:function(e){u({hours:e})}}),wp.element.createElement(l.TextControl,{type:"number",value:c.minutes,placeholder:(0,r.__)("MM","rank-math"),onChange:function(e){u({minutes:e})}})),wp.element.createElement("div",{className:"rank-math-howto-duration-instructions"+(c.hasDuration?"":" hidden")},(0,r.__)("Optional, use first field to describe the duration.","rank-math"))),(0,o.applyFilters)("rank_math_block_howto_data","",e),wp.element.createElement("ul",{style:{textAlign:h}},function(e){var n=e.attributes,r=n.steps,a=n.sizeSlug,o=n.titleWrapper,l=n.titleCssClasses,i=n.contentCssClasses;return(0,t.isEmpty)(r)?null:r.map((function(t,n){return wp.element.createElement("li",{key:t.id},wp.element.createElement(E,D({},t,{index:n,key:t.id+"-step",steps:r,setAttributes:e.setAttributes,sizeSlug:a,titleWrapper:o,titleCssClasses:l,contentCssClasses:i})))}))}(e)),wp.element.createElement(l.Button,{variant:"primary",onClick:function(){!function(e){var n=e.attributes.steps,r=(0,t.isEmpty)(n)?[]:T(n);r.push({id:x("howto-step"),title:"",content:"",visible:!0}),e.setAttributes({steps:r})}(e)}},(0,r.__)("Add New Step","rank-math")),wp.element.createElement("a",{href:"http://rankmath.com/blog/howto-schema/",title:(0,r.__)("More Info","rank-math"),target:"_blank",rel:"noopener noreferrer",className:"rank-math-block-info"},wp.element.createElement(l.Dashicon,{icon:"info"}))))},save:function(e){var n=e.attributes,r=n.steps,a=n.titleWrapper;return(0,t.isEmpty)(r)?null:wp.element.createElement("div",i.useBlockProps.save(),r.map((function(e,t){return!1===e.visible?null:wp.element.createElement("div",{className:"rank-math-howto-step",key:t},e.title&&wp.element.createElement(i.RichText.Content,{tagName:a,value:e.title,className:"rank-math-howto-title"}),e.content&&wp.element.createElement(i.RichText.Content,{tagName:"div",value:e.content,className:"rank-math-howto-content"}))})))},transforms:z})}()}();