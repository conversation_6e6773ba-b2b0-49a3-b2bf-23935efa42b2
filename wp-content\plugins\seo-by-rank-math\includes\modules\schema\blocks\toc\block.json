{"apiVersion": 3, "title": "Table of Contents by Rank Math", "description": "Automatically generate the Table of Contents from the Headings added to this page.", "name": "rank-math/toc-block", "category": "rank-math-blocks", "icon": "rm-icon rm-icon-stories", "textdomain": "rank-math", "editorScript": "file:./assets/js/index.js", "editorStyle": ["file:./assets/css/toc.css", "file:./assets/css/toc_list_style.css"], "attributes": {"title": {"type": "string"}, "headings": {"type": "array", "items": {"type": "object"}}, "listStyle": {"type": "string"}, "titleWrapper": {"type": "string", "default": "h2"}, "excludeHeadings": {"type": "array"}}, "supports": {"color": {"link": true, "gradients": true}, "multiple": false, "spacing": {"margin": true, "padding": true}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalDefaultControls": {"fontSize": true}}, "align": true}}