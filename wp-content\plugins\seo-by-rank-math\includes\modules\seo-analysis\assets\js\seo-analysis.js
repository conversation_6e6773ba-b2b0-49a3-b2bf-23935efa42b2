!function(){"use strict";var e={n:function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,{a:n}),n},d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{updateResults:function(){return xe},updateUrl:function(){return Te}});var n={};e.r(n),e.d(n,{appUi:function(){return We}});var r={};e.r(r),e.d(r,{getResults:function(){return Ie},getUrl:function(){return Be}});var a=jQuery,o=e.n(a),l=wp.element;var i,s=React,u=ReactDOM;function c(){return c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},c.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(i||(i={}));const m="popstate";function p(e,t){if(!1===e||null==e)throw new Error(t)}function f(e,t){return{usr:e.state,key:e.key,idx:t}}function h(e,t,n,r){return void 0===n&&(n=null),c({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?y(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function d(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function y(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function b(e,t,n,r){void 0===r&&(r={});let{window:a=document.defaultView,v5Compat:o=!1}=r,l=a.history,s=i.Pop,u=null,y=b();function b(){return(l.state||{idx:null}).idx}function g(){s=i.Pop;let e=b(),t=null==e?null:e-y;y=e,u&&u({action:s,location:v.location,delta:t})}function w(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"==typeof e?e:d(e);return p(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==y&&(y=0,l.replaceState(c({},l.state,{idx:y}),""));let v={get action(){return s},get location(){return e(a,l)},listen(e){if(u)throw new Error("A history only accepts one active listener");return a.addEventListener(m,g),u=e,()=>{a.removeEventListener(m,g),u=null}},createHref(e){return t(a,e)},createURL:w,encodeLocation(e){let t=w(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){s=i.Push;let r=h(v.location,e,t);n&&n(r,e),y=b()+1;let c=f(r,y),m=v.createHref(r);try{l.pushState(c,"",m)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;a.location.assign(m)}o&&u&&u({action:s,location:v.location,delta:1})},replace:function(e,t){s=i.Replace;let r=h(v.location,e,t);n&&n(r,e),y=b();let a=f(r,y),c=v.createHref(r);l.replaceState(a,"",c),o&&u&&u({action:s,location:v.location,delta:0})},go(e){return l.go(e)}};return v}var g;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(g||(g={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function w(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function v(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function k(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function E(e,t,n,r){let a;void 0===r&&(r=!1),"string"==typeof e?a=y(e):(a=c({},e),p(!a.pathname||!a.pathname.includes("?"),v("?","pathname","search",a)),p(!a.pathname||!a.pathname.includes("#"),v("#","pathname","hash",a)),p(!a.search||!a.search.includes("#"),v("#","search","hash",a)));let o,l=""===e||""===a.pathname,i=l?"/":a.pathname;if(null==i)o=n;else if(r){let e=t[t.length-1].replace(/^\//,"").split("/");if(i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e.pop();a.pathname=t.join("/")}o="/"+e.join("/")}else{let e=t.length-1;if(i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let s=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"==typeof e?y(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:O(r),hash:_(a)}}(a,o),u=i&&"/"!==i&&i.endsWith("/"),m=(l||"."===i)&&n.endsWith("/");return s.pathname.endsWith("/")||!u&&!m||(s.pathname+="/"),s}const S=e=>e.join("/").replace(/\/\/+/g,"/"),O=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",_=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;const P=["post","put","patch","delete"],A=(new Set(P),["get",...P]);new Set(A),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function j(){return j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},j.apply(this,arguments)}const R=s.createContext(null);const C=s.createContext(null);const U=s.createContext(null);const N=s.createContext({outlet:null,matches:[],isDataRoute:!1});function x(){return null!=s.useContext(U)}function T(){return x()||p(!1),s.useContext(U).location}function M(e){s.useContext(C).static||s.useLayoutEffect(e)}function z(){let{isDataRoute:e}=s.useContext(N);return e?function(){let{router:e}=F(D.UseNavigateStable),t=W(L.UseNavigateStable),n=s.useRef(!1);return M((()=>{n.current=!0})),s.useCallback((function(r,a){void 0===a&&(a={}),n.current&&("number"==typeof r?e.navigate(r):e.navigate(r,j({fromRouteId:t},a)))}),[e,t])}():function(){x()||p(!1);let e=s.useContext(R),{basename:t,navigator:n}=s.useContext(C),{matches:r}=s.useContext(N),{pathname:a}=T(),o=JSON.stringify(k(r).map((e=>e.pathnameBase))),l=s.useRef(!1);return M((()=>{l.current=!0})),s.useCallback((function(r,i){if(void 0===i&&(i={}),!l.current)return;if("number"==typeof r)return void n.go(r);let s=E(r,JSON.parse(o),a,"path"===i.relative);null==e&&"/"!==t&&(s.pathname="/"===s.pathname?t:S([t,s.pathname])),(i.replace?n.replace:n.push)(s,i.state,i)}),[t,n,o,a,e])}()}s.Component;var D=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(D||{}),L=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(L||{});function F(e){let t=s.useContext(R);return t||p(!1),t}function W(e){let t=function(e){let t=s.useContext(N);return t||p(!1),t}(),n=t.matches[t.matches.length-1];return n.route.id||p(!1),n.route.id}s.startTransition;function I(e){let{basename:t="/",children:n=null,location:r,navigationType:a=i.Pop,navigator:o,static:l=!1}=e;x()&&p(!1);let u=t.replace(/^\/*/,"/"),c=s.useMemo((()=>({basename:u,navigator:o,static:l})),[u,o,l]);"string"==typeof r&&(r=y(r));let{pathname:m="/",search:f="",hash:h="",state:d=null,key:b="default"}=r,g=s.useMemo((()=>{let e=w(m,u);return null==e?null:{location:{pathname:e,search:f,hash:h,state:d,key:b},navigationType:a}}),[u,m,f,h,d,b,a]);return null==g?null:s.createElement(C.Provider,{value:c},s.createElement(U.Provider,{children:n,value:g}))}new Promise((()=>{}));s.Component;function B(e){return void 0===e&&(e=""),new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce(((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map((e=>[n,e])):[[n,r]])}),[]))}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);new Map;const H=s.startTransition;u.flushSync;function V(e){let{basename:t,children:n,future:r,window:a}=e,o=s.useRef();var l;null==o.current&&(o.current=(void 0===(l={window:a,v5Compat:!0})&&(l={}),b((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return h("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:d(t)}),null,l)));let i=o.current,[u,c]=s.useState({action:i.action,location:i.location}),{v7_startTransition:m}=r||{},p=s.useCallback((e=>{m&&H?H((()=>c(e))):c(e)}),[c,m]);return s.useLayoutEffect((()=>i.listen(p)),[i,p]),s.createElement(I,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:i})}"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;var K,$;function J(e){let t=s.useRef(B(e)),n=s.useRef(!1),r=T(),a=s.useMemo((()=>function(e,t){let n=B(e);return t&&t.forEach(((e,r)=>{n.has(r)||t.getAll(r).forEach((e=>{n.append(r,e)}))})),n}(r.search,n.current?null:t.current)),[r.search]),o=z(),l=s.useCallback(((e,t)=>{const r=B("function"==typeof e?e(a):e);n.current=!0,o("?"+r,t)}),[o,a]);return[a,l]}(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(K||(K={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}($||($={}));var q=lodash,Y=wp.i18n,G=wp.hooks,Q=window.rankMathComponents,Z=wp.data,X=wp.compose;function ee(e,t,n){return o().ajax({url:rankMath.ajaxurl,type:n||"POST",dataType:"json",data:o().extend(!0,{action:"rank_math_"+e,security:rankMath.security},t)})}function te(e){return te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},te(e)}var ne=["children","startAudit"];function re(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ae(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?re(Object(n),!0).forEach((function(t){oe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):re(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function oe(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==te(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==te(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===te(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function le(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],s=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);s=!0);}catch(e){u=!0,a=e}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ie(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ie(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ie(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function se(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}var ue=(0,l.createContext)(),ce=(0,X.compose)((0,Z.withSelect)((function(e){var t=e("rank-math-seo-analysis"),n=t.getResults,r=t.getUrl;return{results:n(),url:r()}})),(0,Z.withDispatch)((function(e){return{updateUrl:function(t){e("rank-math-seo-analysis").updateUrl(t)},updateResults:function(t){e("rank-math-seo-analysis").updateResults(t)},startAudit:function(t,n){t(!0),ee("analyze").always((function(r){r.error?n(r.error):e("rank-math-seo-analysis").updateResults(r),t(!1)}))}}})))((function(e){var t=e.children,n=e.startAudit,r=se(e,ne),a=le((0,l.useState)(!1),2),o=a[0],i=a[1],s=le((0,l.useState)(""),2),u=s[0],c=s[1];return wp.element.createElement(ue.Provider,{value:ae(ae({},r),{},{startProgress:o,setStartProgress:i,startAnalysis:function(){n(i,c)},analysisError:u})},t)})),me=function(){return(0,l.useContext)(ue)};function pe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],s=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);s=!0);}catch(e){u=!0,a=e}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return fe(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return fe(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fe(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var he=function(){var e=pe((0,l.useState)(!1),2),t=e[0],n=e[1],r=me(),a=r.url,o=r.updateUrl,i=r.startAnalysis;return wp.element.createElement("p",{className:"page-analysis-selected"},(0,Y.__)("Selected page: ","rank-math"),t?wp.element.createElement("form",{className:"changeurl-form",onSubmit:function(e){e.preventDefault(),n(!1),i()}},wp.element.createElement(Q.TextControl,{className:"rank-math-analyze-url",variant:"default",value:a,onChange:o})," ",wp.element.createElement(Q.Button,{type:"submit",variant:"secondary",className:"rank-math-changeurl-ok",disabled:!a},(0,Y.__)("OK","rank-math"))):wp.element.createElement(React.Fragment,null,wp.element.createElement("a",{href:a,target:"_blank",rel:"noreferrer",className:"rank-math-current-url"},a)," ",wp.element.createElement(Q.Button,{variant:"secondary",className:"rank-math-changeurl",onClick:function(){n(!0)}},(0,Y.__)("Change URL","rank-math"))))},de=function(){var e=me(),t=e.results,n=e.startProgress,r=e.startAnalysis,a=e.analysisError,o=rankMath.analyzeSubpage;return(0,q.isEmpty)(t)?wp.element.createElement(React.Fragment,null,a&&wp.element.createElement(l.RawHTML,null,a),wp.element.createElement("div",{className:"rank-math-seo-analysis-header"},n?wp.element.createElement(React.Fragment,null,o?wp.element.createElement("h2",null,(0,Y.__)("Analysing Page…","rank-math")):wp.element.createElement("h2",null,(0,Y.__)("Analysing Website…","rank-math")),wp.element.createElement(Q.ProgressBar,null)):wp.element.createElement(React.Fragment,null,o&&wp.element.createElement(he,null),wp.element.createElement(Q.Button,{variant:"primary",size:"xlarge",className:"rank-math-recheck",onClick:r},o?(0,Y.__)("Start Page Analysis","rank-math"):(0,Y.__)("Start SEO Analyzer","rank-math"))))):wp.element.createElement(Q.AnalyzerResult,{results:t})},ye=wp.components;function be(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=rankMath.links[e]||"";if(!n)return"#";if(!t)return n;var r={utm_source:"Plugin",utm_medium:encodeURIComponent(t),utm_campaign:"WP"};return n+"?"+Object.keys(r).map((function(e){return"".concat(e,"=").concat(r[e])})).join("&")}var ge=function(){var e=me(),t=e.results,n=e.updateResults,r=e.startAnalysis;if(!(0,q.isEmpty)(t))return wp.element.createElement("div",{className:"analyzer-result-actions"},wp.element.createElement(Q.Button,{variant:"link",className:"rank-math-recheck",onClick:function(){n([]),r()}},(0,Y.__)("Restart SEO Analyzer","rank-math"),wp.element.createElement(ye.Dashicon,{icon:"update"})),wp.element.createElement("div",{className:"analyzer-results-header"},(0,G.applyFilters)("rank_math_seo_analysis_print_result",wp.element.createElement("div",{id:"print-results"},wp.element.createElement(Q.Button,{href:be("pro","SEO Analyzer Print Button"),variant:"secondary",className:"rank-math-print-results is-inactive",target:"_blank"},wp.element.createElement("span",{className:"dashicons dashicons-printer"}),(0,Y.__)("Print","rank-math"),wp.element.createElement("span",{className:"rank-math-pro-badge"},"PRO")))),wp.element.createElement(Q.Button,{variant:"primary",href:"#analysis-result",className:"rank-math-view-issues"},(0,Y.__)("View Issues","rank-math"))))},we=function(){var e=rankMath,t=e.connectUrl,n=e.isSiteConnected;return wp.element.createElement(React.Fragment,null,wp.element.createElement("header",{className:"rank-math-box"},wp.element.createElement("h2",null,wp.element.createElement("span",{className:"title-prefix"},(0,Y.__)("SEO Analysis for","rank-math")),wp.element.createElement("span",null,window.location.hostname)),n&&wp.element.createElement(ge,null)),wp.element.createElement("div",{className:"rank-math-box rank-math-analyzer-result"},n?wp.element.createElement(de,null):wp.element.createElement("div",{className:"rank-math-seo-analysis-header"},wp.element.createElement("h3",null,(0,Y.__)("Analyze your site by ","rank-math"),wp.element.createElement("a",{href:t,target:"_blank",rel:"noreferrer"},(0,Y.__)("linking your Rank Math account","rank-math"))))))},ve=function(){var e={metrices:{percent:85,total:29,statuses:{ok:20,warning:2,fail:7}},date:{date:"October 17, 2024",time:"8:01 am"},serpData:{favicon:"https://t0.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=https%3A%2F%2Fwww.rankmath.com%2F&size=128",url:"https://www.rankmath.com/",title:"Rank Math - Best Free WordPress SEO Tools in 2025",description:"Rank Math WordPress SEO plugin will help you rank higher in search engines. DOWNLOAD for FREE this plugin today to optimize WordPress website for higher ra..."},results:{basic:[{status:"info",test_id:"common_keywords",title:"Common Keywords",tooltip:"A list of keywords that appear frequently in the text of the content.",kb_link:"https://rankmath.com/kb/seo-analysis/#common-keywords-test",message:"Here are the most common keywords we found on the page:",data:{rank:31,math:29,wordpress:15,plugin:10,best:9,search:8,google:7,support:7,features:6,free:6}},{status:"fail",test_id:"description_length",title:"SEO Description",tooltip:"SEO analysis of page's meta-description.",kb_link:"https://rankmath.com/kb/seo-analysis/#seo-description-test",message:"The description of page has 184 characters. Most search engines will truncate meta description to 160 characters.",fix:"<p>Write a meta-description for page. Use target keyword(s) (in a natural way) and write with human readers in mind. Summarize the content - describe the topics article discusses.</p><p>The description should stimulate reader interest and get them to click on the article. Think of it as a mini-advert for content.</p>",data:["Rank Math WordPress SEO plugin will help you rank higher in search engines. DOWNLOAD for FREE this plugin today to optimize WordPress website for higher rankings and more traffic."]},{status:"ok",test_id:"h1_heading",title:"H1 Heading",tooltip:"SEO Analysis of the H1 Tags on the page.",kb_link:"https://rankmath.com/kb/seo-analysis/#h1-heading-test",message:"One H1 tag was found on the page.",data:["WordPress SEO Made Easy"]},{status:"ok",test_id:"h2_headings",title:"H2 Headings",tooltip:"SEO analysis of the H2 headings on your page.",kb_link:"https://rankmath.com/kb/seo-analysis/#h2-headings-test",message:"One or more H2 tags were found on the page.",data:["Powering SEO optimization for businesses around the world","What is Rank Math?","Recommended By the Best SEOs On The Planet","What you can do with Rank Math","Take The Guesswork Out of WordPress SEO","Your all-in-one solution for all the SEO needs","Leading SEOs are Loving Rank Math!"]},{status:"ok",test_id:"img_alt",title:"Image ALT Attributes",tooltip:'SEO analysis of the "alt" attribute for image tags.',kb_link:"https://rankmath.com/kb/seo-analysis/#image-alt-attributes-test",message:"All images on the page have alt attributes."},{status:"ok",test_id:"keywords_meta",title:"Keywords in Title & Description",tooltip:"SEO analysis of the HTML page's Title and meta description content.",kb_link:"https://rankmath.com/kb/seo-analysis/#keywords-in-title-and-description-test",message:"One or more common keywords were found in the title and description of the page.",data:{title:["rank","math","wordpress","best","free"],description:["rank","math","wordpress","plugin","search","free"]}}]}};return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"rank-math-box blurred"},wp.element.createElement("h2",null,(0,Y.__)("Competitor Analyzer","rank-math")),wp.element.createElement("p",null,(0,Y.__)("Enter a site URL to see how it ranks for the same SEO criteria as site.","rank-math")),wp.element.createElement("div",{className:"url-form"},wp.element.createElement("input",{type:"text",name:"competitor_url",id:"competitor_url",placeholder:"https://rankmath.com",disabled:!0}),wp.element.createElement(Q.Button,{variant:"primary",id:"competitor_url_submit",disabled:!0},(0,Y.__)("Start SEO Analyzer","rank-math")))),wp.element.createElement("div",{className:"rank-math-box rank-math-analyzer-result blurred"},wp.element.createElement("span",{className:"wp-header-end"}),!(0,q.isEmpty)(e)&&wp.element.createElement(Q.AnalyzerResult,{results:e})),wp.element.createElement("div",{id:"rank-math-pro-cta",className:"center"},wp.element.createElement("div",{className:"rank-math-cta-box blue-ticks width-50 top-20 less-padding"},wp.element.createElement("h3",null,(0,Y.__)("Competitor Analyzer","rank-math")),wp.element.createElement("ul",null,wp.element.createElement("li",null,(0,Y.__)("Analyze competitor websites to gain an edge","rank-math")),wp.element.createElement("li",null,(0,Y.__)("Evaluate strengths and weaknesses","rank-math")),wp.element.createElement("li",null,(0,Y.__)("Explore new keywords and opportunities","rank-math")),wp.element.createElement("li",null,(0,Y.__)("Make more informed decisions & strategy","rank-math"))),wp.element.createElement(Q.Button,{variant:"green",href:be("pro","Competitor Analyzer Tab"),target:"_blank",rel:"noreferrer"},(0,Y.__)("Upgrade","rank-math")))))};function ke(e){return ke="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ke(e)}function Ee(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ke(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ke(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ke(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Se(e){return function(e){if(Array.isArray(e))return Pe(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||_e(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Oe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],s=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);s=!0);}catch(e){u=!0,a=e}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw a}}return i}}(e,t)||_e(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _e(e,t){if(e){if("string"==typeof e)return Pe(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Pe(e,t):void 0}}function Pe(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ae={name:"seo_analyzer",title:(0,Y.__)("SEO Analyzer","rank-math")},je={name:"competitor_analyzer",title:(0,Y.__)("Competitor Analyzer","rank-math")},Re=function(){return(0,G.applyFilters)("rank_math_analyzer_tabs",[{name:Ae.name,title:wp.element.createElement(React.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-analyzer"}),Ae.title),view:we},{name:je.name,title:wp.element.createElement(React.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-users"}),je.title),view:ve}])},Ce=function(){var e=Oe(J(),2),t=e[0],n=e[1],r=t.get("view")||Re()[0].name,a=function(e){return n((function(t){return(0,q.fromPairs)([].concat(Se(t),[["view",e]]))}))},o=Ee(Ee(Ee({},Ae.name,Ae.title),je.name,je.title),"side_by_side",je.title);return wp.element.createElement(React.Fragment,null,wp.element.createElement(Q.Breadcrumbs,{activePage:o[r]}),wp.element.createElement(Q.TabPanel,{tabs:Re(),key:r,initialTabName:r,onSelect:function(e){return a(e)}},(function(e){var t=e.name,n=e.view;return wp.element.createElement("div",{className:"rank-math-ui seo-analysis ".concat(t)},wp.element.createElement(n,{onTabSelect:a}))})))},Ue=function(){return wp.element.createElement(V,null,wp.element.createElement(ce,null,wp.element.createElement(Ce,null)))};function Ne(e,t){return{type:"RANK_MATH_APP_UI",key:e,value:t}}function xe(e){return Ne("results",e)}function Te(e){return Ne("url",e)}function Me(e){return Me="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Me(e)}function ze(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function De(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ze(Object(n),!0).forEach((function(t){Le(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ze(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Le(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Me(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Me(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Me(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Fe={results:rankMath.results,url:rankMath.analyzeUrl};function We(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Fe,t=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===t.type?De(De({},e),{},Le({},t.key,t.value)):e}function Ie(e){return e.appUi.results}function Be(e){return e.appUi.url}var He;(0,Z.register)((0,Z.createReduxStore)("rank-math-seo-analysis",{reducer:(0,Z.combineReducers)(n),selectors:r,actions:t}));He=function(){(0,l.createRoot)(document.querySelector(".rank-math-wrap")).render(wp.element.createElement(Ue,null)),o()(document).on("click",".enable-auto-update",(function(e){e.preventDefault(),ee("enable_auto_update"),o()(this).closest(".auto-update-disabled").addClass("hidden").siblings(".auto-update-enabled").removeClass("hidden").closest(".row-description").find(".status-icon").removeClass("status-warning dashicons-warning").addClass("status-ok dashicons-yes")}))},"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",He):He())}();