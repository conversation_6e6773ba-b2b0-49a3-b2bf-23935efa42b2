"use strict";(self.webpackChunkrank_math=self.webpackChunkrank_math||[]).push([[43],{829:function(e,t,n){n.d(t,{Z:function(){return o}});var r=n(311),a=n.n(r);function o(e,t,n,r){t=t||"error",r=r||!1;var o=a()('<div class="notice notice-'+t+' is-dismissible"><p>'+e+"</p></div>").hide();n.next(".notice").remove(),n.after(o),o.slideDown(),a()(document).trigger("wp-updates-notice-added"),a()("html,body").animate({scrollTop:o.offset().top-50},"slow"),r&&setTimeout((function(){o.fadeOut((function(){o.remove()}))}),r)}},766:function(e,t,n){n.r(t),n.d(t,{default:function(){return w}});var r=n(85),a=n(3),o=n(610),s=n(142),i=n(311),l=n.n(i),c=n(829),u=n(180),f=n(69);function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}var d=rankMath.batchSize,p=0,h=function(e,t,n,o){0===p&&(0,u.Z)((0,a.__)("Starting SEO score recalculation","rank-math"),t,n);var s=(0,r.isUndefined)(o.offset)?0:o.offset,i=p+1,l=(0,r.keys)(e).length+s,c=!(0,r.isUndefined)(o.update_all_scores)&&o.update_all_scores?rankMath.totalPosts:rankMath.totalPostsWithoutScore;(0,f.Z)(e,t,n,(function(){(p+=d)>=c?(0,u.Z)((0,a.__)("The SEO Scores have been recalculated successfully!","rank-math"),t,n):(o.offset=p,y("update_seo_score",t,n,o))}),i,l,c)},y=function(e,t,n,r){var o=l()(".wp-header-end");l().ajax({url:rankMath.api.root+"rankmath/v1/toolsAction",method:"POST",beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",rankMath.restNonce)},data:{action:e,args:r}}).fail((function(e){e&&(e.responseJSON&&e.responseJSON.message?(0,c.Z)(e.responseJSON.message,"error",o):(0,c.Z)(e.statusText,"error",o))})).done((function(s){if(s){if("update_seo_score"===e)return void h(s,t,n,r);if("string"==typeof s)return void(0,c.Z)(s,"success",o,!1);if("object"===m(s)&&s.status&&s.message)return void(0,c.Z)(s.message,s.status,o,!1)}(0,c.Z)((0,a.__)("Something went wrong. Please try again later.","rank-math"),"error",o)}))};function b(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,s,i=[],l=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);l=!0);}catch(e){c=!0,a=e}finally{try{if(!l&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return g(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return g(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var w=function(e){var t=e.data.tools,n=b((0,o.useState)([]),2),i=n[0],l=n[1],c=b((0,o.useState)(!0),2),u=c[0],f=c[1];return wp.element.createElement("table",{className:"rank-math-status-table striped rank-math-tools-table widefat rank-math-box"},wp.element.createElement("tbody",{className:"tools"},(0,r.map)(t,(function(e,t){var n=e.title,r=e.description,o=e.button_text,c=e.confirm_text;return wp.element.createElement("tr",{key:t,className:t},wp.element.createElement("th",null,wp.element.createElement("h4",{className:"name"},n),wp.element.createElement("p",{className:"description"},r)),wp.element.createElement("td",{className:"run-tool"},wp.element.createElement(s.Button,{isDestructive:!0,size:"large",className:"tools-action",onClick:function(){if(c&&!confirm(c))return!1;y(t,i,l,u?{update_all_scores:!0}:{})}},o),"update_seo_score"===t&&rankMath.totalPostsWithoutScore>0&&wp.element.createElement("div",{className:"update_all_scores"},wp.element.createElement(s.CheckboxControl,{__nextHasNoMarginBottom:!0,label:(0,a.__)("Include posts/pages where the score is already set","rank-math"),checked:u,onChange:f}))))})),0!==i.length&&wp.element.createElement("tr",{key:"update-score-logger"},wp.element.createElement("td",{colSpan:2},wp.element.createElement(s.TextareaControl,{disable:"true",value:i.join("\n"),className:"import-progress-area large-text",rows:"8",style:{marginRight:"20px",background:"#eee"}})))))}},180:function(e,t){function n(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return r(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}t.Z=function(e,t,r){var a=new Date,o="["+(10>a.getHours()?"0":"")+a.getHours()+":"+(10>a.getMinutes()?"0":"")+a.getMinutes()+":"+(10>a.getSeconds()?"0":"")+a.getSeconds()+"] "+e;t.push(o),r(n(t))}},69:function(e,t,n){var r=n(311),a=n.n(r),o=n(85),s=n(3),i=n(813),l=n(180),c=[];t.Z=function(e,t,n,r,u,f,m){var d={};if("complete"!==e)return new Promise((function(t){(0,o.forEach)(e,(function(e,t){if(-1===c.indexOf(t)){c.push(t);var n=new i.ResultManager,r=wp.i18n,a=new i.Paper;a.setTitle(e.title),a.setDescription(e.description),a.setText(e.content),a.setKeyword(e.keyword),a.setKeywords(e.keywords),a.setPermalink(e.url),a.setUrl(e.url),e.thumbnail&&a.setThumbnail(e.thumbnail),a.setContentAI(e.hasContentAi);var s=function(e){var t=rankMath.assessor.researchesTests;return t=(0,o.difference)(t,["keywordNotUsed"]),e.isProduct?t=(0,o.difference)(t,["keywordInSubheadings","linksHasExternals","linksNotAllExternals","linksHasInternal","titleSentiment","titleHasNumber","contentHasTOC"]):t}(e);new i.Analyzer({i18n:r,analysis:s}).analyzeSome(s,a).then((function(r){n.update(a.getKeyword(),r,!0);var o=n.getScore(e.keyword);e.isProduct&&(o=e.isReviewEnabled?o+1:o,o=e.hasProductSchema?o+1:o),d[t]=o}))}})),t()})).then((function(){a().ajax({url:rankMath.api.root+"rankmath/v1/updateSeoScore",method:"POST",beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",rankMath.restNonce)},data:{action:"rank_math_update_seo_score",postScores:d},success:function(){(0,l.Z)((0,s.sprintf)((0,s.__)("Calculating SEO score for posts %1$d - %2$d out of %3$d","rank-math"),u,f,m),t,n),r()},error:function(e){(0,l.Z)(e.statusText,t,n)}})}));r()}}}]);