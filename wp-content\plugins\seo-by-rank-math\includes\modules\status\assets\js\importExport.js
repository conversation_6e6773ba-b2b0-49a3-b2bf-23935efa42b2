"use strict";(self.webpackChunkrank_math=self.webpackChunkrank_math||[]).push([[147],{829:function(e,t,n){n.d(t,{Z:function(){return o}});var r=n(311),a=n.n(r);function o(e,t,n,r){t=t||"error",r=r||!1;var o=a()('<div class="notice notice-'+t+' is-dismissible"><p>'+e+"</p></div>").hide();n.next(".notice").remove(),n.after(o),o.slideDown(),a()(document).trigger("wp-updates-notice-added"),a()("html,body").animate({scrollTop:o.offset().top-50},"slow"),r&&setTimeout((function(){o.fadeOut((function(){o.remove()}))}),r)}},698:function(e,t,n){n.r(t),n.d(t,{default:function(){return P}});var r=n(882),a=n(3),o=n(537);function l(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=rankMath.links[e]||"";if(!n)return"#";if(!t)return n;var r={utm_source:"Plugin",utm_medium:encodeURIComponent(t),utm_campaign:"WP"};return n+"?"+Object.keys(r).map((function(e){return"".concat(e,"=").concat(r[e])})).join("&")}var i=n(311),s=n.n(i),c=n(179),m=n.n(c),u=n(610),p=n(142),f=n(829);function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],s=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);s=!0);}catch(e){c=!0,a=e}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(c)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var g=n(85);function w(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],s=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);s=!0);}catch(e){c=!0,a=e}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(c)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return k(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return k(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function k(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var y={general:(0,a.__)("General Settings","rank-math"),titles:(0,a.__)("Titles & Metas","rank-math"),sitemap:(0,a.__)("Sitemap Settings","rank-math"),"role-manager":(0,a.__)("Role Manager Settings","rank-math"),redirections:(0,a.__)("Redirections","rank-math")},b=[{name:"rank-math-import-form",title:wp.element.createElement(React.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-import"}),wp.element.createElement("span",{className:"rank-math-tab-text"},(0,a.__)("Import Settings","rank-math"))),view:function(){var e=h((0,u.useState)(!1),2),t=e[0],n=e[1],r=new FormData;return r.append("import-me",t),wp.element.createElement("div",{id:"rank-math-import-form",className:"rank-math-export-form field-form"},wp.element.createElement("div",null,wp.element.createElement("label",{htmlFor:"import-me"},wp.element.createElement("strong",null,(0,a.__)("Settings File","rank-math")))),wp.element.createElement("div",null,wp.element.createElement(o.FormFileUpload,{__next40pxDefaultSize:!0,accept:".json",onChange:function(e){return n(e.currentTarget.files[0])}},wp.element.createElement("span",{className:"import-file-button"},(0,a.__)("Choose File","rank-math")),t&&wp.element.createElement("span",null,t.name)),wp.element.createElement("br",null),wp.element.createElement("span",{className:"validation-message"},(0,a.__)("Please select a file to import.","rank-math"))),wp.element.createElement("div",{className:"description"},(0,a.__)('Import settings by locating settings file and clicking "Import settings".',"rank-math")),wp.element.createElement("footer",null,wp.element.createElement(p.Button,{variant:"primary",disabled:!1===t,onClick:function(){confirm((0,a.__)("Are you sure you want to import settings into Rank Math? Don't worry, your current configuration will be saved as a backup.","rank-math"))&&m()({method:"POST",headers:{},path:"/rankmath/v1/status/importSettings",body:r}).catch((function(e){alert(e.message)})).then((function(e){var t=s()(".rank-math-breadcrumbs-wrap");e.error?(0,f.Z)(e.error,"error",t):(0,f.Z)(e.success,"success",t),n(!1)}))}},(0,a.__)("Import","rank-math"))))}},{name:"rank-math-export-form",title:wp.element.createElement(React.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-export"}),wp.element.createElement("span",{className:"rank-math-tab-text"},(0,a.__)("Export Settings","rank-math"))),view:function(){var e=w((0,u.useState)(Object.keys(y)),2),t=e[0],n=e[1],r=(0,g.map)((0,g.entries)(y),(function(e){var t=w(e,2);return{id:t[0],label:t[1]}}));return wp.element.createElement("div",{id:"rank-math-export-form",className:"rank-math-export-form field-form"},wp.element.createElement(p.CheckboxList,{variant:"default",value:t,onChange:n,options:r}),wp.element.createElement("p",{className:"description"},(0,a.__)("Choose the panels to export.","rank-math")),wp.element.createElement("footer",null,wp.element.createElement(p.Button,{variant:"primary",disabled:(0,g.isEmpty)(t),onClick:function(){m()({method:"POST",headers:{},path:"/rankmath/v1/status/exportSettings",data:{panels:t}}).catch((function(e){alert(e.message)})).then((function(e){var t=new Blob([e],{type:"application/json"}),n=URL.createObjectURL(t),r=document.createElement("a");r.href=n,r.download="rank-math-settings-".concat((new Date).toISOString().replace(/[:.]/g,"-"),".json"),document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(n)}))}},(0,a.__)("Export","rank-math"))))}}],v=function(){return wp.element.createElement("div",{className:"import-export-settings"},wp.element.createElement("h2",null,(0,a.__)("Plugin Settings","rank-math")),wp.element.createElement("p",{className:"description"},(0,a.__)("Import or export your Rank Math settings. This option is useful for replicating Rank Math settings across multiple websites. ","rank-math"),wp.element.createElement("a",{href:l("import-export-settings","Options Panel Import Export Page"),target:"_blank",rel:"noreferrer"},(0,a.__)("Learn more about the Import/Export options.","rank-math"))),wp.element.createElement("div",{className:"rank-math-box no-padding"},wp.element.createElement(o.TabPanel,{tabs:b},(function(e){var t=e.view;return wp.element.createElement("div",{className:"rank-math-box-content"},wp.element.createElement(t,null))}))))};function _(e,t,n){return s().ajax({url:rankMath.ajaxurl,type:n||"POST",dataType:"json",data:s().extend(!0,{action:"rank_math_"+e,security:rankMath.security},t)})}var E=n(69),S=n(180),x=function e(t,n,r,o,l,i){if(0===n.length){var s=(0,a.__)("Import finished.","rank-math");return(0,S.Z)(s,r,o),void i()}var c=n.shift(),m="deactivate"===c?"Deactivating plugin":"Importing "+c;l=l||1,"recalculate"===c&&(m=(0,a.__)("Starting SEO score recalculation","rank-math")),(0,S.Z)(m,r,o),_("import_plugin",{perform:c,pluginSlug:t,paged:l}).done((function(s){l=1,s&&s.page&&s.page<s.total_pages&&(l=s.page+1,n.unshift(c)),"recalculate"===c&&s.total_items>0?(0,E.Z)(s.data,r,o,(function(){e(t,n,r,l,i)})):("recalculate"===c&&0===s.total_items&&(s.message=(0,a.__)("No posts found with SEO score.","rank-math")),(0,S.Z)(s.success?s.message:s.error,r,o),e(t,n,r,o,l,i))})).fail((function(a){(0,S.Z)(a.statusText,r,o),e(t,n,r,o,null,i)}))};function O(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],s=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);s=!0);}catch(e){c=!0,a=e}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(c)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return N(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return N(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var j=function(e){var t=e.slug,n=e.choices,r=e.pluginName,o=e.importablePlugins,l=e.updateViewData;"redirections"!==t&&(n.recalculate=(0,a.__)("Calculate SEO Scores","rank-math"));var i=O((0,u.useState)((0,g.keys)(n)),2),c=i[0],m=i[1],h=O((0,u.useState)(!1),2),d=h[0],w=h[1],k=O((0,u.useState)([]),2),y=k[0],b=k[1],v=s()(".wp-header-end");return wp.element.createElement("div",{className:"rank-math-box-content"},wp.element.createElement(p.CheckboxList,{variant:"default",value:c,onChange:m,options:(0,g.map)(n,(function(e,t){return{id:t,label:e}}))}),0!==y.length&&wp.element.createElement(p.TextareaControl,{disable:"true",value:y.join("\n"),className:"import-progress-area large-text",rows:"8",style:{marginRight:"20px",background:"#eee"}}),wp.element.createElement("footer",null,wp.element.createElement(p.Button,{variant:"primary",onClick:function(){if(confirm((0,a.sprintf)((0,a.__)("Are you sure you want to import data from %s?","rank-math"),r)))if(c.length<1)(0,f.Z)((0,a.__)("Select data to import.","rank-math"),"error",v,5e3);else{w(!0);var e=c;e.push("deactivate"),(0,S.Z)("Import started...",y,b),x(t,e,y,b,null,(function(){w(!0),setTimeout((function(){b([])}),1e4)}))}},disabled:d},(0,a.__)("Import","rank-math")),wp.element.createElement(p.Button,{isDestructive:!0,onClick:function(){confirm((0,a.sprintf)((0,a.__)("Are you sure you want erase all traces of %s?","rank-math"),r))&&_("clean_plugin",{pluginSlug:t}).done((function(e){if(e.success){var n=o;delete n[t],l({importablePlugins:n})}(0,f.Z)(e.success?e.message:e.error,e.success?"success":"error",v,5e3)}))}},(0,a.__)("Clean","rank-math"))))};function A(){return A=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},A.apply(this,arguments)}var C=function(e){var t=e.data,n=e.updateViewData,r=t.importablePlugins,i=(0,g.map)(r,(function(e,t){return{slug:t,name:"import-plugin-".concat(t),choices:e.choices,pluginName:e.name,title:wp.element.createElement(React.Fragment,null,wp.element.createElement("i",{className:"rm-icon rm-icon-import"}),wp.element.createElement("span",null,e.name))}}));return wp.element.createElement("div",{className:"import-plugins"},wp.element.createElement("h2",null,(0,a.__)("Other Plugins","rank-math")),wp.element.createElement("p",{className:"description"},(0,a.__)("If you were using another plugin to add important SEO information to your website before switching to Rank Math SEO, you can import the settings and data here. ","rank-math"),wp.element.createElement("a",{href:l("import-export-settings","Options Panel Import Export Page Other Plugins"),target:"_blank",rel:"noreferrer"},(0,a.__)("Learn more about the Import/Export options.","rank-math"))),wp.element.createElement("div",{className:"rank-math-box no-padding rank-math-export-form field-form"},wp.element.createElement("div",{className:"with-action at-top"},(0,g.isEmpty)(r)?wp.element.createElement("p",{className:"empty-notice"},(0,a.__)("No plugin detected with importable data.","rank-math")):wp.element.createElement(o.TabPanel,{tabs:i},(function(e){return wp.element.createElement(j,A({key:e.slug},e,{importablePlugins:r,updateViewData:n}))})))))},I=function(e){var t=e.data,n=e.updateViewData,r=t.backups,o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;m()({method:"POST",path:"/rankmath/v1/status/runBackup",data:{action:e,key:t}}).catch((function(e){alert(e.message)})).then((function(e){var t=s()(".wp-header-end");(0,f.Z)(e.message,e.type,t),!1!==e.backups&&n({backups:e.backups})}))};return wp.element.createElement("div",{className:"settings-backup"},wp.element.createElement(p.Button,{variant:"primary",className:"alignright",onClick:function(){return o("create")}},(0,a.__)("Create Backup","rank-math")),wp.element.createElement("h3",null,(0,a.__)("Settings Backup","rank-math")),wp.element.createElement("p",{className:"description"},(0,a.__)("Take a backup of your plugin settings in case you wish to restore them in future. Use it as backup before making substantial changes to Rank Math settings. For taking a backup of the SEO data of your content, use the XML Export option.","rank-math")),wp.element.createElement("div",{className:"rank-math-settings-backup-form field-form"},wp.element.createElement("div",{className:"list-table with-action"},wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,!(0,g.isEmpty)(r)&&(0,g.map)(r,(function(e,t){return wp.element.createElement("tr",{key:t},wp.element.createElement("th",null,(0,a.sprintf)((0,a.__)("Backup: %s","rank-math"),e)),wp.element.createElement("td",null,wp.element.createElement(p.Button,{size:"small",variant:"secondary",onClick:function(){confirm((0,a.__)("Are you sure you want to restore this backup? Your current configuration will be overwritten.","rank-math"))&&o("restore",t)}},(0,a.__)("Restore","rank-math")),wp.element.createElement(p.Button,{size:"small",isDestructive:!0,onClick:function(){confirm((0,a.__)("Are you sure you want to delete this backup?","rank-math"))&&o("delete",t)}},(0,a.__)("Delete","rank-math"))))}))))),(0,g.isEmpty)(r)&&wp.element.createElement("p",{id:"rank-math-no-backup-message"},(0,a.__)("There is no backup.","rank-math"))))},P=function(e){return wp.element.createElement("div",{className:"rank-math-import-export"},wp.element.createElement(v,null),(0,r.applyFilters)("rank_math_status_import_export_tabs","",e),wp.element.createElement(C,e),wp.element.createElement(I,e))}},180:function(e,t){function n(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return r(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}t.Z=function(e,t,r){var a=new Date,o="["+(10>a.getHours()?"0":"")+a.getHours()+":"+(10>a.getMinutes()?"0":"")+a.getMinutes()+":"+(10>a.getSeconds()?"0":"")+a.getSeconds()+"] "+e;t.push(o),r(n(t))}},69:function(e,t,n){var r=n(311),a=n.n(r),o=n(85),l=n(3),i=n(813),s=n(180),c=[];t.Z=function(e,t,n,r,m,u,p){var f={};if("complete"!==e)return new Promise((function(t){(0,o.forEach)(e,(function(e,t){if(-1===c.indexOf(t)){c.push(t);var n=new i.ResultManager,r=wp.i18n,a=new i.Paper;a.setTitle(e.title),a.setDescription(e.description),a.setText(e.content),a.setKeyword(e.keyword),a.setKeywords(e.keywords),a.setPermalink(e.url),a.setUrl(e.url),e.thumbnail&&a.setThumbnail(e.thumbnail),a.setContentAI(e.hasContentAi);var l=function(e){var t=rankMath.assessor.researchesTests;return t=(0,o.difference)(t,["keywordNotUsed"]),e.isProduct?t=(0,o.difference)(t,["keywordInSubheadings","linksHasExternals","linksNotAllExternals","linksHasInternal","titleSentiment","titleHasNumber","contentHasTOC"]):t}(e);new i.Analyzer({i18n:r,analysis:l}).analyzeSome(l,a).then((function(r){n.update(a.getKeyword(),r,!0);var o=n.getScore(e.keyword);e.isProduct&&(o=e.isReviewEnabled?o+1:o,o=e.hasProductSchema?o+1:o),f[t]=o}))}})),t()})).then((function(){a().ajax({url:rankMath.api.root+"rankmath/v1/updateSeoScore",method:"POST",beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",rankMath.restNonce)},data:{action:"rank_math_update_seo_score",postScores:f},success:function(){(0,s.Z)((0,l.sprintf)((0,l.__)("Calculating SEO score for posts %1$d - %2$d out of %3$d","rank-math"),m,u,p),t,n),r()},error:function(e){(0,s.Z)(e.statusText,t,n)}})}));r()}}}]);