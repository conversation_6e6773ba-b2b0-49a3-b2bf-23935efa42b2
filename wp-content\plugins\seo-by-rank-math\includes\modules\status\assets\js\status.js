!function(){"use strict";var e,t,n={674:function(e,t,n){n.r(t),n.d(t,{default:function(){return O}});var r=n(3),a=n(142),o=n(610),i=n(179),l=n.n(i);function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){m(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==u(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===u(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],u=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){c=!0,a=e}finally{try{if(!u&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(c)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return f(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var h=function(e){var t=p((0,o.useState)((0,r.__)("Save Changes","rank-math")),2),n=t[0],i=t[1],u=p((0,o.useState)(!1),2),c=u[0],m=u[1];return wp.element.createElement("footer",null,wp.element.createElement(a.Button,{type:"submit",variant:"primary",size:"xlarge",disabled:c,onClick:function(){i((0,r.__)("Saving…","rank-math")),m(!0),l()({method:"POST",path:"/rankmath/v1/status/updateViewData",data:s({},e)}).catch((function(e){console.error(e.message),i((0,r.__)("Failed! Try again","rank-math"))})).then((function(e){i(e?(0,r.__)("Saved","rank-math"):(0,r.__)("Failed! Try again","rank-math"))})).finally((function(){setTimeout((function(){m(!1),i((0,r.__)("Save Changes","rank-math"))}),1e3)}))}},n))},d=function(e){var t=e.title,n=e.description,a=e.warning,o=void 0===a?"":a;return wp.element.createElement(React.Fragment,null,wp.element.createElement("header",null,wp.element.createElement("h3",null,t)),wp.element.createElement("p",null,n),o&&wp.element.createElement("p",{className:"description warning"},wp.element.createElement("strong",null,wp.element.createElement("span",{className:"warning"},(0,r.__)("Warning: ","rank-math")),o)))};function b(){return b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},b.apply(this,arguments)}var y=function(e){var t=e.data,n=e.updateViewData,o=t.autoUpdate,i=t.updateNotificationEmail,l=t.isPluginUpdateDisabled,u=t.rollbackVersion;return wp.element.createElement("div",{className:"rank-math-auto-update-form field-form rank-math-box"},wp.element.createElement(d,{title:(0,r.__)("Auto Update","rank-math"),description:l?(0,r.__)("You cannot turn on auto-updates to automatically update to stable versions of Rank Math as soon as they are released, because site wide plugins auto-update option is disabled on your site.","rank-math"):(0,r.__)("Turn on auto-updates to automatically update to stable versions of Rank Math as soon as they are released. The beta versions of Rank Math as soon as they are released. The beta versions will never install automatically.","rank-math")}),!l&&wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",{className:"field-row field-type-switch"},wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"enable_auto_update"},(0,r.__)("Auto Update Plugin","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(a.ToggleControl,{id:"enable_auto_update",checked:o,onChange:function(e){t.autoUpdate=e,n(t)}}))))),wp.element.createElement("div",{id:"control_update_notification_email"},wp.element.createElement("p",null,(0,r.__)("When auto-updates are turned off, you can enable update notifications, to send an email to the site administrator when an update is available for Rank Math.","rank-math")),wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",{className:"field-row field-type-switch"},wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"enable_update_notification_email"},(0,r.__)("Update Notification Email","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(a.ToggleControl,{id:"enable_update_notification_email",checked:i,onChange:function(e){t.updateNotificationEmail=e,n(t)}})))))),!l&&u&&wp.element.createElement(a.Notice,{variant:"alt",status:"warning"},wp.element.createElement("p",null,(0,r.__)("Rank Math will not auto-update because you have rolled back to a previous version. Update to the latest version manually to make this option work again.","rank-math"))),wp.element.createElement(h,b({panel:"auto_update"},t)))};function w(){return w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},w.apply(this,arguments)}var v=function(e){var t=e.data,n=e.updateViewData,o=t.betaOptin,i=t.isPluginUpdateDisabled;return wp.element.createElement("div",{className:"rank-math-beta-optin-form field-form rank-math-box"},wp.element.createElement(d,{title:(0,r.__)("Beta Opt-in","rank-math"),description:i?(0,r.__)("You cannot turn on the Beta Tester feature because site wide plugins auto-update option is disabled on your site.","rank-math"):(0,r.__)("You can take part in shaping Rank Math by test-driving the newest features and letting us know what you think. Turn on the Beta Tester feature to get notified about new beta releases. The beta version will not install automatically and you always have the option to ignore it.","rank-math"),warning:i?"":(0,r.__)("It is not recommended to use the beta version on live production sites.","rank-math")}),!i&&wp.element.createElement(React.Fragment,null,wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",{className:"field-row field-type-switch"},wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"beta_tester"},(0,r.__)("Beta Tester","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(a.ToggleControl,{id:"beta_tester",checked:o,onChange:function(e){t.betaOptin=e,n(t)}}))))),wp.element.createElement(h,w({panel:"beta_optin"},t))))},g=n(85);function E(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],u=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){c=!0,a=e}finally{try{if(!u&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(c)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return k(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return k(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function k(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var S=function(e){var t=e.data,n=t.latestVersion,i=t.isRollbackVersion,l=t.isPluginUpdateDisabled,u=t.availableVersions,c=t.updateCoreUrl,s=t.rollbackNonce;if((0,g.isEmpty)(u))return"";var m=rankMath.version,p=E((0,o.useState)(u[1]),2),f=p[0],h=p[1],b=E((0,o.useState)(!1),2),y=b[0],w=b[1],v=(0,g.reduce)(u,(function(e,t){return e[t]=t,e}),{});return wp.element.createElement("form",{className:"rank-math-rollback-form field-form rank-math-box",method:"post",action:""},wp.element.createElement(d,{title:(0,r.__)("Rollback to Previous Version","rank-math"),description:(0,r.__)("If you are facing issues after an update, you can reinstall a previous version with this tool.","rank-math"),warning:(0,r.__)("Previous versions may not be secure or stable. Proceed with caution and always create a backup.","rank-math")}),wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",null,wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"your-verions"},(0,r.__)("Your Version","rank-math"))),wp.element.createElement("td",null,wp.element.createElement("strong",null,m),i&&wp.element.createElement(React.Fragment,null,wp.element.createElement("br",null),wp.element.createElement("span",{className:"rollback-version-label"},(0,r.__)("Rolled Back Version: ","rank-math")),(0,r.__)("Auto updates will not work, please update the plugin manually.","rank-math")),m===n?wp.element.createElement("p",{className:"description"},(0,r.__)("You are using the latest version of the plugin.","rank-math")):wp.element.createElement("p",{className:"description"},(0,r.__)("This is the version you are using on this site.","rank-math")))),m!==n&&wp.element.createElement("tr",null,wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"latest-stable"},(0,r.__)("Latest Stable Version","rank-math"))),wp.element.createElement("td",null,wp.element.createElement("strong",null,n),l&&m<n&&wp.element.createElement("a",{href:c,className:"update-link"},(0,r.__)("Update Now","rank-math")),wp.element.createElement("p",{className:"description"},(0,r.__)("This is the latest version of the plugin.","rank-math")))),wp.element.createElement("tr",null,wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"rollback_version"},(0,r.__)("Rollback Version","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(a.SelectControl,{variant:"default",id:"rm_rollback_version",name:"rm_rollback_version",value:f,options:v,disabledOptions:[m],onChange:function(e){return h(e)}}),wp.element.createElement("p",{className:"description"},(0,r.__)("Roll back to this version.","rank-math")))))),wp.element.createElement("footer",null,wp.element.createElement(a.TextControl,{type:"hidden",name:"_wpnonce",value:s}),wp.element.createElement(a.Button,{type:"submit",variant:"primary",size:"xlarge",id:"rm-rollback-button",onClick:function(){return w(!0)}},(0,r.__)("Install Version ","rank-math"),f),y&&wp.element.createElement("div",{className:"alignright rollback-loading-indicator"},wp.element.createElement("span",{className:"loading-indicator-text"},(0,r.__)("Reinstalling, please wait…","rank-math")),wp.element.createElement("span",{className:"spinner is-active"}))))},O=function(e){return wp.element.createElement(React.Fragment,null,wp.element.createElement(S,e),wp.element.createElement(v,e),wp.element.createElement(y,e))}},311:function(e){e.exports=jQuery},85:function(e){e.exports=lodash},813:function(e){e.exports=rankMathAnalyzer},142:function(e){e.exports=window.rankMathComponents},179:function(e){e.exports=wp.apiFetch},537:function(e){e.exports=wp.components},749:function(e){e.exports=wp.compose},610:function(e){e.exports=wp.element},882:function(e){e.exports=wp.hooks},3:function(e){e.exports=wp.i18n}},r={};function a(e){var t=r[e];if(void 0!==t)return t.exports;var o=r[e]={exports:{}};return n[e](o,o.exports,a),o.exports}a.m=n,a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,{a:t}),t},a.d=function(e,t){for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.f={},a.e=function(e){return Promise.all(Object.keys(a.f).reduce((function(t,n){return a.f[n](e,t),t}),[]))},a.u=function(e){return{43:"databaseTools",147:"importExport",532:"systemStatus"}[e]+".js"},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},e={},t="rank-math:",a.l=function(n,r,o,i){if(e[n])e[n].push(r);else{var l,u;if(void 0!==o)for(var c=document.getElementsByTagName("script"),s=0;s<c.length;s++){var m=c[s];if(m.getAttribute("src")==n||m.getAttribute("data-webpack")==t+o){l=m;break}}l||(u=!0,(l=document.createElement("script")).charset="utf-8",l.timeout=120,a.nc&&l.setAttribute("nonce",a.nc),l.setAttribute("data-webpack",t+o),l.src=n),e[n]=[r];var p=function(t,r){l.onerror=l.onload=null,clearTimeout(f);var a=e[n];if(delete e[n],l.parentNode&&l.parentNode.removeChild(l),a&&a.forEach((function(e){return e(r)})),t)return t(r)},f=setTimeout(p.bind(null,void 0,{type:"timeout",target:l}),12e4);l.onerror=p.bind(null,l.onerror),l.onload=p.bind(null,l.onload),u&&document.head.appendChild(l)}},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){var e;a.g.importScripts&&(e=a.g.location+"");var t=a.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var n=t.getElementsByTagName("script");if(n.length)for(var r=n.length-1;r>-1&&!e;)e=n[r--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),a.p=e}(),function(){var e={426:0};a.f.j=function(t,n){var r=a.o(e,t)?e[t]:void 0;if(0!==r)if(r)n.push(r[2]);else{var o=new Promise((function(n,a){r=e[t]=[n,a]}));n.push(r[2]=o);var i=a.p+a.u(t),l=new Error;a.l(i,(function(n){if(a.o(e,t)&&(0!==(r=e[t])&&(e[t]=void 0),r)){var o=n&&("load"===n.type?"missing":n.type),i=n&&n.target&&n.target.src;l.message="Loading chunk "+t+" failed.\n("+o+": "+i+")",l.name="ChunkLoadError",l.type=o,l.request=i,r[1](l)}}),"chunk-"+t,t)}};var t=function(t,n){var r,o,i=n[0],l=n[1],u=n[2],c=0;if(i.some((function(t){return 0!==e[t]}))){for(r in l)a.o(l,r)&&(a.m[r]=l[r]);if(u)u(a)}for(t&&t(n);c<i.length;c++)o=i[c],a.o(e,o)&&e[o]&&e[o][0](),e[o]=0},n=self.webpackChunkrank_math=self.webpackChunkrank_math||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))}(),function(){var e={};a.r(e),a.d(e,{updateViewData:function(){return re}});var t={};a.r(t),a.d(t,{appUi:function(){return ue}});var n={};a.r(n),a.d(n,{getViewData:function(){return ce}});var r,o=React,i=ReactDOM;function l(){return l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},l.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(r||(r={}));const u="popstate";function c(e,t){if(!1===e||null==e)throw new Error(t)}function s(e,t){return{usr:e.state,key:e.key,idx:t}}function m(e,t,n,r){return void 0===n&&(n=null),l({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?f(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function p(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function f(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function h(e,t,n,a){void 0===a&&(a={});let{window:o=document.defaultView,v5Compat:i=!1}=a,f=o.history,h=r.Pop,d=null,b=y();function y(){return(f.state||{idx:null}).idx}function w(){h=r.Pop;let e=y(),t=null==e?null:e-b;b=e,d&&d({action:h,location:g.location,delta:t})}function v(e){let t="null"!==o.location.origin?o.location.origin:o.location.href,n="string"==typeof e?e:p(e);return c(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==b&&(b=0,f.replaceState(l({},f.state,{idx:b}),""));let g={get action(){return h},get location(){return e(o,f)},listen(e){if(d)throw new Error("A history only accepts one active listener");return o.addEventListener(u,w),d=e,()=>{o.removeEventListener(u,w),d=null}},createHref(e){return t(o,e)},createURL:v,encodeLocation(e){let t=v(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){h=r.Push;let a=m(g.location,e,t);n&&n(a,e),b=y()+1;let l=s(a,b),u=g.createHref(a);try{f.pushState(l,"",u)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;o.location.assign(u)}i&&d&&d({action:h,location:g.location,delta:1})},replace:function(e,t){h=r.Replace;let a=m(g.location,e,t);n&&n(a,e),b=y();let o=s(a,b),l=g.createHref(a);f.replaceState(o,"",l),i&&d&&d({action:h,location:g.location,delta:0})},go(e){return f.go(e)}};return g}var d;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(d||(d={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function b(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function y(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function w(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function v(e,t,n,r){let a;void 0===r&&(r=!1),"string"==typeof e?a=f(e):(a=l({},e),c(!a.pathname||!a.pathname.includes("?"),y("?","pathname","search",a)),c(!a.pathname||!a.pathname.includes("#"),y("#","pathname","hash",a)),c(!a.search||!a.search.includes("#"),y("#","search","hash",a)));let o,i=""===e||""===a.pathname,u=i?"/":a.pathname;if(null==u)o=n;else if(r){let e=t[t.length-1].replace(/^\//,"").split("/");if(u.startsWith("..")){let t=u.split("/");for(;".."===t[0];)t.shift(),e.pop();a.pathname=t.join("/")}o="/"+e.join("/")}else{let e=t.length-1;if(u.startsWith("..")){let t=u.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let s=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"==typeof e?f(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:E(r),hash:k(a)}}(a,o),m=u&&"/"!==u&&u.endsWith("/"),p=(i||"."===u)&&n.endsWith("/");return s.pathname.endsWith("/")||!m&&!p||(s.pathname+="/"),s}const g=e=>e.join("/").replace(/\/\/+/g,"/"),E=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",k=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;const S=["post","put","patch","delete"],O=(new Set(S),["get",...S]);new Set(O),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function _(){return _=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_.apply(this,arguments)}const j=o.createContext(null);const P=o.createContext(null);const N=o.createContext(null);const T=o.createContext({outlet:null,matches:[],isDataRoute:!1});function D(){return null!=o.useContext(N)}function A(){return D()||c(!1),o.useContext(N).location}function C(e){o.useContext(P).static||o.useLayoutEffect(e)}function R(){let{isDataRoute:e}=o.useContext(T);return e?function(){let{router:e}=V(x.UseNavigateStable),t=M(U.UseNavigateStable),n=o.useRef(!1);return C((()=>{n.current=!0})),o.useCallback((function(r,a){void 0===a&&(a={}),n.current&&("number"==typeof r?e.navigate(r):e.navigate(r,_({fromRouteId:t},a)))}),[e,t])}():function(){D()||c(!1);let e=o.useContext(j),{basename:t,navigator:n}=o.useContext(P),{matches:r}=o.useContext(T),{pathname:a}=A(),i=JSON.stringify(w(r).map((e=>e.pathnameBase))),l=o.useRef(!1);return C((()=>{l.current=!0})),o.useCallback((function(r,o){if(void 0===o&&(o={}),!l.current)return;if("number"==typeof r)return void n.go(r);let u=v(r,JSON.parse(i),a,"path"===o.relative);null==e&&"/"!==t&&(u.pathname="/"===u.pathname?t:g([t,u.pathname])),(o.replace?n.replace:n.push)(u,o.state,o)}),[t,n,i,a,e])}()}o.Component;var x=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(x||{}),U=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(U||{});function V(e){let t=o.useContext(j);return t||c(!1),t}function M(e){let t=function(e){let t=o.useContext(T);return t||c(!1),t}(),n=t.matches[t.matches.length-1];return n.route.id||c(!1),n.route.id}o.startTransition;function F(e){let{basename:t="/",children:n=null,location:a,navigationType:i=r.Pop,navigator:l,static:u=!1}=e;D()&&c(!1);let s=t.replace(/^\/*/,"/"),m=o.useMemo((()=>({basename:s,navigator:l,static:u})),[s,l,u]);"string"==typeof a&&(a=f(a));let{pathname:p="/",search:h="",hash:d="",state:y=null,key:w="default"}=a,v=o.useMemo((()=>{let e=b(p,s);return null==e?null:{location:{pathname:e,search:h,hash:d,state:y,key:w},navigationType:i}}),[s,p,h,d,y,w,i]);return null==v?null:o.createElement(P.Provider,{value:m},o.createElement(N.Provider,{children:n,value:v}))}new Promise((()=>{}));o.Component;function I(e){return void 0===e&&(e=""),new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce(((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map((e=>[n,e])):[[n,r]])}),[]))}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);new Map;const L=o.startTransition;i.flushSync;function B(e){let{basename:t,children:n,future:r,window:a}=e,i=o.useRef();var l;null==i.current&&(i.current=(void 0===(l={window:a,v5Compat:!0})&&(l={}),h((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return m("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:p(t)}),null,l)));let u=i.current,[c,s]=o.useState({action:u.action,location:u.location}),{v7_startTransition:f}=r||{},d=o.useCallback((e=>{f&&L?L((()=>s(e))):s(e)}),[s,f]);return o.useLayoutEffect((()=>u.listen(d)),[u,d]),o.createElement(F,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:u})}"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;var W,z;function $(e){let t=o.useRef(I(e)),n=o.useRef(!1),r=A(),a=o.useMemo((()=>function(e,t){let n=I(e);return t&&t.forEach(((e,r)=>{n.has(r)||t.getAll(r).forEach((e=>{n.append(r,e)}))})),n}(r.search,n.current?null:t.current)),[r.search]),i=R(),l=o.useCallback(((e,t)=>{const r=I("function"==typeof e?e(a):e);n.current=!0,i("?"+r,t)}),[i,a]);return[a,l]}(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(W||(W={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(z||(z={}));var H=a(610),Y=a(85),J=a(179),q=a.n(J),K=a(749),Q=wp.data,G=function(e){var t=e.title;return wp.element.createElement("div",{className:"rank-math-skeleton rank-math-system-status rank-math-ui container"},wp.element.createElement("div",{className:"rank-math-box"},wp.element.createElement("header",null,wp.element.createElement("h3",null,t)),wp.element.createElement("div",{className:"copy-button-wrapper"}),wp.element.createElement("div",{className:"rank-math-panel components-panel"},(0,Y.map)(Array.from({length:8}),(function(e,t){return wp.element.createElement("div",{key:t,className:"components-panel__body"})})))))},X=a(674),Z=(0,K.compose)((0,Q.withSelect)((function(e){return{data:e("rank-math-status").getViewData("version_control")}})),(0,Q.withDispatch)((function(e){var t="version_control";return{getViewData:function(){q()({method:"POST",path:"/rankmath/v1/status/getViewData",data:{activeTab:t}}).catch((function(e){alert(e.message)})).then((function(n){e("rank-math-status").updateViewData(t,n)}))},updateViewData:function(n){e("rank-math-status").updateViewData(t,n)}}})))((function(e){var t=e.data,n=e.getViewData,r=e.updateViewData;return(0,H.useEffect)((function(){(0,Y.isEmpty)(t)&&n()}),[]),(0,Y.isUndefined)(t)?wp.element.createElement(G,{title:"Version Control"}):wp.element.createElement("div",{className:"rank-math-ui container version-control"},wp.element.createElement(X.default,{data:t,updateViewData:r}))}));function ee(e){return ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ee(e)}function te(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ne(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ee(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ee(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ee(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function re(e,t){return n=e,r=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?te(Object(n),!0).forEach((function(t){ne(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):te(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},t),{type:"RANK_MATH_APP_UI",key:n,value:r};var n,r}function ae(e){return ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ae(e)}function oe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ie(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?oe(Object(n),!0).forEach((function(t){le(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):oe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function le(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ae(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ae(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ae(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ue(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return"RANK_MATH_APP_UI"===t.type?ie(ie({},e),{},le({},t.key,t.value)):e}function ce(e,t){return e.appUi[t]}(0,Q.register)((0,Q.createReduxStore)("rank-math-status",{reducer:(0,Q.combineReducers)(t),selectors:n,actions:e}));var se=a(3),me=(0,H.lazy)((function(){return Promise.resolve().then(a.bind(a,674))})),pe=(0,H.lazy)((function(){return a.e(43).then(a.bind(a,766))})),fe=(0,H.lazy)((function(){return a.e(532).then(a.bind(a,360))})),he=(0,H.lazy)((function(){return a.e(147).then(a.bind(a,698))})),de=rankMath,be=de.isAdvancedMode,ye=de.isPluginActiveForNetwork,we=de.canUser,ve="version_control",ge="tools",Ee="status",ke="import_export",Se=(0,Y.filter)([be&&(!ye||we.setupNetwork)&&we.installPlugins&&{name:ve,title:(0,se.__)("Version Control","rank-math"),view:me},be&&(!ye||we.manageOptions)&&{name:ge,title:(0,se.__)("Database Tools","rank-math"),view:pe},we.manageOptions&&{name:Ee,title:(0,se.__)("System Status","rank-math"),view:fe},be&&we.manageOptions&&{name:ke,title:(0,se.__)("Import & Export","rank-math"),view:he}],Boolean),Oe={version_control:(0,se.__)("Version Control","rank-math"),tools:(0,se.__)("Tools","rank-math"),status:(0,se.__)("Status","rank-math"),import_export:(0,se.__)("Import & Export","rank-math")},_e=a(142);function je(e){return je="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},je(e)}function Pe(e){return function(e){if(Array.isArray(e))return Ne(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Ne(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ne(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ne(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Te(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function De(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Te(Object(n),!0).forEach((function(t){Ae(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Te(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ae(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==je(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==je(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===je(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ce=(0,K.compose)((0,Q.withSelect)((function(e,t){var n=t.searchParams.get("view"),r=e("rank-math-status");return De(De({},t),{},{data:r.getViewData(n),activeTab:n})})),(0,Q.withDispatch)((function(e,t){var n=t.activeTab,r=t.setSearchParams;return{onTabChange:function(e){e!==n&&r((function(t){return(0,Y.fromPairs)([].concat(Pe(t),[["view",e]]))}))},getViewData:function(){q()({method:"POST",path:"/rankmath/v1/status/getViewData",data:{activeTab:n}}).catch((function(e){alert(e.message)})).then((function(t){e("rank-math-status").updateViewData(n,t)}))},updateViewData:function(t){e("rank-math-status").updateViewData(n,t)}}})))((function(e){var t=e.data,n=e.activeTab,r=e.onTabChange,a=e.getViewData,o=e.updateViewData;return(0,H.useEffect)((function(){(0,Y.isEmpty)(t)&&a()}),[n]),wp.element.createElement(React.Fragment,null,wp.element.createElement(_e.Breadcrumbs,{activePage:Oe[n]}),wp.element.createElement("span",{className:"wp-header-end"}),wp.element.createElement(H.Suspense,null,wp.element.createElement(_e.TabPanel,{tabs:Se,key:n,initialTabName:n,onSelect:r},(function(e){var n=e.name,r=e.title,a=e.view;return(0,Y.isUndefined)(t)?wp.element.createElement(G,{title:r,name:n}):wp.element.createElement("div",{className:"rank-math-ui container ".concat(n)},wp.element.createElement(a,{data:t,updateViewData:o}))}))))}));function Re(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],u=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){c=!0,a=e}finally{try{if(!u&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(c)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return xe(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return xe(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xe(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ue,Ve=function(){var e=Re($({view:Se[0].name}),2),t=e[0],n=e[1];return wp.element.createElement(Ce,{searchParams:t,setSearchParams:n})};Ue=function(){var e=document.querySelector(".rank-math-tools-wrap");e&&(0,H.createRoot)(e).render(wp.element.createElement(B,null,wp.element.createElement(Ve,null)));var t=document.getElementById("rank-math-version-control-wrapper");t&&(0,H.createRoot)(t).render(wp.element.createElement(Z,null))},"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",Ue):Ue())}()}();