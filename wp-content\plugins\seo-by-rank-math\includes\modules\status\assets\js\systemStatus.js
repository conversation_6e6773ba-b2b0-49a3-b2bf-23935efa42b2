"use strict";(self.webpackChunkrank_math=self.webpackChunkrank_math||[]).push([[532],{360:function(e,t,r){r.r(t),r.d(t,{default:function(){return h}});var n=r(3),a=r(610),l=r(749),o=r(142);function c(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,l,o,c=[],u=!0,i=!1;try{if(l=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=l.call(r)).done)&&(c.push(n.value),c.length!==t);u=!0);}catch(e){i=!0,a=e}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(i)throw a}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var i=function(e){var t=e.errorLog,r=e.errorLogPath,u=e.errorLogSize,i=e.errorLogError,m=c((0,a.useState)(!1),2),s=m[0],p=m[1],f=(0,l.useCopyToClipboard)(t,(function(){p(!0),setTimeout((function(){p(!1)}),2e3)}));return(0,a.useEffect)((function(){var e=document.getElementById("rank-math-status-error-log");e&&(e.scrollTop=e.scrollHeight)}),[]),wp.element.createElement("div",{className:"rank-math-system-status rank-math-box"},wp.element.createElement("header",null,wp.element.createElement("h3",null,(0,n.__)("Error Log","rank-math"))),wp.element.createElement("p",{className:"description",dangerouslySetInnerHTML:{__html:(0,n.sprintf)((0,n.__)("If you have %s enabled, errors will be stored in a log file. Here you can find the last 100 lines in reversed order so that you or the Rank Math support team can view it easily. The file cannot be edited here.","rank-math"),'<a href="https://wordpress.org/support/article/debugging-in-wordpress/" target=_blank" >WP_DEBUG_LOG</a>')}}),i&&wp.element.createElement("strong",{className:"error-log-cannot-display"},i),!i&&wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{className:"copy-button-wrapper"},wp.element.createElement(o.Button,{ref:f},(0,n.__)("Copy Log to Clipboard","rank-math")),s&&wp.element.createElement("span",{className:"success"},(0,n.__)("Copied!","rank-math"))),wp.element.createElement("div",{id:"error-log-wrapper"},wp.element.createElement(o.TextareaControl,{rows:16,cols:80,value:t,variant:"code-box",className:"code",id:"rank-math-status-error-log",disabled:!0})),t&&wp.element.createElement("div",{className:"error-log-info"},wp.element.createElement("code",null,r),wp.element.createElement("em",null," (",u,")"))))},m=r(85),s=r(537),p=function(e){var t=function(e){return(0,m.reduce)(e,(function(e,t){var r=t.label,n=t.value;return e[r]=n,e}),{})};return wp.element.createElement(s.Panel,{className:"rank-math-panel"},(0,m.map)(e,(function(e,r){var n=e.label,a=e.fields,l=e.show_count;if(!(0,m.isEmpty)(a)){var c="(".concat((0,m.entries)(a).length,")"),u="".concat(n," ").concat(l?c:"");return wp.element.createElement(s.PanelBody,{key:r,title:u,initialOpen:!1},wp.element.createElement(s.PanelRow,null,wp.element.createElement(o.Table,{size:"small",fields:t((0,m.values)(a))})))}})))};function f(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,l,o,c=[],u=!0,i=!1;try{if(l=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=l.call(r)).done)&&(c.push(n.value),c.length!==t);u=!0);}catch(e){i=!0,a=e}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(i)throw a}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return d(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var y=function(e){var t=e.systemInfo,r=e.systemInfoCopy,c=f((0,a.useState)(!1),2),u=c[0],i=c[1],m=(0,l.useCopyToClipboard)(r,(function(){i(!0),setTimeout((function(){i(!1)}),3e3)}));return wp.element.createElement("div",{className:"rank-math-system-status rank-math-box"},wp.element.createElement("header",null,wp.element.createElement("h3",null,(0,n.__)("System Info","rank-math"))),wp.element.createElement("div",{className:"copy-button-wrapper"},wp.element.createElement(o.Button,{ref:m},(0,n.__)("Copy System Info to Clipboard","rank-math")),u&&wp.element.createElement("span",{className:"success"},(0,n.__)("Copied!","rank-math"))),wp.element.createElement(p,t))},h=function(e){return wp.element.createElement(React.Fragment,null,wp.element.createElement(y,e.data),wp.element.createElement(i,e.data))}}}]);