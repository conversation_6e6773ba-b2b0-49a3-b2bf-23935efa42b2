<<<<<<< Updated upstream
"use strict";(self.webpackChunkrank_math=self.webpackChunkrank_math||[]).push([["versionControl"],{"./includes/modules/status/assets/src/tabs/version-control/AutoUpdatePanel.js":function(e,t,n){n.r(t);var a=n("@wordpress/i18n"),r=n("@rank-math/components"),l=n("./includes/modules/status/assets/src/tabs/version-control/Footer.js"),o=n("./includes/modules/status/assets/src/tabs/version-control/Header.js");function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},s.apply(this,arguments)}t.default=function(e){var t=e.data,n=e.updateViewData,i=t.autoUpdate,c=t.updateNotificationEmail,u=t.isPluginUpdateDisabled,m=t.rollbackVersion;return wp.element.createElement("div",{className:"rank-math-auto-update-form field-form rank-math-box"},wp.element.createElement(o.default,{title:(0,a.__)("Auto Update","rank-math"),description:u?(0,a.__)("You cannot turn on auto-updates to automatically update to stable versions of Rank Math as soon as they are released, because site wide plugins auto-update option is disabled on your site.","rank-math"):(0,a.__)("Turn on auto-updates to automatically update to stable versions of Rank Math as soon as they are released. The beta versions of Rank Math as soon as they are released. The beta versions will never install automatically.","rank-math")}),!u&&wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",{className:"field-row field-type-switch"},wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"enable_auto_update"},(0,a.__)("Auto Update Plugin","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(r.ToggleControl,{id:"enable_auto_update",checked:i,onChange:function(e){t.autoUpdate=e,n(t)}}))))),wp.element.createElement("div",{id:"control_update_notification_email"},wp.element.createElement("p",null,(0,a.__)("When auto-updates are turned off, you can enable update notifications, to send an email to the site administrator when an update is available for Rank Math.","rank-math")),wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",{className:"field-row field-type-switch"},wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"enable_update_notification_email"},(0,a.__)("Update Notification Email","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(r.ToggleControl,{id:"enable_update_notification_email",checked:c,onChange:function(e){t.updateNotificationEmail=e,n(t)}})))))),!u&&m&&wp.element.createElement(r.Notice,{variant:"alt",status:"warning"},wp.element.createElement("p",null,(0,a.__)("Rank Math will not auto-update because you have rolled back to a previous version. Update to the latest version manually to make this option work again.","rank-math"))),wp.element.createElement(l.default,s({panel:"auto_update"},t)))}},"./includes/modules/status/assets/src/tabs/version-control/BetaOptInPanel.js":function(e,t,n){n.r(t);var a=n("@wordpress/i18n"),r=n("@rank-math/components"),l=n("./includes/modules/status/assets/src/tabs/version-control/Footer.js"),o=n("./includes/modules/status/assets/src/tabs/version-control/Header.js");function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},s.apply(this,arguments)}t.default=function(e){var t=e.data,n=e.updateViewData,i=t.betaOptin,c=t.isPluginUpdateDisabled;return wp.element.createElement("div",{className:"rank-math-beta-optin-form field-form rank-math-box"},wp.element.createElement(o.default,{title:(0,a.__)("Beta Opt-in","rank-math"),description:c?(0,a.__)("You cannot turn on the Beta Tester feature because site wide plugins auto-update option is disabled on your site.","rank-math"):(0,a.__)("You can take part in shaping Rank Math by test-driving the newest features and letting us know what you think. Turn on the Beta Tester feature to get notified about new beta releases. The beta version will not install automatically and you always have the option to ignore it.","rank-math"),warning:c?"":(0,a.__)("It is not recommended to use the beta version on live production sites.","rank-math")}),!c&&wp.element.createElement(React.Fragment,null,wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",{className:"field-row field-type-switch"},wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"beta_tester"},(0,a.__)("Beta Tester","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(r.ToggleControl,{id:"beta_tester",checked:i,onChange:function(e){t.betaOptin=e,n(t)}}))))),wp.element.createElement(l.default,s({panel:"beta_optin"},t))))}},"./includes/modules/status/assets/src/tabs/version-control/Footer.js":function(e,t,n){n.r(t);var a=n("@wordpress/i18n"),r=n("@wordpress/element"),l=n("@wordpress/api-fetch"),o=n.n(l),s=n("@rank-math/components");function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){m(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==i(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==i(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===i(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,l,o,s=[],i=!0,c=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;i=!1}else for(;!(i=(a=l.call(n)).done)&&(s.push(a.value),s.length!==t);i=!0);}catch(e){c=!0,r=e}finally{try{if(!i&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}t.default=function(e){var t=p((0,r.useState)((0,a.__)("Save Changes","rank-math")),2),n=t[0],l=t[1],i=p((0,r.useState)(!1),2),c=i[0],m=i[1];return wp.element.createElement("footer",null,wp.element.createElement(s.Button,{type:"submit",variant:"primary",size:"xlarge",disabled:c,onClick:function(){l((0,a.__)("Saving…","rank-math")),m(!0),o()({method:"POST",path:"/rankmath/v1/status/updateViewData",data:u({},e)}).catch((function(e){console.error(e.message),l((0,a.__)("Failed! Try again","rank-math"))})).then((function(e){l(e?(0,a.__)("Saved","rank-math"):(0,a.__)("Failed! Try again","rank-math"))})).finally((function(){setTimeout((function(){m(!1),l((0,a.__)("Save Changes","rank-math"))}),1e3)}))}},n))}},"./includes/modules/status/assets/src/tabs/version-control/Header.js":function(e,t,n){n.r(t);var a=n("@wordpress/i18n");t.default=function(e){var t=e.title,n=e.description,r=e.warning,l=void 0===r?"":r;return wp.element.createElement(React.Fragment,null,wp.element.createElement("header",null,wp.element.createElement("h3",null,t)),wp.element.createElement("p",null,n),l&&wp.element.createElement("p",{className:"description warning"},wp.element.createElement("strong",null,wp.element.createElement("span",{className:"warning"},(0,a.__)("Warning: ","rank-math")),l)))}},"./includes/modules/status/assets/src/tabs/version-control/VersionControlPanel.js":function(e,t,n){n.r(t);var a=n("lodash"),r=n("@wordpress/i18n"),l=n("@wordpress/element"),o=n("@rank-math/components"),s=n("./includes/modules/status/assets/src/tabs/version-control/Header.js");function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,l,o,s=[],i=!0,c=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;i=!1}else for(;!(i=(a=l.call(n)).done)&&(s.push(a.value),s.length!==t);i=!0);}catch(e){c=!0,r=e}finally{try{if(!i&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}t.default=function(e){var t=e.data,n=t.latestVersion,c=t.isRollbackVersion,u=t.isPluginUpdateDisabled,m=t.availableVersions,p=t.updateCoreUrl,d=t.rollbackNonce,f=rankMath.version,h=i((0,l.useState)(m[1]),2),b=h[0],w=h[1],v=i((0,l.useState)(!1),2),y=v[0],_=v[1],k=(0,a.reduce)(m,(function(e,t){return e[t]=t,e}),{});return wp.element.createElement("form",{className:"rank-math-rollback-form field-form rank-math-box",method:"post",action:""},wp.element.createElement(s.default,{title:(0,r.__)("Rollback to Previous Version","rank-math"),description:(0,r.__)("If you are facing issues after an update, you can reinstall a previous version with this tool.","rank-math"),warning:(0,r.__)("Previous versions may not be secure or stable. Proceed with caution and always create a backup.","rank-math")}),wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",null,wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"your-verions"},(0,r.__)("Your Version","rank-math"))),wp.element.createElement("td",null,wp.element.createElement("strong",null,f),c&&wp.element.createElement(React.Fragment,null,wp.element.createElement("br",null),wp.element.createElement("span",{className:"rollback-version-label"},(0,r.__)("Rolled Back Version: ","rank-math")),(0,r.__)("Auto updates will not work, please update the plugin manually.","rank-math")),f===n?wp.element.createElement("p",{className:"description"},(0,r.__)("You are using the latest version of the plugin.","rank-math")):wp.element.createElement("p",{className:"description"},(0,r.__)("This is the version you are using on this site.","rank-math")))),f!==n&&wp.element.createElement("tr",null,wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"latest-stable"},(0,r.__)("Latest Stable Version","rank-math"))),wp.element.createElement("td",null,wp.element.createElement("strong",null,n),u&&f<n&&wp.element.createElement("a",{href:p,className:"update-link"},(0,r.__)("Update Now","rank-math")),wp.element.createElement("p",{className:"description"},(0,r.__)("This is the latest version of the plugin.","rank-math")))),wp.element.createElement("tr",null,wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"rollback_version"},(0,r.__)("Rollback Version","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(o.SelectControl,{variant:"default",id:"rm_rollback_version",name:"rm_rollback_version",value:b,options:k,disabledOptions:[f],onChange:function(e){return w(e)}}),wp.element.createElement("p",{className:"description"},(0,r.__)("Roll back to this version.","rank-math")))))),wp.element.createElement("footer",null,wp.element.createElement(o.TextControl,{type:"hidden",name:"_wpnonce",value:d}),wp.element.createElement(o.Button,{type:"submit",variant:"primary",size:"xlarge",id:"rm-rollback-button",onClick:function(){return _(!0)}},(0,r.__)("Install Version ","rank-math"),b),y&&wp.element.createElement("div",{className:"alignright rollback-loading-indicator"},wp.element.createElement("span",{className:"loading-indicator-text"},(0,r.__)("Reinstalling, please wait…","rank-math")),wp.element.createElement("span",{className:"spinner is-active"}))))}},"./includes/modules/status/assets/src/tabs/version-control/index.js":function(e,t,n){n.r(t);var a=n("./includes/modules/status/assets/src/tabs/version-control/AutoUpdatePanel.js"),r=n("./includes/modules/status/assets/src/tabs/version-control/BetaOptInPanel.js"),l=n("./includes/modules/status/assets/src/tabs/version-control/VersionControlPanel.js");t.default=function(e){return wp.element.createElement(React.Fragment,null,wp.element.createElement(l.default,e),wp.element.createElement(r.default,e),wp.element.createElement(a.default,e))}}}]);
=======
"use strict";(self.webpackChunkrank_math=self.webpackChunkrank_math||[]).push([["versionControl"],{"./includes/modules/status/assets/src/tabs/version-control/AutoUpdatePanel.js":function(e,t,n){n.r(t);var a=n("@wordpress/i18n"),r=n("@rank-math/components"),l=n("./includes/modules/status/assets/src/tabs/version-control/Footer.js"),o=n("./includes/modules/status/assets/src/tabs/version-control/Header.js");function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},s.apply(null,arguments)}t.default=function(e){var t=e.data,n=e.updateViewData,i=t.autoUpdate,c=t.updateNotificationEmail,u=t.isPluginUpdateDisabled,m=t.rollbackVersion;return wp.element.createElement("div",{className:"rank-math-auto-update-form field-form rank-math-box"},wp.element.createElement(o.default,{title:(0,a.__)("Auto Update","rank-math"),description:u?(0,a.__)("You cannot turn on auto-updates to automatically update to stable versions of Rank Math as soon as they are released, because site wide plugins auto-update option is disabled on your site.","rank-math"):(0,a.__)("Turn on auto-updates to automatically update to stable versions of Rank Math as soon as they are released. The beta versions of Rank Math as soon as they are released. The beta versions will never install automatically.","rank-math")}),!u&&wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",{className:"field-row field-type-switch"},wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"enable_auto_update"},(0,a.__)("Auto Update Plugin","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(r.ToggleControl,{id:"enable_auto_update",checked:i,onChange:function(e){t.autoUpdate=e,n(t)}}))))),wp.element.createElement("div",{id:"control_update_notification_email"},wp.element.createElement("p",null,(0,a.__)("When auto-updates are turned off, you can enable update notifications, to send an email to the site administrator when an update is available for Rank Math.","rank-math")),wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",{className:"field-row field-type-switch"},wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"enable_update_notification_email"},(0,a.__)("Update Notification Email","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(r.ToggleControl,{id:"enable_update_notification_email",checked:c,onChange:function(e){t.updateNotificationEmail=e,n(t)}})))))),!u&&m&&wp.element.createElement(r.Notice,{variant:"alt",status:"warning"},wp.element.createElement("p",null,(0,a.__)("Rank Math will not auto-update because you have rolled back to a previous version. Update to the latest version manually to make this option work again.","rank-math"))),wp.element.createElement(l.default,s({panel:"auto_update"},t)))}},"./includes/modules/status/assets/src/tabs/version-control/BetaOptInPanel.js":function(e,t,n){n.r(t);var a=n("@wordpress/i18n"),r=n("@rank-math/components"),l=n("./includes/modules/status/assets/src/tabs/version-control/Footer.js"),o=n("./includes/modules/status/assets/src/tabs/version-control/Header.js");function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},s.apply(null,arguments)}t.default=function(e){var t=e.data,n=e.updateViewData,i=t.betaOptin,c=t.isPluginUpdateDisabled;return wp.element.createElement("div",{className:"rank-math-beta-optin-form field-form rank-math-box"},wp.element.createElement(o.default,{title:(0,a.__)("Beta Opt-in","rank-math"),description:c?(0,a.__)("You cannot turn on the Beta Tester feature because site wide plugins auto-update option is disabled on your site.","rank-math"):(0,a.__)("You can take part in shaping Rank Math by test-driving the newest features and letting us know what you think. Turn on the Beta Tester feature to get notified about new beta releases. The beta version will not install automatically and you always have the option to ignore it.","rank-math"),warning:c?"":(0,a.__)("It is not recommended to use the beta version on live production sites.","rank-math")}),!c&&wp.element.createElement(React.Fragment,null,wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",{className:"field-row field-type-switch"},wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"beta_tester"},(0,a.__)("Beta Tester","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(r.ToggleControl,{id:"beta_tester",checked:i,onChange:function(e){t.betaOptin=e,n(t)}}))))),wp.element.createElement(l.default,s({panel:"beta_optin"},t))))}},"./includes/modules/status/assets/src/tabs/version-control/Footer.js":function(e,t,n){n.r(t);var a=n("@wordpress/i18n"),r=n("@wordpress/element"),l=n("@wordpress/api-fetch"),o=n.n(l),s=n("@rank-math/components");function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){m(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=i(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,l,o,s=[],i=!0,c=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;i=!1}else for(;!(i=(a=l.call(n)).done)&&(s.push(a.value),s.length!==t);i=!0);}catch(e){c=!0,r=e}finally{try{if(!i&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return d(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}t.default=function(e){var t=p((0,r.useState)((0,a.__)("Save Changes","rank-math")),2),n=t[0],l=t[1],i=p((0,r.useState)(!1),2),c=i[0],m=i[1];return wp.element.createElement("footer",null,wp.element.createElement(s.Button,{type:"submit",variant:"primary",size:"xlarge",disabled:c,onClick:function(){l((0,a.__)("Saving…","rank-math")),m(!0),o()({method:"POST",path:"/rankmath/v1/status/updateViewData",data:u({},e)}).catch((function(e){console.error(e.message),l((0,a.__)("Failed! Try again","rank-math"))})).then((function(e){l(e?(0,a.__)("Saved","rank-math"):(0,a.__)("Failed! Try again","rank-math"))})).finally((function(){setTimeout((function(){m(!1),l((0,a.__)("Save Changes","rank-math"))}),1e3)}))}},n))}},"./includes/modules/status/assets/src/tabs/version-control/Header.js":function(e,t,n){n.r(t);var a=n("@wordpress/i18n");t.default=function(e){var t=e.title,n=e.description,r=e.warning,l=void 0===r?"":r;return wp.element.createElement(React.Fragment,null,wp.element.createElement("header",null,wp.element.createElement("h3",null,t)),wp.element.createElement("p",null,n),l&&wp.element.createElement("p",{className:"description warning"},wp.element.createElement("strong",null,wp.element.createElement("span",{className:"warning"},(0,a.__)("Warning: ","rank-math")),l)))}},"./includes/modules/status/assets/src/tabs/version-control/VersionControlPanel.js":function(e,t,n){n.r(t);var a=n("lodash"),r=n("@wordpress/i18n"),l=n("@wordpress/element"),o=n("@rank-math/components"),s=n("./includes/modules/status/assets/src/tabs/version-control/Header.js");function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,l,o,s=[],i=!0,c=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;i=!1}else for(;!(i=(a=l.call(n)).done)&&(s.push(a.value),s.length!==t);i=!0);}catch(e){c=!0,r=e}finally{try{if(!i&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return c(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}t.default=function(e){var t=e.data,n=t.latestVersion,c=t.isRollbackVersion,u=t.isPluginUpdateDisabled,m=t.availableVersions,p=t.updateCoreUrl,d=t.rollbackNonce,f=rankMath.version,h=i((0,l.useState)(m[1]),2),b=h[0],w=h[1],v=i((0,l.useState)(!1),2),y=v[0],_=v[1],k=(0,a.reduce)(m,(function(e,t){return e[t]=t,e}),{});return wp.element.createElement("form",{className:"rank-math-rollback-form field-form rank-math-box",method:"post",action:""},wp.element.createElement(s.default,{title:(0,r.__)("Rollback to Previous Version","rank-math"),description:(0,r.__)("If you are facing issues after an update, you can reinstall a previous version with this tool.","rank-math"),warning:(0,r.__)("Previous versions may not be secure or stable. Proceed with caution and always create a backup.","rank-math")}),wp.element.createElement("table",{className:"form-table"},wp.element.createElement("tbody",null,wp.element.createElement("tr",null,wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"your-verions"},(0,r.__)("Your Version","rank-math"))),wp.element.createElement("td",null,wp.element.createElement("strong",null,f),c&&wp.element.createElement(React.Fragment,null,wp.element.createElement("br",null),wp.element.createElement("span",{className:"rollback-version-label"},(0,r.__)("Rolled Back Version: ","rank-math")),(0,r.__)("Auto updates will not work, please update the plugin manually.","rank-math")),f===n?wp.element.createElement("p",{className:"description"},(0,r.__)("You are using the latest version of the plugin.","rank-math")):wp.element.createElement("p",{className:"description"},(0,r.__)("This is the version you are using on this site.","rank-math")))),f!==n&&wp.element.createElement("tr",null,wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"latest-stable"},(0,r.__)("Latest Stable Version","rank-math"))),wp.element.createElement("td",null,wp.element.createElement("strong",null,n),u&&f<n&&wp.element.createElement("a",{href:p,className:"update-link"},(0,r.__)("Update Now","rank-math")),wp.element.createElement("p",{className:"description"},(0,r.__)("This is the latest version of the plugin.","rank-math")))),wp.element.createElement("tr",null,wp.element.createElement("th",{scope:"row"},wp.element.createElement("label",{htmlFor:"rollback_version"},(0,r.__)("Rollback Version","rank-math"))),wp.element.createElement("td",null,wp.element.createElement(o.SelectControl,{variant:"default",id:"rm_rollback_version",name:"rm_rollback_version",value:b,options:k,disabledOptions:[f],onChange:function(e){return w(e)}}),wp.element.createElement("p",{className:"description"},(0,r.__)("Roll back to this version.","rank-math")))))),wp.element.createElement("footer",null,wp.element.createElement(o.TextControl,{type:"hidden",name:"_wpnonce",value:d}),wp.element.createElement(o.Button,{type:"submit",variant:"primary",size:"xlarge",id:"rm-rollback-button",onClick:function(){return _(!0)}},(0,r.__)("Install Version ","rank-math"),b),y&&wp.element.createElement("div",{className:"alignright rollback-loading-indicator"},wp.element.createElement("span",{className:"loading-indicator-text"},(0,r.__)("Reinstalling, please wait…","rank-math")),wp.element.createElement("span",{className:"spinner is-active"}))))}},"./includes/modules/status/assets/src/tabs/version-control/index.js":function(e,t,n){n.r(t);var a=n("./includes/modules/status/assets/src/tabs/version-control/AutoUpdatePanel.js"),r=n("./includes/modules/status/assets/src/tabs/version-control/BetaOptInPanel.js"),l=n("./includes/modules/status/assets/src/tabs/version-control/VersionControlPanel.js");t.default=function(e){return wp.element.createElement(React.Fragment,null,wp.element.createElement(l.default,e),wp.element.createElement(r.default,e),wp.element.createElement(a.default,e))}}}]);
>>>>>>> Stashed changes
