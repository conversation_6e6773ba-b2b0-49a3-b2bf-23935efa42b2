!function(){"use strict";var e={n:function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,{a:r}),r},d:function(t,r){for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}},t=jQuery,r=e.n(t),n=lodash,i=wp.hooks,o=wp.data;function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,u(n.key),n)}}function u(e){var t=function(e,t){if("object"!==c(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===c(t)?t:String(t)}var s=new(function(){function e(){var t,r,n;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,n=null,(r=u(r="map"))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n}var t,n,i;return t=e,(n=[{key:"swap",value:function(e,t){var r=this;if(!(e=e||""))return"";var n=new RegExp(/%(([a-z0-9_-]+)\(([^)]*)\)|[^\s]+)%/,"giu");return e.replace(" %page%","").replace("%sep% %sep%","%sep%").replace(n,(function(e){return r.replace(t,e)})).trim()}},{key:"replace",value:function(e,t){var n=t.toLowerCase().slice(1,-1);return["term_description","user_description"].includes(n)?"undefined"!=typeof tinymce&&void 0!==tinymce.editors.rank_math_description_editor?tinymce.editors.rank_math_description_editor.getContent():r()("#description").val():n.includes("customfield(")?(n=n.replace("customfield(","").replace(")",""))in rankMath.customFields?rankMath.customFields[n]:"":(e=e||this.getMap(),(n="seo_description"===(n="seo_title"===(n=n.includes("(")?n.split("(")[0]:n)?"title":n)?"excerpt":n)in e?e[n]:"")}},{key:"getMap",value:function(){var e=this;return null!==this.map||(this.map={},r().each(rankMath.variables,(function(t,r){t=t.toLowerCase().replace(/%+/g,"").split("(")[0],e.map[t]=r.example}))),this.map}},{key:"setVariable",value:function(e,t){null!==this.map?this.map[e]=t:void 0!==rankMath.variables[e]&&(rankMath.variables[e].example=t)}}])&&a(t.prototype,n),i&&a(t,i),Object.defineProperty(t,"prototype",{writable:!1}),e}()),l=function(){return!((0,n.isNil)(window.wp)||(0,n.isNil)(wp.data)||(0,n.isNil)(wp.data.select("core/editor"))||!window.document.body.classList.contains("block-editor-page")||!(0,n.isFunction)(wp.data.select("core/editor").getEditedPostAttribute))};function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach((function(t){d(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function d(e,t,r){return(t=h(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,h(n.key),n)}}function h(e){var t=function(e,t){if("object"!==p(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==p(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===p(t)?t:String(t)}var b=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.excerpt=r()("#excerpt"),this.elemPrice=r()("#_sale_price"),this.elemRegPrice=r()("#_regular_price"),this._data={excerpt:!1},this.refreshWCPrice=this.refreshWCPrice.bind(this),this.events=this.events.bind(this),this.ensureIframe=this.ensureIframe.bind(this),this.events(),this.hooks()}var t,c,a;return t=e,(c=[{key:"hooks",value:function(){void 0!==this.excerpt&&((0,i.addFilter)("rank_math_content","rank-math",this.getContent.bind(this),11),(0,i.addFilter)("rank_math_dataCollector_data","rank-math",this.syncExcerpt.bind(this),11))}},{key:"getContent",value:function(e){return e+="undefined"!=typeof tinymce&&tinymce.activeEditor&&"excerpt"===tinymce.activeEditor.id?tinymce.activeEditor.getContent():this.excerpt.val()}},{key:"syncExcerpt",value:function(e){return(0,n.has)(e,"excerpt")&&!1!==this._data.excerpt?f(f({},e),{},{excerpt:this._data.excerpt}):e}},{key:"events",value:function(){var e=this;"undefined"!=typeof tinymce&&tinymce.activeEditor&&void 0!==tinymce.editors.excerpt&&tinymce.editors.excerpt.on("keyup change",(0,n.debounce)((function(){rankMathEditor.refresh("content")}),500)),tinymce.on("AddEditor",this.ensureIframe),this.debounceWCPrice=(0,n.debounce)(this.refreshWCPrice,500),this.elemPrice.on("input",this.debounceWCPrice),this.elemRegPrice.on("input",this.debounceWCPrice),this.ensureIframe(),l()&&this.excerpt.on("input",(0,n.debounce)((function(t){e._data.excerpt=t.currentTarget.value,(0,o.dispatch)("rank-math").updateSerpDescription(e._data.excerpt)}),500))}},{key:"refreshWCPrice",value:function(){s.setVariable("wc_price",this.getWooCommerceProductPrice()),(0,i.doAction)("rank_math_update_description_preview")}},{key:"getWooCommerceProductPrice",value:function(){var e=this.elemPrice.val()?this.elemPrice.val():this.elemRegPrice.val();return accounting.formatMoney(e,{symbol:woocommerce_admin_meta_boxes.currency_format_symbol,decimal:woocommerce_admin_meta_boxes.currency_format_decimal_sep,thousand:woocommerce_admin_meta_boxes.currency_format_thousand_sep,precision:woocommerce_admin_meta_boxes.currency_format_num_decimals,format:woocommerce_admin_meta_boxes.currency_format})}},{key:"ensureIframe",value:function(){var e=this;tinymce.editors&&!(0,n.isUndefined)(tinymce.editors.excerpt)&&tinymce.editors.excerpt.on("keyup change",(0,n.debounce)((function(){e._data.excerpt=tinymce.get("excerpt").getContent(),(0,o.dispatch)("rank-math").updateSerpDescription(e._data.excerpt)}),500))}}])&&y(t.prototype,c),a&&y(t,a),Object.defineProperty(t,"prototype",{writable:!1}),e}();r()((function(){new b}))}();