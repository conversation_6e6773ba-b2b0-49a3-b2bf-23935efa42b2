<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'MyThemeShop\\Admin\\List_Table' => $baseDir . '/src/admin/class-list-table.php',
    'MyThemeShop\\Admin\\Page' => $baseDir . '/src/admin/class-page.php',
    'MyThemeShop\\Database\\Clauses' => $baseDir . '/src/database/class-clauses.php',
    'MyThemeShop\\Database\\Database' => $baseDir . '/src/database/class-database.php',
    'MyThemeShop\\Database\\Escape' => $baseDir . '/src/database/class-escape.php',
    'MyThemeShop\\Database\\GroupBy' => $baseDir . '/src/database/class-groupby.php',
    'MyThemeShop\\Database\\Joins' => $baseDir . '/src/database/class-joins.php',
    'MyThemeShop\\Database\\OrderBy' => $baseDir . '/src/database/class-orderby.php',
    'MyThemeShop\\Database\\Query_Builder' => $baseDir . '/src/database/class-query-builder.php',
    'MyThemeShop\\Database\\Select' => $baseDir . '/src/database/class-select.php',
    'MyThemeShop\\Database\\Translate' => $baseDir . '/src/database/class-translate.php',
    'MyThemeShop\\Database\\Where' => $baseDir . '/src/database/class-where.php',
    'MyThemeShop\\Helpers\\Arr' => $baseDir . '/src/helpers/class-arr.php',
    'MyThemeShop\\Helpers\\Attachment' => $baseDir . '/src/helpers/class-attachment.php',
    'MyThemeShop\\Helpers\\Conditional' => $baseDir . '/src/helpers/class-conditional.php',
    'MyThemeShop\\Helpers\\DB' => $baseDir . '/src/helpers/class-db.php',
    'MyThemeShop\\Helpers\\HTML' => $baseDir . '/src/helpers/class-html.php',
    'MyThemeShop\\Helpers\\Param' => $baseDir . '/src/helpers/class-param.php',
    'MyThemeShop\\Helpers\\Str' => $baseDir . '/src/helpers/class-str.php',
    'MyThemeShop\\Helpers\\Url' => $baseDir . '/src/helpers/class-url.php',
    'MyThemeShop\\Helpers\\WordPress' => $baseDir . '/src/helpers/class-wordpress.php',
    'MyThemeShop\\Json_Manager' => $baseDir . '/src/class-json-manager.php',
    'MyThemeShop\\Notification' => $baseDir . '/src/class-notification.php',
    'MyThemeShop\\Notification_Center' => $baseDir . '/src/class-notification-center.php',
);
