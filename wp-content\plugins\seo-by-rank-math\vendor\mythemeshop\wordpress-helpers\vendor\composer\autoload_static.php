<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit65a0993a63c917989297698ea08a315e
{
    public static $prefixLengthsPsr4 = array (
        'M' => 
        array (
            'MyThemeShop\\Helpers\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'MyThemeShop\\Helpers\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'MyThemeShop\\Admin\\List_Table' => __DIR__ . '/../..' . '/src/admin/class-list-table.php',
        'MyThemeShop\\Admin\\Page' => __DIR__ . '/../..' . '/src/admin/class-page.php',
        'MyThemeShop\\Database\\Clauses' => __DIR__ . '/../..' . '/src/database/class-clauses.php',
        'MyThemeShop\\Database\\Database' => __DIR__ . '/../..' . '/src/database/class-database.php',
        'MyThemeShop\\Database\\Escape' => __DIR__ . '/../..' . '/src/database/class-escape.php',
        'MyThemeShop\\Database\\GroupBy' => __DIR__ . '/../..' . '/src/database/class-groupby.php',
        'MyThemeShop\\Database\\Joins' => __DIR__ . '/../..' . '/src/database/class-joins.php',
        'MyThemeShop\\Database\\OrderBy' => __DIR__ . '/../..' . '/src/database/class-orderby.php',
        'MyThemeShop\\Database\\Query_Builder' => __DIR__ . '/../..' . '/src/database/class-query-builder.php',
        'MyThemeShop\\Database\\Select' => __DIR__ . '/../..' . '/src/database/class-select.php',
        'MyThemeShop\\Database\\Translate' => __DIR__ . '/../..' . '/src/database/class-translate.php',
        'MyThemeShop\\Database\\Where' => __DIR__ . '/../..' . '/src/database/class-where.php',
        'MyThemeShop\\Helpers\\Arr' => __DIR__ . '/../..' . '/src/helpers/class-arr.php',
        'MyThemeShop\\Helpers\\Attachment' => __DIR__ . '/../..' . '/src/helpers/class-attachment.php',
        'MyThemeShop\\Helpers\\Conditional' => __DIR__ . '/../..' . '/src/helpers/class-conditional.php',
        'MyThemeShop\\Helpers\\DB' => __DIR__ . '/../..' . '/src/helpers/class-db.php',
        'MyThemeShop\\Helpers\\HTML' => __DIR__ . '/../..' . '/src/helpers/class-html.php',
        'MyThemeShop\\Helpers\\Param' => __DIR__ . '/../..' . '/src/helpers/class-param.php',
        'MyThemeShop\\Helpers\\Str' => __DIR__ . '/../..' . '/src/helpers/class-str.php',
        'MyThemeShop\\Helpers\\Url' => __DIR__ . '/../..' . '/src/helpers/class-url.php',
        'MyThemeShop\\Helpers\\WordPress' => __DIR__ . '/../..' . '/src/helpers/class-wordpress.php',
        'MyThemeShop\\Json_Manager' => __DIR__ . '/../..' . '/src/class-json-manager.php',
        'MyThemeShop\\Notification' => __DIR__ . '/../..' . '/src/class-notification.php',
        'MyThemeShop\\Notification_Center' => __DIR__ . '/../..' . '/src/class-notification-center.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit65a0993a63c917989297698ea08a315e::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit65a0993a63c917989297698ea08a315e::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit65a0993a63c917989297698ea08a315e::$classMap;

        }, null, ClassLoader::class);
    }
}
