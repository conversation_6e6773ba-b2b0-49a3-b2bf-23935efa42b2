(()=>{"use strict";var e,t={728:(e,t,n)=>{const r=window.wp.i18n,s=window.wp.element,a=window.wp.components,i=window.ReactJSXRuntime,o=({settings:e,onToggleChange:t,isSaving:n,strings:o})=>{const l=!0===window.wordpressMcpSettings?.featureApiAvailable||"1"===window.wordpressMcpSettings?.featureApiAvailable;(0,s.useEffect)((()=>{}),[l]);const c=!e.enabled||!l,d=l?o.enableFeaturesAdapterDescription||(0,r.__)("Enable or disable the WordPress Features Adapter. This option only works when MCP is enabled.","wordpress-mcp"):(0,s.createInterpolateElement)((0,r.__)("WordPress Feature API is not available. Please <a>install</a> and activate the WordPress Feature API plugin.","wordpress-mcp"),{a:(0,i.jsx)("a",{href:"https://github.com/Automattic/wp-feature-api",target:"_blank",rel:"noopener noreferrer"})});return(0,i.jsxs)(a.Card,{children:[(0,i.jsx)(a.CardHeader,{children:(0,i.jsx)("h2",{children:(0,r.__)("General Settings","wordpress-mcp")})}),(0,i.jsxs)(a.CardBody,{children:[(0,i.jsx)("div",{className:"setting-row",children:(0,i.jsx)(a.ToggleControl,{label:o.enableMcp||(0,r.__)("Enable MCP functionality","wordpress-mcp"),help:o.enableMcpDescription||(0,r.__)("Toggle to enable or disable the MCP plugin functionality.","wordpress-mcp"),checked:e.enabled,onChange:()=>t("enabled")})}),(0,i.jsx)("div",{className:"setting-row",children:(0,i.jsx)(a.ToggleControl,{label:o.enableFeaturesAdapter||(0,r.__)("Enable WordPress Features Adapter","wordpress-mcp"),help:d,checked:!!l&&e.features_adapter_enabled,onChange:()=>{l&&e.enabled&&t("features_adapter_enabled")},disabled:c})}),(0,i.jsx)("div",{className:"setting-row",children:(0,i.jsx)(a.ToggleControl,{label:o.enableCreateTools||(0,r.__)("Enable Create Tools","wordpress-mcp"),help:o.enableCreateToolsDescription||(0,r.__)("Allow create operations via tools.","wordpress-mcp"),checked:e.enable_create_tools,onChange:()=>t("enable_create_tools"),disabled:!e.enabled})}),(0,i.jsx)("div",{className:"setting-row",children:(0,i.jsx)(a.ToggleControl,{label:o.enableUpdateTools||(0,r.__)("Enable Update Tools","wordpress-mcp"),help:o.enableUpdateToolsDescription||(0,r.__)("Allow update operations via tools.","wordpress-mcp"),checked:e.enable_update_tools,onChange:()=>t("enable_update_tools"),disabled:!e.enabled})}),(0,i.jsx)("div",{className:"setting-row",children:(0,i.jsx)(a.ToggleControl,{label:o.enableDeleteTools||(0,r.__)("Enable Delete Tools","wordpress-mcp"),help:o.enableDeleteToolsDescription||(0,r.__)("⚠️ CAUTION: Allow deletion operations via tools.","wordpress-mcp"),checked:e.enable_delete_tools,onChange:()=>t("enable_delete_tools"),disabled:!e.enabled})}),(0,i.jsx)("div",{className:"setting-row",children:(0,i.jsx)(a.ToggleControl,{label:o.enableRestApiCrudTools||(0,r.__)("🧪 Enable REST API CRUD Tools (EXPERIMENTAL)","wordpress-mcp"),help:o.enableRestApiCrudToolsDescription||(0,r.__)("⚠️ EXPERIMENTAL FEATURE: Enable or disable the generic REST API CRUD tools for accessing WordPress endpoints. This is experimental functionality that may change or be removed in future versions. When enabled, all tools that are a rest_alias or have the disabled_by_rest_crud flag will be disabled.","wordpress-mcp"),checked:e.enable_rest_api_crud_tools,onChange:()=>t("enable_rest_api_crud_tools"),disabled:!e.enabled})})]}),n&&(0,i.jsx)(a.CardFooter,{children:(0,i.jsxs)("div",{className:"settings-saving-indicator",children:[(0,i.jsx)(a.Spinner,{}),(0,r.__)("Saving...","wordpress-mcp")]})})]})},l=window.wp.apiFetch;var c=n.n(l);const d=()=>{const[e,t]=(0,s.useState)([]),[n,o]=(0,s.useState)(!0),[l,d]=(0,s.useState)(null),[p,u]=(0,s.useState)(!1);return(0,s.useEffect)((()=>{(async()=>{try{o(!0);const e=await c()({path:"/wp/v2/wpmcp",method:"POST",data:{jsonrpc:"2.0",method:"tools/list/all",params:{}}});e&&e.tools?t(e.tools):d((0,r.__)("Failed to load tools data","wordpress-mcp"))}catch(e){d((0,r.__)("Error loading tools: ","wordpress-mcp")+e.message)}finally{o(!1)}})()}),[]),(0,i.jsxs)(a.Card,{children:[(0,i.jsx)(a.CardHeader,{children:(0,i.jsx)("h2",{children:(0,r.__)("Registered Tools","wordpress-mcp")})}),(0,i.jsxs)(a.CardBody,{children:[(0,i.jsx)("p",{children:(0,r.__)("List of all registered tools in the system. Use the toggles to enable or disable individual tools.","wordpress-mcp")}),n?(0,i.jsxs)("div",{className:"wordpress-mcp-loading",children:[(0,i.jsx)(a.Spinner,{}),(0,i.jsx)("p",{children:(0,r.__)("Loading tools...","wordpress-mcp")})]}):l?(0,i.jsx)("div",{className:"wordpress-mcp-error",children:(0,i.jsx)("p",{children:l})}):0===e.length?(0,i.jsx)("p",{children:(0,r.__)("No tools are currently registered.","wordpress-mcp")}):(0,i.jsxs)("table",{className:"wordpress-mcp-table",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{children:(0,r.__)("Name","wordpress-mcp")}),(0,i.jsx)("th",{children:(0,r.__)("Description","wordpress-mcp")}),(0,i.jsx)("th",{children:(0,r.__)("Functionality Type","wordpress-mcp")}),(0,i.jsx)("th",{children:(0,r.__)("Status","wordpress-mcp")})]})}),(0,i.jsx)("tbody",{children:e.map((e=>(0,i.jsxs)("tr",{children:[(0,i.jsx)("td",{children:(0,i.jsx)("strong",{children:e.name})}),(0,i.jsx)("td",{children:e.description}),(0,i.jsx)("td",{children:e.type}),(0,i.jsx)("td",{children:(0,i.jsx)(a.ToggleControl,{checked:e.tool_enabled&&e.tool_type_enabled,onChange:n=>(async(e,n)=>{try{u(!0),t((t=>t.map((t=>t.name===e?{...t,tool_enabled:n}:t))));const r=new FormData;r.append("action","wordpress_mcp_toggle_tool"),r.append("nonce",window.wordpressMcpSettings.nonce),r.append("tool",e),r.append("tool_enabled",n);const s=await fetch(ajaxurl,{method:"POST",body:r,credentials:"same-origin"}),a=await s.json();if(!a.success)throw new Error(a.data.message||window.wordpressMcpSettings.strings.settingsError);d(null)}catch(r){t((t=>t.map((t=>t.name===e?{...t,tool_enabled:!n}:t)))),d(r.message||window.wordpressMcpSettings.strings.settingsError),console.error("Error saving tool state:",r)}finally{u(!1)}})(e.name,n),disabled:p||!e.tool_type_enabled,label:e.tool_enabled&&e.tool_type_enabled?(0,r.__)("Enabled","wordpress-mcp"):(0,r.__)("Disabled","wordpress-mcp")})})]},e.name)))})]})]})]})},p=e=>{try{if("string"==typeof e)try{const t=JSON.parse(e);return JSON.stringify(t,null,2)}catch(t){return e}if(e&&"object"==typeof e&&e.text&&"string"==typeof e.text)try{const t=JSON.parse(e.text);return JSON.stringify(t,null,2)}catch(e){}return JSON.stringify(e,null,2)}catch(e){return"Error formatting JSON: "+e.message}},u=()=>{const[e,t]=(0,s.useState)([]),[n,o]=(0,s.useState)(!0),[l,d]=(0,s.useState)(null),[u,h]=(0,s.useState)(null),[m,g]=(0,s.useState)(null),[f,w]=(0,s.useState)(!1),[x,b]=(0,s.useState)(null),[k,_]=(0,s.useState)(null);(0,s.useEffect)((()=>{(async()=>{try{o(!0);const e=await c()({path:"/wp/v2/wpmcp",method:"POST",data:{jsonrpc:"2.0",method:"resources/list",params:{}}});e&&e.resources?t(e.resources):d((0,r.__)("Failed to load resources data","wordpress-mcp"))}catch(e){d((0,r.__)("Error loading resources: ","wordpress-mcp")+e.message)}finally{o(!1)}})()}),[]);return(0,i.jsxs)(a.Card,{children:[(0,i.jsx)(a.CardHeader,{children:(0,i.jsx)("h2",{children:(0,r.__)("Available Resources","wordpress-mcp")})}),(0,i.jsxs)(a.CardBody,{children:[(0,i.jsx)("p",{children:(0,r.__)("List of all available resources in the system.","wordpress-mcp")}),n?(0,i.jsxs)("div",{className:"wordpress-mcp-loading",children:[(0,i.jsx)(a.Spinner,{}),(0,i.jsx)("p",{children:(0,r.__)("Loading resources...","wordpress-mcp")})]}):l?(0,i.jsx)("div",{className:"wordpress-mcp-error",children:(0,i.jsx)("p",{children:l})}):0===e.length?(0,i.jsx)("p",{children:(0,r.__)("No resources are currently available.","wordpress-mcp")}):(0,i.jsxs)("table",{className:"wordpress-mcp-table",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{children:(0,r.__)("Name","wordpress-mcp")}),(0,i.jsx)("th",{children:(0,r.__)("URI","wordpress-mcp")}),(0,i.jsx)("th",{children:(0,r.__)("Description","wordpress-mcp")}),(0,i.jsx)("th",{children:(0,r.__)("Actions","wordpress-mcp")})]})}),(0,i.jsx)("tbody",{children:e.map((e=>(0,i.jsxs)("tr",{children:[(0,i.jsx)("td",{children:(0,i.jsx)("strong",{children:e.name})}),(0,i.jsx)("td",{children:e.uri}),(0,i.jsx)("td",{children:e.description||"-"}),(0,i.jsx)("td",{children:(0,i.jsx)(a.Button,{variant:"secondary",onClick:()=>(e=>{h(e),(async e=>{try{w(!0),b(null),_(null);const t=await c()({path:"/wp/v2/wpmcp",method:"POST",data:{jsonrpc:"2.0",method:"resources/read",uri:e.uri}});if(t&&t.contents){if(g(t.contents),t.contents.text&&"string"==typeof t.contents.text)try{const e=JSON.parse(t.contents.text);_(e)}catch(e){console.log("Failed to parse JSON text:",e)}}else b((0,r.__)("Failed to load resource details","wordpress-mcp"))}catch(e){b((0,r.__)("Error loading resource details: ","wordpress-mcp")+e.message)}finally{w(!1)}})(e)})(e),children:(0,r.__)("View","wordpress-mcp")})})]},e.name)))})]}),u&&(0,i.jsx)(a.Modal,{title:(0,r.__)("Resource Details","wordpress-mcp"),onRequestClose:()=>{h(null),g(null),_(null),b(null)},className:"wordpress-mcp-resource-modal",children:f?(0,i.jsxs)("div",{className:"wordpress-mcp-loading",children:[(0,i.jsx)(a.Spinner,{}),(0,i.jsx)("p",{children:(0,r.__)("Loading resource details...","wordpress-mcp")})]}):x?(0,i.jsx)("div",{className:"wordpress-mcp-error",children:(0,i.jsx)("p",{children:x})}):m?(0,i.jsxs)("div",{className:"wordpress-mcp-resource-details",children:[(0,i.jsx)("h3",{children:m.name||u.name}),(0,i.jsxs)("div",{className:"wordpress-mcp-resource-json",children:[(0,i.jsx)("h4",{children:(0,r.__)("Full Resource Data","wordpress-mcp")}),(0,i.jsx)("pre",{className:"wordpress-mcp-json-display",children:p(m)})]})]}):(0,i.jsx)("p",{children:(0,r.__)("No details available for this resource.","wordpress-mcp")})})]})]})},h=()=>{const[e,t]=(0,s.useState)([]),[n,o]=(0,s.useState)(!0),[l,d]=(0,s.useState)(null),[p,u]=(0,s.useState)(null),[h,m]=(0,s.useState)(!1),[g,f]=(0,s.useState)(!1),[w,x]=(0,s.useState)(null);(0,s.useEffect)((()=>{(async()=>{try{o(!0);const e=await c()({path:"/wp/v2/wpmcp",method:"POST",data:{jsonrpc:"2.0",method:"prompts/list",params:{}}});e&&e.prompts?t(e.prompts):d((0,r.__)("Failed to load prompts data","wordpress-mcp"))}catch(e){d((0,r.__)("Error loading prompts: ","wordpress-mcp")+e.message)}finally{o(!1)}})()}),[]);const b=()=>{m(!1),u(null),x(null)};return(0,i.jsxs)(a.Card,{children:[(0,i.jsx)(a.CardHeader,{children:(0,i.jsx)("h2",{children:(0,r.__)("Available Prompts","wordpress-mcp")})}),(0,i.jsxs)(a.CardBody,{children:[(0,i.jsx)("p",{children:(0,r.__)("List of all available prompts in the system.","wordpress-mcp")}),n?(0,i.jsxs)("div",{className:"wordpress-mcp-loading",children:[(0,i.jsx)(a.Spinner,{}),(0,i.jsx)("p",{children:(0,r.__)("Loading prompts...","wordpress-mcp")})]}):l?(0,i.jsx)("div",{className:"wordpress-mcp-error",children:(0,i.jsx)("p",{children:l})}):0===e.length?(0,i.jsx)("p",{children:(0,r.__)("No prompts are currently available.","wordpress-mcp")}):(0,i.jsxs)("table",{className:"wordpress-mcp-table",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{children:(0,r.__)("Name","wordpress-mcp")}),(0,i.jsx)("th",{children:(0,r.__)("Description","wordpress-mcp")}),(0,i.jsx)("th",{children:(0,r.__)("Actions","wordpress-mcp")})]})}),(0,i.jsx)("tbody",{children:e.map((e=>(0,i.jsxs)("tr",{children:[(0,i.jsx)("td",{children:(0,i.jsx)("strong",{children:e.name})}),(0,i.jsx)("td",{children:e.description||"-"}),(0,i.jsx)("td",{children:(0,i.jsx)(a.Button,{variant:"secondary",onClick:()=>(async e=>{try{f(!0),x(null);const t=await c()({path:"/wp/v2/wpmcp",method:"POST",data:{jsonrpc:"2.0",method:"prompts/get",name:e.name}});if(t&&(t.description||t.messages)){const n={name:e.name,description:t.description||"",content:t.messages&&t.messages.length>0&&t.messages[0].content.text||"",parameters:t.parameters||{}};u(n),m(!0),console.log("Setting showPromptDetails to true",n)}else x((0,r.__)("Failed to load prompt details","wordpress-mcp"))}catch(e){x((0,r.__)("Error loading prompt details: ","wordpress-mcp")+e.message)}finally{f(!1)}})(e),children:(0,r.__)("View","wordpress-mcp")})})]},e.id)))})]}),h&&(0,i.jsxs)(a.Modal,{title:p?p.name:(0,r.__)("Prompt Details","wordpress-mcp"),onRequestClose:b,className:"wordpress-mcp-prompt-modal",children:[g?(0,i.jsxs)("div",{className:"wordpress-mcp-loading",children:[(0,i.jsx)(a.Spinner,{}),(0,i.jsx)("p",{children:(0,r.__)("Loading prompt details...","wordpress-mcp")})]}):w?(0,i.jsx)("div",{className:"wordpress-mcp-error",children:(0,i.jsx)("p",{children:w})}):p?(0,i.jsxs)("div",{className:"wordpress-mcp-prompt-details-content",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:(0,r.__)("Description:","wordpress-mcp")})," ",p.description||(0,r.__)("No description available","wordpress-mcp")]}),p.content&&(0,i.jsxs)("div",{className:"wordpress-mcp-prompt-content",children:[(0,i.jsx)("strong",{children:(0,r.__)("Content:","wordpress-mcp")}),(0,i.jsx)("div",{children:p.content})]}),p.parameters&&(0,i.jsxs)("div",{className:"wordpress-mcp-prompt-parameters",children:[(0,i.jsx)("strong",{children:(0,r.__)("Parameters:","wordpress-mcp")}),(0,i.jsx)("pre",{children:JSON.stringify(p.parameters,null,2)})]})]}):null,(0,i.jsx)("div",{className:"wordpress-mcp-modal-footer",children:(0,i.jsx)(a.Button,{variant:"primary",onClick:b,children:(0,r.__)("Close","wordpress-mcp")})})]})]})]})},m=window.React,g={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function f(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}const w={date:f({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:f({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:f({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},x={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function b(e){return(t,n)=>{let r;if("formatting"===(n?.context?String(n.context):"standalone")&&e.formattingValues){const t=e.defaultFormattingWidth||e.defaultWidth,s=n?.width?String(n.width):t;r=e.formattingValues[s]||e.formattingValues[t]}else{const t=e.defaultWidth,s=n?.width?String(n.width):e.defaultWidth;r=e.values[s]||e.values[t]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function k(e){return(t,n={})=>{const r=n.width,s=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],a=t.match(s);if(!a)return null;const i=a[0],o=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],l=Array.isArray(o)?function(e){for(let t=0;t<e.length;t++)if(e[t].test(i))return t}(o):function(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&e[t].test(i))return t}(o);let c;return c=e.valueCallback?e.valueCallback(l):l,c=n.valueCallback?n.valueCallback(c):c,{value:c,rest:t.slice(i.length)}}}var _;const y={code:"en-US",formatDistance:(e,t,n)=>{let r;const s=g[e];return r="string"==typeof s?s:1===t?s.one:s.other.replace("{{count}}",t.toString()),n?.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r},formatLong:w,formatRelative:(e,t,n,r)=>x[e],localize:{ordinalNumber:(e,t)=>{const n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:b({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:b({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:b({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:b({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:b({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(_={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)},(e,t={})=>{const n=e.match(_.matchPattern);if(!n)return null;const r=n[0],s=e.match(_.parsePattern);if(!s)return null;let a=_.valueCallback?_.valueCallback(s[0]):s[0];return a=t.valueCallback?t.valueCallback(a):a,{value:a,rest:e.slice(r.length)}}),era:k({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:k({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:k({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:k({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:k({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};let j={};function S(){return j}Math.pow(10,8);const v=6048e5;function T(e){const t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new e.constructor(+e):"number"==typeof e||"[object Number]"===t||"string"==typeof e||"[object String]"===t?new Date(e):new Date(NaN)}function C(e){const t=T(e);return t.setHours(0,0,0,0),t}function P(e){const t=T(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function M(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}function R(e){const t=T(e);return function(e,t){const n=C(e),r=C(t),s=+n-P(n),a=+r-P(r);return Math.round((s-a)/864e5)}(t,function(e){const t=T(e),n=M(e,0);return n.setFullYear(t.getFullYear(),0,1),n.setHours(0,0,0,0),n}(t))+1}function $(e,t){const n=S(),r=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,s=T(e),a=s.getDay(),i=(a<r?7:0)+a-r;return s.setDate(s.getDate()-i),s.setHours(0,0,0,0),s}function N(e){return $(e,{weekStartsOn:1})}function A(e){const t=T(e),n=t.getFullYear(),r=M(e,0);r.setFullYear(n+1,0,4),r.setHours(0,0,0,0);const s=N(r),a=M(e,0);a.setFullYear(n,0,4),a.setHours(0,0,0,0);const i=N(a);return t.getTime()>=s.getTime()?n+1:t.getTime()>=i.getTime()?n:n-1}function E(e){const t=T(e),n=+N(t)-+function(e){const t=A(e),n=M(e,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),N(n)}(t);return Math.round(n/v)+1}function z(e,t){const n=T(e),r=n.getFullYear(),s=S(),a=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??s.firstWeekContainsDate??s.locale?.options?.firstWeekContainsDate??1,i=M(e,0);i.setFullYear(r+1,0,a),i.setHours(0,0,0,0);const o=$(i,t),l=M(e,0);l.setFullYear(r,0,a),l.setHours(0,0,0,0);const c=$(l,t);return n.getTime()>=o.getTime()?r+1:n.getTime()>=c.getTime()?r:r-1}function D(e,t){const n=T(e),r=+$(n,t)-+function(e,t){const n=S(),r=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,s=z(e,t),a=M(e,0);return a.setFullYear(s,0,r),a.setHours(0,0,0,0),$(a,t)}(n,t);return Math.round(r/v)+1}function O(e,t){return(e<0?"-":"")+Math.abs(e).toString().padStart(t,"0")}const L={y(e,t){const n=e.getFullYear(),r=n>0?n:1-n;return O("yy"===t?r%100:r,t.length)},M(e,t){const n=e.getMonth();return"M"===t?String(n+1):O(n+1,2)},d:(e,t)=>O(e.getDate(),t.length),a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(e,t)=>O(e.getHours()%12||12,t.length),H:(e,t)=>O(e.getHours(),t.length),m:(e,t)=>O(e.getMinutes(),t.length),s:(e,t)=>O(e.getSeconds(),t.length),S(e,t){const n=t.length,r=e.getMilliseconds();return O(Math.trunc(r*Math.pow(10,n-3)),t.length)}},B={G:function(e,t,n){const r=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if("yo"===t){const t=e.getFullYear(),r=t>0?t:1-t;return n.ordinalNumber(r,{unit:"year"})}return L.y(e,t)},Y:function(e,t,n,r){const s=z(e,r),a=s>0?s:1-s;return"YY"===t?O(a%100,2):"Yo"===t?n.ordinalNumber(a,{unit:"year"}):O(a,t.length)},R:function(e,t){return O(A(e),t.length)},u:function(e,t){return O(e.getFullYear(),t.length)},Q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return O(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return O(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){const r=e.getMonth();switch(t){case"M":case"MM":return L.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){const r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return O(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){const s=D(e,r);return"wo"===t?n.ordinalNumber(s,{unit:"week"}):O(s,t.length)},I:function(e,t,n){const r=E(e);return"Io"===t?n.ordinalNumber(r,{unit:"week"}):O(r,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getDate(),{unit:"date"}):L.d(e,t)},D:function(e,t,n){const r=R(e);return"Do"===t?n.ordinalNumber(r,{unit:"dayOfYear"}):O(r,t.length)},E:function(e,t,n){const r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){const s=e.getDay(),a=(s-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(a);case"ee":return O(a,2);case"eo":return n.ordinalNumber(a,{unit:"day"});case"eee":return n.day(s,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(s,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(s,{width:"short",context:"formatting"});default:return n.day(s,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){const s=e.getDay(),a=(s-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(a);case"cc":return O(a,t.length);case"co":return n.ordinalNumber(a,{unit:"day"});case"ccc":return n.day(s,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(s,{width:"narrow",context:"standalone"});case"cccccc":return n.day(s,{width:"short",context:"standalone"});default:return n.day(s,{width:"wide",context:"standalone"})}},i:function(e,t,n){const r=e.getDay(),s=0===r?7:r;switch(t){case"i":return String(s);case"ii":return O(s,t.length);case"io":return n.ordinalNumber(s,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){const r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,t,n){const r=e.getHours();let s;switch(s=12===r?"noon":0===r?"midnight":r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(s,{width:"narrow",context:"formatting"});default:return n.dayPeriod(s,{width:"wide",context:"formatting"})}},B:function(e,t,n){const r=e.getHours();let s;switch(s=r>=17?"evening":r>=12?"afternoon":r>=4?"morning":"night",t){case"B":case"BB":case"BBB":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(s,{width:"narrow",context:"formatting"});default:return n.dayPeriod(s,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),n.ordinalNumber(t,{unit:"hour"})}return L.h(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getHours(),{unit:"hour"}):L.H(e,t)},K:function(e,t,n){const r=e.getHours()%12;return"Ko"===t?n.ordinalNumber(r,{unit:"hour"}):O(r,t.length)},k:function(e,t,n){let r=e.getHours();return 0===r&&(r=24),"ko"===t?n.ordinalNumber(r,{unit:"hour"}):O(r,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):L.m(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getSeconds(),{unit:"second"}):L.s(e,t)},S:function(e,t){return L.S(e,t)},X:function(e,t,n){const r=e.getTimezoneOffset();if(0===r)return"Z";switch(t){case"X":return W(r);case"XXXX":case"XX":return I(r);default:return I(r,":")}},x:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"x":return W(r);case"xxxx":case"xx":return I(r);default:return I(r,":")}},O:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+q(r,":");default:return"GMT"+I(r,":")}},z:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+q(r,":");default:return"GMT"+I(r,":")}},t:function(e,t,n){return O(Math.trunc(e.getTime()/1e3),t.length)},T:function(e,t,n){return O(e.getTime(),t.length)}};function q(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),s=Math.trunc(r/60),a=r%60;return 0===a?n+String(s):n+String(s)+t+O(a,2)}function W(e,t){return e%60==0?(e>0?"-":"+")+O(Math.abs(e)/60,2):I(e,t)}function I(e,t=""){const n=e>0?"-":"+",r=Math.abs(e);return n+O(Math.trunc(r/60),2)+t+O(r%60,2)}const F=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},H=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},Y={p:H,P:(e,t)=>{const n=e.match(/(P+)(p+)?/)||[],r=n[1],s=n[2];if(!s)return F(e,t);let a;switch(r){case"P":a=t.dateTime({width:"short"});break;case"PP":a=t.dateTime({width:"medium"});break;case"PPP":a=t.dateTime({width:"long"});break;default:a=t.dateTime({width:"full"})}return a.replace("{{date}}",F(r,t)).replace("{{time}}",H(s,t))}},G=/^D+$/,Q=/^Y+$/,Z=["D","DD","YY","YYYY"];function X(e){if(!(t=e,t instanceof Date||"object"==typeof t&&"[object Date]"===Object.prototype.toString.call(t)||"number"==typeof e))return!1;var t;const n=T(e);return!isNaN(Number(n))}const J=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,U=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,V=/^'([^]*?)'?$/,K=/''/g,ee=/[a-zA-Z]/;function te(e){const t=e.match(V);return t?t[1].replace(K,"'"):e}const ne=()=>{const[e,t]=(0,m.useState)(null),[n,s]=(0,m.useState)([]),[o,l]=(0,m.useState)(!1),[d,p]=(0,m.useState)(null),[u,h]=(0,m.useState)(!1),[g,f]=(0,m.useState)(3600),w=[{label:(0,r.__)("1 hour","wordpress-mcp"),value:3600},{label:(0,r.__)("2 hours","wordpress-mcp"),value:7200},{label:(0,r.__)("6 hours","wordpress-mcp"),value:21600},{label:(0,r.__)("12 hours","wordpress-mcp"),value:43200},{label:(0,r.__)("24 hours (1 day)","wordpress-mcp"),value:86400}];(0,m.useEffect)((()=>{x()}),[]),(0,m.useEffect)((()=>{if(u){const e=setTimeout((()=>{h(!1)}),2e3);return()=>clearTimeout(e)}}),[u]);const x=async()=>{try{const e=await c()({path:"/jwt-auth/v1/tokens",method:"GET",includeCredentials:!0});s(e)}catch(e){p((0,r.__)("Error fetching tokens","wordpress-mcp"))}},b=e=>{const t=document.createElement("textarea");t.value=e,t.style.position="fixed",t.style.left="-999999px",t.style.top="-999999px",document.body.appendChild(t),t.focus(),t.select();try{document.execCommand("copy"),h(!0)}catch(e){p((0,r.__)("Failed to copy to clipboard","wordpress-mcp"))}document.body.removeChild(t)},k=e=>function(e,t,n){const r=S(),s=n?.locale??r.locale??y,a=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,i=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,o=T(e);if(!X(o))throw new RangeError("Invalid time value");let l=t.match(U).map((e=>{const t=e[0];return"p"===t||"P"===t?(0,Y[t])(e,s.formatLong):e})).join("").match(J).map((e=>{if("''"===e)return{isToken:!1,value:"'"};const t=e[0];if("'"===t)return{isToken:!1,value:te(e)};if(B[t])return{isToken:!0,value:e};if(t.match(ee))throw new RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}}));s.localize.preprocessor&&(l=s.localize.preprocessor(o,l));const c={firstWeekContainsDate:a,weekStartsOn:i,locale:s};return l.map((r=>{if(!r.isToken)return r.value;const a=r.value;return(!n?.useAdditionalWeekYearTokens&&function(e){return Q.test(e)}(a)||!n?.useAdditionalDayOfYearTokens&&function(e){return G.test(e)}(a))&&function(e,t,n){const r=function(e,t,n){const r="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(e,t,n);if(console.warn(r),Z.includes(e))throw new RangeError(r)}(a,t,String(e)),(0,B[a[0]])(o,a,s.localize,c)})).join("")}(new Date(1e3*e),"PPpp");return(0,i.jsx)("div",{className:"mcp-settings-tab",children:(0,i.jsx)(a.Card,{children:(0,i.jsxs)(a.CardBody,{children:[(0,i.jsx)("h2",{children:(0,r.__)("Authentication Tokens","wordpress-mcp")}),(0,i.jsxs)("div",{className:"mcp-info-section",style:{marginBottom:"24px",padding:"16px",backgroundColor:"#f9f9f9",border:"1px solid #ddd",borderRadius:"4px"},children:[(0,i.jsx)("h3",{children:(0,r.__)("What are Authentication Tokens?","wordpress-mcp")}),(0,i.jsx)("p",{children:(0,r.__)("MCP authentication tokens are secure, temporary credentials that allow external MCP clients to the WordPress MCP server","wordpress-mcp")}),(0,i.jsx)("p",{children:(0,r.__)("These tokens are implemented using JWT (JSON Web Tokens), providing secure and stateless authentication.","wordpress-mcp")}),(0,i.jsx)("h4",{children:(0,r.__)("How They Work","wordpress-mcp")}),(0,i.jsxs)("ul",{style:{marginLeft:"20px"},children:[(0,i.jsx)("li",{children:(0,r.__)("Tokens are generated with your current user permissions and expire automatically","wordpress-mcp")}),(0,i.jsx)("li",{children:(0,r.__)("They provide access to MCP route endpoints for your WordPress content","wordpress-mcp")}),(0,i.jsx)("li",{children:(0,r.__)("Each token is unique and can be revoked individually if compromised","wordpress-mcp")}),(0,i.jsx)("li",{children:(0,r.__)("Tokens automatically inherit your user role and capabilities","wordpress-mcp")})]}),(0,i.jsx)("h4",{children:(0,r.__)("Security Best Practices","wordpress-mcp")}),(0,i.jsxs)("div",{style:{padding:"12px",backgroundColor:"#fff3cd",border:"1px solid #ffeaa7",borderRadius:"4px",marginBottom:"12px"},children:[(0,i.jsx)("strong",{children:(0,r.__)("⚠️ Important Security Notes:","wordpress-mcp")}),(0,i.jsxs)("ul",{style:{marginLeft:"20px",marginTop:"8px"},children:[(0,i.jsx)("li",{children:(0,r.__)("Never share tokens in public repositories, emails, or chat messages","wordpress-mcp")}),(0,i.jsx)("li",{children:(0,r.__)("Use the shortest expiration time that meets your needs","wordpress-mcp")}),(0,i.jsx)("li",{children:(0,r.__)("Revoke tokens immediately if you suspect they may be compromised","wordpress-mcp")}),(0,i.jsx)("li",{children:(0,r.__)("Regularly review and clean up unused or expired tokens","wordpress-mcp")})]})]}),(0,i.jsxs)("div",{style:{padding:"12px",backgroundColor:"#e8f4fd",border:"1px solid #b3d9ff",borderRadius:"4px",marginTop:"12px",marginBottom:"12px"},children:[(0,i.jsx)("strong",{children:(0,r.__)("ℹ️ Important Note:","wordpress-mcp")}),(0,i.jsx)("p",{style:{marginTop:"8px",marginBottom:"0"},children:(0,r.__)("These tokens are exclusively for MCP protocol access (stdio and streamable/HTTP). They will not work with WordPress REST API endpoints or other authentication systems.","wordpress-mcp")})]}),(0,i.jsx)("p",{style:{marginTop:"16px",fontStyle:"italic",color:"#666"},children:(0,r.__)("Remember: These tokens provide access based on your user permissions. If you can see it in WordPress, applications using your token can see it too.","wordpress-mcp")})]}),(0,i.jsx)("p",{children:(0,r.__)("Generate tokens below to allow secure access to your WordPress content through the MCP protocol.","wordpress-mcp")}),(0,i.jsx)("div",{className:"mcp-form-field",children:(0,i.jsx)(a.SelectControl,{label:(0,r.__)("Token Duration","wordpress-mcp"),value:g,options:w,onChange:e=>f(parseInt(e)),help:(0,r.__)("Choose how long the token will remain valid","wordpress-mcp")})}),(0,i.jsx)("div",{className:"mcp-form-field",children:(0,i.jsx)(a.Button,{isPrimary:!0,onClick:async()=>{l(!0),p(null);try{const e=await c()({path:"/jwt-auth/v1/token",method:"POST",data:{expires_in:g},includeCredentials:!0});t(e),x()}catch(e){p(e.message||(0,r.__)("Error generating token","wordpress-mcp"))}finally{l(!1)}},isBusy:o,disabled:o,children:(0,r.__)("Generate New Token","wordpress-mcp")})}),d&&(0,i.jsx)("div",{className:"mcp-error-message",children:d}),e&&(0,i.jsx)("div",{className:"mcp-token-section",children:(0,i.jsxs)("div",{className:"mcp-token-container",children:[(0,i.jsx)("h3",{children:(0,r.__)("Generated Token","wordpress-mcp")}),(0,i.jsx)(a.TextareaControl,{readOnly:!0,value:e.token,rows:4}),(0,i.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"10px"},children:[(0,i.jsx)(a.Button,{isSecondary:!0,onClick:()=>{return t=e.token,void(navigator.clipboard&&window.isSecureContext?navigator.clipboard.writeText(t).then((()=>{h(!0)}),(()=>{b(t)})):b(t));var t},children:(0,r.__)("Copy","wordpress-mcp")}),u&&(0,i.jsx)("span",{style:{color:"#00a32a"},children:(0,r.__)("Token copied!","wordpress-mcp")})]}),(0,i.jsxs)("p",{className:"description",children:[e.expires_in?`${(0,r.__)("Expires in","wordpress-mcp")} ${(e=>{const t=Math.floor(e/3600);if(t>=24){const e=Math.floor(t/24);return 1===e?(0,r.__)("1 day","wordpress-mcp"):`${e} ${(0,r.__)("days","wordpress-mcp")}`}return 1===t?(0,r.__)("1 hour","wordpress-mcp"):`${t} ${(0,r.__)("hours","wordpress-mcp")}`})(e.expires_in)}`:(0,r.__)("Expires in 1 hour","wordpress-mcp"),e.expires_at&&(0,i.jsxs)("span",{children:[" (",k(e.expires_at),")"]})]})]})}),(0,i.jsxs)("div",{className:"mcp-tokens-list",children:[(0,i.jsx)("h3",{children:(0,r.__)("Your Active Tokens","wordpress-mcp")}),(0,i.jsxs)("table",{className:"wp-list-table widefat fixed striped",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{children:(0,r.__)("User","wordpress-mcp")}),(0,i.jsx)("th",{children:(0,r.__)("Issued At","wordpress-mcp")}),(0,i.jsx)("th",{children:(0,r.__)("Expires At","wordpress-mcp")}),(0,i.jsx)("th",{children:(0,r.__)("Status","wordpress-mcp")}),(0,i.jsx)("th",{children:(0,r.__)("Actions","wordpress-mcp")})]})}),(0,i.jsx)("tbody",{children:n.map((e=>(0,i.jsxs)("tr",{children:[(0,i.jsxs)("td",{children:[e.user.display_name,(0,i.jsx)("br",{}),(0,i.jsx)("small",{style:{color:"#666"},children:e.user.username})]}),(0,i.jsx)("td",{children:k(e.issued_at)}),(0,i.jsx)("td",{children:k(e.expires_at)}),(0,i.jsx)("td",{children:e.revoked?(0,r.__)("Revoked","wordpress-mcp"):e.is_expired?(0,r.__)("Expired","wordpress-mcp"):(0,r.__)("Active","wordpress-mcp")}),(0,i.jsx)("td",{children:!e.revoked&&!e.is_expired&&(0,i.jsx)(a.Button,{isDestructive:!0,isSmall:!0,onClick:()=>(async e=>{try{await c()({path:"/jwt-auth/v1/revoke",method:"POST",data:{jti:e},includeCredentials:!0}),x()}catch(e){p(e.message||(0,r.__)("Error revoking token","wordpress-mcp"))}})(e.jti),children:(0,r.__)("Revoke","wordpress-mcp")})})]},e.jti)))})]})]})]})})})};var re={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function se(e){re=e}var ae={exec:()=>null};function ie(e,t=""){let n="string"==typeof e?e:e.source;const r={replace:(e,t)=>{let s="string"==typeof t?t:t.source;return s=s.replace(oe.caret,"$1"),n=n.replace(e,s),r},getRegex:()=>new RegExp(n,t)};return r}var oe={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[\t ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},le=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,ce=/(?:[*+-]|\d{1,9}[.)])/,de=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,pe=ie(de).replace(/bull/g,ce).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),ue=ie(de).replace(/bull/g,ce).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),he=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,me=/(?!\s*\])(?:\\.|[^\[\]\\])+/,ge=ie(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",me).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),fe=ie(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,ce).getRegex(),we="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",xe=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,be=ie("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$))","i").replace("comment",xe).replace("tag",we).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),ke=ie(he).replace("hr",le).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",we).getRegex(),_e={blockquote:ie(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",ke).getRegex(),code:/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,def:ge,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:le,html:be,lheading:pe,list:fe,newline:/^(?:[ \t]*(?:\n|$))+/,paragraph:ke,table:ae,text:/^[^\n]+/},ye=ie("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",le).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}\t)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",we).getRegex(),je={..._e,lheading:ue,table:ye,paragraph:ie(he).replace("hr",le).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",ye).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",we).getRegex()},Se={..._e,html:ie("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",xe).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ae,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:ie(he).replace("hr",le).replace("heading"," *#{1,6} *[^\n]").replace("lheading",pe).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},ve=/^( {2,}|\\)\n(?!\s*$)/,Te=/[\p{P}\p{S}]/u,Ce=/[\s\p{P}\p{S}]/u,Pe=/[^\s\p{P}\p{S}]/u,Me=ie(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,Ce).getRegex(),Re=/(?!~)[\p{P}\p{S}]/u,$e=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,Ne=ie($e,"u").replace(/punct/g,Te).getRegex(),Ae=ie($e,"u").replace(/punct/g,Re).getRegex(),Ee="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",ze=ie(Ee,"gu").replace(/notPunctSpace/g,Pe).replace(/punctSpace/g,Ce).replace(/punct/g,Te).getRegex(),De=ie(Ee,"gu").replace(/notPunctSpace/g,/(?:[^\s\p{P}\p{S}]|~)/u).replace(/punctSpace/g,/(?!~)[\s\p{P}\p{S}]/u).replace(/punct/g,Re).getRegex(),Oe=ie("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Pe).replace(/punctSpace/g,Ce).replace(/punct/g,Te).getRegex(),Le=ie(/\\(punct)/,"gu").replace(/punct/g,Te).getRegex(),Be=ie(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),qe=ie(xe).replace("(?:--\x3e|$)","--\x3e").getRegex(),We=ie("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",qe).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Ie=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Fe=ie(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",Ie).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),He=ie(/^!?\[(label)\]\[(ref)\]/).replace("label",Ie).replace("ref",me).getRegex(),Ye=ie(/^!?\[(ref)\](?:\[\])?/).replace("ref",me).getRegex(),Ge={_backpedal:ae,anyPunctuation:Le,autolink:Be,blockSkip:/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,br:ve,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:ae,emStrongLDelim:Ne,emStrongRDelimAst:ze,emStrongRDelimUnd:Oe,escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,link:Fe,nolink:Ye,punctuation:Me,reflink:He,reflinkSearch:ie("reflink|nolink(?!\\()","g").replace("reflink",He).replace("nolink",Ye).getRegex(),tag:We,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:ae},Qe={...Ge,link:ie(/^!?\[(label)\]\((.*?)\)/).replace("label",Ie).getRegex(),reflink:ie(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Ie).getRegex()},Ze={...Ge,emStrongRDelimAst:De,emStrongLDelim:Ae,url:ie(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Xe={...Ze,br:ie(ve).replace("{2,}","*").getRegex(),text:ie(Ze.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Je={normal:_e,gfm:je,pedantic:Se},Ue={normal:Ge,gfm:Ze,breaks:Xe,pedantic:Qe},Ve={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Ke=e=>Ve[e];function et(e,t){if(t){if(oe.escapeTest.test(e))return e.replace(oe.escapeReplace,Ke)}else if(oe.escapeTestNoEncode.test(e))return e.replace(oe.escapeReplaceNoEncode,Ke);return e}function tt(e){try{e=encodeURI(e).replace(oe.percentDecode,"%")}catch{return null}return e}function nt(e,t){const n=e.replace(oe.findPipe,((e,t,n)=>{let r=!1,s=t;for(;--s>=0&&"\\"===n[s];)r=!r;return r?"|":" |"})).split(oe.splitPipe);let r=0;if(n[0].trim()||n.shift(),n.length>0&&!n.at(-1)?.trim()&&n.pop(),t)if(n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(oe.slashPipe,"|");return n}function rt(e,t,n){const r=e.length;if(0===r)return"";let s=0;for(;s<r;){const a=e.charAt(r-s-1);if(a!==t||n){if(a===t||!n)break;s++}else s++}return e.slice(0,r-s)}function st(e,t,n,r,s){const a=t.href,i=t.title||null,o=e[1].replace(s.other.outputLinkReplace,"$1");r.state.inLink=!0;const l={type:"!"===e[0].charAt(0)?"image":"link",raw:n,href:a,title:i,text:o,tokens:r.inlineTokens(o)};return r.state.inLink=!1,l}var at=class{options;rules;lexer;constructor(e){this.options=e||re}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const e=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?e:rt(e,"\n")}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const e=t[0],n=function(e,t,n){const r=e.match(n.other.indentCodeCompensation);if(null===r)return t;const s=r[1];return t.split("\n").map((e=>{const t=e.match(n.other.beginningSpace);if(null===t)return e;const[r]=t;return r.length>=s.length?e.slice(s.length):e})).join("\n")}(e,t[3]||"",this.rules);return{type:"code",raw:e,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:n}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let e=t[2].trim();if(this.rules.other.endingHash.test(e)){const t=rt(e,"#");this.options.pedantic?e=t.trim():t&&!this.rules.other.endingSpaceChar.test(t)||(e=t.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:this.lexer.inline(e)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:rt(t[0],"\n")}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){let e=rt(t[0],"\n").split("\n"),n="",r="";const s=[];for(;e.length>0;){let t=!1;const a=[];let i;for(i=0;i<e.length;i++)if(this.rules.other.blockquoteStart.test(e[i]))a.push(e[i]),t=!0;else{if(t)break;a.push(e[i])}e=e.slice(i);const o=a.join("\n"),l=o.replace(this.rules.other.blockquoteSetextReplace,"\n    $1").replace(this.rules.other.blockquoteSetextReplace2,"");n=n?`${n}\n${o}`:o,r=r?`${r}\n${l}`:l;const c=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(l,s,!0),this.lexer.state.top=c,0===e.length)break;const d=s.at(-1);if("code"===d?.type)break;if("blockquote"===d?.type){const t=d,a=t.raw+"\n"+e.join("\n"),i=this.blockquote(a);s[s.length-1]=i,n=n.substring(0,n.length-t.raw.length)+i.raw,r=r.substring(0,r.length-t.text.length)+i.text;break}if("list"!==d?.type);else{const t=d,a=t.raw+"\n"+e.join("\n"),i=this.list(a);s[s.length-1]=i,n=n.substring(0,n.length-d.raw.length)+i.raw,r=r.substring(0,r.length-t.raw.length)+i.raw,e=a.substring(s.at(-1).raw.length).split("\n")}}return{type:"blockquote",raw:n,tokens:s,text:r}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const r=n.length>1,s={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");const a=this.rules.other.listItemRegex(n);let i=!1;for(;e;){let n=!1,r="",o="";if(!(t=a.exec(e)))break;if(this.rules.block.hr.test(e))break;r=t[0],e=e.substring(r.length);let l=t[2].split("\n",1)[0].replace(this.rules.other.listReplaceTabs,(e=>" ".repeat(3*e.length))),c=e.split("\n",1)[0],d=!l.trim(),p=0;if(this.options.pedantic?(p=2,o=l.trimStart()):d?p=t[1].length+1:(p=t[2].search(this.rules.other.nonSpaceChar),p=p>4?1:p,o=l.slice(p),p+=t[1].length),d&&this.rules.other.blankLine.test(c)&&(r+=c+"\n",e=e.substring(c.length+1),n=!0),!n){const t=this.rules.other.nextBulletRegex(p),n=this.rules.other.hrRegex(p),s=this.rules.other.fencesBeginRegex(p),a=this.rules.other.headingBeginRegex(p),i=this.rules.other.htmlBeginRegex(p);for(;e;){const u=e.split("\n",1)[0];let h;if(c=u,this.options.pedantic?(c=c.replace(this.rules.other.listReplaceNesting,"  "),h=c):h=c.replace(this.rules.other.tabCharGlobal,"    "),s.test(c))break;if(a.test(c))break;if(i.test(c))break;if(t.test(c))break;if(n.test(c))break;if(h.search(this.rules.other.nonSpaceChar)>=p||!c.trim())o+="\n"+h.slice(p);else{if(d)break;if(l.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4)break;if(s.test(l))break;if(a.test(l))break;if(n.test(l))break;o+="\n"+c}d||c.trim()||(d=!0),r+=u+"\n",e=e.substring(u.length+1),l=h.slice(p)}}s.loose||(i?s.loose=!0:this.rules.other.doubleBlankLine.test(r)&&(i=!0));let u,h=null;this.options.gfm&&(h=this.rules.other.listIsTask.exec(o),h&&(u="[ ] "!==h[0],o=o.replace(this.rules.other.listReplaceTask,""))),s.items.push({type:"list_item",raw:r,task:!!h,checked:u,loose:!1,text:o,tokens:[]}),s.raw+=r}const o=s.items.at(-1);if(!o)return;o.raw=o.raw.trimEnd(),o.text=o.text.trimEnd(),s.raw=s.raw.trimEnd();for(let e=0;e<s.items.length;e++)if(this.lexer.state.top=!1,s.items[e].tokens=this.lexer.blockTokens(s.items[e].text,[]),!s.loose){const t=s.items[e].tokens.filter((e=>"space"===e.type)),n=t.length>0&&t.some((e=>this.rules.other.anyLine.test(e.raw)));s.loose=n}if(s.loose)for(let e=0;e<s.items.length;e++)s.items[e].loose=!0;return s}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:"pre"===t[1]||"script"===t[1]||"style"===t[1],text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const e=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),n=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:e,raw:t[0],href:n,title:r}}}table(e){const t=this.rules.block.table.exec(e);if(!t)return;if(!this.rules.other.tableDelimiter.test(t[2]))return;const n=nt(t[1]),r=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),s=t[3]?.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split("\n"):[],a={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(const e of r)this.rules.other.tableAlignRight.test(e)?a.align.push("right"):this.rules.other.tableAlignCenter.test(e)?a.align.push("center"):this.rules.other.tableAlignLeft.test(e)?a.align.push("left"):a.align.push(null);for(let e=0;e<n.length;e++)a.header.push({text:n[e],tokens:this.lexer.inline(n[e]),header:!0,align:a.align[e]});for(const e of s)a.rows.push(nt(e,a.header.length).map(((e,t)=>({text:e,tokens:this.lexer.inline(e),header:!1,align:a.align[t]}))));return a}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const e="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:e,tokens:this.lexer.inline(e)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const e=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(e)){if(!this.rules.other.endAngleBracket.test(e))return;const t=rt(e.slice(0,-1),"\\");if((e.length-t.length)%2==0)return}else{const e=function(e,t){if(-1===e.indexOf(t[1]))return-1;let n=0;for(let r=0;r<e.length;r++)if("\\"===e[r])r++;else if(e[r]===t[0])n++;else if(e[r]===t[1]&&(n--,n<0))return r;return n>0?-2:-1}(t[2],"()");if(-2===e)return;if(e>-1){const n=(0===t[0].indexOf("!")?5:4)+t[1].length+e;t[2]=t[2].substring(0,e),t[0]=t[0].substring(0,n).trim(),t[3]=""}}let n=t[2],r="";if(this.options.pedantic){const e=this.rules.other.pedanticHrefTitle.exec(n);e&&(n=e[1],r=e[3])}else r=t[3]?t[3].slice(1,-1):"";return n=n.trim(),this.rules.other.startAngleBracket.test(n)&&(n=this.options.pedantic&&!this.rules.other.endAngleBracket.test(e)?n.slice(1):n.slice(1,-1)),st(t,{href:n?n.replace(this.rules.inline.anyPunctuation,"$1"):n,title:r?r.replace(this.rules.inline.anyPunctuation,"$1"):r},t[0],this.lexer,this.rules)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const e=t[(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal," ").toLowerCase()];if(!e){const e=n[0].charAt(0);return{type:"text",raw:e,text:e}}return st(n,e,n[0],this.lexer,this.rules)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(r&&(!r[3]||!n.match(this.rules.other.unicodeAlphaNumeric))&&(!r[1]&&!r[2]||!n||this.rules.inline.punctuation.exec(n))){const n=[...r[0]].length-1;let s,a,i=n,o=0;const l="*"===r[0][0]?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(l.lastIndex=0,t=t.slice(-1*e.length+n);null!=(r=l.exec(t));){if(s=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!s)continue;if(a=[...s].length,r[3]||r[4]){i+=a;continue}if((r[5]||r[6])&&n%3&&!((n+a)%3)){o+=a;continue}if(i-=a,i>0)continue;a=Math.min(a,a+i+o);const t=[...r[0]][0].length,l=e.slice(0,n+r.index+t+a);if(Math.min(n,a)%2){const e=l.slice(1,-1);return{type:"em",raw:l,text:e,tokens:this.lexer.inlineTokens(e)}}const c=l.slice(2,-2);return{type:"strong",raw:l,text:c,tokens:this.lexer.inlineTokens(c)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let e=t[2].replace(this.rules.other.newLineCharGlobal," ");const n=this.rules.other.nonSpaceChar.test(e),r=this.rules.other.startingSpaceChar.test(e)&&this.rules.other.endingSpaceChar.test(e);return n&&r&&(e=e.substring(1,e.length-1)),{type:"codespan",raw:t[0],text:e}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let e,n;return"@"===t[2]?(e=t[1],n="mailto:"+e):(e=t[1],n=e),{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let e,n;if("@"===t[2])e=t[0],n="mailto:"+e;else{let r;do{r=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??""}while(r!==t[0]);e=t[0],n="www."===t[1]?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){const e=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:e}}}},it=class e{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||re,this.options.tokenizer=this.options.tokenizer||new at,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={other:oe,block:Je.normal,inline:Ue.normal};this.options.pedantic?(t.block=Je.pedantic,t.inline=Ue.pedantic):this.options.gfm&&(t.block=Je.gfm,this.options.breaks?t.inline=Ue.breaks:t.inline=Ue.gfm),this.tokenizer.rules=t}static get rules(){return{block:Je,inline:Ue}}static lex(t,n){return new e(n).lex(t)}static lexInline(t,n){return new e(n).inlineTokens(t)}lex(e){e=e.replace(oe.carriageReturn,"\n"),this.blockTokens(e,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const t=this.inlineQueue[e];this.inlineTokens(t.src,t.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){for(this.options.pedantic&&(e=e.replace(oe.tabCharGlobal,"    ").replace(oe.spaceLine,""));e;){let r;if(this.options.extensions?.block?.some((n=>!!(r=n.call({lexer:this},e,t))&&(e=e.substring(r.raw.length),t.push(r),!0))))continue;if(r=this.tokenizer.space(e)){e=e.substring(r.raw.length);const n=t.at(-1);1===r.raw.length&&void 0!==n?n.raw+="\n":t.push(r);continue}if(r=this.tokenizer.code(e)){e=e.substring(r.raw.length);const n=t.at(-1);"paragraph"===n?.type||"text"===n?.type?(n.raw+="\n"+r.raw,n.text+="\n"+r.text,this.inlineQueue.at(-1).src=n.text):t.push(r);continue}if(r=this.tokenizer.fences(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.heading(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.hr(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.blockquote(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.list(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.html(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.def(e)){e=e.substring(r.raw.length);const n=t.at(-1);"paragraph"===n?.type||"text"===n?.type?(n.raw+="\n"+r.raw,n.text+="\n"+r.raw,this.inlineQueue.at(-1).src=n.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.lheading(e)){e=e.substring(r.raw.length),t.push(r);continue}let s=e;if(this.options.extensions?.startBlock){let t=1/0;const n=e.slice(1);let r;this.options.extensions.startBlock.forEach((e=>{r=e.call({lexer:this},n),"number"==typeof r&&r>=0&&(t=Math.min(t,r))})),t<1/0&&t>=0&&(s=e.substring(0,t+1))}if(this.state.top&&(r=this.tokenizer.paragraph(s))){const a=t.at(-1);n&&"paragraph"===a?.type?(a.raw+="\n"+r.raw,a.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=a.text):t.push(r),n=s.length!==e.length,e=e.substring(r.raw.length)}else if(r=this.tokenizer.text(e)){e=e.substring(r.raw.length);const n=t.at(-1);"text"===n?.type?(n.raw+="\n"+r.raw,n.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=n.text):t.push(r)}else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n=e,r=null;if(this.tokens.links){const e=Object.keys(this.tokens.links);if(e.length>0)for(;null!=(r=this.tokenizer.rules.inline.reflinkSearch.exec(n));)e.includes(r[0].slice(r[0].lastIndexOf("[")+1,-1))&&(n=n.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(r=this.tokenizer.rules.inline.anyPunctuation.exec(n));)n=n.slice(0,r.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;null!=(r=this.tokenizer.rules.inline.blockSkip.exec(n));)n=n.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let s=!1,a="";for(;e;){let r;if(s||(a=""),s=!1,this.options.extensions?.inline?.some((n=>!!(r=n.call({lexer:this},e,t))&&(e=e.substring(r.raw.length),t.push(r),!0))))continue;if(r=this.tokenizer.escape(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.tag(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.link(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(r.raw.length);const n=t.at(-1);"text"===r.type&&"text"===n?.type?(n.raw+=r.raw,n.text+=r.text):t.push(r);continue}if(r=this.tokenizer.emStrong(e,n,a)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.codespan(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.br(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.del(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.autolink(e)){e=e.substring(r.raw.length),t.push(r);continue}if(!this.state.inLink&&(r=this.tokenizer.url(e))){e=e.substring(r.raw.length),t.push(r);continue}let i=e;if(this.options.extensions?.startInline){let t=1/0;const n=e.slice(1);let r;this.options.extensions.startInline.forEach((e=>{r=e.call({lexer:this},n),"number"==typeof r&&r>=0&&(t=Math.min(t,r))})),t<1/0&&t>=0&&(i=e.substring(0,t+1))}if(r=this.tokenizer.inlineText(i)){e=e.substring(r.raw.length),"_"!==r.raw.slice(-1)&&(a=r.raw.slice(-1)),s=!0;const n=t.at(-1);"text"===n?.type?(n.raw+=r.raw,n.text+=r.text):t.push(r)}else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}return t}},ot=class{options;parser;constructor(e){this.options=e||re}space(e){return""}code({text:e,lang:t,escaped:n}){const r=(t||"").match(oe.notSpaceStart)?.[0],s=e.replace(oe.endingNewline,"")+"\n";return r?'<pre><code class="language-'+et(r)+'">'+(n?s:et(s,!0))+"</code></pre>\n":"<pre><code>"+(n?s:et(s,!0))+"</code></pre>\n"}blockquote({tokens:e}){return`<blockquote>\n${this.parser.parse(e)}</blockquote>\n`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>\n`}hr(e){return"<hr>\n"}list(e){const t=e.ordered,n=e.start;let r="";for(let t=0;t<e.items.length;t++){const n=e.items[t];r+=this.listitem(n)}const s=t?"ol":"ul";return"<"+s+(t&&1!==n?' start="'+n+'"':"")+">\n"+r+"</"+s+">\n"}listitem(e){let t="";if(e.task){const n=this.checkbox({checked:!!e.checked});e.loose?"paragraph"===e.tokens[0]?.type?(e.tokens[0].text=n+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&"text"===e.tokens[0].tokens[0].type&&(e.tokens[0].tokens[0].text=n+" "+et(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):t+=n+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>\n`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>\n`}table(e){let t="",n="";for(let t=0;t<e.header.length;t++)n+=this.tablecell(e.header[t]);t+=this.tablerow({text:n});let r="";for(let t=0;t<e.rows.length;t++){const s=e.rows[t];n="";for(let e=0;e<s.length;e++)n+=this.tablecell(s[e]);r+=this.tablerow({text:n})}return r&&(r=`<tbody>${r}</tbody>`),"<table>\n<thead>\n"+t+"</thead>\n"+r+"</table>\n"}tablerow({text:e}){return`<tr>\n${e}</tr>\n`}tablecell(e){const t=this.parser.parseInline(e.tokens),n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>\n`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${et(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){const r=this.parser.parseInline(n),s=tt(e);if(null===s)return r;let a='<a href="'+(e=s)+'"';return t&&(a+=' title="'+et(t)+'"'),a+=">"+r+"</a>",a}image({href:e,title:t,text:n,tokens:r}){r&&(n=this.parser.parseInline(r,this.parser.textRenderer));const s=tt(e);if(null===s)return et(n);let a=`<img src="${e=s}" alt="${n}"`;return t&&(a+=` title="${et(t)}"`),a+=">",a}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:et(e.text)}},lt=class{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}},ct=class e{options;renderer;textRenderer;constructor(e){this.options=e||re,this.options.renderer=this.options.renderer||new ot,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new lt}static parse(t,n){return new e(n).parse(t)}static parseInline(t,n){return new e(n).parseInline(t)}parse(e,t=!0){let n="";for(let r=0;r<e.length;r++){const s=e[r];if(this.options.extensions?.renderers?.[s.type]){const e=s,t=this.options.extensions.renderers[e.type].call({parser:this},e);if(!1!==t||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(e.type)){n+=t||"";continue}}const a=s;switch(a.type){case"space":n+=this.renderer.space(a);continue;case"hr":n+=this.renderer.hr(a);continue;case"heading":n+=this.renderer.heading(a);continue;case"code":n+=this.renderer.code(a);continue;case"table":n+=this.renderer.table(a);continue;case"blockquote":n+=this.renderer.blockquote(a);continue;case"list":n+=this.renderer.list(a);continue;case"html":n+=this.renderer.html(a);continue;case"paragraph":n+=this.renderer.paragraph(a);continue;case"text":{let s=a,i=this.renderer.text(s);for(;r+1<e.length&&"text"===e[r+1].type;)s=e[++r],i+="\n"+this.renderer.text(s);n+=t?this.renderer.paragraph({type:"paragraph",raw:i,text:i,tokens:[{type:"text",raw:i,text:i,escaped:!0}]}):i;continue}default:{const e='Token with "'+a.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw new Error(e)}}}return n}parseInline(e,t=this.renderer){let n="";for(let r=0;r<e.length;r++){const s=e[r];if(this.options.extensions?.renderers?.[s.type]){const e=this.options.extensions.renderers[s.type].call({parser:this},s);if(!1!==e||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){n+=e||"";continue}}const a=s;switch(a.type){case"escape":case"text":n+=t.text(a);break;case"html":n+=t.html(a);break;case"link":n+=t.link(a);break;case"image":n+=t.image(a);break;case"strong":n+=t.strong(a);break;case"em":n+=t.em(a);break;case"codespan":n+=t.codespan(a);break;case"br":n+=t.br(a);break;case"del":n+=t.del(a);break;default:{const e='Token with "'+a.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw new Error(e)}}}return n}},dt=class{options;block;constructor(e){this.options=e||re}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?it.lex:it.lexInline}provideParser(){return this.block?ct.parse:ct.parseInline}},pt=new class{defaults={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=ct;Renderer=ot;TextRenderer=lt;Lexer=it;Tokenizer=at;Hooks=dt;constructor(...e){this.use(...e)}walkTokens(e,t){let n=[];for(const r of e)switch(n=n.concat(t.call(this,r)),r.type){case"table":{const e=r;for(const r of e.header)n=n.concat(this.walkTokens(r.tokens,t));for(const r of e.rows)for(const e of r)n=n.concat(this.walkTokens(e.tokens,t));break}case"list":{const e=r;n=n.concat(this.walkTokens(e.items,t));break}default:{const e=r;this.defaults.extensions?.childTokens?.[e.type]?this.defaults.extensions.childTokens[e.type].forEach((r=>{const s=e[r].flat(1/0);n=n.concat(this.walkTokens(s,t))})):e.tokens&&(n=n.concat(this.walkTokens(e.tokens,t)))}}return n}use(...e){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach((e=>{const n={...e};if(n.async=this.defaults.async||n.async||!1,e.extensions&&(e.extensions.forEach((e=>{if(!e.name)throw new Error("extension name required");if("renderer"in e){const n=t.renderers[e.name];t.renderers[e.name]=n?function(...t){let r=e.renderer.apply(this,t);return!1===r&&(r=n.apply(this,t)),r}:e.renderer}if("tokenizer"in e){if(!e.level||"block"!==e.level&&"inline"!==e.level)throw new Error("extension level must be 'block' or 'inline'");const n=t[e.level];n?n.unshift(e.tokenizer):t[e.level]=[e.tokenizer],e.start&&("block"===e.level?t.startBlock?t.startBlock.push(e.start):t.startBlock=[e.start]:"inline"===e.level&&(t.startInline?t.startInline.push(e.start):t.startInline=[e.start]))}"childTokens"in e&&e.childTokens&&(t.childTokens[e.name]=e.childTokens)})),n.extensions=t),e.renderer){const t=this.defaults.renderer||new ot(this.defaults);for(const n in e.renderer){if(!(n in t))throw new Error(`renderer '${n}' does not exist`);if(["options","parser"].includes(n))continue;const r=n,s=e.renderer[r],a=t[r];t[r]=(...e)=>{let n=s.apply(t,e);return!1===n&&(n=a.apply(t,e)),n||""}}n.renderer=t}if(e.tokenizer){const t=this.defaults.tokenizer||new at(this.defaults);for(const n in e.tokenizer){if(!(n in t))throw new Error(`tokenizer '${n}' does not exist`);if(["options","rules","lexer"].includes(n))continue;const r=n,s=e.tokenizer[r],a=t[r];t[r]=(...e)=>{let n=s.apply(t,e);return!1===n&&(n=a.apply(t,e)),n}}n.tokenizer=t}if(e.hooks){const t=this.defaults.hooks||new dt;for(const n in e.hooks){if(!(n in t))throw new Error(`hook '${n}' does not exist`);if(["options","block"].includes(n))continue;const r=n,s=e.hooks[r],a=t[r];dt.passThroughHooks.has(n)?t[r]=e=>{if(this.defaults.async)return Promise.resolve(s.call(t,e)).then((e=>a.call(t,e)));const n=s.call(t,e);return a.call(t,n)}:t[r]=(...e)=>{let n=s.apply(t,e);return!1===n&&(n=a.apply(t,e)),n}}n.hooks=t}if(e.walkTokens){const t=this.defaults.walkTokens,r=e.walkTokens;n.walkTokens=function(e){let n=[];return n.push(r.call(this,e)),t&&(n=n.concat(t.call(this,e))),n}}this.defaults={...this.defaults,...n}})),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return it.lex(e,t??this.defaults)}parser(e,t){return ct.parse(e,t??this.defaults)}parseMarkdown(e){return(t,n)=>{const r={...n},s={...this.defaults,...r},a=this.onError(!!s.silent,!!s.async);if(!0===this.defaults.async&&!1===r.async)return a(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(null==t)return a(new Error("marked(): input parameter is undefined or null"));if("string"!=typeof t)return a(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));s.hooks&&(s.hooks.options=s,s.hooks.block=e);const i=s.hooks?s.hooks.provideLexer():e?it.lex:it.lexInline,o=s.hooks?s.hooks.provideParser():e?ct.parse:ct.parseInline;if(s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(t):t).then((e=>i(e,s))).then((e=>s.hooks?s.hooks.processAllTokens(e):e)).then((e=>s.walkTokens?Promise.all(this.walkTokens(e,s.walkTokens)).then((()=>e)):e)).then((e=>o(e,s))).then((e=>s.hooks?s.hooks.postprocess(e):e)).catch(a);try{s.hooks&&(t=s.hooks.preprocess(t));let e=i(t,s);s.hooks&&(e=s.hooks.processAllTokens(e)),s.walkTokens&&this.walkTokens(e,s.walkTokens);let n=o(e,s);return s.hooks&&(n=s.hooks.postprocess(n)),n}catch(e){return a(e)}}}onError(e,t){return n=>{if(n.message+="\nPlease report this to https://github.com/markedjs/marked.",e){const e="<p>An error occurred:</p><pre>"+et(n.message+"",!0)+"</pre>";return t?Promise.resolve(e):e}if(t)return Promise.reject(n);throw n}}};function ut(e,t){return pt.parse(e,t)}ut.options=ut.setOptions=function(e){return pt.setOptions(e),ut.defaults=pt.defaults,se(ut.defaults),ut},ut.getDefaults=function(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}},ut.defaults=re,ut.use=function(...e){return pt.use(...e),ut.defaults=pt.defaults,se(ut.defaults),ut},ut.walkTokens=function(e,t){return pt.walkTokens(e,t)},ut.parseInline=pt.parseInline,ut.Parser=ct,ut.parser=ct.parse,ut.Renderer=ot,ut.TextRenderer=lt,ut.Lexer=it,ut.lexer=it.lex,ut.Tokenizer=at,ut.Hooks=dt,ut.parse=ut,ut.options,ut.setOptions,ut.use,ut.walkTokens,ut.parseInline,ct.parse,it.lex;const ht=()=>{const[e,t]=(0,s.useState)(""),[n,o]=(0,s.useState)(!0),[l,c]=(0,s.useState)(null);return ut.setOptions({breaks:!0,gfm:!0,sanitize:!1}),(0,s.useEffect)((()=>{(async()=>{try{o(!0),c(null);const e=await fetch(`${window.wordpressMcpSettings.pluginUrl}/docs/client-setup.md`);if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const n=ut(await e.text());t(n)}catch(e){console.error("Error loading documentation:",e),c(e.message)}finally{o(!1)}})()}),[]),n?(0,i.jsx)(a.Card,{children:(0,i.jsx)(a.CardBody,{children:(0,i.jsxs)("div",{className:"documentation-loading",children:[(0,i.jsx)(a.Spinner,{}),(0,i.jsx)("p",{children:(0,r.__)("Loading documentation...","wordpress-mcp")})]})})}):l?(0,i.jsxs)(a.Card,{children:[(0,i.jsx)(a.CardHeader,{children:(0,i.jsx)("h2",{children:(0,r.__)("Documentation","wordpress-mcp")})}),(0,i.jsx)(a.CardBody,{children:(0,i.jsxs)("div",{className:"documentation-error",children:[(0,i.jsxs)("p",{children:[(0,r.__)("Error loading documentation:","wordpress-mcp")," ",l]}),(0,i.jsx)("p",{children:(0,r.__)("Please check that the documentation file exists and is accessible.","wordpress-mcp")})]})})]}):(0,i.jsxs)(a.Card,{children:[(0,i.jsx)(a.CardHeader,{children:(0,i.jsx)("h2",{children:(0,r.__)("MCP Client Setup Guide","wordpress-mcp")})}),(0,i.jsx)(a.CardBody,{children:(0,i.jsx)("div",{className:"wordpress-mcp-documentation",dangerouslySetInnerHTML:{__html:e}})})]})},mt=()=>{const[e,t]=(0,s.useState)({enabled:!1,features_adapter_enabled:!1,enable_create_tools:!1,enable_update_tools:!1,enable_delete_tools:!1}),[n,l]=(0,s.useState)(!1),[c,p]=(0,s.useState)(null),[m,g]=(0,s.useState)(window.location.hash.replace("#","")||"settings"),f=(0,s.useRef)(null),w=(0,s.useMemo)((()=>[{name:"settings",title:(0,r.__)("Settings","wordpress-mcp"),className:"wordpress-mcp-settings-tab"},{name:"authentication-tokens",title:(0,r.__)("Authentication Tokens","wordpress-mcp"),className:"authentication-tokens-tab"},{name:"documentation",title:(0,r.__)("Documentation","wordpress-mcp"),className:"wordpress-mcp-documentation-tab"},{name:"tools",title:(0,r.__)("Tools","wordpress-mcp"),className:"wordpress-mcp-tools-tab",disabled:!e.enabled},{name:"resources",title:(0,r.__)("Resources","wordpress-mcp"),className:"wordpress-mcp-resources-tab",disabled:!e.enabled},{name:"prompts",title:(0,r.__)("Prompts","wordpress-mcp"),className:"wordpress-mcp-prompts-tab",disabled:!e.enabled}]),[e.enabled]);(0,s.useEffect)((()=>{if(window.wordpressMcpSettings&&window.wordpressMcpSettings.settings){const e=window.wordpressMcpSettings.settings;t((t=>({...t,...e,enable_create_tools:"boolean"==typeof e.enable_create_tools&&e.enable_create_tools,enable_update_tools:"boolean"==typeof e.enable_update_tools&&e.enable_update_tools,enable_delete_tools:"boolean"==typeof e.enable_delete_tools&&e.enable_delete_tools})))}}),[]),(0,s.useEffect)((()=>()=>{f.current&&clearTimeout(f.current)}),[]);const x=n=>{const r=!e[n];t((e=>{const t={...e,[n]:r};return"enabled"!==n||r||"settings"===m||(g("settings"),window.location.hash="settings"),f.current&&clearTimeout(f.current),f.current=setTimeout((()=>{b(t),f.current=null}),500),t}))},b=e=>{l(!0),p(null);const t=new FormData;t.append("action","wordpress_mcp_save_settings"),t.append("nonce",window.wordpressMcpSettings.nonce),t.append("settings",JSON.stringify(e)),fetch(ajaxurl,{method:"POST",body:t,credentials:"same-origin"}).then((e=>e.json())).then((e=>{l(!1),e.success?p({status:"success",message:e.data.message||window.wordpressMcpSettings.strings.settingsSaved}):p({status:"error",message:e.data.message||window.wordpressMcpSettings.strings.settingsError})})).catch((e=>{l(!1),p({status:"error",message:window.wordpressMcpSettings.strings.settingsError}),console.error("Error saving settings:",e)}))},k=window.wordpressMcpSettings?window.wordpressMcpSettings.strings:{};return(0,i.jsxs)("div",{className:"wordpress-mcp-settings",children:[c&&(0,i.jsx)(a.Notice,{status:c.status,isDismissible:!0,onRemove:()=>p(null),className:`notice notice-${c.status} is-dismissible`,children:c.message}),(0,i.jsx)(a.TabPanel,{className:"wordpress-mcp-tabs",tabs:w,activeClass:"is-active",initialTabName:m,onSelect:e=>w.find((t=>t.name===e)).disabled?m:(g(e),window.location.hash=e,e),children:t=>{if(t.disabled)return(0,i.jsxs)("div",{className:"wordpress-mcp-disabled-tab-notice",children:[(0,i.jsx)("p",{children:(0,r.__)("This feature is only available when MCP functionality is enabled.","wordpress-mcp")}),(0,i.jsx)("p",{children:(0,r.__)("Please enable MCP in the Settings tab first.","wordpress-mcp")})]});switch(t.name){case"settings":return(0,i.jsx)(o,{settings:e,onToggleChange:x,isSaving:n,strings:k});case"authentication-tokens":return(0,i.jsx)(ne,{});case"documentation":return(0,i.jsx)(ht,{});case"tools":return(0,i.jsx)(d,{});case"resources":return(0,i.jsx)(u,{});case"prompts":return(0,i.jsx)(h,{});default:return null}}})]})};document.addEventListener("DOMContentLoaded",(function(){const e=document.getElementById("wordpress-mcp-settings-app");e&&(0,s.createRoot)(e).render((0,i.jsx)(mt,{}))}))}},n={};function r(e){var s=n[e];if(void 0!==s)return s.exports;var a=n[e]={exports:{}};return t[e](a,a.exports,r),a.exports}r.m=t,e=[],r.O=(t,n,s,a)=>{if(!n){var i=1/0;for(d=0;d<e.length;d++){for(var[n,s,a]=e[d],o=!0,l=0;l<n.length;l++)(!1&a||i>=a)&&Object.keys(r.O).every((e=>r.O[e](n[l])))?n.splice(l--,1):(o=!1,a<i&&(i=a));if(o){e.splice(d--,1);var c=s();void 0!==c&&(t=c)}}return t}a=a||0;for(var d=e.length;d>0&&e[d-1][2]>a;d--)e[d]=e[d-1];e[d]=[n,s,a]},r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={57:0,350:0};r.O.j=t=>0===e[t];var t=(t,n)=>{var s,a,[i,o,l]=n,c=0;if(i.some((t=>0!==e[t]))){for(s in o)r.o(o,s)&&(r.m[s]=o[s]);if(l)var d=l(r)}for(t&&t(n);c<i.length;c++)a=i[c],r.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return r.O(d)},n=globalThis.webpackChunkwordpress_mcp=globalThis.webpackChunkwordpress_mcp||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var s=r.O(void 0,[350],(()=>r(728)));s=r.O(s)})();