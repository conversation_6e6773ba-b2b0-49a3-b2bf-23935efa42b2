.wordpress-mcp-settings{margin:20px 0}.wordpress-mcp-settings .setting-row{border-bottom:1px solid #f0f0f1;margin-bottom:20px;padding-bottom:10px}.wordpress-mcp-settings .setting-row:last-child{border-bottom:none}.wordpress-mcp-settings .components-card{margin-bottom:20px}.wordpress-mcp-settings .components-card-header{border-bottom:1px solid #f0f0f1;padding:16px}.wordpress-mcp-settings .components-card-body{padding:16px}.wordpress-mcp-settings .components-card-footer{border-top:1px solid #f0f0f1;display:flex;justify-content:flex-end;padding:16px}.wordpress-mcp-settings .components-toggle-control{margin-bottom:10px}.wordpress-mcp-settings .components-notice{margin-bottom:20px}.wordpress-mcp-settings .settings-saving-indicator{align-items:center;animation:fade-in .3s ease-in-out;background-color:rgba(56,88,233,.05);border-radius:4px;color:#3858e9;display:flex;font-size:14px;font-style:italic;justify-content:flex-end;padding:4px 8px}@keyframes fade-in{0%{opacity:0}to{opacity:1}}.wordpress-mcp-settings .settings-saving-indicator .components-spinner{margin-right:10px}.wordpress-mcp-table{border-collapse:collapse;margin-top:20px;width:100%}.wordpress-mcp-table td,.wordpress-mcp-table th{border-bottom:1px solid #f0f0f1;padding:12px;text-align:left}.wordpress-mcp-table th{font-weight:600}.wordpress-mcp-table th,.wordpress-mcp-table tr:hover{background-color:#f8f9fa}.wordpress-mcp-error,.wordpress-mcp-loading{align-items:center;display:flex;justify-content:center;padding:20px}.wordpress-mcp-loading{flex-direction:column}.wordpress-mcp-error{color:#d63638}.wordpress-mcp-tabs .components-tab-panel__tabs{margin-bottom:5px;margin-left:5px}.wordpress-mcp-tabs .components-tab-panel__tabs-item[aria-disabled=true],.wordpress-mcp-tabs .components-tab-panel__tabs-item[disabled]{cursor:not-allowed;opacity:.6;pointer-events:none}.wordpress-mcp-disabled-tab-notice{background-color:#f8f9fa;border:1px solid #ddd;border-radius:4px;margin-top:20px;padding:24px;text-align:center}.wordpress-mcp-disabled-tab-notice p{color:#757575;font-size:14px;margin:0 0 10px}.wordpress-mcp-disabled-tab-notice p:last-child{margin-bottom:0}.mcp-token-section{border-top:1px solid #ddd;margin-top:2rem;padding-top:1rem}.mcp-token-container{background:#fff;border:1px solid #ccd0d4;border-radius:4px;margin-bottom:1.5rem;padding:1.5rem}.mcp-token-container h3{margin-bottom:1rem;margin-top:0}.mcp-token-container .components-textarea-control__input{background:#f8f9fa;border:1px solid #ddd;font-family:monospace;padding:.75rem;resize:vertical;width:100%}.mcp-token-container .components-button{margin-top:.5rem}.mcp-token-container .description{color:#666;font-style:italic;margin:.5rem 0 0}.mcp-error-message{background:#f8d7da;border:1px solid #f5c6cb;border-radius:4px;color:#d63638;margin:1rem 0;padding:.75rem}.mcp-form-field{margin-bottom:1.5rem}.mcp-form-field .components-select-control__input{max-width:400px}.wordpress-mcp-documentation{color:#1e1e1e;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;line-height:1.6}.wordpress-mcp-documentation h1,.wordpress-mcp-documentation h2,.wordpress-mcp-documentation h3,.wordpress-mcp-documentation h4,.wordpress-mcp-documentation h5,.wordpress-mcp-documentation h6{color:#1e1e1e;font-weight:600;line-height:1.4;margin-bottom:16px;margin-top:24px}.wordpress-mcp-documentation h1{border-bottom:1px solid #ddd;font-size:2em;padding-bottom:8px}.wordpress-mcp-documentation h2{border-bottom:1px solid #eee;font-size:1.5em;padding-bottom:4px}.wordpress-mcp-documentation h3{font-size:1.25em}.wordpress-mcp-documentation h4{font-size:1em}.wordpress-mcp-documentation p{margin-bottom:16px}.wordpress-mcp-documentation ol,.wordpress-mcp-documentation ul{margin-bottom:16px;padding-left:24px}.wordpress-mcp-documentation li{margin-bottom:4px}.wordpress-mcp-documentation pre{background:#f6f8fa;border:1px solid #d1d9e0;border-radius:6px;font-size:85%;line-height:1.45;margin-bottom:16px;overflow:auto;padding:16px}.wordpress-mcp-documentation code{background:#f6f8fa;border-radius:3px;font-family:SFMono-Regular,Consolas,Liberation Mono,Menlo,monospace;font-size:85%;margin:0;padding:.2em .4em}.wordpress-mcp-documentation pre code{background:transparent;border-radius:0;display:block;font-size:100%;margin:0;padding:0;white-space:pre;word-break:normal;word-wrap:normal}.wordpress-mcp-documentation blockquote{border-left:4px solid #ddd;color:#666;margin:0 0 16px;padding:0 16px}.wordpress-mcp-documentation table{border-collapse:collapse;border-spacing:0;margin-bottom:16px;width:100%}.wordpress-mcp-documentation table td,.wordpress-mcp-documentation table th{border:1px solid #ddd;padding:8px 12px;text-align:left}.wordpress-mcp-documentation table th{background:#f6f8fa;font-weight:600}.wordpress-mcp-documentation a{color:#0073aa;text-decoration:none}.wordpress-mcp-documentation a:hover{text-decoration:underline}.wordpress-mcp-documentation strong{font-weight:600}.wordpress-mcp-documentation em{font-style:italic}.documentation-loading{align-items:center;display:flex;flex-direction:column;padding:40px;text-align:center}.documentation-loading .components-spinner{margin-bottom:16px}.documentation-error{background:#fef7f7;border:1px solid #f5c6cb;border-radius:4px;color:#d63638;padding:20px;text-align:center}
