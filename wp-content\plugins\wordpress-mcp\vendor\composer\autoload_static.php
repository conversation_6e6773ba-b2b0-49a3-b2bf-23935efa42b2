<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit495754488bb4dc60595fe4c525d27fbe
{
    public static $prefixLengthsPsr4 = array (
        'F' => 
        array (
            'Firebase\\JWT\\' => 13,
        ),
        'A' => 
        array (
            'Automattic\\WordpressMcp\\' => 24,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Firebase\\JWT\\' => 
        array (
            0 => __DIR__ . '/..' . '/firebase/php-jwt/src',
        ),
        'Automattic\\WordpressMcp\\' => 
        array (
            0 => __DIR__ . '/../..' . '/includes',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit495754488bb4dc60595fe4c525d27fbe::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit495754488bb4dc60595fe4c525d27fbe::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit495754488bb4dc60595fe4c525d27fbe::$classMap;

        }, null, ClassLoader::class);
    }
}
