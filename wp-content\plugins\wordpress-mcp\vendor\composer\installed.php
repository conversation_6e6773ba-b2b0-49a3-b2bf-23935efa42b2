<?php return array(
    'root' => array(
        'name' => 'automattic/wordpress-mcp',
        'pretty_version' => 'dev-trunk',
        'version' => 'dev-trunk',
        'reference' => '684a7b79ccd7da3e92ad3b88707accbe024c90ed',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'automattic/wordpress-mcp' => array(
            'pretty_version' => 'dev-trunk',
            'version' => 'dev-trunk',
            'reference' => '684a7b79ccd7da3e92ad3b88707accbe024c90ed',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.11.1',
            'version' => '6.11.1.0',
            'reference' => 'd1e91ecf8c598d073d0995afa8cd5c75c6e19e66',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
