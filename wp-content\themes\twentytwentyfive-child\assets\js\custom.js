/**
 * Custom JavaScript for Taxi Freddy Theme
 */

jQuery(document).ready(function($) {
    
    // Smooth scrolling for anchor links
    $('a[href*="#"]:not([href="#"])').click(function() {
        if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && location.hostname == this.hostname) {
            var target = $(this.hash);
            target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 1000);
                return false;
            }
        }
    });
    
    // Fade in animation on scroll
    function fadeInOnScroll() {
        $('.fade-in').each(function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();
            
            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                $(this).addClass('visible');
            }
        });
    }
    
    // Run on scroll
    $(window).on('scroll', fadeInOnScroll);
    
    // Run on load
    fadeInOnScroll();
    
    // Mobile menu toggle
    $('.mobile-menu-toggle').click(function() {
        $('.wp-block-navigation').toggleClass('mobile-open');
        $(this).toggleClass('active');
    });
    
    // Phone number click tracking
    $('a[href^="tel:"]').click(function() {
        var phoneNumber = $(this).attr('href').replace('tel:', '');
        console.log('Phone call initiated: ' + phoneNumber);
        // Add analytics tracking here if needed
    });
    
    // WhatsApp link click tracking
    $('a[href*="whatsapp"]').click(function() {
        console.log('WhatsApp link clicked');
        // Add analytics tracking here if needed
    });
    
    // Service card hover effects
    $('.service-card').hover(
        function() {
            $(this).addClass('hovered');
        },
        function() {
            $(this).removeClass('hovered');
        }
    );
    
    // Sticky header on scroll
    $(window).scroll(function() {
        var scroll = $(window).scrollTop();
        if (scroll >= 100) {
            $('.site-header').addClass('scrolled');
        } else {
            $('.site-header').removeClass('scrolled');
        }
    });
    
    // Contact form validation (if contact form exists)
    $('#contact-form').submit(function(e) {
        var isValid = true;
        var errorMessage = '';
        
        // Check required fields
        $(this).find('[required]').each(function() {
            if ($(this).val() === '') {
                isValid = false;
                $(this).addClass('error');
                errorMessage += 'Vul alle verplichte velden in.\n';
            } else {
                $(this).removeClass('error');
            }
        });
        
        // Check email format
        var email = $(this).find('input[type="email"]').val();
        if (email && !isValidEmail(email)) {
            isValid = false;
            $(this).find('input[type="email"]').addClass('error');
            errorMessage += 'Voer een geldig e-mailadres in.\n';
        }
        
        // Check phone format
        var phone = $(this).find('input[type="tel"]').val();
        if (phone && !isValidPhone(phone)) {
            isValid = false;
            $(this).find('input[type="tel"]').addClass('error');
            errorMessage += 'Voer een geldig telefoonnummer in.\n';
        }
        
        if (!isValid) {
            e.preventDefault();
            alert(errorMessage);
        }
    });
    
    // Email validation function
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // Phone validation function
    function isValidPhone(phone) {
        var phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        return phoneRegex.test(phone);
    }
    
    // Price calculator (if needed)
    function calculatePrice(distance, type) {
        var basePrice = 0;
        
        switch(type) {
            case 'zaventem':
                basePrice = 75;
                break;
            case 'charleroi':
                basePrice = 175;
                break;
            case 'eindhoven':
                basePrice = 175;
                break;
            default:
                basePrice = distance * 2.5; // €2.50 per km
        }
        
        return basePrice;
    }
    
    // Auto-resize textareas
    $('textarea').each(function() {
        this.setAttribute('style', 'height:' + (this.scrollHeight) + 'px;overflow-y:hidden;');
    }).on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Loading animation for buttons
    $('.wp-block-button__link').click(function() {
        var $button = $(this);
        var originalText = $button.text();
        
        if ($button.attr('href').indexOf('tel:') === 0 || $button.attr('href').indexOf('mailto:') === 0) {
            $button.text('Verbinden...');
            setTimeout(function() {
                $button.text(originalText);
            }, 2000);
        }
    });
    
    // Parallax effect for hero section
    $(window).scroll(function() {
        var scrolled = $(window).scrollTop();
        var parallax = $('.hero-section');
        var speed = 0.5;
        
        parallax.css('transform', 'translateY(' + (scrolled * speed) + 'px)');
    });
    
    // Cookie consent (basic implementation)
    if (!localStorage.getItem('cookieConsent')) {
        $('body').append('<div id="cookie-notice" style="position: fixed; bottom: 0; left: 0; right: 0; background: #333; color: white; padding: 15px; text-align: center; z-index: 9999;"><p>Deze website gebruikt cookies om uw ervaring te verbeteren. <button id="accept-cookies" style="background: #009FE8; color: white; border: none; padding: 5px 15px; margin-left: 10px; border-radius: 5px; cursor: pointer;">Accepteren</button></p></div>');
        
        $('#accept-cookies').click(function() {
            localStorage.setItem('cookieConsent', 'true');
            $('#cookie-notice').fadeOut();
        });
    }
    
    // Back to top button
    $('body').append('<button id="back-to-top" style="position: fixed; bottom: 20px; right: 20px; background: #009FE8; color: white; border: none; padding: 10px 15px; border-radius: 50%; cursor: pointer; display: none; z-index: 1000; font-size: 18px;">↑</button>');
    
    $(window).scroll(function() {
        if ($(window).scrollTop() > 300) {
            $('#back-to-top').fadeIn();
        } else {
            $('#back-to-top').fadeOut();
        }
    });
    
    $('#back-to-top').click(function() {
        $('html, body').animate({scrollTop: 0}, 800);
    });
    
});

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Add loading class to body
    document.body.classList.add('loading');
    
    // Remove loading class when page is fully loaded
    window.addEventListener('load', function() {
        document.body.classList.remove('loading');
        document.body.classList.add('loaded');
    });
});
