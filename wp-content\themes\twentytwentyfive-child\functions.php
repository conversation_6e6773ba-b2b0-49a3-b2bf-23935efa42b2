<?php
/**
 * Twenty Twenty-Five Child Theme Functions
 * 
 * @package TwentyTwentyFive_Child
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue parent and child theme styles
 */
function twentytwentyfive_child_enqueue_styles() {
    // Get parent theme version
    $parent_style = 'twentytwentyfive-style';
    
    // Enqueue parent theme stylesheet
    wp_enqueue_style($parent_style, get_template_directory_uri() . '/style.css');
    
    // Enqueue child theme stylesheet
    wp_enqueue_style('twentytwentyfive-child-style',
        get_stylesheet_directory_uri() . '/style.css',
        array($parent_style),
        wp_get_theme()->get('Version')
    );
    
    // Enqueue custom JavaScript
    wp_enqueue_script('twentytwentyfive-child-script',
        get_stylesheet_directory_uri() . '/assets/js/custom.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );
}
add_action('wp_enqueue_scripts', 'twentytwentyfive_child_enqueue_styles');

/**
 * Add custom theme support
 */
function twentytwentyfive_child_theme_support() {
    // Add support for custom logo
    add_theme_support('custom-logo', array(
        'height'      => 60,
        'width'       => 200,
        'flex-height' => true,
        'flex-width'  => true,
    ));
    
    // Add support for custom header
    add_theme_support('custom-header', array(
        'default-color' => '009FE8',
        'height'        => 200,
        'flex-height'   => true,
    ));
    
    // Add support for custom background
    add_theme_support('custom-background', array(
        'default-color' => 'ffffff',
    ));
}
add_action('after_setup_theme', 'twentytwentyfive_child_theme_support');

/**
 * Register navigation menus
 */
function twentytwentyfive_child_register_menus() {
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'twentytwentyfive-child'),
        'footer'  => __('Footer Menu', 'twentytwentyfive-child'),
    ));
}
add_action('init', 'twentytwentyfive_child_register_menus');

/**
 * Add custom body classes
 */
function twentytwentyfive_child_body_classes($classes) {
    // Add taxi-freddy class to body
    $classes[] = 'taxi-freddy-theme';
    
    // Add page-specific classes
    if (is_front_page()) {
        $classes[] = 'homepage';
    }
    
    return $classes;
}
add_filter('body_class', 'twentytwentyfive_child_body_classes');

/**
 * Customize excerpt length
 */
function twentytwentyfive_child_excerpt_length($length) {
    return 30;
}
add_filter('excerpt_length', 'twentytwentyfive_child_excerpt_length');

/**
 * Add custom excerpt more text
 */
function twentytwentyfive_child_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'twentytwentyfive_child_excerpt_more');

/**
 * Add custom image sizes
 */
function twentytwentyfive_child_image_sizes() {
    add_image_size('service-card', 400, 300, true);
    add_image_size('hero-image', 1200, 600, true);
}
add_action('after_setup_theme', 'twentytwentyfive_child_image_sizes');

/**
 * Enqueue Google Fonts
 */
function twentytwentyfive_child_google_fonts() {
    wp_enqueue_style('google-fonts', 
        'https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap', 
        false
    );
}
add_action('wp_enqueue_scripts', 'twentytwentyfive_child_google_fonts');

/**
 * Add custom CSS variables
 */
function twentytwentyfive_child_css_variables() {
    echo '<style>
        :root {
            --taxi-primary: #009FE8;
            --taxi-primary-dark: #0080c7;
            --taxi-secondary: #28a745;
            --taxi-light: #f8f9fa;
            --taxi-dark: #333333;
            --taxi-white: #ffffff;
            --taxi-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
            --taxi-shadow-hover: 0 10px 35px rgba(0, 159, 232, 0.2);
            --taxi-border-radius: 15px;
            --taxi-transition: all 0.3s ease;
        }
    </style>';
}
add_action('wp_head', 'twentytwentyfive_child_css_variables');

/**
 * Add custom admin styles
 */
function twentytwentyfive_child_admin_styles() {
    echo '<style>
        .wp-admin #wpadminbar {
            background: #009FE8;
        }
        .wp-admin #wpadminbar .ab-item {
            color: white;
        }
    </style>';
}
add_action('admin_head', 'twentytwentyfive_child_admin_styles');

/**
 * Customize login page
 */
function twentytwentyfive_child_login_styles() {
    echo '<style>
        body.login {
            background: linear-gradient(135deg, #009FE8 0%, #0080c7 100%);
        }
        .login h1 a {
            background-image: none;
            background-color: white;
            color: #009FE8;
            font-weight: bold;
            text-decoration: none;
            width: auto;
            height: auto;
            padding: 20px;
            border-radius: 10px;
        }
        .login form {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        .wp-core-ui .button-primary {
            background: #009FE8;
            border-color: #009FE8;
            border-radius: 25px;
        }
        .wp-core-ui .button-primary:hover {
            background: #0080c7;
            border-color: #0080c7;
        }
    </style>';
}
add_action('login_head', 'twentytwentyfive_child_login_styles');

/**
 * Change login logo URL
 */
function twentytwentyfive_child_login_logo_url() {
    return home_url();
}
add_filter('login_headerurl', 'twentytwentyfive_child_login_logo_url');

/**
 * Change login logo title
 */
function twentytwentyfive_child_login_logo_title() {
    return get_bloginfo('name');
}
add_filter('login_headertext', 'twentytwentyfive_child_login_logo_title');
