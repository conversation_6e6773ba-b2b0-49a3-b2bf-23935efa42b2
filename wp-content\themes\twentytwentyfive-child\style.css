/*
Theme Name: Twenty Twenty-Five Child - Taxi Freddy
Description: Child theme of Twenty Twenty-Five for Taxi Freddy website
Template: twentytwentyfive
Version: 1.0.0
*/

/* Import parent theme styles */
@import url("../twentytwentyfive/style.css");

/* Custom styles for Taxi Freddy based on Mockup.webp */

/* Header Styles */
.site-header {
    background: #009FE8;
    padding: 10px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 159, 232, 0.2);
}

.header-contact-bar {
    background: #009FE8;
    color: white;
    padding: 8px 0;
    font-size: 14px;
    font-weight: 600;
}

.header-contact-bar a {
    color: white;
    text-decoration: none;
}

.header-contact-bar a:hover {
    text-decoration: underline;
}

/* Navigation Styles */
.wp-block-navigation {
    background: white;
    padding: 15px 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.wp-block-navigation .wp-block-navigation-item a {
    color: #333;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 0.5px;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.wp-block-navigation .wp-block-navigation-item a:hover,
.wp-block-navigation .wp-block-navigation-item.current-menu-item a {
    background: #009FE8;
    color: white;
}

/* Logo Styles */
.site-logo {
    max-height: 60px;
    width: auto;
}

/* Hero Section Styles */
.hero-section {
    background: linear-gradient(135deg, #009FE8 0%, #0080c7 100%);
    padding: 80px 0;
    text-align: center;
    color: white;
}

.hero-section h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-section p {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.95;
}

/* Button Styles */
.wp-block-button__link {
    border-radius: 50px !important;
    padding: 15px 35px !important;
    font-weight: 700 !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.wp-block-button__link:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Primary Button */
.btn-primary {
    background: white !important;
    color: #009FE8 !important;
    border: 2px solid white;
}

.btn-primary:hover {
    background: transparent !important;
    color: white !important;
    border: 2px solid white;
}

/* Secondary Button */
.btn-secondary {
    background: transparent !important;
    color: white !important;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white !important;
    color: #009FE8 !important;
}

/* Card Styles */
.service-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 35px rgba(0, 159, 232, 0.2);
}

.service-card h3 {
    color: #009FE8;
    font-weight: 700;
    margin-bottom: 15px;
}

.service-card .price {
    color: #009FE8;
    font-size: 1.5rem;
    font-weight: 800;
    margin: 10px 0;
}

/* Section Styles */
.section-padding {
    padding: 80px 0;
}

.section-bg-light {
    background: #f8f9fa;
}

.section-bg-primary {
    background: #009FE8;
    color: white;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
}

h2 {
    color: #009FE8;
    margin-bottom: 40px;
}

.section-bg-primary h2 {
    color: white;
}

/* Contact Section */
.contact-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-card h3 {
    color: white;
    margin-bottom: 20px;
}

.contact-card .phone-number {
    font-size: 2rem;
    font-weight: 800;
    color: white;
    margin: 15px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-section p {
        font-size: 1.1rem;
    }
    
    .wp-block-button__link {
        padding: 12px 25px !important;
        font-size: 14px;
    }
    
    .service-card {
        margin-bottom: 20px;
    }
    
    .contact-card .phone-number {
        font-size: 1.5rem;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-white {
    color: white;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-20 {
    margin-bottom: 20px;
}

.mb-40 {
    margin-bottom: 40px;
}

.mt-40 {
    margin-top: 40px;
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Custom WordPress Block Styles */
.wp-block-group.hero-section {
    background: linear-gradient(135deg, #009FE8 0%, #0080c7 100%) !important;
}

.wp-block-columns.service-grid .wp-block-column {
    margin-bottom: 30px;
}

/* Footer Styles */
.site-footer {
    background: #333;
    color: white;
    padding: 40px 0 20px;
}

.site-footer a {
    color: #009FE8;
}

.site-footer a:hover {
    color: white;
}

/* Modern Layout Improvements based on Mockup */

/* Header Improvements */
.header-contact-bar {
    font-size: 13px;
    font-weight: 500;
}

.main-header {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 999;
}

.wp-block-site-title a {
    text-decoration: none;
    color: #009FE8;
}

/* Navigation Improvements */
.wp-block-navigation-item a {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 13px;
    letter-spacing: 0.8px;
    padding: 12px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
    color: #333;
}

.wp-block-navigation-item a:hover {
    background: #009FE8;
    color: white;
    transform: translateY(-2px);
}

/* Hero Section Modern Style */
.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,100 1000,0 1000,100"/></svg>') no-repeat bottom;
    background-size: cover;
}

/* Card Hover Effects */
.wp-block-group[style*="background-color:#f8f9fa"],
.wp-block-group[style*="background-color:#ffffff"] {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 159, 232, 0.1);
}

.wp-block-group[style*="background-color:#f8f9fa"]:hover,
.wp-block-group[style*="background-color:#ffffff"]:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 159, 232, 0.15);
    border-color: rgba(0, 159, 232, 0.3);
}

/* Button Enhancements */
.wp-block-button__link {
    font-size: 14px !important;
    font-weight: 700 !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease !important;
}

/* Price Styling */
.wp-block-paragraph[style*="font-size:1.5rem"][style*="font-weight:700"] {
    background: linear-gradient(45deg, #009FE8, #0080c7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
}

/* Section Spacing */
.wp-block-group[style*="padding-top:var(--wp--preset--spacing--80)"] {
    padding-top: 100px !important;
    padding-bottom: 100px !important;
}

/* Typography Improvements */
.wp-block-heading {
    font-weight: 700;
    line-height: 1.2;
}

.wp-block-heading[style*="color:#009FE8"] {
    position: relative;
}

.wp-block-heading[style*="color:#009FE8"]::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(45deg, #009FE8, #0080c7);
    border-radius: 2px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .header-contact-bar .wp-block-group {
        flex-direction: column;
        text-align: center;
        gap: 5px;
    }

    .main-header .wp-block-group {
        flex-direction: column;
        gap: 20px;
    }

    .wp-block-navigation {
        justify-content: center;
    }

    .wp-block-navigation-item a {
        padding: 8px 15px;
        font-size: 12px;
    }
}

/* Loading Animation */
.loading {
    overflow: hidden;
}

.loading * {
    animation-play-state: paused;
}

.loaded .fade-in {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scroll Animations */
.wp-block-group {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.wp-block-group.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced Contact Cards */
.wp-block-group[style*="background-color:#009FE8"] .wp-block-group,
.wp-block-group[style*="background-color:#28a745"] .wp-block-group {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Improved List Styling */
.wp-block-list li {
    margin-bottom: 8px;
    padding-left: 5px;
}

.wp-block-list li::marker {
    color: #009FE8;
}

/* Enhanced Footer */
.site-footer .wp-block-columns {
    gap: 40px;
}

.site-footer .wp-block-heading {
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.site-footer .wp-block-list {
    margin-top: 15px;
}

.site-footer .wp-block-list li {
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.site-footer .wp-block-list li:hover {
    transform: translateX(5px);
}
